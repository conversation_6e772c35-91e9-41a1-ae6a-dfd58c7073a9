#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
表名检测测试脚本
测试动态表名检测功能
"""

def test_table_detection():
    """测试表名检测"""
    try:
        print("🔍 测试表名检测功能...")
        
        from src.quota_revision_processor import QuotaRevisionProcessor
        from src.advanced_quota_manager import AdvancedQuotaManager
        from src.config_persistence_manager import ConfigPersistenceManager
        import psycopg2
        
        # 创建管理器
        quota_manager = AdvancedQuotaManager()
        
        # 加载配置并连接数据库
        config_manager = ConfigPersistenceManager()
        config = config_manager.load_config()
        quota_db_config = config.get('database_configs', {}).get('quota_db', {})
        
        # 手动连接数据库
        conn = psycopg2.connect(
            host=quota_db_config.get('host', 'localhost'),
            port=int(quota_db_config.get('port', 5432)),
            user=quota_db_config.get('username', 'postgres'),
            password=quota_db_config.get('password', ''),
            database=quota_db_config.get('db_name', 'beijing2021_quota_database'),
            client_encoding='utf8'
        )
        
        # 设置管理器的连接信息
        quota_manager.connection = conn
        quota_manager.db_path = quota_db_config.get('db_name')
        quota_manager.db_type = 'postgresql'
        
        # 创建修订处理器
        revision_processor = QuotaRevisionProcessor(quota_manager=quota_manager)
        
        # 测试表名检测
        cursor = conn.cursor()
        parent_table, child_table = revision_processor._detect_table_names(cursor)
        
        print(f"✅ 表名检测结果:")
        print(f"   定额表: {parent_table}")
        print(f"   资源表: {child_table}")
        
        if parent_table and child_table:
            # 测试数据加载
            print(f"\n🔍 测试数据加载...")
            
            success, message, quota_data, stats = revision_processor.load_from_connected_database()
            
            if success:
                print(f"✅ 数据加载成功:")
                print(f"   消息: {message}")
                print(f"   统计: {stats}")
                print(f"   数据行数: {len(quota_data)}")
                
                # 显示前几行数据
                if quota_data:
                    print(f"\n📋 前3行数据:")
                    for i, row in enumerate(quota_data[:3]):
                        print(f"   行{i+1}: {row}")
                
                return True
            else:
                print(f"❌ 数据加载失败: {message}")
                return False
        else:
            print("❌ 表名检测失败")
            return False
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_direct_query():
    """直接查询测试"""
    try:
        print("\n🔍 直接查询测试...")
        
        from src.config_persistence_manager import ConfigPersistenceManager
        import psycopg2
        
        config_manager = ConfigPersistenceManager()
        config = config_manager.load_config()
        quota_db_config = config.get('database_configs', {}).get('quota_db', {})
        
        conn = psycopg2.connect(
            host=quota_db_config.get('host', 'localhost'),
            port=int(quota_db_config.get('port', 5432)),
            user=quota_db_config.get('username', 'postgres'),
            password=quota_db_config.get('password', ''),
            database=quota_db_config.get('db_name', 'beijing2021_quota_database'),
            client_encoding='utf8'
        )
        
        cursor = conn.cursor()
        
        # 查询所有表
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
        """)
        tables = [row[0] for row in cursor.fetchall()]
        
        print(f"📊 数据库中的表: {tables}")
        
        # 查询每个表的行数
        for table in tables:
            try:
                cursor.execute(f'SELECT COUNT(*) FROM "{table}"')
                count = cursor.fetchone()[0]
                print(f"   {table}: {count} 行")
                
                # 如果是定额表，显示列信息
                if 'quota' in table.lower():
                    cursor.execute(f"""
                        SELECT column_name, data_type 
                        FROM information_schema.columns 
                        WHERE table_name = '{table}'
                        ORDER BY ordinal_position
                    """)
                    columns = cursor.fetchall()
                    print(f"     列: {[f'{col[0]}({col[1]})' for col in columns]}")
                    
                    # 显示前几行数据
                    cursor.execute(f'SELECT * FROM "{table}" LIMIT 3')
                    rows = cursor.fetchall()
                    for i, row in enumerate(rows):
                        print(f"     行{i+1}: {row}")
                
            except Exception as e:
                print(f"   {table}: 查询失败 - {e}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 直接查询测试失败: {e}")
        return False

def main():
    """主测试流程"""
    print("🔧 表名检测测试工具")
    print("=" * 50)
    
    # 1. 直接查询测试
    if not test_direct_query():
        return
    
    # 2. 表名检测测试
    if test_table_detection():
        print("\n🎉 所有测试通过！")
        print("✅ 表名检测功能正常")
        print("✅ 数据加载功能正常")
        print("✅ 识别定额修订功能应该可以正常工作了")
    else:
        print("\n❌ 测试失败")
        print("💡 请检查错误信息")

if __name__ == "__main__":
    main()
