#!/usr/bin/env python3
"""
测试解析修复功能
Test parsing fix functionality
"""

import os
import sys
import json

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_list_format_parsing():
    """测试列表格式解析"""
    print("🧪 测试列表格式解析")
    print("=" * 50)
    
    try:
        from src.intelligent_price_info_processor import IntelligentPriceInfoProcessor
        processor = IntelligentPriceInfoProcessor()
        
        # 模拟AI返回的列表格式结果
        test_list_result = [
            {
                'text': '''```json
{
    "page_header": "工程造价信息价",
    "chapters": [
        {
            "chapter_code": "01",
            "chapter_name": "黑色及有色金属",
            "remarks": "",
            "price_items": [
                {
                    "resource_code": "0101010002-2",
                    "product_name": "热轧光圆钢筋",
                    "specifications": "HPB300 Φ8",
                    "unit": "t",
                    "price_with_tax": "4172.00",
                    "price_without_tax": "3692.00"
                },
                {
                    "resource_code": "0101010003-2",
                    "product_name": "热轧光圆钢筋",
                    "specifications": "HPB300 Φ10",
                    "unit": "t",
                    "price_with_tax": "4096.00",
                    "price_without_tax": "3625.00"
                }
            ]
        }
    ]
}
```'''
            }
        ]
        
        print("测试用例1: 列表格式（包含markdown代码块）")
        result = processor._parse_price_info_result(test_list_result, 1)
        
        if result:
            print(f"✅ 解析成功，章节数: {len(result)}")
            print(f"   价格条目数: {len(result[0]['price_items'])}")
            print(f"   第一个条目: {result[0]['price_items'][0]['product_name']}")
        else:
            print("❌ 解析失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_markdown_extraction():
    """测试markdown代码块提取"""
    print("\n🧪 测试markdown代码块提取")
    print("=" * 50)
    
    try:
        from src.intelligent_price_info_processor import IntelligentPriceInfoProcessor
        processor = IntelligentPriceInfoProcessor()
        
        # 测试用例1: 标准markdown代码块
        test_markdown = '''```json
{
    "page_header": "测试",
    "chapters": []
}
```'''
        
        extracted = processor._extract_json_from_markdown(test_markdown)
        if extracted and '{' in extracted:
            print("✅ 标准markdown代码块提取成功")
        else:
            print("❌ 标准markdown代码块提取失败")
            return False
        
        # 测试用例2: 无标签代码块
        test_markdown2 = '''```
{
    "page_header": "测试2",
    "chapters": []
}
```'''
        
        extracted2 = processor._extract_json_from_markdown(test_markdown2)
        if extracted2 and '{' in extracted2:
            print("✅ 无标签代码块提取成功")
        else:
            print("❌ 无标签代码块提取失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_format_normalization():
    """测试格式标准化"""
    print("\n🧪 测试格式标准化")
    print("=" * 50)
    
    try:
        from src.intelligent_price_info_processor import IntelligentPriceInfoProcessor
        processor = IntelligentPriceInfoProcessor()
        
        # 测试用例1: 字符串格式
        test_string = "这是一个字符串"
        result1 = processor._normalize_result_format(test_string, 1)
        if result1 == test_string:
            print("✅ 字符串格式标准化成功")
        else:
            print("❌ 字符串格式标准化失败")
            return False
        
        # 测试用例2: 列表格式
        test_list = [{"text": "列表中的文本"}]
        result2 = processor._normalize_result_format(test_list, 2)
        if result2 == "列表中的文本":
            print("✅ 列表格式标准化成功")
        else:
            print("❌ 列表格式标准化失败")
            print(f"   期望: 列表中的文本")
            print(f"   实际: {result2}")
            return False
        
        # 测试用例3: 字典格式
        test_dict = {"content": "字典中的内容"}
        result3 = processor._normalize_result_format(test_dict, 3)
        if result3 == "字典中的内容":
            print("✅ 字典格式标准化成功")
        else:
            print("❌ 字典格式标准化失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_complex_json_parsing():
    """测试复杂JSON解析"""
    print("\n🧪 测试复杂JSON解析")
    print("=" * 50)
    
    try:
        from src.intelligent_price_info_processor import IntelligentPriceInfoProcessor
        processor = IntelligentPriceInfoProcessor()
        
        # 模拟实际的复杂结果
        complex_result = '''根据图片分析，我识别到以下信息：

```json
{
    "page_header": "工程造价信息价",
    "chapters": [
        {
            "chapter_code": "01",
            "chapter_name": "黑色及有色金属",
            "remarks": "价格仅供参考",
            "price_items": [
                {
                    "resource_code": "0101010002-2",
                    "product_name": "热轧光圆钢筋",
                    "specifications": "HPB300 Φ8",
                    "unit": "t",
                    "price_with_tax": "4172.00",
                    "price_without_tax": "3692.00"
                }
            ]
        }
    ]
}
```

以上是我的分析结果。'''
        
        result = processor._parse_price_info_result(complex_result, 1)
        
        if result:
            print(f"✅ 复杂JSON解析成功，章节数: {len(result)}")
            chapter = result[0]
            print(f"   页眉: {chapter['page_header']}")
            print(f"   章节: {chapter['chapter_name']}")
            print(f"   条目数: {len(chapter['price_items'])}")
            
            if chapter['price_items']:
                item = chapter['price_items'][0]
                print(f"   第一个条目: {item['product_name']}")
                print(f"   资源编号: {item['resource_code']}")
        else:
            print("❌ 复杂JSON解析失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n🧪 测试错误处理")
    print("=" * 50)
    
    try:
        from src.intelligent_price_info_processor import IntelligentPriceInfoProcessor
        processor = IntelligentPriceInfoProcessor()
        
        # 测试用例1: 空结果
        result1 = processor._parse_price_info_result("", 1)
        if result1 == []:
            print("✅ 空结果处理正确")
        else:
            print("❌ 空结果处理失败")
            return False
        
        # 测试用例2: 无效JSON
        result2 = processor._parse_price_info_result("这不是JSON", 2)
        if result2 == []:
            print("✅ 无效JSON处理正确")
        else:
            print("❌ 无效JSON处理失败")
            return False
        
        # 测试用例3: None值
        result3 = processor._parse_price_info_result(None, 3)
        if result3 == []:
            print("✅ None值处理正确")
        else:
            print("❌ None值处理失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 解析修复功能测试")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("列表格式解析", test_list_format_parsing),
        ("markdown代码块提取", test_markdown_extraction),
        ("格式标准化", test_format_normalization),
        ("复杂JSON解析", test_complex_json_parsing),
        ("错误处理", test_error_handling),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！解析修复功能正常。")
        print("💡 现在信息价识别可以正确处理各种AI返回格式：")
        print("   - 列表格式结果")
        print("   - markdown代码块")
        print("   - 复杂的混合格式")
        print("   - 错误和异常情况")
    elif passed >= total - 1:
        print("✅ 基本功能正常！可能有个别小问题。")
        print("💡 建议在Web界面中测试实际功能。")
    else:
        print("⚠️ 存在多个问题，需要进一步检查。")

if __name__ == "__main__":
    main()
