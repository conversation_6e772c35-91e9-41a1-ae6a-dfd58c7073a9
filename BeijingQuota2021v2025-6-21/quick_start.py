#!/usr/bin/env python3
"""
快速启动脚本 - 一键启动北京市消耗定额智能提取系统
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    print("=" * 60)
    print("🚀 北京市消耗定额智能提取系统")
    print("=" * 60)
    print("基于MCP的智能PDF定额数据提取工具")
    print("支持自动识别表格并生成CSV文件")
    print("=" * 60)

def check_poppler():
    """检查Poppler是否安装"""
    try:
        result = subprocess.run(["pdftoppm", "-h"], 
                              capture_output=True, text=True, timeout=5)
        return True
    except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.CalledProcessError):
        return False

def show_poppler_install_guide():
    """显示Poppler安装指南"""
    print("\n⚠️ 检测到Poppler未安装，这是PDF处理的必需组件")
    print("\n📥 Windows安装Poppler:")
    print("1. 下载Poppler: https://github.com/oschwartz10612/poppler-windows/releases/")
    print("2. 解压到任意目录，例如: C:\\poppler")
    print("3. 将bin目录添加到PATH环境变量: C:\\poppler\\bin")
    print("4. 重启命令行窗口")
    print("\n或者使用包管理器安装:")
    print("- Chocolatey: choco install poppler")
    print("- Scoop: scoop install poppler")
    
    choice = input("\n是否继续启动系统？(y/n): ").lower().strip()
    return choice == 'y' or choice == 'yes'

def start_gradio_app():
    """启动Gradio应用"""
    try:
        print("\n🚀 正在启动Web界面...")
        print("请稍等，首次启动可能需要一些时间...")
        
        # 启动主程序
        python_cmd = "py" if os.name == 'nt' else "python"
        process = subprocess.Popen([python_cmd, "main.py"])
        
        # 等待几秒钟让服务器启动
        time.sleep(5)
        
        # 自动打开浏览器
        url = "http://localhost:7860"
        print(f"\n🌐 正在打开浏览器: {url}")
        webbrowser.open(url)
        
        print("\n" + "=" * 60)
        print("✅ 系统已启动！")
        print("=" * 60)
        print(f"🌐 Web界面地址: {url}")
        print("📖 使用说明:")
        print("  1. 在浏览器中上传PDF定额文件")
        print("  2. 设置要提取的页码范围")
        print("  3. 点击'开始提取'按钮")
        print("  4. 等待处理完成后下载CSV文件")
        print("\n💡 提示:")
        print("  - 首次使用时浏览器会自动打开DeepSeek网站")
        print("  - 如需登录DeepSeek，请在浏览器中手动登录")
        print("  - 处理大文件时请耐心等待")
        print("  - 按Ctrl+C可以停止系统")
        print("=" * 60)
        
        # 等待用户中断
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n👋 正在停止系统...")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            print("✅ 系统已停止")
        
        return True
        
    except FileNotFoundError:
        print("❌ 找不到main.py文件，请确保在正确的目录中运行")
        return False
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def main():
    """主函数"""
    print_banner()
    
    # 检查Python环境
    print("🔍 检查Python环境...")
    try:
        python_cmd = "py" if os.name == 'nt' else "python"
        result = subprocess.run([python_cmd, "--version"], 
                              capture_output=True, text=True, timeout=5)
        print(f"✅ {result.stdout.strip()}")
    except Exception as e:
        print(f"❌ Python环境检查失败: {e}")
        return False
    
    # 检查必要文件
    print("🔍 检查系统文件...")
    required_files = ["main.py", "src/config.py"]
    missing_files = [f for f in required_files if not Path(f).exists()]
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        print("请确保在正确的目录中运行此脚本")
        return False
    
    print("✅ 系统文件检查完成")
    
    # 检查Poppler
    print("🔍 检查PDF处理组件...")
    if check_poppler():
        print("✅ Poppler已安装")
    else:
        if not show_poppler_install_guide():
            print("👋 用户取消启动")
            return False
    
    # 启动系统
    return start_gradio_app()

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 用户中断，退出程序")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        sys.exit(1)
