# 阿里云通义千问-QVQ模型配置指南

## 🌟 QVQ模型简介

通义千问-QVQ（Qwen with Visual Question-answering）是阿里云推出的具有强大视觉推理能力的多模态大模型，特别适合处理北京市消耗定额表格的识别和分析。

### 🎯 QVQ模型特点

- **🧠 强大的视觉推理能力**：具备思维链推理，能够逐步分析复杂图像
- **📊 表格理解专长**：特别适合处理结构化数据和表格内容  
- **🔍 细节识别精准**：能够准确识别图像中的文字、数字和结构
- **💭 思考过程可见**：提供详细的推理过程，便于理解AI的分析思路
- **🎯 中文优化**：针对中文内容进行了特别优化
- **⚡ 流式输出**：支持实时查看思考和回答过程

### 📋 可用模型版本

1. **QVQ-Max**：最强版本，推理能力最佳，适合复杂表格分析
2. **QVQ-Plus**：平衡版本，速度与效果兼顾，适合批量处理

## 🔧 配置步骤

### 第一步：获取API密钥

1. **访问阿里云百炼控制台**
   - 网址：https://bailian.console.aliyun.com/
   - 使用阿里云账号登录

2. **开通百炼服务**
   - 如果是首次使用，需要开通百炼服务
   - 选择合适的计费方式（按量付费或包年包月）

3. **创建API密钥**
   - 在控制台中找到"API密钥管理"
   - 点击"创建API密钥"
   - 复制生成的API密钥（格式类似：sk-xxx）

### 第二步：配置环境变量

#### 方法1：使用配置助手（推荐）

```bash
py setup_api_keys.py
```

在配置过程中：
1. 选择"阿里云百炼/通义千问-QVQ"
2. 输入您获取的API密钥
3. 其他模型可以跳过（直接按回车）

#### 方法2：手动配置

1. **创建.env文件**（如果不存在）
2. **添加以下内容**：
```env
# 阿里云百炼/通义千问-QVQ模型
DASHSCOPE_API_KEY=your_dashscope_api_key_here
```

3. **替换API密钥**：
   - 将`your_dashscope_api_key_here`替换为您的实际API密钥

### 第三步：验证配置

运行测试脚本验证配置：

```bash
py test_qvq_model.py
```

如果配置正确，您会看到：
- ✅ 找到API密钥
- ✅ 可用的QVQ模型：2个
- ✅ QVQ-Max API连接成功

## 🚀 使用方法

### 启动系统

```bash
py main.py
```

系统将在浏览器中打开：`http://localhost:7862`

### 选择QVQ模型

在Web界面中：
1. **选择AI模型**：从下拉菜单中选择
   - "阿里通义千问-QVQ-Max"（推荐，效果最佳）
   - "阿里通义千问-QVQ-Plus"（速度较快）

2. **上传PDF文件**：选择北京市消耗定额PDF文件

3. **设置页码范围**：建议先测试1-2页

4. **开始提取**：点击按钮开始处理

### 查看处理过程

QVQ模型的独特优势是可以看到AI的思考过程：

1. **思考阶段**：AI会分析表格结构，识别各个部分
2. **推理阶段**：逐步提取定额信息和资源消耗数据
3. **输出阶段**：生成结构化的JSON结果

## 📊 输出格式

QVQ模型会按照以下格式提取定额信息：

### 父级定额信息
- 编号：04-01-1-6
- 定额项名称：人工挖沟槽土方 一、二类土
- 工作内容：挖土、余土清理、修整底边、打钉拍底等
- 单位：m³

### 子级资源消耗
- 资源编号：00010701, 99030030, 99460004
- 类别：人工、机械、其他费用
- 子项名称：综合用工三类、电动打钉机、其他机具费占人工费
- 单位：工日/m³、台班/m³、%
- 消耗量：0.182、0.0039、1.50

## 💰 计费说明

### QVQ模型计费方式

- **按Token计费**：根据输入和输出的Token数量计费
- **图片处理**：每张图片会产生一定的Token消耗
- **流式输出**：思考过程和最终回答都会计入Token

### 成本优化建议

1. **批量处理**：一次处理多页可以提高效率
2. **选择合适模型**：QVQ-Plus成本更低，QVQ-Max效果更好
3. **优化图片质量**：清晰的图片可以减少重试次数

## 🔍 故障排除

### 常见问题

1. **API密钥错误**
   - 检查密钥格式是否正确
   - 确认密钥是否已激活
   - 验证账户余额是否充足

2. **网络连接问题**
   - 确保网络可以访问阿里云服务
   - 检查防火墙设置

3. **识别效果不佳**
   - 尝试提高PDF转图片的DPI设置
   - 确保表格图片清晰可读
   - 检查是否为标准的北京市定额表格格式

### 调试方法

1. **查看详细日志**：
   ```bash
   py main.py
   ```
   在控制台中查看详细的处理日志

2. **测试单张图片**：
   使用简化版测试程序测试单张图片

3. **检查API配额**：
   在阿里云控制台查看API调用次数和余额

## 📞 技术支持

如果遇到问题，可以：

1. **查看阿里云文档**：https://help.aliyun.com/zh/model-studio/qvq
2. **联系阿里云技术支持**：通过控制台提交工单
3. **查看系统日志**：检查logs目录下的日志文件

## 🎉 开始使用

配置完成后，您就可以享受QVQ模型强大的视觉推理能力来处理北京市消耗定额表格了！

QVQ模型特别适合处理复杂的表格结构，能够准确识别定额编号、工作内容、资源消耗等信息，并且提供详细的思考过程，让您了解AI是如何分析和理解表格内容的。
