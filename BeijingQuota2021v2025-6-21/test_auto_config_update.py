#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试自动配置更新功能
验证界面连接数据库后是否自动更新配置文件
"""

def test_auto_config_update():
    """测试自动配置更新"""
    try:
        print("🔍 测试自动配置更新功能...")
        
        from src.config_persistence_manager import ConfigPersistenceManager
        
        # 1. 查看当前配置
        print("\n📊 当前配置:")
        config_manager = ConfigPersistenceManager()
        config = config_manager.load_config()
        quota_db_config = config.get('database_configs', {}).get('quota_db', {})
        
        print(f"   数据库类型: {quota_db_config.get('db_type')}")
        print(f"   数据库名称: {quota_db_config.get('db_name')}")
        print(f"   主机: {quota_db_config.get('host')}")
        print(f"   端口: {quota_db_config.get('port')}")
        print(f"   用户: {quota_db_config.get('username')}")
        
        # 2. 模拟界面连接到不同的数据库
        print("\n🔗 模拟界面连接到 beijing2021_quota_database...")
        
        from src.advanced_quota_handlers import AdvancedQuotaHandlers
        
        handlers = AdvancedQuotaHandlers()
        
        # 模拟连接参数
        db_type = "postgresql"
        db_path = ""
        db_host = "localhost"
        db_port = "5432"
        db_name = "beijing2021_quota_database"  # 连接到空数据库
        db_user = "postgres"
        db_password = "postgres123"
        
        # 调用连接方法
        result = handlers.connect_to_database(
            db_type, db_path, db_host, db_port, db_name, db_user, db_password
        )
        
        connection_status = result[0]
        print(f"   连接结果: {connection_status}")
        
        # 3. 检查配置是否自动更新
        print("\n📊 连接后的配置:")
        config = config_manager.load_config()
        quota_db_config = config.get('database_configs', {}).get('quota_db', {})
        
        print(f"   数据库类型: {quota_db_config.get('db_type')}")
        print(f"   数据库名称: {quota_db_config.get('db_name')}")
        print(f"   主机: {quota_db_config.get('host')}")
        print(f"   端口: {quota_db_config.get('port')}")
        print(f"   用户: {quota_db_config.get('username')}")
        
        # 4. 验证配置是否正确更新
        if quota_db_config.get('db_name') == db_name:
            print(f"✅ 配置文件自动更新成功！")
            
            # 5. 现在连接到有数据的数据库
            print(f"\n🔗 模拟界面连接到 beijing2021_quota_test...")
            
            db_name_with_data = "beijing2021_quota_test"
            
            result2 = handlers.connect_to_database(
                db_type, db_path, db_host, db_port, db_name_with_data, db_user, db_password
            )
            
            connection_status2 = result2[0]
            print(f"   连接结果: {connection_status2}")
            
            # 6. 再次检查配置
            print("\n📊 第二次连接后的配置:")
            config = config_manager.load_config()
            quota_db_config = config.get('database_configs', {}).get('quota_db', {})
            
            print(f"   数据库类型: {quota_db_config.get('db_type')}")
            print(f"   数据库名称: {quota_db_config.get('db_name')}")
            print(f"   主机: {quota_db_config.get('host')}")
            print(f"   端口: {quota_db_config.get('port')}")
            print(f"   用户: {quota_db_config.get('username')}")
            
            if quota_db_config.get('db_name') == db_name_with_data:
                print(f"✅ 配置文件第二次自动更新成功！")
                return True
            else:
                print(f"❌ 配置文件第二次更新失败")
                return False
        else:
            print(f"❌ 配置文件自动更新失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_persistence():
    """测试配置持久化"""
    try:
        print("\n🔍 测试配置持久化...")
        
        from src.config_persistence_manager import ConfigPersistenceManager
        
        config_manager = ConfigPersistenceManager()
        config = config_manager.load_config()
        
        quota_db_config = config.get('database_configs', {}).get('quota_db', {})
        
        print(f"📊 最终配置:")
        print(f"   数据库类型: {quota_db_config.get('db_type')}")
        print(f"   数据库名称: {quota_db_config.get('db_name')}")
        print(f"   主机: {quota_db_config.get('host')}")
        print(f"   端口: {quota_db_config.get('port')}")
        print(f"   用户: {quota_db_config.get('username')}")
        
        # 验证其他模块是否能正确读取
        print(f"\n🔍 验证其他模块读取配置...")
        
        from src.advanced_quota_manager import AdvancedQuotaManager
        
        quota_manager = AdvancedQuotaManager()
        
        if quota_manager.revision_processor:
            print(f"✅ 修订处理器可以访问配置")
            
            # 测试连接
            try:
                success, message, quota_data, stats = quota_manager.revision_processor.load_from_connected_database()
                
                if success:
                    print(f"✅ 使用最新配置加载数据成功: {stats}")
                    return True
                else:
                    print(f"❌ 使用最新配置加载数据失败: {message}")
                    return False
            except Exception as e:
                print(f"❌ 数据加载测试异常: {e}")
                return False
        else:
            print(f"❌ 修订处理器不存在")
            return False
        
    except Exception as e:
        print(f"❌ 配置持久化测试失败: {e}")
        return False

def main():
    """主测试流程"""
    print("🔧 自动配置更新测试工具")
    print("=" * 60)
    
    # 1. 测试自动配置更新
    update_success = test_auto_config_update()
    
    # 2. 测试配置持久化
    persistence_success = test_config_persistence()
    
    print("\n" + "=" * 60)
    
    if update_success and persistence_success:
        print("🎉 所有测试通过！")
        print("✅ 自动配置更新功能正常")
        print("✅ 配置持久化功能正常")
        print("✅ 界面连接数据库后会自动更新配置文件")
        
        print("\n💡 现在的工作流程:")
        print("1. 用户在界面连接数据库")
        print("2. 连接成功后自动更新配置文件")
        print("3. 所有模块使用最新的配置")
        print("4. 不再需要手动修改配置文件")
    else:
        print("❌ 部分测试失败")
        print("💡 请检查失败的项目")

if __name__ == "__main__":
    main()
