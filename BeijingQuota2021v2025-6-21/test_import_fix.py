#!/usr/bin/env python3
"""
测试导入修复
"""

import sys
import os

def test_imports():
    """测试各种导入方式"""
    print("🧪 测试模块导入...")
    
    # 测试1: 直接导入MCP工具
    print("\n1️⃣ 测试MCP数据库转换工具导入...")
    try:
        sys.path.append('src')
        from mcp_database_converter import MCPDatabaseConverter
        converter = MCPDatabaseConverter()
        print("✅ MCP数据库转换工具导入成功")
        print(f"   支持的格式: {list(converter.supported_formats.keys())}")
    except Exception as e:
        print(f"❌ MCP工具导入失败: {e}")
        return False
    
    # 测试2: 导入企业定额管理器
    print("\n2️⃣ 测试企业定额管理器导入...")
    try:
        from enterprise_quota_manager import EnterpriseQuotaManager
        manager = EnterpriseQuotaManager()
        print("✅ 企业定额管理器导入成功")
        print(f"   支持的数据库类型: {list(manager.supported_db_types.keys())}")
    except Exception as e:
        print(f"❌ 企业定额管理器导入失败: {e}")
        return False
    
    # 测试3: 模拟Web环境导入
    print("\n3️⃣ 测试Web环境导入...")
    try:
        # 模拟从main.py中导入的方式
        try:
            from src.enterprise_quota_manager import EnterpriseQuotaManager as WebManager
        except ImportError:
            sys.path.append('src')
            from enterprise_quota_manager import EnterpriseQuotaManager as WebManager
        
        web_manager = WebManager()
        print("✅ Web环境导入成功")
        print(f"   MCP工具集成: {'✅' if hasattr(web_manager, 'mcp_converter') else '❌'}")
    except Exception as e:
        print(f"❌ Web环境导入失败: {e}")
        return False
    
    # 测试4: 功能测试
    print("\n4️⃣ 测试基本功能...")
    try:
        # 测试支持的数据库类型
        db_types = manager.supported_db_types
        print(f"✅ 支持的数据库类型: {len(db_types)} 种")
        for db_type, description in db_types.items():
            print(f"   - {db_type}: {description}")
        
        # 测试MCP工具集成
        if hasattr(manager, 'mcp_converter'):
            print("✅ MCP工具集成正常")
        else:
            print("❌ MCP工具集成失败")
            return False
            
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🔧 导入修复验证工具")
    print("=" * 40)
    
    # 显示当前环境信息
    print(f"📍 当前工作目录: {os.getcwd()}")
    print(f"🐍 Python路径: {sys.executable}")
    print(f"📦 模块搜索路径:")
    for i, path in enumerate(sys.path[:5]):  # 只显示前5个
        print(f"   {i+1}. {path}")
    
    # 运行测试
    success = test_imports()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 所有导入测试通过!")
        print("\n💡 修复效果:")
        print("1. ✅ MCP工具导入正常")
        print("2. ✅ 企业定额管理器导入正常")
        print("3. ✅ Web环境兼容性良好")
        print("4. ✅ 功能集成完整")
        
        print("\n🚀 现在可以正常使用Web界面创建数据库了!")
    else:
        print("❌ 导入测试失败!")
        print("\n🔧 建议检查:")
        print("1. 确认src目录下有所需的模块文件")
        print("2. 检查文件权限和路径")
        print("3. 确认Python环境配置正确")
    
    return success

if __name__ == "__main__":
    main()
