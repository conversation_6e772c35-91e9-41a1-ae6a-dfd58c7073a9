# 北京市消耗定额智能提取系统 - 使用说明

## 🎯 系统概述

本系统使用AI模型自动从PDF定额文件中提取表格数据，并生成包含价格计算公式的CSV文件。

## 🚀 快速开始

### 方法1：一键启动（推荐）

```bash
py start_system.py
```

这将同时启动：
- **主功能界面**: http://localhost:7862
- **模型配置界面**: http://localhost:7863

### 方法2：分别启动

```bash
# 启动主功能界面
py main.py

# 启动模型配置界面（新终端）
py model_config_standalone.py
```

## 📋 使用流程

### 第一步：配置AI模型

1. **访问模型配置界面**: http://localhost:7863
2. **选择要配置的模型**（推荐顺序）：
   - 🎯 **阿里通义千问-QVQ-Max** (效果最佳)
   - 🔬 **DeepSeek API** (性价比高)
   - 🤖 **OpenAI GPT-4 Vision** (知名度高)
3. **输入API密钥**
4. **点击"测试"**验证连接
5. **点击"保存"**保存配置

### 第二步：使用定额提取功能

1. **访问主功能界面**: http://localhost:7862
2. **点击"🔄 刷新模型列表"**更新可用模型
3. **上传PDF文件**
4. **选择AI模型**
5. **设置页码范围**（建议先测试1-2页）
6. **点击"开始提取"**
7. **查看处理过程**和**下载CSV文件**

## 🤖 支持的AI模型

### 云端模型（需要API密钥）

| 模型 | 特点 | 成本 | 获取地址 |
|------|------|------|----------|
| 🎯 **QVQ-Max** | 最佳效果，思维链推理 | 中等 | https://bailian.console.aliyun.com/ |
| 🔬 **DeepSeek** | 性价比高，国内服务 | 低 | https://platform.deepseek.com/api_keys |
| 🤖 **GPT-4V** | 知名度高，效果好 | 高 | https://platform.openai.com/api-keys |
| 🧠 **Claude** | 理解能力强 | 高 | https://console.anthropic.com/ |
| 🔍 **Gemini** | Google出品 | 中等 | https://makersuite.google.com/app/apikey |

### 本地模型（免费）

| 模型 | 特点 | 要求 |
|------|------|------|
| 🏠 **Ollama** | 完全免费，数据安全 | 需要本地安装Ollama |

**推荐本地模型**: `qwen2.5vl:7b` - 专门的视觉语言模型

## 📊 输出格式

系统会生成符合以下格式的CSV文件：

### 父级定额表格
- **编号**: 04-01-1-6
- **定额项名称**: 人工挖沟槽土方 一、二类土
- **工作内容**: 挖土、余土清理、修整底边、打钉拍底等
- **单位**: m³
- **综合单价（元/单位）**: 自动计算

### 子级资源消耗表格
- **定额编号**: 04-01-1-6
- **资源编号**: 00010701, 99030030, 99460004
- **类别**: 人工、机械、其他费用
- **子项名称**: 综合用工三类、电动打钉机、其他机具费占人工费
- **资源单位**: 工日/m³、台班/m³、%
- **消耗量**: 0.182、0.0039、1.50
- **单价**: 用户输入或API获取
- **合价**: 自动计算

### 特殊计算逻辑
- **普通项**: 合价 = 单价 × 消耗量
- **百分比项**: 单价 = 其他项合价总和，合价 = 单价 × 消耗量 / 100

## 🔧 故障排除

### 问题1：看不到配置的模型

**解决方案**:
1. 确认在 http://localhost:7863 中已保存API密钥
2. 在主界面点击"🔄 刷新模型列表"
3. 检查API密钥是否正确

### 问题2：模型连接失败

**解决方案**:
1. 检查网络连接
2. 验证API密钥格式
3. 确认账户余额充足
4. 尝试其他模型

### 问题3：识别效果不佳

**解决方案**:
1. 尝试使用QVQ-Max模型（效果最佳）
2. 确保PDF图片清晰
3. 检查是否为标准定额表格格式
4. 尝试调整PDF转图片的DPI设置

### 问题4：Ollama模型不可用

**解决方案**:
1. 确认Ollama服务正在运行
2. 检查模型是否正确安装
3. 重启Ollama服务

## 💡 使用技巧

### 1. 模型选择建议
- **高精度需求**: QVQ-Max
- **批量处理**: DeepSeek或Ollama
- **测试阶段**: 本地Ollama模型

### 2. 成本优化
- 先用免费的Ollama模型测试
- 确认效果后再使用付费模型
- 批量处理时选择性价比高的模型

### 3. 效率提升
- 先测试少量页面验证效果
- 使用页码范围功能分批处理
- 保存常用的模型配置

## 📞 技术支持

### 系统相关
- 查看控制台日志获取详细错误信息
- 检查logs目录下的日志文件
- 确保所有依赖已正确安装

### API相关
- 查看各服务商的官方文档
- 联系对应平台的技术支持
- 检查API配额和计费状态

## 🎉 开始使用

1. **运行**: `py start_system.py`
2. **配置**: 访问 http://localhost:7863
3. **使用**: 访问 http://localhost:7862
4. **享受**: 智能定额提取服务！

---

**祝您使用愉快！如有问题，请查看故障排除部分或检查系统日志。** 🚀
