# 配置持久化功能实现说明

## 🎯 功能概述

根据您的要求，已为API密钥设置和数据库访问配置实现了完整的持久化记忆功能，确保在同一台计算机上重启计算机和程序后这些设置信息仍然保留。

## 🔧 实现的功能

### 1. 配置持久化管理器
**文件**: `src/config_persistence_manager.py`

#### 核心特性
- **🔐 加密存储**: 使用Fernet加密算法保护敏感信息
- **🏠 本地存储**: 配置文件存储在 `user_config/` 目录
- **🔄 自动备份**: 支持配置导出和恢复
- **📊 统计信息**: 记录访问次数和时间戳

#### 存储内容
- **API密钥**: DashScope和OpenAI API密钥
- **数据库配置**: 定额和信息价数据库连接信息
- **UI偏好**: 用户界面偏好设置
- **系统信息**: 创建时间、访问记录等

### 2. API密钥持久化
#### 定额识别界面
- **自动加载**: 启动时从配置文件加载已保存的API密钥
- **自动保存**: 输入API密钥后自动保存到持久化配置
- **加密保护**: API密钥使用加密算法安全存储

#### 信息价识别界面
- **双密钥支持**: 支持DashScope和OpenAI两种API密钥
- **同步保存**: 保存时同时更新运行时配置和持久化配置
- **状态反馈**: 保存成功后显示确认信息

### 3. 数据库配置持久化
#### 定额数据库配置
- **多类型支持**: SQLite、MySQL、PostgreSQL、MongoDB
- **完整配置**: 数据库类型、名称、主机、端口、用户名、密码
- **自动保存**: 创建数据库时自动保存配置

#### 信息价数据库配置
- **合并策略**: 智能合并、完全覆盖、时间戳新建
- **连接信息**: 主机地址、端口、用户名、密码、默认数据库
- **实时保存**: 写入数据库时自动保存配置

## 📁 存储结构

### 目录组织
```
user_config/
├── user_settings.json      # 加密后的配置文件
└── encryption.key          # 加密密钥文件
```

### 配置文件结构
```json
{
  "api_keys": {
    "dashscope_key": "加密后的密钥",
    "openai_key": "加密后的密钥",
    "last_updated": "2025-06-29T19:56:12"
  },
  "database_configs": {
    "quota_db": {
      "db_type": "sqlite",
      "db_name": "enterprise_quota.db",
      "host": "localhost",
      "port": "3306",
      "username": "加密后的用户名",
      "password": "加密后的密码",
      "default_db": "quota_db"
    },
    "price_db": {
      "db_type": "sqlite",
      "db_name": "price_info.db",
      "merge_strategy": "smart_merge",
      "host": "localhost",
      "port": "3306",
      "username": "加密后的用户名",
      "password": "加密后的密码",
      "default_db": "price_db"
    }
  },
  "ui_preferences": {
    "last_model_type": "dashscope",
    "default_start_page": "1",
    "default_end_page": "10",
    "default_volume_code": "04",
    "default_chapter_codes": "01"
  },
  "system_info": {
    "created_time": "2025-06-29T19:56:12",
    "last_access_time": "2025-06-29T19:56:12",
    "access_count": 1
  }
}
```

## 🔐 安全特性

### 1. 加密算法
- **Fernet加密**: 使用cryptography库的Fernet对称加密
- **机器绑定**: 基于机器信息生成唯一加密密钥
- **PBKDF2**: 使用PBKDF2密钥派生函数增强安全性

### 2. 密钥生成
```python
def _generate_encryption_key(self) -> bytes:
    # 使用机器特征生成密钥
    machine_info = f"{platform.node()}-{platform.system()}-{getpass.getuser()}"
    password = machine_info.encode()
    
    # 使用固定的盐值
    salt = b'quota_tool_salt_2025'
    
    # 生成密钥
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )
    key = base64.urlsafe_b64encode(kdf.derive(password))
    return key
```

### 3. 敏感数据保护
- **API密钥**: 完全加密存储
- **数据库密码**: 加密存储
- **用户名**: 加密存储
- **其他配置**: 明文存储（非敏感信息）

## 🔄 使用流程

### 1. 首次使用
1. **启动程序**: 系统自动创建配置目录和加密密钥
2. **输入配置**: 在界面中输入API密钥和数据库配置
3. **自动保存**: 配置信息自动加密保存到本地文件

### 2. 重启后使用
1. **自动加载**: 程序启动时自动加载已保存的配置
2. **界面填充**: API密钥和数据库配置自动填入界面
3. **无缝使用**: 用户无需重新输入配置信息

### 3. 配置更新
1. **修改配置**: 在界面中修改API密钥或数据库配置
2. **实时保存**: 修改后自动保存到持久化配置
3. **即时生效**: 新配置立即在当前会话中生效

## 🌟 技术亮点

### 1. 智能初始化
```python
# API密钥输入框自动加载已保存的值
saved_dashscope_key, saved_openai_key = self.config_persistence.get_api_keys()

api_key_input = gr.Textbox(
    label="🔑 API密钥",
    value=saved_dashscope_key,  # 自动填入已保存的密钥
    info="配置会自动保存"
)
```

### 2. 透明保存
```python
def save_api_key(api_key):
    # 保存到持久化配置
    success = self.config_persistence.save_api_keys(dashscope_key=api_key)
    
    if success:
        # 同时保存到环境变量文件（向后兼容）
        self._save_api_key("DASHSCOPE_API_KEY", api_key)
        return f"✅ API密钥已保存并持久化"
```

### 3. 配置合并
```python
def _merge_with_defaults(self, config: Dict[str, Any]) -> Dict[str, Any]:
    """合并默认配置，处理新增字段"""
    def merge_dict(default: Dict, user: Dict) -> Dict:
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = merge_dict(result[key], value)
            else:
                result[key] = value
        return result
    
    return merge_dict(self.default_config, config)
```

## 📊 管理功能

### 1. 配置统计
```python
def get_config_summary(self) -> Dict[str, Any]:
    """获取配置摘要"""
    return {
        "api_keys_configured": {
            "dashscope": bool(config['api_keys']['dashscope_key']),
            "openai": bool(config['api_keys']['openai_key'])
        },
        "database_configs": {
            "quota_db_type": config['database_configs']['quota_db']['db_type'],
            "price_db_type": config['database_configs']['price_db']['db_type']
        },
        "system_info": config['system_info']
    }
```

### 2. 配置导出
```python
def export_config(self, export_path: str, include_sensitive: bool = False) -> bool:
    """导出配置，可选择是否包含敏感信息"""
    if not include_sensitive:
        # 移除敏感信息
        export_config['api_keys']['dashscope_key'] = "***已隐藏***"
        export_config['api_keys']['openai_key'] = "***已隐藏***"
```

### 3. 配置清理
```python
def clear_config(self) -> bool:
    """清除所有配置"""
    if self.config_file.exists():
        self.config_file.unlink()
    if self.key_file.exists():
        self.key_file.unlink()
```

## 🔧 界面集成

### 1. 定额识别界面
- **API密钥输入**: 自动加载已保存的DashScope密钥
- **数据库配置**: 自动加载定额数据库配置
- **保存提示**: 显示"配置会自动保存"提示信息

### 2. 信息价识别界面
- **双密钥输入**: 自动加载DashScope和OpenAI密钥
- **数据库配置**: 自动加载信息价数据库配置
- **合并策略**: 保存数据合并策略偏好

### 3. 高级管理界面
- **数据库连接**: 自动加载数据库连接配置
- **用户偏好**: 保存界面操作偏好设置

## 🚀 部署状态

### ✅ 已完成功能
1. **配置持久化管理器**: 完整的加密存储和管理功能 ✅
2. **API密钥持久化**: 定额和信息价界面的API密钥自动保存 ✅
3. **数据库配置持久化**: 定额和信息价数据库配置自动保存 ✅
4. **界面集成**: 所有配置输入框自动加载已保存的值 ✅
5. **加密安全**: 敏感信息使用Fernet加密算法保护 ✅

### 🌐 使用方式
1. **启动程序**: http://localhost:7864
2. **输入配置**: 在相应界面输入API密钥和数据库配置
3. **自动保存**: 配置会自动保存，无需手动操作
4. **重启验证**: 重启程序后配置自动加载

### 💡 使用提示
1. **首次配置**: 输入配置后会自动创建 `user_config/` 目录
2. **安全性**: 配置文件使用加密存储，确保信息安全
3. **兼容性**: 同时支持新的持久化配置和原有的环境变量配置
4. **可移植性**: 配置文件绑定到特定机器，确保安全性

## 🔮 扩展功能

### 1. 配置备份
- **自动备份**: 定期备份配置文件
- **版本管理**: 保留配置历史版本
- **恢复功能**: 支持配置恢复

### 2. 多用户支持
- **用户隔离**: 不同用户的配置分别存储
- **权限管理**: 配置访问权限控制
- **共享配置**: 支持团队共享配置

### 3. 云同步
- **云存储**: 配置同步到云端
- **多设备**: 支持多设备配置同步
- **冲突解决**: 自动处理配置冲突

**🌟 现在API密钥设置和数据库访问配置都具备了完整的持久化记忆功能，用户在重启计算机和程序后无需重新输入这些设置信息！**
