#!/usr/bin/env python3
"""
简化版主程序 - 用于测试AI模型功能
"""

import gradio as gr
import asyncio
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple

from src.pdf_processor import PDFProcessor
from src.ai_model_processor import AIModelProcessor
from src.data_processor import DataProcessor
from src.config import Config

class SimpleQuotaApp:
    """简化版定额提取应用"""
    
    def __init__(self):
        self.config = Config()
        self.pdf_processor = PDFProcessor()
        self.ai_processor = AIModelProcessor()
        self.data_processor = DataProcessor()
    
    def get_model_status(self) -> str:
        """获取模型状态"""
        available_models = self.ai_processor.get_available_models()
        
        if not available_models:
            return "❌ 无可用模型\n请配置API密钥或启动Ollama"
        
        status_lines = ["✅ 可用模型:"]
        for model_id, model_name in available_models.items():
            status_lines.append(f"• {model_name}")
        
        return "\n".join(status_lines)
    
    async def process_single_image(self, image_path: str, model_type: str) -> str:
        """处理单张图片"""
        try:
            result = await self.ai_processor.process_image(image_path, model_type)
            if result:
                return f"✅ 识别成功\n结果长度: {len(result)} 字符\n\n前200字符:\n{result[:200]}..."
            else:
                return "❌ 识别失败"
        except Exception as e:
            return f"❌ 处理错误: {str(e)}"
    
    def create_interface(self):
        """创建简化界面"""
        
        with gr.Blocks(title="AI模型测试系统") as interface:
            
            gr.Markdown("""
            # AI模型测试系统
            
            测试不同AI模型的图像识别能力
            """)
            
            with gr.Row():
                with gr.Column():
                    # 模型选择
                    available_models = self.ai_processor.get_available_models()
                    if not available_models:
                        available_models = {"none": "无可用模型"}
                    
                    model_dropdown = gr.Dropdown(
                        label="选择AI模型",
                        choices=list(available_models.items()),
                        value=list(available_models.keys())[0] if available_models else None
                    )
                    
                    # 图片上传
                    image_input = gr.Image(
                        label="上传测试图片",
                        type="filepath"
                    )
                    
                    # 测试按钮
                    test_btn = gr.Button("测试识别", variant="primary")
                
                with gr.Column():
                    # 模型状态
                    model_status = gr.Textbox(
                        label="模型状态",
                        value=self.get_model_status(),
                        lines=6,
                        interactive=False
                    )
                    
                    # 识别结果
                    result_text = gr.Textbox(
                        label="识别结果",
                        lines=10,
                        interactive=False
                    )
            
            # 处理函数
            def process_image(image_path, model_type):
                if not image_path:
                    return "请先上传图片"
                
                if not model_type or model_type == "none":
                    return "请选择AI模型"
                
                # 运行异步处理
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    result = loop.run_until_complete(
                        self.process_single_image(image_path, model_type)
                    )
                    return result
                finally:
                    loop.close()
            
            # 绑定事件
            test_btn.click(
                fn=process_image,
                inputs=[image_input, model_dropdown],
                outputs=[result_text]
            )
        
        return interface

def main():
    """主函数"""
    print("🚀 启动AI模型测试系统...")
    
    try:
        app = SimpleQuotaApp()
        interface = app.create_interface()
        
        print("✅ 界面创建成功")
        print("🌐 启动Web服务器...")
        
        interface.launch(
            server_name="0.0.0.0",
            server_port=7861,
            share=False,
            debug=True
        )
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
