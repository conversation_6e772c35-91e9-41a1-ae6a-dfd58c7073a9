#!/usr/bin/env python3
"""
测试章节号文件命名功能
Test Chapter Code Filename Feature
"""

import sys
import os
sys.path.append('src')

# 修复相对导入问题
import importlib.util
spec = importlib.util.spec_from_file_location("data_processor", "src/data_processor.py")
data_processor_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(data_processor_module)

spec = importlib.util.spec_from_file_location("config", "src/config.py")
config_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(config_module)

DataProcessor = data_processor_module.DataProcessor
Config = config_module.Config

def test_chapter_filename():
    """测试章节号文件命名功能"""
    print("🧪 测试章节号文件命名功能")
    print("=" * 50)
    
    # 初始化配置和数据处理器
    config = Config()
    data_processor = DataProcessor(config)
    
    # 模拟测试数据
    test_data = [
        {
            "type": "parent",
            "quota_code": "04-01-1-1",
            "quota_name": "挖土方",
            "work_content": "人工挖土方",
            "unit": "m³",
            "comprehensive_price": 25.50
        },
        {
            "type": "child",
            "quota_code": "04-01-1-1",
            "resource_code": "A001",
            "category": "人工",
            "resource_name": "普通工",
            "resource_unit": "工日",
            "consumption": 0.5,
            "unit_price": 120.0,
            "total_price": 60.0
        }
    ]
    
    # 测试不同的章节号组合
    test_cases = [
        {
            "name": "完整章节号",
            "volume_code": "04",
            "chapter_codes": "01,02,03",
            "expected_pattern": "Vol04_Ch01-02-03"
        },
        {
            "name": "单个章节号",
            "volume_code": "04",
            "chapter_codes": "01",
            "expected_pattern": "Vol04_Ch01"
        },
        {
            "name": "只有分册号",
            "volume_code": "04",
            "chapter_codes": "",
            "expected_pattern": "Vol04"
        },
        {
            "name": "无章节号",
            "volume_code": "",
            "chapter_codes": "",
            "expected_pattern": "无特殊标识"
        },
        {
            "name": "带空格的章节号",
            "volume_code": "04",
            "chapter_codes": " 01 , 02 , 03 ",
            "expected_pattern": "Vol04_Ch01-02-03"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {test_case['name']}")
        print(f"   分册编号: '{test_case['volume_code']}'")
        print(f"   章节编号: '{test_case['chapter_codes']}'")
        print(f"   期望模式: {test_case['expected_pattern']}")
        
        try:
            # 生成CSV文件
            csv_path = data_processor.generate_csv(
                test_data, 
                test_case['volume_code'], 
                test_case['chapter_codes']
            )
            
            # 提取文件名
            filename = os.path.basename(csv_path)
            print(f"   ✅ 生成文件: {filename}")
            
            # 验证文件名是否包含期望的模式
            if test_case['expected_pattern'] != "无特殊标识":
                if test_case['expected_pattern'] in filename:
                    print(f"   ✅ 文件名格式正确")
                else:
                    print(f"   ❌ 文件名格式错误，期望包含: {test_case['expected_pattern']}")
            else:
                # 检查是否不包含Vol或Ch标识
                if "Vol" not in filename and "Ch" not in filename:
                    print(f"   ✅ 文件名格式正确（无特殊标识）")
                else:
                    print(f"   ❌ 文件名应该不包含Vol或Ch标识")
            
            # 验证文件是否存在
            if os.path.exists(csv_path):
                print(f"   ✅ 文件创建成功")
                # 读取文件验证内容
                import pandas as pd
                df = pd.read_csv(csv_path, encoding='utf-8-sig')
                print(f"   ✅ 文件内容验证成功，共 {len(df)} 行数据")
            else:
                print(f"   ❌ 文件创建失败")
                
        except Exception as e:
            print(f"   ❌ 测试失败: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🎯 测试完成！")
    
    # 显示输出目录中的文件
    output_dir = config.get_output_dir()
    print(f"\n📁 输出目录: {output_dir}")
    
    try:
        files = [f for f in os.listdir(output_dir) if f.endswith('.csv')]
        recent_files = sorted(files, key=lambda x: os.path.getctime(os.path.join(output_dir, x)), reverse=True)[:10]
        
        print("📋 最近生成的CSV文件:")
        for i, file in enumerate(recent_files, 1):
            file_path = os.path.join(output_dir, file)
            file_time = os.path.getctime(file_path)
            import time
            time_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(file_time))
            print(f"   {i:2d}. {file} ({time_str})")
            
    except Exception as e:
        print(f"❌ 无法列出输出文件: {str(e)}")

def test_filename_patterns():
    """测试文件名模式生成"""
    print("\n🔍 测试文件名模式生成")
    print("-" * 30)
    
    test_patterns = [
        ("04", "01,02,03", "quota_extraction_result_Vol04_Ch01-02-03_12345678.csv"),
        ("04", "01", "quota_extraction_result_Vol04_Ch01_12345678.csv"),
        ("04", "", "quota_extraction_result_Vol04_12345678.csv"),
        ("", "", "quota_extraction_result_12345678.csv"),
        ("", "01,02", "quota_extraction_result_Ch01-02_12345678.csv"),
    ]
    
    for volume, chapters, expected in test_patterns:
        # 模拟文件名生成逻辑
        filename_parts = ["quota_extraction_result"]
        if volume:
            filename_parts.append(f"Vol{volume}")
        if chapters:
            clean_chapters = [ch.strip() for ch in chapters.split(',') if ch.strip()]
            if clean_chapters:
                chapters_str = "-".join(clean_chapters)
                filename_parts.append(f"Ch{chapters_str}")
        filename_parts.append("12345678")  # 模拟UUID
        
        generated = "_".join(filename_parts) + ".csv"
        
        print(f"输入: Vol='{volume}', Ch='{chapters}'")
        print(f"期望: {expected}")
        print(f"生成: {generated}")
        print(f"结果: {'✅ 匹配' if generated == expected else '❌ 不匹配'}")
        print()

if __name__ == "__main__":
    test_chapter_filename()
    test_filename_patterns()
