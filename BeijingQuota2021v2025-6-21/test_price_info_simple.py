#!/usr/bin/env python3
"""
简化的信息价识别测试
使用模拟数据测试信息价处理流程
"""

import os
import sys
import pandas as pd
import json
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.advanced_quota_manager import AdvancedQuotaManager

def create_mock_price_info_data():
    """创建模拟的信息价数据"""
    print("📊 创建模拟信息价数据...")
    
    # 模拟信息价数据
    mock_data = [
        {
            '页眉标识': '工程造价信息',
            '章节编号': '01',
            '章节名称': '黑色及有色金属',
            '资源编号': '3001010401',
            '产品名称': '网络球型摄像机',
            '规格型号及特征': '1/3英寸CMOS，200万像素，支持H.264/H.265编码',
            '计量单位': '台',
            '市场参考价（含税）': '1200.00',
            '市场参考价（不含税）': '1061.95',
            '备注': '含安装配件'
        },
        {
            '页眉标识': '工程造价信息',
            '章节编号': '01',
            '章节名称': '黑色及有色金属',
            '资源编号': '3001010402',
            '产品名称': '网络枪型摄像机',
            '规格型号及特征': '1/3英寸CMOS，400万像素，支持红外夜视',
            '计量单位': '台',
            '市场参考价（含税）': '1800.00',
            '市场参考价（不含税）': '1592.92',
            '备注': '含镜头和支架'
        },
        {
            '页眉标识': '工程造价信息',
            '章节编号': '02',
            '章节名称': '建筑材料',
            '资源编号': '2001020101',
            '产品名称': '水泥',
            '规格型号及特征': 'P.O 42.5，袋装',
            '计量单位': 't',
            '市场参考价（含税）': '450.00',
            '市场参考价（不含税）': '398.23',
            '备注': '普通硅酸盐水泥'
        },
        {
            '页眉标识': '工程造价信息',
            '章节编号': '02',
            '章节名称': '建筑材料',
            '资源编号': '2001020102',
            '产品名称': '钢筋',
            '规格型号及特征': 'HRB400，直径12mm',
            '计量单位': 't',
            '市场参考价（含税）': '4200.00',
            '市场参考价（不含税）': '3716.81',
            '备注': '热轧带肋钢筋'
        },
        {
            '页眉标识': '市场参考价',
            '章节编号': '03',
            '章节名称': '机械设备',
            '资源编号': '4001030201',
            '产品名称': '挖掘机',
            '规格型号及特征': '斗容量1.0m³，功率120kW',
            '计量单位': '台班',
            '市场参考价（含税）': '800.00',
            '市场参考价（不含税）': '707.96',
            '备注': '含操作员工资'
        }
    ]
    
    # 保存为CSV文件
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    csv_file = f"output/price_info_result_{timestamp}.csv"
    
    os.makedirs("output", exist_ok=True)
    df = pd.DataFrame(mock_data)
    df.to_csv(csv_file, index=False, encoding='utf-8-sig')
    
    print(f"✅ 模拟数据已保存到: {csv_file}")
    print(f"📊 数据统计: {len(mock_data)} 条记录")
    
    return csv_file

def create_mock_quota_resources_data():
    """创建模拟的定额资源数据"""
    print("📋 创建模拟定额资源数据...")
    
    # 模拟定额资源数据（包含一些与信息价匹配的资源编号）
    mock_data = [
        {
            '定额编号': '1-1',
            '资源编号': '3001010401',
            '资源类别': '材料',
            '子项名称': '网络球型摄像机',
            '单位': '台',
            '消耗量': '2.0',
            '单价': '0.0',
            '合价': '0.0'
        },
        {
            '定额编号': '1-1',
            '资源编号': '2001020101',
            '资源类别': '材料',
            '子项名称': '水泥',
            '单位': 't',
            '消耗量': '0.5',
            '单价': '0.0',
            '合价': '0.0'
        },
        {
            '定额编号': '1-2',
            '资源编号': '3001010402',
            '资源类别': '材料',
            '子项名称': '网络枪型摄像机',
            '单位': '台',
            '消耗量': '1.0',
            '单价': '0.0',
            '合价': '0.0'
        },
        {
            '定额编号': '1-2',
            '资源编号': '2001020102',
            '资源类别': '材料',
            '子项名称': '钢筋',
            '单位': 't',
            '消耗量': '0.2',
            '单价': '0.0',
            '合价': '0.0'
        },
        {
            '定额编号': '1-3',
            '资源编号': '4001030201',
            '资源类别': '机械',
            '子项名称': '挖掘机',
            '单位': '台班',
            '消耗量': '0.1',
            '单价': '0.0',
            '合价': '0.0'
        },
        {
            '定额编号': '1-3',
            '资源编号': '9999999999',  # 不匹配的资源编号
            '资源类别': '人工',
            '子项名称': '综合用工',
            '单位': '工日',
            '消耗量': '1.0',
            '单价': '120.0',
            '合价': '120.0'
        }
    ]
    
    # 保存为CSV文件
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    csv_file = f"output/child_resources_{timestamp}.csv"
    
    df = pd.DataFrame(mock_data)
    df.to_csv(csv_file, index=False, encoding='utf-8-sig')
    
    print(f"✅ 模拟定额资源数据已保存到: {csv_file}")
    print(f"📊 数据统计: {len(mock_data)} 条记录")
    
    return csv_file

def test_price_info_database_with_mock_data():
    """使用模拟数据测试信息价数据库功能"""
    print("🗄️ 测试信息价数据库功能（模拟数据）")
    print("=" * 50)
    
    # 创建模拟数据
    price_file = create_mock_price_info_data()
    
    # 创建数据库管理器
    manager = AdvancedQuotaManager()
    
    # 连接到SQLite数据库
    db_config = {
        'database_path': 'output/price_info_test_mock.db'
    }
    
    print("\n🔗 连接到SQLite数据库...")
    success, message = manager.connect_to_database('sqlite', db_config)
    
    if not success:
        print(f"❌ 数据库连接失败: {message}")
        return False
    
    print("✅ 数据库连接成功")
    
    # 导入信息价数据
    print("\n📥 导入信息价数据...")
    success, message = manager.import_price_info_data(price_file)
    
    if success:
        print("✅ 信息价数据导入成功!")
        print(f"📋 导入结果:")
        print(message)
        
        # 查询数据验证
        print("\n🔍 验证导入的数据...")
        try:
            cursor = manager.connection.cursor()
            cursor.execute("SELECT COUNT(*) FROM price_info")
            count = cursor.fetchone()[0]
            print(f"📊 数据库中共有 {count} 条信息价记录")
            
            # 显示所有记录
            cursor.execute("""
                SELECT resource_code, product_name, unit, price_with_tax, price_without_tax, chapter_name
                FROM price_info 
                ORDER BY resource_code
            """)
            records = cursor.fetchall()
            
            if records:
                print("\n📋 所有记录:")
                for record in records:
                    print(f"   资源编号: {record[0]}, 产品名称: {record[1]}, 单位: {record[2]}, "
                          f"含税价: {record[3]}, 不含税价: {record[4]}, 章节: {record[5]}")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据验证失败: {str(e)}")
            return False
    else:
        print(f"❌ 信息价数据导入失败: {message}")
        return False

def test_price_merge_with_mock_data():
    """使用模拟数据测试信息价与定额数据合并"""
    print("\n🔗 测试信息价与定额数据合并（模拟数据）")
    print("=" * 50)
    
    # 创建模拟数据
    price_file = create_mock_price_info_data()
    quota_file = create_mock_quota_resources_data()
    
    # 导入处理器
    from src.price_info_processor import PriceInfoProcessor
    processor = PriceInfoProcessor()
    
    print("\n🔄 开始合并数据...")
    success, message, output_file = processor.merge_price_info_with_quotas(
        price_file, quota_file
    )
    
    if success:
        print("✅ 数据合并成功!")
        print(f"📋 合并结果:")
        print(message)
        print(f"📁 输出文件: {output_file}")
        
        # 验证合并结果
        if os.path.exists(output_file):
            try:
                df = pd.read_csv(output_file, encoding='utf-8-sig')
                print(f"\n📊 合并文件统计:")
                print(f"   - 总记录数: {len(df)}")
                print(f"   - 列数: {len(df.columns)}")
                
                # 统计匹配情况
                if '匹配状态' in df.columns:
                    matched = len(df[df['匹配状态'] == '已匹配'])
                    unmatched = len(df[df['匹配状态'] == '未匹配'])
                    print(f"   - 已匹配: {matched}")
                    print(f"   - 未匹配: {unmatched}")
                    print(f"   - 匹配率: {matched/(matched+unmatched)*100:.1f}%")
                
                # 显示匹配的记录
                matched_records = df[df['匹配状态'] == '已匹配']
                if len(matched_records) > 0:
                    print(f"\n📋 匹配成功的记录:")
                    for _, row in matched_records.iterrows():
                        print(f"   定额编号: {row['定额编号']}, 资源编号: {row['资源编号']}, "
                              f"产品名称: {row.get('信息价_产品名称', 'N/A')}, "
                              f"含税价格: {row.get('信息价_含税价格', 'N/A')}")
                
                return True
                
            except Exception as e:
                print(f"❌ 验证合并结果失败: {str(e)}")
                return False
        else:
            print(f"❌ 输出文件不存在: {output_file}")
            return False
    else:
        print(f"❌ 数据合并失败: {message}")
        return False

def main():
    """主测试函数"""
    print("🚀 信息价识别处理器简化测试")
    print("=" * 60)
    print("使用模拟数据测试信息价处理流程")
    
    # 创建输出目录
    os.makedirs("output", exist_ok=True)
    print("✅ 输出目录已准备")
    
    # 运行测试
    tests = [
        ("信息价数据库功能（模拟数据）", test_price_info_database_with_mock_data),
        ("信息价数据合并（模拟数据）", test_price_merge_with_mock_data)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！信息价处理功能基本正常。")
        print("💡 接下来可以尝试使用真实的PDF文件进行信息价识别。")
    else:
        print("⚠️ 部分测试失败，请检查相关配置和依赖。")

if __name__ == "__main__":
    main()
