#!/usr/bin/env python3
"""
调试Web界面调用
"""

import os
import sys
import pandas as pd
from pathlib import Path

# 添加src目录到路径
sys.path.append('src')

def create_test_data():
    """创建测试数据文件"""
    # 确保output目录存在
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    # 创建parent_quotas测试数据
    parent_data = {
        '定额编号': ['001-001', '001-002'],
        '定额名称': ['混凝土浇筑C30', '钢筋绑扎HPB300'],
        '单位': ['m³', 'kg'],
        '人工费': [120.5, 85.2],
        '材料费': [450.8, 320.5],
        '机械费': [80.3, 45.8],
        '合计': [651.6, 451.5]
    }
    
    parent_df = pd.DataFrame(parent_data)
    parent_csv_path = output_dir / "debug_parent_quotas.csv"
    parent_df.to_csv(parent_csv_path, index=False, encoding='utf-8-sig')
    
    # 创建child_resources测试数据
    child_data = {
        '定额编号': ['001-001', '001-002'],
        '资源编号': ['R001', 'R002'],
        '资源名称': ['C30混凝土', 'HPB300钢筋'],
        '资源类型': ['材料', '材料'],
        '数量': [1.05, 1.02],
        '单位': ['m³', 'kg'],
        '单价': [420.0, 4.2],
        '合价': [441.0, 4.284]
    }
    
    child_df = pd.DataFrame(child_data)
    child_csv_path = output_dir / "debug_child_resources.csv"
    child_df.to_csv(child_csv_path, index=False, encoding='utf-8-sig')
    
    print(f"✅ 创建调试测试数据文件:")
    print(f"   - {parent_csv_path}")
    print(f"   - {child_csv_path}")
    
    return ["debug_parent_quotas.csv"], ["debug_child_resources.csv"]

def simulate_web_interface_call():
    """模拟Web界面的调用"""
    try:
        # 模拟Web界面的导入方式
        import os  # 确保os模块可用
        try:
            from src.enterprise_quota_manager import EnterpriseQuotaManager
        except ImportError:
            import sys
            sys.path.append('src')
            from enterprise_quota_manager import EnterpriseQuotaManager

        # 创建测试数据
        parent_files, child_files = create_test_data()
        
        print("\n🔍 模拟Web界面调用...")
        
        # 模拟Web界面的参数
        db_type = "postgresql"  # 这里是关键！
        db_name = "debug_enterprise_quota"
        db_host = "localhost"
        db_port = 5432
        default_db = "postgres"
        db_user = "postgres"
        db_password = "postgres123"
        
        print(f"📋 调用参数:")
        print(f"   - db_type: '{db_type}'")
        print(f"   - db_name: '{db_name}'")
        print(f"   - db_host: '{db_host}'")
        print(f"   - db_port: {db_port}")
        print(f"   - default_db: '{default_db}'")
        print(f"   - db_user: '{db_user}'")
        
        # 构建数据库配置（模拟Web界面的逻辑）
        if db_type == "sqlite":
            db_config = {
                'database_path': os.path.join("output", db_name)
            }
        else:
            db_config = {
                'host': db_host,
                'port': int(db_port),
                'user': db_user,
                'password': db_password,
                'database': db_name,
                'default_db': default_db
            }
        
        print(f"📋 db_config: {db_config}")
        
        # 获取文件路径（模拟Web界面的逻辑）
        parent_paths = [os.path.join("output", f) for f in parent_files]
        child_paths = [os.path.join("output", f) for f in child_files]
        
        print(f"📋 文件路径:")
        print(f"   - parent_paths: {parent_paths}")
        print(f"   - child_paths: {child_paths}")
        
        # 创建管理器并创建数据库（完全模拟Web界面）
        manager = EnterpriseQuotaManager()
        print("\n🚀 调用 manager.create_quota_database...")
        
        success, message = manager.create_quota_database(
            db_type, db_config, parent_paths, child_paths
        )
        
        print(f"\n📊 调用结果:")
        print(f"   - success: {success}")
        print(f"   - message: {message}")
        
        if success:
            print("✅ 模拟Web界面调用成功!")
            
            # 验证数据库是否真的创建了
            print("\n🔍 验证数据库创建...")
            
            import psycopg2
            
            # 连接到默认数据库检查目标数据库是否存在
            conn = psycopg2.connect(
                host=db_config['host'],
                port=db_config['port'],
                user=db_config['user'],
                password=db_config['password'],
                database=db_config['default_db']
            )
            cursor = conn.cursor()
            
            cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (db_config['database'],))
            db_exists = cursor.fetchone() is not None
            
            if db_exists:
                print(f"✅ 数据库 '{db_config['database']}' 确实已创建!")
                return True
            else:
                print(f"❌ 数据库 '{db_config['database']}' 未找到!")
                return False
        else:
            print(f"❌ 模拟Web界面调用失败: {message}")
            return False
        
    except Exception as e:
        print(f"❌ 模拟调用过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_debug_database():
    """清理调试数据库"""
    try:
        import psycopg2
        
        config = {
            'host': 'localhost',
            'port': 5432,
            'user': 'postgres',
            'password': 'postgres123',
            'database': 'postgres'
        }
        
        conn = psycopg2.connect(**config)
        conn.set_isolation_level(psycopg2.extensions.ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # 检查调试数据库是否存在
        cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", ('debug_enterprise_quota',))
        if cursor.fetchone():
            # 断开所有连接并删除数据库
            cursor.execute("""
                SELECT pg_terminate_backend(pid)
                FROM pg_stat_activity
                WHERE datname = 'debug_enterprise_quota' AND pid <> pg_backend_pid()
            """)
            cursor.execute('DROP DATABASE "debug_enterprise_quota"')
            print("🗑️ 清理调试数据库: debug_enterprise_quota")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"⚠️ 清理调试数据库时出错: {e}")

def main():
    """主函数"""
    print("🔍 调试Web界面调用")
    print("=" * 50)
    
    # 清理之前的调试数据库
    cleanup_debug_database()
    
    # 运行模拟调用
    success = simulate_web_interface_call()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Web界面调用模拟成功!")
        print("\n💡 这说明:")
        print("1. ✅ 企业定额管理器的PostgreSQL功能正常")
        print("2. ✅ 数据库确实被创建在PostgreSQL服务器中")
        print("3. ✅ Web界面的调用逻辑正确")
        
        print("\n🔧 如果Web界面仍显示SQL脚本，可能的原因:")
        print("1. 缓存问题 - 需要刷新页面")
        print("2. 服务器未重启 - 需要重启开发服务器")
        print("3. 数据库类型选择问题 - 确认选择了PostgreSQL")
    else:
        print("❌ Web界面调用模拟失败!")
        print("\n🔧 需要检查:")
        print("1. PostgreSQL连接配置")
        print("2. 企业定额管理器的实现")
        print("3. 调用参数的传递")
    
    # 询问是否清理调试数据库
    if success:
        try:
            response = input("\n🗑️ 是否清理调试数据库? (y/N): ").strip().lower()
            if response == 'y':
                cleanup_debug_database()
        except KeyboardInterrupt:
            print("\n")
    
    return success

if __name__ == "__main__":
    main()
