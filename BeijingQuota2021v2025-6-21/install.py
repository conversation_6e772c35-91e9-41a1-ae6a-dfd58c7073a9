#!/usr/bin/env python3
"""
安装脚本 - 自动安装依赖和配置环境
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """运行命令并显示进度"""
    print(f"\n{'='*50}")
    print(f"正在{description}...")
    print(f"命令: {command}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description}成功")
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败")
        print(f"错误: {e.stderr}")
        return False

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def install_system_dependencies():
    """安装系统依赖"""
    print("\n正在检查系统依赖...")
    
    # 检查是否为Windows系统
    if os.name == 'nt':
        print("检测到Windows系统")
        print("请确保已安装以下软件:")
        print("1. Google Chrome浏览器")
        print("2. Poppler (用于PDF处理)")
        print("   下载地址: https://github.com/oschwartz10612/poppler-windows/releases/")
        print("   解压后将bin目录添加到PATH环境变量")
        
        # 检查Chrome是否安装
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
        ]
        
        chrome_found = any(os.path.exists(path) for path in chrome_paths)
        if chrome_found:
            print("✅ 找到Chrome浏览器")
        else:
            print("❌ 未找到Chrome浏览器，请先安装")
            return False
    
    else:
        # Linux/Mac系统
        print("检测到Unix系统，尝试安装系统依赖...")
        
        # 尝试安装poppler
        commands = [
            "sudo apt-get update && sudo apt-get install -y poppler-utils",  # Ubuntu/Debian
            "brew install poppler",  # macOS
            "sudo yum install -y poppler-utils",  # CentOS/RHEL
        ]
        
        for cmd in commands:
            if run_command(cmd, "安装poppler"):
                break
    
    return True

def install_python_dependencies():
    """安装Python依赖"""
    print("\n正在安装Python依赖包...")
    
    # 在Windows环境中使用py命令
    python_cmd = "py" if os.name == 'nt' else sys.executable

    # 升级pip
    run_command(f"{python_cmd} -m pip install --upgrade pip", "升级pip")

    # 安装依赖
    return run_command(f"{python_cmd} -m pip install -r requirements.txt", "安装Python依赖")

def setup_chromedriver():
    """设置ChromeDriver"""
    print("\n正在设置ChromeDriver...")
    
    try:
        # 尝试安装webdriver-manager来自动管理ChromeDriver
        python_cmd = "py" if os.name == 'nt' else sys.executable
        run_command(f"{python_cmd} -m pip install webdriver-manager", "安装webdriver-manager")
        
        # 测试ChromeDriver
        test_code = """
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

try:
    service = Service(ChromeDriverManager().install())
    options = webdriver.ChromeOptions()
    options.add_argument('--headless')
    driver = webdriver.Chrome(service=service, options=options)
    driver.quit()
    print("✅ ChromeDriver配置成功")
except Exception as e:
    print(f"❌ ChromeDriver配置失败: {e}")
"""
        
        with open("test_chromedriver.py", "w") as f:
            f.write(test_code)
        
        python_cmd = "py" if os.name == 'nt' else sys.executable
        result = subprocess.run([python_cmd, "test_chromedriver.py"],
                              capture_output=True, text=True)
        
        os.remove("test_chromedriver.py")
        
        if result.returncode == 0:
            print(result.stdout)
            return True
        else:
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ ChromeDriver设置失败: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    print("\n正在创建目录结构...")
    
    directories = ["temp", "output", "logs"]
    
    for dir_name in directories:
        dir_path = Path(dir_name)
        dir_path.mkdir(exist_ok=True)
        print(f"✅ 创建目录: {dir_path}")
    
    return True

def setup_environment():
    """设置环境配置"""
    print("\n正在设置环境配置...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        import shutil
        shutil.copy(env_example, env_file)
        print("✅ 创建.env配置文件")
        print("请根据需要修改.env文件中的配置")
    
    return True

def main():
    """主安装流程"""
    print("🚀 北京市消耗定额智能提取系统 - 安装程序")
    print("="*60)
    
    # 检查Python版本
    if not check_python_version():
        return False
    
    # 安装系统依赖
    if not install_system_dependencies():
        print("⚠️ 系统依赖安装可能有问题，请手动检查")
    
    # 安装Python依赖
    if not install_python_dependencies():
        print("❌ Python依赖安装失败")
        return False
    
    # 设置ChromeDriver
    if not setup_chromedriver():
        print("❌ ChromeDriver设置失败")
        return False
    
    # 创建目录
    if not create_directories():
        print("❌ 目录创建失败")
        return False
    
    # 设置环境
    if not setup_environment():
        print("❌ 环境设置失败")
        return False
    
    print("\n" + "="*60)
    print("🎉 安装完成!")
    print("="*60)
    print("\n使用方法:")
    print("1. 运行: python main.py")
    print("2. 在浏览器中打开: http://localhost:7860")
    print("3. 上传PDF文件并设置页码范围")
    print("4. 点击'开始提取'按钮")
    print("\n注意事项:")
    print("- 首次运行时浏览器会自动打开DeepSeek网站")
    print("- 如需登录DeepSeek，请在浏览器中手动登录")
    print("- 处理大文件时请耐心等待")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
