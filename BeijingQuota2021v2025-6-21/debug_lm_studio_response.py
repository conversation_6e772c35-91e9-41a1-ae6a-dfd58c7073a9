#!/usr/bin/env python3
"""
诊断LM Studio响应问题
Debug LM Studio response issues
"""

import sys
import os
import json
import requests
import base64
from PIL import Image
import io

# 添加src目录到路径
sys.path.insert(0, 'src')

def encode_image_to_base64(image_path: str) -> str:
    """将图片编码为base64"""
    try:
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    except Exception as e:
        print(f"图片编码失败: {e}")
        return ""

def test_lm_studio_connection():
    """测试LM Studio连接"""
    print("🧪 测试LM Studio连接")
    print("=" * 50)
    
    try:
        # 检查服务器状态
        response = requests.get("http://127.0.0.1:1234/v1/models", timeout=10)
        if response.status_code == 200:
            models_data = response.json()
            print("✅ LM Studio服务器运行正常")
            print(f"📊 可用模型数量: {len(models_data.get('data', []))}")
            
            for model in models_data.get('data', []):
                model_id = model.get('id', 'unknown')
                print(f"   • {model_id}")
            
            return True, models_data
        else:
            print(f"❌ LM Studio服务器响应错误: {response.status_code}")
            return False, None
            
    except Exception as e:
        print(f"❌ 无法连接到LM Studio: {str(e)}")
        return False, None

def test_model_response(model_id: str, test_prompt: str = "Hello, this is a test."):
    """测试特定模型的响应"""
    print(f"\n🧪 测试模型响应: {model_id}")
    print("=" * 50)
    
    try:
        headers = {
            "Content-Type": "application/json"
        }
        
        data = {
            "model": model_id,
            "messages": [
                {
                    "role": "user",
                    "content": test_prompt
                }
            ],
            "max_tokens": 100,
            "temperature": 0.1
        }
        
        print(f"📤 发送请求到模型: {model_id}")
        print(f"📝 测试提示词: {test_prompt}")
        
        response = requests.post(
            "http://127.0.0.1:1234/v1/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )
        
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 模型响应成功")
            print(f"📄 响应结构: {list(result.keys())}")
            
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0].get('message', {}).get('content', '')
                print(f"💬 响应内容: {content[:200]}...")
                return True, result
            else:
                print("❌ 响应格式异常: 缺少choices或content")
                print(f"🔍 完整响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                return False, result
        else:
            print(f"❌ 模型响应失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"🔍 错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"🔍 错误文本: {response.text}")
            return False, None
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, None

def test_vision_model_with_image(model_id: str, image_path: str = None):
    """测试视觉模型的图像处理"""
    print(f"\n🧪 测试视觉模型图像处理: {model_id}")
    print("=" * 50)
    
    # 如果没有提供图片，创建一个简单的测试图片
    if not image_path or not os.path.exists(image_path):
        print("📷 创建测试图片...")
        # 创建一个简单的测试图片
        img = Image.new('RGB', (200, 100), color='white')
        from PIL import ImageDraw, ImageFont
        draw = ImageDraw.Draw(img)
        try:
            # 尝试使用默认字体
            draw.text((10, 10), "Test Image\n测试图片\n123.45", fill='black')
        except:
            # 如果字体加载失败，使用简单文本
            draw.text((10, 10), "Test 123", fill='black')
        
        # 保存为临时文件
        test_image_path = "temp_test_image.png"
        img.save(test_image_path)
        image_path = test_image_path
        print(f"✅ 测试图片已创建: {image_path}")
    
    try:
        # 编码图片
        base64_image = encode_image_to_base64(image_path)
        if not base64_image:
            print("❌ 图片编码失败")
            return False, None
        
        print(f"📷 图片编码成功，大小: {len(base64_image)} 字符")
        
        headers = {
            "Content-Type": "application/json"
        }
        
        # 简单的视觉测试提示词
        test_prompt = """请描述这张图片中的内容。如果图片中有文字，请提取出来。请用JSON格式回复：
{
  "description": "图片描述",
  "text_content": "提取的文字内容",
  "numbers": "提取的数字"
}"""
        
        data = {
            "model": model_id,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": test_prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 500,
            "temperature": 0.1
        }
        
        print(f"📤 发送视觉请求到模型: {model_id}")
        
        response = requests.post(
            "http://127.0.0.1:1234/v1/chat/completions",
            headers=headers,
            json=data,
            timeout=60
        )
        
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 视觉模型响应成功")
            
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0].get('message', {}).get('content', '')
                print(f"💬 视觉识别结果:")
                print(f"   {content}")
                
                # 尝试解析JSON
                try:
                    if '{' in content and '}' in content:
                        # 提取JSON部分
                        start = content.find('{')
                        end = content.rfind('}') + 1
                        json_part = content[start:end]
                        parsed = json.loads(json_part)
                        print(f"📊 解析的JSON结果:")
                        for key, value in parsed.items():
                            print(f"   {key}: {value}")
                except Exception as e:
                    print(f"⚠️ JSON解析失败: {e}")
                
                return True, result
            else:
                print("❌ 响应格式异常: 缺少choices或content")
                return False, result
        else:
            print(f"❌ 视觉模型响应失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"🔍 错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"🔍 错误文本: {response.text}")
            return False, None
            
    except Exception as e:
        print(f"❌ 视觉测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, None
    finally:
        # 清理临时文件
        if image_path == "temp_test_image.png" and os.path.exists(image_path):
            os.remove(image_path)

def diagnose_response_format():
    """诊断响应格式问题"""
    print("\n🧪 诊断响应格式问题")
    print("=" * 50)
    
    # 检查LM Studio连接
    connected, models_data = test_lm_studio_connection()
    if not connected:
        return False
    
    # 获取可用的视觉模型
    vision_models = []
    for model in models_data.get('data', []):
        model_id = model.get('id', '').lower()
        if any(keyword in model_id for keyword in ['vl', 'vision', 'gemma', 'ocr', 'llava']):
            vision_models.append(model.get('id', ''))
    
    print(f"\n🔍 找到 {len(vision_models)} 个视觉模型:")
    for model in vision_models:
        print(f"   • {model}")
    
    if not vision_models:
        print("❌ 未找到视觉模型")
        return False
    
    # 测试每个视觉模型
    results = []
    for model_id in vision_models[:3]:  # 只测试前3个模型
        print(f"\n{'='*60}")
        
        # 先测试基本文本响应
        text_success, text_result = test_model_response(model_id, "请回复：测试成功")
        
        # 再测试视觉响应
        vision_success, vision_result = test_vision_model_with_image(model_id)
        
        results.append({
            'model': model_id,
            'text_success': text_success,
            'vision_success': vision_success,
            'text_result': text_result,
            'vision_result': vision_result
        })
    
    # 总结结果
    print(f"\n{'='*60}")
    print("📊 诊断结果总结:")
    
    for result in results:
        model = result['model']
        text_status = "✅" if result['text_success'] else "❌"
        vision_status = "✅" if result['vision_success'] else "❌"
        print(f"   {model}:")
        print(f"     文本响应: {text_status}")
        print(f"     视觉响应: {vision_status}")
    
    return True

def main():
    """主诊断函数"""
    print("🚀 LM Studio响应问题诊断")
    print("=" * 60)
    
    try:
        diagnose_response_format()
        
        print(f"\n{'='*60}")
        print("💡 诊断建议:")
        print("1. 如果文本响应正常但视觉响应失败，可能是模型不支持视觉输入")
        print("2. 如果响应格式异常，可能是模型输出格式与预期不符")
        print("3. 如果连接失败，请检查LM Studio是否正在运行")
        print("4. 如果超时，可能需要增加timeout时间或使用更小的模型")
        print("5. 检查模型是否正确加载并支持OpenAI兼容的API格式")
        
    except Exception as e:
        print(f"❌ 诊断过程出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
