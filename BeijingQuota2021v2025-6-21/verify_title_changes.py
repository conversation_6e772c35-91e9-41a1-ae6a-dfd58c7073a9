#!/usr/bin/env python3
"""
验证标题修改是否正确
"""

def verify_title_changes():
    """验证标题修改"""
    print("🔍 验证标题修改")
    print("=" * 50)
    
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查修改的内容
        changes = [
            {
                "old": "🏗️ 北京定额表格识别系统",
                "new": "🏗️ 北京市2021消耗定额智能提取工具",
                "description": "主标题"
            },
            {
                "old": "📄页码范围设置",
                "new": "📄自定义页码范围设置", 
                "description": "页码范围标题"
            },
            {
                "old": "建议先测试少量页面，确认效果后再处理全部",
                "new": "整体识别过程太长，建议按章节页面提取，后续将所有数据合并处理",
                "description": "建议文本"
            }
        ]
        
        print("📋 检查修改结果:")
        all_correct = True
        
        for change in changes:
            old_count = content.count(change["old"])
            new_count = content.count(change["new"])
            
            print(f"\n🔍 {change['description']}:")
            print(f"  旧文本 '{change['old']}': {old_count} 处")
            print(f"  新文本 '{change['new']}': {new_count} 处")
            
            if old_count == 0 and new_count > 0:
                print(f"  ✅ 修改成功 ({new_count} 处)")
            elif old_count > 0:
                print(f"  ⚠️ 仍有旧文本未修改 ({old_count} 处)")
                all_correct = False
            elif new_count == 0:
                print(f"  ❌ 新文本未找到")
                all_correct = False
        
        # 检查具体位置
        print(f"\n📍 检查具体位置:")
        
        # 检查title属性
        if "北京市2021消耗定额智能提取工具" in content and "title=" in content:
            print("  ✅ 浏览器标题已更新")
        else:
            print("  ❌ 浏览器标题未更新")
            all_correct = False
        
        # 检查主标题
        if '<div class="main-title">' in content and "北京市2021消耗定额智能提取工具" in content:
            print("  ✅ 主页面标题已更新")
        else:
            print("  ❌ 主页面标题未更新")
            all_correct = False
        
        # 检查页脚
        if "北京市2021消耗定额智能提取工具" in content and "专业级AI识别" in content:
            print("  ✅ 页脚标题已更新")
        else:
            print("  ❌ 页脚标题未更新")
            all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def verify_system_running():
    """验证系统是否正常运行"""
    print(f"\n🚀 验证系统运行状态")
    print("=" * 40)
    
    try:
        import requests
        
        try:
            response = requests.get("http://0.0.0.0:7863", timeout=10)
            if response.status_code == 200:
                print("✅ 系统运行正常")
                
                # 检查页面内容是否包含新标题
                if "北京市2021消耗定额智能提取工具" in response.text:
                    print("✅ 页面显示新标题")
                    return True
                else:
                    print("⚠️ 页面未显示新标题")
                    return False
            else:
                print(f"⚠️ 系统响应异常: {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            print("⚠️ 无法连接到系统")
            return False
        except Exception as e:
            print(f"⚠️ 连接测试失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 标题修改验证")
    print("=" * 60)
    
    # 验证文件修改
    file_check = verify_title_changes()
    
    # 验证系统运行
    system_check = verify_system_running()
    
    print(f"\n" + "=" * 60)
    print("📊 验证结果汇总:")
    print(f"   📝 文件修改: {'✅ 正确' if file_check else '❌ 有问题'}")
    print(f"   🚀 系统运行: {'✅ 正常' if system_check else '❌ 异常'}")
    
    if file_check and system_check:
        print(f"\n🎉 所有修改验证通过！")
        print(f"💡 修改内容:")
        print(f"   • 主标题: 🏗️ 北京市2021消耗定额智能提取工具")
        print(f"   • 页码设置: 📄自定义页码范围设置")
        print(f"   • 建议文本: 整体识别过程太长，建议按章节页面提取，后续将所有数据合并处理")
        print(f"\n🌐 访问地址: http://0.0.0.0:7863")
    else:
        print(f"\n⚠️ 部分修改需要检查")

if __name__ == "__main__":
    main()
