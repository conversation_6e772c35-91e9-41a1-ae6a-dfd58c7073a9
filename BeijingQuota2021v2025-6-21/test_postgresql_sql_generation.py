#!/usr/bin/env python3
"""
测试PostgreSQL SQL生成
"""

import sys
import os
from pathlib import Path

# 添加src目录到路径
sys.path.append('src')

def test_sql_generation():
    """测试SQL生成"""
    try:
        from mcp_database_converter import MCPDatabaseConverter
        
        # 创建转换器
        converter = MCPDatabaseConverter()
        
        # 查找最新的处理文件
        output_dir = Path("output")
        
        # 查找最新的时间戳
        processed_files = []
        for file_path in output_dir.glob("processed_*_20250626_*.csv"):
            processed_files.append(str(file_path))
        
        if not processed_files:
            print("❌ 未找到处理后的文件")
            return False
        
        # 按时间戳排序，取最新的
        processed_files.sort()

        # 分别查找parent和child文件
        parent_files = [f for f in processed_files if 'parent_quotas' in f]
        child_files = [f for f in processed_files if 'child_resources' in f]

        if not parent_files or not child_files:
            print(f"❌ 未找到完整的文件对")
            print(f"   - parent文件: {len(parent_files)} 个")
            print(f"   - child文件: {len(child_files)} 个")
            return False

        parent_file = parent_files[-1]  # 最新的
        child_file = child_files[-1]    # 最新的
        
        if not os.path.exists(parent_file) or not os.path.exists(child_file):
            print(f"❌ 文件不存在: {parent_file} 或 {child_file}")
            return False
        
        print(f"📋 使用文件:")
        print(f"   - 定额项: {parent_file}")
        print(f"   - 资源: {child_file}")
        
        # 生成SQL
        timestamp = os.path.basename(parent_file).split('_')[-1].replace('.csv', '')
        sql_output = f"output/test_postgresql_{timestamp}.sql"
        success, message, stats = converter.convert_to_sql_script(
            [parent_file, child_file], sql_output, 'postgresql'
        )
        
        if success:
            print(f"✅ SQL生成成功: {message}")
            print(f"📊 统计信息:")
            print(f"   - 成功文件: {stats.get('successful_files', 0)}")
            print(f"   - 总行数: {stats.get('total_rows', 0)}")
            print(f"   - 表数量: {len(stats.get('tables', []))}")
            
            # 显示表信息
            for table in stats.get('tables', []):
                print(f"   - {table['name']}: {table['rows']} 行, {table['columns']} 列")
            
            # 检查生成的SQL文件
            if os.path.exists(sql_output):
                with open(sql_output, 'r', encoding='utf-8') as f:
                    sql_content = f.read()
                
                print(f"\n📄 SQL文件信息:")
                print(f"   - 文件大小: {len(sql_content)} 字符")
                print(f"   - 行数: {len(sql_content.splitlines())}")
                
                # 显示前几行
                lines = sql_content.splitlines()
                print(f"\n📋 SQL内容预览 (前10行):")
                for i, line in enumerate(lines[:10]):
                    print(f"   {i+1:2d}: {line}")
                
                if len(lines) > 10:
                    print(f"   ... 还有 {len(lines) - 10} 行")
                
                # 检查是否包含CREATE TABLE语句
                create_tables = [line for line in lines if 'CREATE TABLE' in line.upper()]
                print(f"\n🔍 CREATE TABLE语句: {len(create_tables)} 个")
                for table_stmt in create_tables:
                    print(f"   - {table_stmt.strip()}")
                
                # 检查是否包含INSERT语句
                insert_statements = [line for line in lines if 'INSERT INTO' in line.upper()]
                print(f"\n📥 INSERT语句: {len(insert_statements)} 个")
                if insert_statements:
                    print(f"   - 第一个: {insert_statements[0][:100]}...")
                    if len(insert_statements) > 1:
                        print(f"   - 最后一个: {insert_statements[-1][:100]}...")
                
                return True
            else:
                print(f"❌ SQL文件未生成: {sql_output}")
                return False
        else:
            print(f"❌ SQL生成失败: {message}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 PostgreSQL SQL生成测试")
    print("=" * 50)
    
    success = test_sql_generation()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ SQL生成测试通过")
    else:
        print("❌ SQL生成测试失败")
    
    return success

if __name__ == "__main__":
    main()
