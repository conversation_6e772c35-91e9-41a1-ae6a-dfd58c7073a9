#!/usr/bin/env python3
"""
测试信息价识别处理器
Test Price Information Recognition Processor
"""

import os
import sys
import asyncio
import pandas as pd
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.price_info_processor import PriceInfoProcessor
from src.advanced_quota_manager import AdvancedQuotaManager

def test_price_info_processor():
    """测试信息价识别处理器"""
    print("🧪 测试信息价识别处理器")
    print("=" * 50)
    
    # 检查PDF文件是否存在
    pdf_file = "北京市2025-06造价信息.pdf"
    if not os.path.exists(pdf_file):
        print(f"❌ 未找到PDF文件: {pdf_file}")
        print("请确保PDF文件在当前目录下")
        return False
    
    print(f"📄 找到PDF文件: {pdf_file}")
    
    # 创建处理器
    processor = PriceInfoProcessor()
    
    # 测试处理PDF（只处理第1页作为测试）
    print("\n🔄 开始处理PDF文件...")
    
    async def run_test():
        try:
            success, message, stats = await processor.process_price_info_pdf(
                pdf_file,
                start_page=1,
                end_page=2,  # 只处理前2页作为测试
                model_type="qwen_qvq_max",
                output_dir="output"
            )
            
            if success:
                print("✅ 信息价识别成功!")
                print(f"📋 结果消息:")
                print(message)
                
                # 检查输出文件
                output_files = stats.get('output_files', [])
                if output_files:
                    print(f"\n📁 生成的文件:")
                    for file_path in output_files:
                        if os.path.exists(file_path):
                            size_kb = os.path.getsize(file_path) / 1024
                            print(f"   - {file_path} ({size_kb:.1f} KB)")
                            
                            # 如果是CSV文件，显示前几行
                            if file_path.endswith('.csv'):
                                try:
                                    df = pd.read_csv(file_path, encoding='utf-8-sig')
                                    print(f"     📊 数据行数: {len(df)}")
                                    if len(df) > 0:
                                        print(f"     📋 列名: {list(df.columns)}")
                                        print(f"     🔍 前3行数据:")
                                        print(df.head(3).to_string(index=False))
                                except Exception as e:
                                    print(f"     ❌ 读取CSV失败: {e}")
                
                return True
            else:
                print(f"❌ 信息价识别失败: {message}")
                return False
                
        except Exception as e:
            print(f"❌ 测试过程中出错: {str(e)}")
            return False
    
    # 运行异步测试
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        result = loop.run_until_complete(run_test())
        return result
    finally:
        loop.close()

def test_price_info_database():
    """测试信息价数据库功能"""
    print("\n🗄️ 测试信息价数据库功能")
    print("=" * 50)
    
    # 查找最新的信息价CSV文件
    output_dir = "output"
    price_files = []
    
    if os.path.exists(output_dir):
        for file in os.listdir(output_dir):
            if file.startswith('price_info_result_') and file.endswith('.csv'):
                file_path = os.path.join(output_dir, file)
                price_files.append((file, file_path, os.path.getmtime(file_path)))
    
    if not price_files:
        print("❌ 未找到信息价CSV文件，请先运行信息价识别")
        return False
    
    # 选择最新的文件
    price_files.sort(key=lambda x: x[2], reverse=True)
    latest_file = price_files[0][1]
    print(f"📄 使用信息价文件: {latest_file}")
    
    # 创建数据库管理器
    manager = AdvancedQuotaManager()
    
    # 连接到SQLite数据库
    db_config = {
        'database_path': 'output/price_info_test.db'
    }
    
    print("\n🔗 连接到SQLite数据库...")
    success, message = manager.connect_to_database('sqlite', db_config)
    
    if not success:
        print(f"❌ 数据库连接失败: {message}")
        return False
    
    print("✅ 数据库连接成功")
    
    # 导入信息价数据
    print("\n📥 导入信息价数据...")
    success, message = manager.import_price_info_data(latest_file)
    
    if success:
        print("✅ 信息价数据导入成功!")
        print(f"📋 导入结果:")
        print(message)
        
        # 查询数据验证
        print("\n🔍 验证导入的数据...")
        try:
            cursor = manager.connection.cursor()
            cursor.execute("SELECT COUNT(*) FROM price_info")
            count = cursor.fetchone()[0]
            print(f"📊 数据库中共有 {count} 条信息价记录")
            
            # 显示前几条记录
            cursor.execute("""
                SELECT resource_code, product_name, unit, price_with_tax, price_without_tax 
                FROM price_info 
                LIMIT 5
            """)
            records = cursor.fetchall()
            
            if records:
                print("\n📋 前5条记录:")
                for record in records:
                    print(f"   资源编号: {record[0]}, 产品名称: {record[1]}, 单位: {record[2]}, "
                          f"含税价: {record[3]}, 不含税价: {record[4]}")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据验证失败: {str(e)}")
            return False
    else:
        print(f"❌ 信息价数据导入失败: {message}")
        return False

def test_price_merge():
    """测试信息价与定额数据合并"""
    print("\n🔗 测试信息价与定额数据合并")
    print("=" * 50)
    
    # 查找信息价文件和定额资源文件
    output_dir = "output"
    price_files = []
    quota_files = []
    
    if os.path.exists(output_dir):
        for file in os.listdir(output_dir):
            file_path = os.path.join(output_dir, file)
            if file.startswith('price_info_result_') and file.endswith('.csv'):
                price_files.append((file, file_path, os.path.getmtime(file_path)))
            elif 'child_resources' in file and file.endswith('.csv'):
                quota_files.append((file, file_path, os.path.getmtime(file_path)))
    
    if not price_files:
        print("❌ 未找到信息价CSV文件")
        return False
    
    if not quota_files:
        print("❌ 未找到定额资源CSV文件")
        return False
    
    # 选择最新的文件
    price_files.sort(key=lambda x: x[2], reverse=True)
    quota_files.sort(key=lambda x: x[2], reverse=True)
    
    price_file = price_files[0][1]
    quota_file = quota_files[0][1]
    
    print(f"📊 信息价文件: {price_file}")
    print(f"📋 定额资源文件: {quota_file}")
    
    # 创建处理器并执行合并
    processor = PriceInfoProcessor()
    
    print("\n🔄 开始合并数据...")
    success, message, output_file = processor.merge_price_info_with_quotas(
        price_file, quota_file
    )
    
    if success:
        print("✅ 数据合并成功!")
        print(f"📋 合并结果:")
        print(message)
        print(f"📁 输出文件: {output_file}")
        
        # 验证合并结果
        if os.path.exists(output_file):
            try:
                df = pd.read_csv(output_file, encoding='utf-8-sig')
                print(f"\n📊 合并文件统计:")
                print(f"   - 总记录数: {len(df)}")
                print(f"   - 列数: {len(df.columns)}")
                
                # 统计匹配情况
                if '匹配状态' in df.columns:
                    matched = len(df[df['匹配状态'] == '已匹配'])
                    unmatched = len(df[df['匹配状态'] == '未匹配'])
                    print(f"   - 已匹配: {matched}")
                    print(f"   - 未匹配: {unmatched}")
                    print(f"   - 匹配率: {matched/(matched+unmatched)*100:.1f}%")
                
                return True
                
            except Exception as e:
                print(f"❌ 验证合并结果失败: {str(e)}")
                return False
        else:
            print(f"❌ 输出文件不存在: {output_file}")
            return False
    else:
        print(f"❌ 数据合并失败: {message}")
        return False

def main():
    """主测试函数"""
    print("🚀 信息价识别处理器测试")
    print("=" * 60)
    
    # 检查环境
    print("🔧 检查环境...")
    
    # 检查API密钥
    import os
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if not api_key:
        print("⚠️ 未设置DASHSCOPE_API_KEY环境变量")
        print("   如果要使用千问QVQ模型，请先设置API密钥")
    else:
        print("✅ 找到DASHSCOPE_API_KEY")
    
    # 创建输出目录
    os.makedirs("output", exist_ok=True)
    print("✅ 输出目录已准备")
    
    # 运行测试
    tests = [
        ("信息价识别处理", test_price_info_processor),
        ("信息价数据库功能", test_price_info_database),
        ("信息价数据合并", test_price_merge)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！信息价识别功能已准备就绪。")
    else:
        print("⚠️ 部分测试失败，请检查相关配置和依赖。")

if __name__ == "__main__":
    main()
