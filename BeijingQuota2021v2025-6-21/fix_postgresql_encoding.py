#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PostgreSQL编码问题完整修复脚本
解决数据库文件损坏和编码问题
"""

import os
import sys
import subprocess
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import json
from pathlib import Path

def check_postgresql_service():
    """检查PostgreSQL服务状态"""
    try:
        print("🔍 检查PostgreSQL服务状态...")
        
        # 检查Windows服务
        result = subprocess.run(
            ['sc', 'query', 'postgresql-x64-17'], 
            capture_output=True, 
            text=True,
            encoding='utf-8'
        )
        
        if 'RUNNING' in result.stdout:
            print("✅ PostgreSQL服务正在运行")
            return True
        else:
            print("❌ PostgreSQL服务未运行")
            return False
            
    except Exception as e:
        print(f"❌ 检查服务状态失败: {e}")
        return False

def stop_postgresql_service():
    """停止PostgreSQL服务"""
    try:
        print("🛑 停止PostgreSQL服务...")
        result = subprocess.run(
            ['net', 'stop', 'postgresql-x64-17'], 
            capture_output=True, 
            text=True,
            encoding='utf-8'
        )
        
        if result.returncode == 0:
            print("✅ PostgreSQL服务已停止")
            return True
        else:
            print(f"❌ 停止服务失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 停止服务失败: {e}")
        return False

def start_postgresql_service():
    """启动PostgreSQL服务"""
    try:
        print("🚀 启动PostgreSQL服务...")
        result = subprocess.run(
            ['net', 'start', 'postgresql-x64-17'], 
            capture_output=True, 
            text=True,
            encoding='utf-8'
        )
        
        if result.returncode == 0:
            print("✅ PostgreSQL服务已启动")
            return True
        else:
            print(f"❌ 启动服务失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 启动服务失败: {e}")
        return False

def backup_postgresql_data():
    """备份PostgreSQL数据目录"""
    try:
        print("💾 备份PostgreSQL数据...")
        
        # 常见的PostgreSQL数据目录
        possible_data_dirs = [
            r"C:\Program Files\PostgreSQL\17\data",
            r"C:\Program Files\PostgreSQL\16\data",
            r"C:\Program Files\PostgreSQL\15\data",
            r"C:\PostgreSQL\data",
            r"C:\Program Files (x86)\PostgreSQL\17\data"
        ]
        
        data_dir = None
        for dir_path in possible_data_dirs:
            if os.path.exists(dir_path):
                data_dir = dir_path
                break
        
        if not data_dir:
            print("⚠️ 未找到PostgreSQL数据目录，跳过备份")
            return True
        
        backup_dir = f"{data_dir}_backup_{int(time.time())}"
        
        print(f"📂 数据目录: {data_dir}")
        print(f"📂 备份目录: {backup_dir}")
        
        # 使用robocopy进行备份
        result = subprocess.run(
            ['robocopy', data_dir, backup_dir, '/E', '/COPY:DAT'],
            capture_output=True,
            text=True,
            encoding='utf-8'
        )
        
        # robocopy返回码1-7表示成功
        if result.returncode <= 7:
            print("✅ 数据备份完成")
            return True, backup_dir
        else:
            print(f"❌ 备份失败: {result.stderr}")
            return False, None
            
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return False, None

def reinitialize_postgresql_cluster():
    """重新初始化PostgreSQL集群"""
    try:
        print("🔄 重新初始化PostgreSQL集群...")
        
        # 查找PostgreSQL安装目录
        possible_pg_dirs = [
            r"C:\Program Files\PostgreSQL\17\bin",
            r"C:\Program Files\PostgreSQL\16\bin",
            r"C:\Program Files\PostgreSQL\15\bin",
            r"C:\Program Files (x86)\PostgreSQL\17\bin"
        ]
        
        pg_bin_dir = None
        for dir_path in possible_pg_dirs:
            if os.path.exists(os.path.join(dir_path, "initdb.exe")):
                pg_bin_dir = dir_path
                break
        
        if not pg_bin_dir:
            print("❌ 未找到PostgreSQL安装目录")
            return False
        
        print(f"📂 PostgreSQL安装目录: {pg_bin_dir}")
        
        # 数据目录
        data_dir = os.path.join(os.path.dirname(pg_bin_dir), "data")
        
        # 删除现有数据目录
        if os.path.exists(data_dir):
            import shutil
            shutil.rmtree(data_dir)
            print(f"🗑️ 删除损坏的数据目录: {data_dir}")
        
        # 重新初始化数据库集群
        initdb_cmd = [
            os.path.join(pg_bin_dir, "initdb.exe"),
            "-D", data_dir,
            "-U", "postgres",
            "--encoding=UTF8",
            "--locale=C",
            "--lc-collate=C",
            "--lc-ctype=C"
        ]
        
        print("🔧 执行initdb命令...")
        result = subprocess.run(
            initdb_cmd,
            capture_output=True,
            text=True,
            encoding='utf-8'
        )
        
        if result.returncode == 0:
            print("✅ PostgreSQL集群重新初始化成功")
            return True
        else:
            print(f"❌ 初始化失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 重新初始化失败: {e}")
        return False

def test_clean_connection():
    """测试清洁的连接"""
    try:
        print("🔍 测试清洁的PostgreSQL连接...")
        
        # 等待服务启动
        import time
        time.sleep(5)
        
        conn = psycopg2.connect(
            host='localhost',
            port=5432,
            user='postgres',
            password='postgres123',  # 请根据实际情况修改
            database='postgres',
            client_encoding='utf8'
        )
        
        cursor = conn.cursor()
        cursor.execute("SELECT version()")
        version = cursor.fetchone()[0]
        
        print(f"✅ 连接成功!")
        print(f"   版本: {version.split(',')[0]}")
        
        # 设置数据库编码
        cursor.execute("SHOW server_encoding")
        server_encoding = cursor.fetchone()[0]
        print(f"   服务器编码: {server_encoding}")
        
        cursor.execute("SHOW client_encoding")
        client_encoding = cursor.fetchone()[0]
        print(f"   客户端编码: {client_encoding}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def create_clean_database():
    """创建干净的数据库"""
    try:
        print("🗄️ 创建干净的定额数据库...")
        
        conn = psycopg2.connect(
            host='localhost',
            port=5432,
            user='postgres',
            password='postgres123',  # 请根据实际情况修改
            database='postgres',
            client_encoding='utf8'
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # 创建数据库时指定编码
        cursor.execute("""
            CREATE DATABASE "beijing2021_quota_database"
            WITH ENCODING 'UTF8'
            LC_COLLATE = 'C'
            LC_CTYPE = 'C'
            TEMPLATE = template0
        """)
        
        print("✅ 创建数据库成功: beijing2021_quota_database")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 创建数据库失败: {e}")
        return False

def main():
    """主修复流程"""
    print("🔧 PostgreSQL编码问题完整修复工具")
    print("=" * 60)
    
    print("⚠️ 警告: 此操作将重新初始化PostgreSQL数据库集群")
    print("⚠️ 所有现有数据库和数据将被删除")
    
    choice = input("\n是否继续？(输入 'YES' 确认): ").strip()
    if choice != 'YES':
        print("❌ 操作已取消")
        return
    
    try:
        import time
        
        # 1. 检查服务状态
        if not check_postgresql_service():
            print("❌ PostgreSQL服务未运行，请先启动服务")
            return
        
        # 2. 停止服务
        if not stop_postgresql_service():
            print("❌ 无法停止PostgreSQL服务")
            return
        
        # 3. 备份数据（可选）
        print("\n💾 是否备份现有数据？")
        backup_choice = input("输入 'y' 备份，其他键跳过: ").strip().lower()
        if backup_choice == 'y':
            backup_success, backup_dir = backup_postgresql_data()
            if backup_success:
                print(f"✅ 备份完成: {backup_dir}")
        
        # 4. 重新初始化集群
        if not reinitialize_postgresql_cluster():
            print("❌ 重新初始化失败")
            return
        
        # 5. 启动服务
        if not start_postgresql_service():
            print("❌ 启动服务失败")
            return
        
        # 6. 测试连接
        if not test_clean_connection():
            print("❌ 连接测试失败")
            return
        
        # 7. 创建干净的数据库
        if not create_clean_database():
            print("❌ 创建数据库失败")
            return
        
        print("\n🎉 PostgreSQL编码问题修复完成！")
        print("✅ 数据库集群已重新初始化")
        print("✅ 编码设置为UTF-8")
        print("✅ 创建了干净的定额数据库")
        print("\n💡 现在可以:")
        print("1. 在pgAdmin4中正常连接数据库")
        print("2. 重新导入定额数据")
        print("3. 使用程序的数据库功能")
        
    except Exception as e:
        print(f"❌ 修复过程中发生错误: {e}")

if __name__ == "__main__":
    main()
