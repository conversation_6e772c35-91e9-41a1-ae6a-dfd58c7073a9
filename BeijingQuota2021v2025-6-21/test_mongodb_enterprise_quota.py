#!/usr/bin/env python3
"""
测试企业定额管理系统的MongoDB功能
"""

import os
import sys
import pandas as pd
from pathlib import Path

# 添加src目录到路径
sys.path.append('src')

def create_test_data():
    """创建测试数据文件"""
    # 确保output目录存在
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    # 创建parent_quotas测试数据
    parent_data = {
        '定额编号': ['001-001', '001-002', '001-003', '002-001', '002-002'],
        '定额名称': ['混凝土浇筑C30', '钢筋绑扎HPB300', '模板安装拆除', '砌砖工程MU10', '抹灰工程'],
        '单位': ['m³', 'kg', 'm²', 'm³', 'm²'],
        '人工费': [120.5, 85.2, 95.8, 110.3, 75.6],
        '材料费': [450.8, 320.5, 180.2, 280.9, 120.4],
        '机械费': [80.3, 45.8, 25.6, 35.2, 15.8],
        '合计': [651.6, 451.5, 301.6, 426.4, 211.8]
    }
    
    parent_df = pd.DataFrame(parent_data)
    parent_csv_path = output_dir / "parent_quotas_mongodb_test.csv"
    parent_df.to_csv(parent_csv_path, index=False, encoding='utf-8-sig')
    
    # 创建child_resources测试数据
    child_data = {
        '定额编号': ['001-001', '001-001', '001-001', '001-002', '001-002', '002-001', '002-001'],
        '资源编号': ['R001', 'R002', 'R003', 'R004', 'R005', 'R006', 'R007'],
        '资源名称': ['C30混凝土', '人工', '机械台班', 'HPB300钢筋', '人工', 'MU10砖', '砂浆'],
        '资源类型': ['材料', '人工', '机械', '材料', '人工', '材料', '材料'],
        '数量': [1.05, 8.5, 2.3, 1.02, 12.8, 0.98, 0.25],
        '单位': ['m³', '工日', '台班', 'kg', '工日', 'm³', 'm³'],
        '单价': [420.0, 150.0, 350.0, 4.2, 150.0, 280.0, 180.0],
        '合价': [441.0, 1275.0, 805.0, 4.284, 1920.0, 274.4, 45.0]
    }
    
    child_df = pd.DataFrame(child_data)
    child_csv_path = output_dir / "child_resources_mongodb_test.csv"
    child_df.to_csv(child_csv_path, index=False, encoding='utf-8-sig')
    
    print(f"✅ 创建MongoDB测试数据文件:")
    print(f"   - {parent_csv_path}")
    print(f"   - {child_csv_path}")
    
    return str(parent_csv_path), str(child_csv_path)

def test_mongodb_enterprise_quota():
    """测试MongoDB企业定额管理功能"""
    try:
        from enterprise_quota_manager import EnterpriseQuotaManager
        
        # 检查MongoDB依赖
        try:
            import pymongo
            print("✅ pymongo 已安装")
        except ImportError:
            print("❌ pymongo 未安装，请运行: pip install pymongo")
            return False
        
        # 创建测试数据
        parent_file, child_file = create_test_data()
        
        # 创建管理器
        manager = EnterpriseQuotaManager()
        
        # 测试MongoDB数据库创建
        print("\n🍃 测试MongoDB数据库创建...")
        
        # MongoDB配置（使用本地MongoDB实例）
        db_config = {
            'host': 'localhost',
            'port': 27017,
            'user': '',  # 如果有认证，填写用户名
            'password': '',  # 如果有认证，填写密码
            'database': 'test_enterprise_quota'
        }
        
        print(f"📡 连接MongoDB: {db_config['host']}:{db_config['port']}")
        print(f"🗄️ 数据库: {db_config['database']}")
        
        success, message = manager.create_quota_database(
            'mongodb', db_config, [parent_file], [child_file]
        )
        
        if success:
            print("✅ MongoDB数据库创建成功!")
            print(f"📋 {message}")
            
            # 验证数据
            print("\n🔍 验证MongoDB数据...")
            try:
                from pymongo import MongoClient
                
                client = MongoClient(f"mongodb://{db_config['host']}:{db_config['port']}/")
                db = client[db_config['database']]
                
                # 检查集合
                collections = db.list_collection_names()
                print(f"📊 集合列表: {collections}")
                
                # 检查定额项数据
                parent_count = db.parent_quotas.count_documents({})
                print(f"📋 定额项数量: {parent_count}")
                
                # 检查资源数据
                child_count = db.child_resources.count_documents({})
                print(f"🔧 资源记录数: {child_count}")
                
                # 显示示例数据
                print("\n📄 定额项示例:")
                sample_quota = db.parent_quotas.find_one()
                if sample_quota:
                    print(f"   - 编号: {sample_quota.get('quota_code')}")
                    print(f"   - 名称: {sample_quota.get('quota_name')}")
                    print(f"   - 总价: {sample_quota.get('total_cost')}")
                
                print("\n🔧 资源示例:")
                sample_resource = db.child_resources.find_one()
                if sample_resource:
                    print(f"   - 定额编号: {sample_resource.get('quota_code')}")
                    print(f"   - 资源名称: {sample_resource.get('resource_name')}")
                    print(f"   - 合价: {sample_resource.get('total_price')}")
                
                # 测试关联查询
                print("\n🔗 测试关联查询...")
                quota_code = sample_quota.get('quota_code') if sample_quota else None
                if quota_code:
                    resources = list(db.child_resources.find({'quota_code': quota_code}))
                    print(f"   - 定额项 {quota_code} 的关联资源数: {len(resources)}")
                    for resource in resources:
                        print(f"     • {resource.get('resource_name')}: {resource.get('total_price')}元")
                
                # 测试聚合查询
                print("\n📊 测试聚合查询...")
                pipeline = [
                    {'$group': {
                        '_id': '$quota_code',
                        'total_resources': {'$sum': 1},
                        'total_cost': {'$sum': '$total_price'}
                    }},
                    {'$sort': {'_id': 1}}
                ]
                
                aggregation_results = list(db.child_resources.aggregate(pipeline))
                print("   - 按定额项聚合结果:")
                for result in aggregation_results:
                    print(f"     • {result['_id']}: {result['total_resources']}个资源, 总价{result['total_cost']}元")
                
                client.close()
                
            except Exception as verify_error:
                print(f"⚠️ 数据验证过程中出现错误: {verify_error}")
            
        else:
            print(f"❌ MongoDB数据库创建失败: {message}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 开始测试企业定额管理系统的MongoDB功能")
    print("=" * 60)
    
    # 检查基础依赖
    try:
        import pandas as pd
        print("✅ pandas 已安装")
    except ImportError:
        print("❌ pandas 未安装")
        return False
    
    # 检查MongoDB连接
    print("\n🔍 检查MongoDB连接...")
    try:
        from pymongo import MongoClient
        client = MongoClient('mongodb://localhost:27017/', serverSelectionTimeoutMS=2000)
        client.server_info()  # 触发连接
        print("✅ MongoDB服务器连接正常")
        client.close()
    except Exception as e:
        print(f"❌ MongoDB连接失败: {e}")
        print("💡 请确保MongoDB服务器正在运行")
        print("   - 安装MongoDB: https://www.mongodb.com/try/download/community")
        print("   - 启动服务: mongod")
        return False
    
    # 运行测试
    success = test_mongodb_enterprise_quota()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 MongoDB企业定额管理功能测试通过!")
        print("\n💡 MongoDB功能特点:")
        print("1. 🍃 NoSQL文档数据库，支持灵活的数据结构")
        print("2. 📊 强大的聚合查询功能，适合复杂数据分析")
        print("3. 🚀 高性能和可扩展性，适合大数据量应用")
        print("4. 🔍 支持复杂查询和索引优化")
        print("5. 🌐 分布式架构，支持集群部署")
        
        print("\n🎯 使用场景:")
        print("- 大型企业定额数据管理")
        print("- 复杂的定额数据分析和报表")
        print("- 需要灵活数据结构的应用")
        print("- 高并发的定额查询系统")
    else:
        print("❌ MongoDB企业定额管理功能测试失败!")
    
    return success

if __name__ == "__main__":
    main()
