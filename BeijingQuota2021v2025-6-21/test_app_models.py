#!/usr/bin/env python3
"""
测试应用中的模型检测
"""

import os
import sys

def test_modelscope_detection():
    """测试ModelScope检测逻辑"""
    print("🔍 测试应用中的ModelScope检测逻辑")
    print("=" * 50)
    
    # 1. 检查模型文件是否存在
    model_cache_path = os.path.expanduser("~/.cache/modelscope/hub/models/ZhipuAI/cogvlm2-llama3-chinese-chat-19B-int4")
    print(f"📁 检查模型路径: {model_cache_path}")
    
    if not os.path.exists(model_cache_path):
        print("❌ 模型文件不存在")
        return False
    
    print("✅ 模型文件存在")
    
    # 2. 检查PyTorch依赖
    try:
        import torch
        print(f"✅ PyTorch已安装: {torch.__version__}")
    except ImportError:
        print("❌ PyTorch未安装")
        return False
    
    # 3. 检查ModelScope和transformers依赖
    try:
        from modelscope import AutoTokenizer, AutoModel
        print("✅ ModelScope依赖已安装")
    except ImportError as e:
        print(f"❌ ModelScope依赖缺失: {e}")
        return False
    
    # 4. 尝试加载tokenizer来验证模型完整性
    try:
        model_id = "ZhipuAI/cogvlm2-llama3-chinese-chat-19B-int4"
        tokenizer = AutoTokenizer.from_pretrained(model_id, trust_remote_code=True)
        print("✅ ModelScope CogVLM2模型验证成功")
        return True
    except Exception as e:
        print(f"❌ 模型验证失败: {e}")
        return False

def test_model_availability():
    """测试模型可用性检测"""
    print("\n🔍 模拟应用中的模型可用性检测")
    print("=" * 50)
    
    # 模拟 get_available_models 逻辑
    available = {}
    
    # 检查阿里云通义千问-QVQ-Max
    dashscope_key = os.getenv("DASHSCOPE_API_KEY")
    if dashscope_key:
        available["qwen_qvq_max"] = "阿里通义千问-QVQ-Max"
        print("✅ 阿里通义千问-QVQ-Max (有API密钥)")
    else:
        print("❌ 阿里通义千问-QVQ-Max (无API密钥)")
    
    # 检查ModelScope CogVLM2模型
    if test_modelscope_detection():
        available["modelscope_cogvlm2"] = "ModelScope-CogVLM2-Llama3-Chinese-19B-Int4"
        print("✅ ModelScope-CogVLM2-Llama3-Chinese-19B-Int4")
    else:
        print("❌ ModelScope-CogVLM2-Llama3-Chinese-19B-Int4")
    
    # 检查LM Studio模型 (简化检测)
    try:
        import requests
        response = requests.get("http://127.0.0.1:1234/v1/models", timeout=2)
        if response.status_code == 200:
            models = response.json()
            model_list = [model.get('id', '') for model in models.get('data', [])]
            if model_list:
                print(f"✅ LM Studio模型: {len(model_list)} 个")
                for i, model in enumerate(model_list[:3]):  # 显示前3个
                    available[f"lm_studio_{i}"] = f"LM Studio: {model}"
            else:
                print("❌ LM Studio无模型")
        else:
            print("❌ LM Studio服务不可访问")
    except Exception:
        print("❌ LM Studio未运行")
    
    print(f"\n📋 总计可用模型: {len(available)} 个")
    for key, name in available.items():
        print(f"  • {key}: {name}")
    
    return available

def main():
    """主函数"""
    print("🔧 北京市定额创建工具 - 模型检测测试")
    print("=" * 60)
    
    # 测试ModelScope检测
    modelscope_ok = test_modelscope_detection()
    
    # 测试整体模型可用性
    available_models = test_model_availability()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"  ModelScope CogVLM2: {'✅ 可用' if modelscope_ok else '❌ 不可用'}")
    print(f"  总可用模型数量: {len(available_models)}")
    
    if modelscope_ok:
        print("\n🎉 ModelScope CogVLM2模型完全可用！")
        print("💡 您现在应该能在应用的模型选择中看到这个选项")
        print("📍 位置: AI定额识别 -> AI模型选择 -> ModelScope-CogVLM2-Llama3-Chinese-19B-Int4")
        print("📍 位置: 信息价识别 -> AI模型类型 -> ModelScope-CogVLM2-Llama3-Chinese-19B-Int4")
    else:
        print("\n❌ ModelScope CogVLM2模型不可用")
        print("💡 请检查依赖安装和模型下载状态")

if __name__ == "__main__":
    main()
