#!/usr/bin/env python3
"""
简单的文件名测试
Simple Filename Test
"""

def test_filename_generation():
    """测试文件名生成逻辑"""
    print("🧪 测试章节号文件命名逻辑")
    print("=" * 50)
    
    def generate_filename(base_name, volume_code="", chapter_codes=""):
        """生成包含章节号的文件名"""
        import uuid
        
        filename_parts = [base_name]
        if volume_code:
            filename_parts.append(f"Vol{volume_code}")
        if chapter_codes:
            # 清理章节号，移除空格和特殊字符
            clean_chapters = [ch.strip() for ch in chapter_codes.split(',') if ch.strip()]
            if clean_chapters:
                chapters_str = "-".join(clean_chapters)
                filename_parts.append(f"Ch{chapters_str}")
        filename_parts.append(uuid.uuid4().hex[:8])
        
        return "_".join(filename_parts) + ".csv"
    
    # 测试用例
    test_cases = [
        {
            "name": "完整章节号",
            "volume_code": "04",
            "chapter_codes": "01,02,03",
            "expected_contains": ["Vol04", "Ch01-02-03"]
        },
        {
            "name": "单个章节号",
            "volume_code": "04",
            "chapter_codes": "01",
            "expected_contains": ["Vol04", "Ch01"]
        },
        {
            "name": "只有分册号",
            "volume_code": "04",
            "chapter_codes": "",
            "expected_contains": ["Vol04"]
        },
        {
            "name": "无章节号",
            "volume_code": "",
            "chapter_codes": "",
            "expected_contains": []
        },
        {
            "name": "带空格的章节号",
            "volume_code": "04",
            "chapter_codes": " 01 , 02 , 03 ",
            "expected_contains": ["Vol04", "Ch01-02-03"]
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {test_case['name']}")
        print(f"   分册编号: '{test_case['volume_code']}'")
        print(f"   章节编号: '{test_case['chapter_codes']}'")
        
        # 生成不同类型的文件名
        file_types = [
            "quota_extraction_result",
            "parent_quotas", 
            "child_resources"
        ]
        
        for file_type in file_types:
            filename = generate_filename(
                file_type,
                test_case['volume_code'], 
                test_case['chapter_codes']
            )
            
            print(f"   📄 {file_type}: {filename}")
            
            # 验证文件名是否包含期望的内容
            all_found = True
            for expected in test_case['expected_contains']:
                if expected not in filename:
                    print(f"      ❌ 缺少期望内容: {expected}")
                    all_found = False
            
            if all_found and test_case['expected_contains']:
                print(f"      ✅ 包含所有期望内容")
            elif not test_case['expected_contains']:
                print(f"      ✅ 基础文件名格式正确")
    
    print("\n" + "=" * 50)
    print("🎯 文件名生成逻辑测试完成！")

def show_filename_examples():
    """显示文件名示例"""
    print("\n📋 文件名示例展示")
    print("-" * 30)
    
    examples = [
        ("04", "01,02,03", "市政工程 - 通用、道路、桥涵"),
        ("01", "01", "房建工程 - 土石方"),
        ("02", "01,02", "装饰工程 - 楼地面、墙柱面"),
        ("", "", "无章节信息"),
    ]
    
    for volume, chapters, description in examples:
        print(f"\n🏗️ {description}")
        print(f"   分册: {volume or '无'}, 章节: {chapters or '无'}")
        
        # 模拟生成的文件名
        import uuid
        sample_id = uuid.uuid4().hex[:8]
        
        if volume and chapters:
            clean_chapters = [ch.strip() for ch in chapters.split(',') if ch.strip()]
            chapters_str = "-".join(clean_chapters)
            filename = f"quota_extraction_result_Vol{volume}_Ch{chapters_str}_{sample_id}.csv"
        elif volume:
            filename = f"quota_extraction_result_Vol{volume}_{sample_id}.csv"
        else:
            filename = f"quota_extraction_result_{sample_id}.csv"
        
        print(f"   📄 生成文件名: {filename}")

if __name__ == "__main__":
    test_filename_generation()
    show_filename_examples()
