#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试表名匹配问题
详细查看表名匹配过程
"""

def debug_table_matching():
    """调试表名匹配"""
    try:
        print("🔍 调试表名匹配...")

        from src.config_persistence_manager import ConfigPersistenceManager
        import psycopg2

        # 连接数据库
        config_manager = ConfigPersistenceManager()
        config = config_manager.load_config()
        quota_db_config = config.get('database_configs', {}).get('quota_db', {})

        # 先连接到默认数据库查看所有数据库
        default_conn = psycopg2.connect(
            host=quota_db_config.get('host', 'localhost'),
            port=int(quota_db_config.get('port', 5432)),
            user=quota_db_config.get('username', 'postgres'),
            password=quota_db_config.get('password', ''),
            database='postgres',
            client_encoding='utf8'
        )

        default_cursor = default_conn.cursor()
        default_cursor.execute("SELECT datname FROM pg_database WHERE datistemplate = false")
        databases = [row[0] for row in default_cursor.fetchall()]
        print(f"📊 PostgreSQL中的数据库: {databases}")
        default_cursor.close()
        default_conn.close()

        # 检查两个可能的数据库
        target_databases = ['beijing2021_quota_database', 'beijing2021_quota_test']

        for db_name in target_databases:
            if db_name in databases:
                print(f"\n🔍 检查数据库: {db_name}")
                try:
                    conn = psycopg2.connect(
                        host=quota_db_config.get('host', 'localhost'),
                        port=int(quota_db_config.get('port', 5432)),
                        user=quota_db_config.get('username', 'postgres'),
                        password=quota_db_config.get('password', ''),
                        database=db_name,
                        client_encoding='utf8'
                    )

                    result = check_database_tables(conn, db_name)
                    conn.close()

                    if result:
                        return result

                except Exception as e:
                    print(f"❌ 连接数据库 {db_name} 失败: {e}")
            else:
                print(f"⚠️ 数据库 {db_name} 不存在")

        return None, None

    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def check_database_tables(conn, db_name):
    """检查数据库中的表"""
    try:
        
        cursor = conn.cursor()

        print(f"   连接成功，查询表...")

        # 查询所有表
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
        """)
        tables = [row[0] for row in cursor.fetchall()]

        print(f"   📊 {db_name} 中的表: {tables}")

        if not tables:
            print(f"   ⚠️ {db_name} 中没有表")
            return None

        # 模拟表名匹配逻辑
        parent_table = None
        child_table = None
        
        print(f"\n🔍 表名匹配过程:")
        
        for table in tables:
            table_lower = table.lower()
            print(f"   检查表: {table} (小写: {table_lower})")
            
            # 匹配定额表：包含 parent 和 quota 关键词
            if ('parent' in table_lower and 'quota' in table_lower) or table_lower == 'parent_quotas':
                print(f"     ✅ 匹配为定额表: parent={table_lower.count('parent')}, quota={table_lower.count('quota')}")
                parent_table = table
            # 匹配资源表：包含 child 和 resource 关键词
            elif ('child' in table_lower and 'resource' in table_lower) or table_lower == 'child_resources':
                print(f"     ✅ 匹配为资源表: child={table_lower.count('child')}, resource={table_lower.count('resource')}")
                child_table = table
            else:
                print(f"     ❌ 不匹配")
        
        print(f"\n📋 第一轮匹配结果:")
        print(f"   定额表: {parent_table}")
        print(f"   资源表: {child_table}")
        
        # 如果没找到，尝试更宽泛的匹配
        if not parent_table:
            print(f"\n🔍 定额表第二轮匹配:")
            for table in tables:
                table_lower = table.lower()
                print(f"   检查表: {table}")
                if 'quota' in table_lower and ('parent' in table_lower or 'processed' in table_lower):
                    print(f"     ✅ 宽泛匹配为定额表")
                    parent_table = table
                    break
                else:
                    print(f"     ❌ 不匹配")

        if not child_table:
            print(f"\n🔍 资源表第二轮匹配:")
            for table in tables:
                table_lower = table.lower()
                print(f"   检查表: {table}")
                if 'resource' in table_lower and ('child' in table_lower or 'processed' in table_lower):
                    print(f"     ✅ 宽泛匹配为资源表")
                    child_table = table
                    break
                else:
                    print(f"     ❌ 不匹配")
        
        print(f"\n📋 最终匹配结果:")
        print(f"   定额表: {parent_table}")
        print(f"   资源表: {child_table}")
        
        # 如果找到表，检查数据
        if parent_table and child_table:
            print(f"\n📊 检查表数据:")
            
            # 检查定额表
            cursor.execute(f'SELECT COUNT(*) FROM "{parent_table}"')
            parent_count = cursor.fetchone()[0]
            print(f"   {parent_table}: {parent_count} 行")
            
            # 检查资源表
            cursor.execute(f'SELECT COUNT(*) FROM "{child_table}"')
            child_count = cursor.fetchone()[0]
            print(f"   {child_table}: {child_count} 行")
            
            # 显示定额表的列
            cursor.execute(f"""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = '{parent_table}'
                ORDER BY ordinal_position
            """)
            parent_columns = [row[0] for row in cursor.fetchall()]
            print(f"   {parent_table} 列: {parent_columns}")
            
            # 显示资源表的列
            cursor.execute(f"""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = '{child_table}'
                ORDER BY ordinal_position
            """)
            child_columns = [row[0] for row in cursor.fetchall()]
            print(f"   {child_table} 列: {child_columns}")
        
        if parent_table and child_table:
            print(f"   ✅ {db_name} 中找到了定额表和资源表")
            return (parent_table, child_table)
        else:
            print(f"   ❌ {db_name} 中没有找到完整的表")
            return None
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    debug_table_matching()
