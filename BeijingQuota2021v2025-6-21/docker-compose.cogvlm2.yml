version: '3.8'

services:
  cogvlm2-rtx5090d:
    build:
      context: .
      dockerfile: Dockerfile.cogvlm2-rtx5090d
    container_name: cogvlm2-rtx5090d
    ports:
      - "8765:8765"
    volumes:
      # 挂载模型缓存目录到主机，避免重复下载
      - "./docker_models:/app/models"
      - "./docker_cache:/app/cache"
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
      - CUDA_VISIBLE_DEVICES=0
      - HF_HOME=/app/cache
      - TRANSFORMERS_CACHE=/app/cache
      - MODELSCOPE_CACHE=/app/cache
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8765/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 300s
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"
