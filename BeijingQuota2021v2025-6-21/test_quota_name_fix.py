#!/usr/bin/env python3
"""
测试定额项名称完整性和输出格式修复效果
"""

import json
from src.data_processor import DataProcessor

def test_complete_quota_names():
    """测试完整的定额项名称识别"""
    
    # 模拟AI返回的完整定额项JSON数据（包含差异描述）
    mock_response = """
    {
        "quotas": [
            {
                "parent_quota": {
                    "code": "1-1",
                    "name": "人工挖一般土方 一、二类土",
                    "work_content": "挖土、余土清理、修整底边、打钉拍底等",
                    "unit": "m³"
                },
                "resource_consumption": [
                    {
                        "resource_code": "00010701",
                        "category": "人工",
                        "name": "综合用工三类",
                        "unit": "工日",
                        "consumption": "0.187"
                    },
                    {
                        "resource_code": "99030030",
                        "category": "机械",
                        "name": "电动打桩机",
                        "unit": "台班",
                        "consumption": "0.0039"
                    },
                    {
                        "resource_code": "99460004",
                        "category": "机械",
                        "name": "其他机具费 占人工费",
                        "unit": "%",
                        "consumption": "1.50"
                    }
                ]
            },
            {
                "parent_quota": {
                    "code": "1-2",
                    "name": "人工挖一般土方 三类土",
                    "work_content": "挖土、余土清理、修整底边、打钉拍底等",
                    "unit": "m³"
                },
                "resource_consumption": [
                    {
                        "resource_code": "00010701",
                        "category": "人工",
                        "name": "综合用工三类",
                        "unit": "工日",
                        "consumption": "0.274"
                    },
                    {
                        "resource_code": "99030030",
                        "category": "机械",
                        "name": "电动打桩机",
                        "unit": "台班",
                        "consumption": "0.0048"
                    },
                    {
                        "resource_code": "99460004",
                        "category": "机械",
                        "name": "其他机具费 占人工费",
                        "unit": "%",
                        "consumption": "1.50"
                    }
                ]
            },
            {
                "parent_quota": {
                    "code": "1-3",
                    "name": "人工挖一般土方 四类土",
                    "work_content": "挖土、余土清理、修整底边、打钉拍底等",
                    "unit": "m³"
                },
                "resource_consumption": [
                    {
                        "resource_code": "00010701",
                        "category": "人工",
                        "name": "综合用工三类",
                        "unit": "工日",
                        "consumption": "0.361"
                    },
                    {
                        "resource_code": "99030030",
                        "category": "机械",
                        "name": "电动打桩机",
                        "unit": "台班",
                        "consumption": "0.0056"
                    },
                    {
                        "resource_code": "99460004",
                        "category": "机械",
                        "name": "其他机具费 占人工费",
                        "unit": "%",
                        "consumption": "1.50"
                    }
                ]
            }
        ]
    }
    """
    
    print("🧪 测试定额项名称完整性和输出格式修复")
    print("=" * 60)
    
    # 创建数据处理器
    processor = DataProcessor()
    
    # 解析模拟响应
    result = processor.parse_recognition_result(mock_response, page_number=1)
    
    print(f"📊 解析结果统计:")
    print(f"- 总记录数: {len(result)}")
    
    # 统计父级和子级记录
    parent_records = [r for r in result if r["type"] == "parent"]
    child_records = [r for r in result if r["type"] == "child"]
    
    print(f"- 父级定额项: {len(parent_records)}")
    print(f"- 子级资源项: {len(child_records)}")
    
    print("\n📋 定额项名称检查:")
    print("-" * 40)
    
    for parent in parent_records:
        quota_code = parent["quota_code"]
        quota_name = parent["quota_name"]
        print(f"🏷️  {quota_code}: {quota_name}")
        
        # 检查名称是否包含差异描述
        if "类土" in quota_name:
            print(f"   ✅ 包含差异描述")
        else:
            print(f"   ❌ 缺少差异描述")
    
    # 生成CSV文件测试
    print(f"\n📄 生成CSV文件测试:")
    print("-" * 40)
    
    try:
        csv_path = processor.generate_csv(result)
        print(f"✅ CSV文件生成成功: {csv_path}")
        
        # 读取并显示CSV内容的前几行
        import pandas as pd
        df = pd.read_csv(csv_path, encoding='utf-8-sig')
        
        print(f"\n📊 CSV文件内容预览 (前10行):")
        print("-" * 50)
        print(df.head(10).to_string(index=False))
        
        # 检查是否有分离的表头
        has_quota_header = any("定额项信息" in str(row) for row in df.values)
        has_resource_header = any("资源消耗信息" in str(row) for row in df.values)
        
        print(f"\n🔍 格式检查:")
        print(f"- 包含定额项表头: {'✅' if has_quota_header else '❌'}")
        print(f"- 包含资源消耗表头: {'✅' if has_resource_header else '❌'}")
        
    except Exception as e:
        print(f"❌ CSV生成失败: {e}")
    
    # 验证期望结果
    expected_quotas = 3
    expected_resources_per_quota = 3
    
    print(f"\n✅ 验证结果:")
    print(f"- 期望定额项数: {expected_quotas}, 实际: {len(parent_records)}")
    print(f"- 期望总资源项数: {expected_quotas * expected_resources_per_quota}, 实际: {len(child_records)}")
    
    # 检查定额项名称完整性
    complete_names = sum(1 for p in parent_records if "类土" in p["quota_name"])
    print(f"- 完整定额项名称: {complete_names}/{len(parent_records)}")
    
    success = (len(parent_records) == expected_quotas and 
               len(child_records) == expected_quotas * expected_resources_per_quota and
               complete_names == expected_quotas)
    
    if success:
        print("🎉 测试通过！定额项名称和输出格式修复成功！")
    else:
        print("❌ 测试失败！需要进一步检查。")
    
    return success

if __name__ == "__main__":
    test_complete_quota_names()
