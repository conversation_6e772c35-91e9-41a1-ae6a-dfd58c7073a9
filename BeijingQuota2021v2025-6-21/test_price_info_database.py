#!/usr/bin/env python3
"""
测试信息价数据库写入功能
Test price info database writing functionality
"""

import sys
import os
import pandas as pd
import tempfile
from datetime import datetime

# 添加src目录到路径
sys.path.insert(0, 'src')

def create_test_price_info_csv():
    """创建测试用的信息价CSV文件"""
    test_data = {
        '信息价标识': ['工程造价信息 (2025年6月)', '工程造价信息 (2025年6月)', '工程造价信息 (2025年6月)'],
        '章节编号': ['01', '01', '02'],
        '章节名称': ['黑色及有色金属', '黑色及有色金属', '水泥'],
        '资源编号': ['0101010002-2', '0101010003-2', '0201010001-1'],
        '产品名称': ['热轧光圆钢筋', '热轧带肋钢筋', '普通硅酸盐水泥'],
        '规格型号及特征': ['HPB300 φ6-10', 'HRB400 φ12-25', 'P.O 42.5'],
        '计量单位': ['t', 't', 't'],
        '市场参考价（含税）': ['4200.00', '4350.00', '380.00'],
        '市场参考价（不含税）': ['3716.81', '3849.56', '336.28'],
        '备注': ['', '', '']
    }
    
    df = pd.DataFrame(test_data)
    return df

def test_mcp_database_conversion():
    """测试MCP数据库转换功能"""
    print("🧪 测试MCP数据库转换功能")
    print("=" * 50)
    
    try:
        from mcp_database_converter import MCPDatabaseConverter
        converter = MCPDatabaseConverter()
        
        # 创建临时目录和测试文件
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试CSV文件
            test_df = create_test_price_info_csv()
            csv_file = os.path.join(temp_dir, 'test_price_info.csv')
            test_df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            
            print(f"📝 创建测试CSV文件: {csv_file}")
            print(f"   数据行数: {len(test_df)}")
            print(f"   列数: {len(test_df.columns)}")
            
            # 测试SQLite转换
            print("\n📝 测试SQLite转换...")
            sqlite_db = os.path.join(temp_dir, 'price_info.db')
            success, message, stats = converter.convert_to_sqlite([csv_file], sqlite_db, "smart_merge")
            
            if success:
                print(f"   ✅ SQLite转换成功: {message}")
                print(f"   📊 统计信息: {stats}")
                
                # 验证数据库内容
                import sqlite3
                conn = sqlite3.connect(sqlite_db)
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()
                print(f"   📋 数据库表: {[table[0] for table in tables]}")
                
                for table_name, in tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    print(f"   📈 表 {table_name}: {count} 条记录")
                
                conn.close()
            else:
                print(f"   ❌ SQLite转换失败: {message}")
            
            # 测试MongoDB转换
            print("\n📝 测试MongoDB转换...")
            mongodb_json = os.path.join(temp_dir, 'price_info.json')
            success, message, stats = converter.convert_to_mongodb([csv_file], mongodb_json, "smart_merge")
            
            if success:
                print(f"   ✅ MongoDB转换成功: {message}")
                print(f"   📊 统计信息: {stats}")
                
                # 验证JSON内容
                import json
                with open(mongodb_json, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                collections = data.get('collections', {})
                print(f"   📋 集合数量: {len(collections)}")
                
                for coll_name, coll_data in collections.items():
                    docs = coll_data.get('documents', [])
                    print(f"   📈 集合 {coll_name}: {len(docs)} 个文档")
            else:
                print(f"   ❌ MongoDB转换失败: {message}")
            
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_intelligent_price_info_processor():
    """测试智能信息价处理器"""
    print("\n🧪 测试智能信息价处理器")
    print("=" * 50)
    
    try:
        from intelligent_price_info_processor import IntelligentPriceInfoProcessor
        processor = IntelligentPriceInfoProcessor()
        
        # 测试文件路径保存功能
        print("📝 测试文件路径保存功能...")
        
        # 模拟生成文件
        with tempfile.TemporaryDirectory() as temp_dir:
            test_df = create_test_price_info_csv()
            csv_file = os.path.join(temp_dir, 'test_price_info.csv')
            json_file = os.path.join(temp_dir, 'test_price_info.json')
            
            test_df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            
            import json
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump({'test': 'data'}, f)
            
            # 模拟保存文件路径
            processor.last_csv_file = csv_file
            processor.last_json_file = json_file
            
            print(f"   ✅ CSV文件路径: {processor.last_csv_file}")
            print(f"   ✅ JSON文件路径: {processor.last_json_file}")
            
            # 验证文件存在
            if os.path.exists(processor.last_csv_file):
                print("   ✅ CSV文件存在")
            else:
                print("   ❌ CSV文件不存在")
            
            if os.path.exists(processor.last_json_file):
                print("   ✅ JSON文件存在")
            else:
                print("   ❌ JSON文件不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_database_write_workflow():
    """测试完整的数据库写入工作流程"""
    print("\n🧪 测试完整的数据库写入工作流程")
    print("=" * 50)
    
    try:
        # 模拟完整的工作流程
        print("📝 模拟信息价识别 → 数据库写入流程...")
        
        # 1. 创建测试数据
        test_df = create_test_price_info_csv()
        print(f"   1️⃣ 创建测试信息价数据: {len(test_df)} 条记录")
        
        # 2. 保存为CSV文件
        with tempfile.TemporaryDirectory() as temp_dir:
            csv_file = os.path.join(temp_dir, 'price_info_result_20250628_180000.csv')
            test_df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            print(f"   2️⃣ 保存CSV文件: {csv_file}")
            
            # 3. 模拟数据库写入配置
            db_configs = [
                ("SQLite", "sqlite", "price_info.db"),
                ("MongoDB", "mongodb", "price_info.json"),
            ]
            
            for db_name, db_type, db_file in db_configs:
                print(f"\n   3️⃣ 测试 {db_name} 数据库写入...")
                
                try:
                    from mcp_database_converter import MCPDatabaseConverter
                    converter = MCPDatabaseConverter()
                    
                    output_path = os.path.join(temp_dir, db_file)
                    
                    if db_type == "sqlite":
                        success, message, stats = converter.convert_to_sqlite([csv_file], output_path, "smart_merge")
                    elif db_type == "mongodb":
                        success, message, stats = converter.convert_to_mongodb([csv_file], output_path, "smart_merge")
                    
                    if success:
                        print(f"      ✅ {db_name} 写入成功")
                        print(f"      📂 文件: {output_path}")
                        print(f"      📊 统计: {stats}")
                    else:
                        print(f"      ❌ {db_name} 写入失败: {message}")
                        
                except Exception as e:
                    print(f"      ❌ {db_name} 写入异常: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 信息价数据库写入功能测试")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("MCP数据库转换", test_mcp_database_conversion),
        ("智能信息价处理器", test_intelligent_price_info_processor),
        ("完整工作流程", test_database_write_workflow),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！信息价数据库写入功能正常。")
        print("💡 主要功能:")
        print("   - ✅ 信息价CSV数据转换为数据库格式")
        print("   - ✅ 支持SQLite和MongoDB数据库")
        print("   - ✅ 智能合并策略支持")
        print("   - ✅ 完整的工作流程集成")
        print("🌐 现在可以在Web界面中测试完整功能")
    elif passed >= total - 1:
        print("✅ 基本功能正常！可能有个别小问题。")
        print("💡 建议在Web界面中测试实际功能。")
    else:
        print("⚠️ 存在多个问题，需要进一步检查。")

if __name__ == "__main__":
    main()
