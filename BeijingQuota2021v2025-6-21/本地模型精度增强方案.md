# 本地模型精度增强方案

## 🔍 问题分析

### 对比结果分析

#### ✅ QVQ-Max（在线）- 精准识别
- **定额编号正确**: 04-01-1-22, 04-01-1-23, 04-01-1-24, 04-01-1-25
- **项目名称准确**: 人工挖石方、一般石方、沟槽石方、基坑石方
- **资源消耗完整**: 正确识别了人工、机械等资源消耗
- **数据结构清晰**: 每个定额项都有完整的资源消耗明细

#### ❌ 本地Qwen2.5-VL-7B（严重错误）
1. **定额编号错误**: 04-01-1-2 应该是 04-01-1-22
2. **资源项误识别**: 将资源消耗行错误识别为独立的定额项
3. **识别不全**: 遗漏了大量的定额项和资源消耗
4. **结构混乱**: 没有正确理解表格的上下结构关系

### 根本原因
1. **表格理解能力差**: 本地模型无法理解复杂的双层表格结构
2. **上下文关联弱**: 无法建立定额项与资源消耗的对应关系
3. **数据完整性差**: 容易遗漏或错误解释数据
4. **提示词理解有限**: 对复杂指令的理解和执行能力不足

## 🔧 增强方案实施

### 1. 超详细结构化提示词

#### A. 视觉布局指导
为本地模型提供明确的表格结构图：

```
上半部分（横向）：定额项信息
┌─────┬─────┬─────┬─────┬─────┬─────┐
│ 编号 │ 项目 │ 1-22│ 1-23│ 1-24│ 1-25│
│     │     │人工挖│一般石│沟槽石│基坑石│
│     │     │石方  │方    │方    │方    │
└─────┴─────┴─────┴─────┴─────┴─────┘

下半部分（纵向）：资源消耗明细
┌─────────┬─────────┬─────┬─────┬─────┬─────┬─────┐
│资源编号   │资源名称   │单位  │1-22 │1-23 │1-24 │1-25 │
├─────────┼─────────┼─────┼─────┼─────┼─────┼─────┤
│00010701 │综合用工三类│工日  │0.525│0.067│0.073│0.080│
│99010002 │反铲挖掘机  │台班  │-    │0.0170│0.0182│0.0200│
└─────────┴─────────┴─────┴─────┴─────┴─────┴─────┘
```

#### B. 分步骤指导
- **第一步**: 识别定额项（只有上半部分的1-22, 1-23, 1-24, 1-25）
- **第二步**: 识别资源消耗（下半部分的所有行）
- **第三步**: 建立对应关系（每个定额项对应所有资源的消耗量）

#### C. 示例输出
提供完整的JSON示例，让模型有明确的参考：

```json
{
  "quotas": [
    {
      "parent_quota": {
        "code": "1-22",
        "name": "人工挖石方",
        "work_content": "人工挖掘石方，包括挖掘、装运等",
        "unit": "m³"
      },
      "resource_consumption": [
        {
          "resource_code": "00010701",
          "category": "人工",
          "name": "综合用工三类",
          "unit": "工日",
          "consumption": "0.525"
        }
      ]
    }
  ]
}
```

### 2. 模型特定优化

#### A. Gemma模型优化
- **结构化指导**: 使用📋、🎯、⚠️等图标增强可读性
- **视觉布局**: 提供ASCII表格图示
- **关键强调**: 反复强调定额项与资源消耗的区别

#### B. OCR模型优化
- **文字识别重点**: 强调精确识别数字和符号
- **表格结构**: 明确上下部分的不同功能
- **数值精度**: 特别注意小数点和特殊符号

#### C. 通用模型优化
- **详细步骤**: 提供分步骤的分析流程
- **错误预防**: 明确指出常见错误和避免方法
- **格式要求**: 反复强调quotas格式的重要性

### 3. 数据验证和修复机制

#### A. 自动验证功能
```python
def _validate_and_fix_local_model_result(self, content: str, model_name: str) -> str:
    """验证和修复本地模型的识别结果"""
    # 1. JSON格式修复
    # 2. 数据结构验证
    # 3. 定额项修复
    # 4. 资源消耗验证
```

#### B. 常见错误修复
- **编号格式错误**: 自动修复定额编号格式
- **资源项误识别**: 检测并移除错误的定额项
- **数据缺失**: 验证必要字段的完整性
- **数值格式**: 验证消耗量的数值格式

#### C. 智能过滤
- **资源编号检查**: 过滤掉被误识别为定额编号的资源编号
- **结构验证**: 确保每个定额项都有资源消耗
- **数据完整性**: 验证必要字段的存在

### 4. 提示词层次化设计

#### 第一层：基础理解
- 表格类型识别
- 基本结构理解
- 数据类型区分

#### 第二层：详细指导
- 具体识别步骤
- 数据提取规则
- 格式要求说明

#### 第三层：错误预防
- 常见错误警告
- 正确示例展示
- 验证检查点

## 🎯 预期效果

### 识别精度提升
- **定额编号**: 从错误识别提升到正确格式
- **项目名称**: 从遗漏到完整识别
- **资源消耗**: 从混乱到结构化
- **数据完整性**: 从部分到全面

### 错误类型减少
- ✅ **编号错误**: 通过格式验证和修复
- ✅ **结构混乱**: 通过详细的结构指导
- ✅ **数据遗漏**: 通过完整性检查
- ✅ **误识别**: 通过智能过滤机制

### 处理流程优化
1. **增强提示词** → 更好的理解和执行
2. **结构化指导** → 正确的数据提取
3. **自动验证** → 错误检测和修复
4. **智能过滤** → 无效数据清理

## 🚀 实施状态

### ✅ 已完成
- **提示词优化**: 为Gemma、OCR、通用模型创建专门的提示词
- **结构化指导**: 添加ASCII表格图示和分步骤说明
- **示例展示**: 提供完整的JSON格式示例
- **数据验证**: 实现自动验证和修复机制
- **错误处理**: 添加常见错误的检测和修复

### 🔄 应用方式
- **自动选择**: 根据模型类型自动选择最适合的提示词
- **实时验证**: 在响应处理过程中自动验证和修复
- **详细日志**: 记录验证和修复过程，便于调试

## 💡 使用建议

### 1. 立即测试
- 重启项目应用优化
- 使用相同的PDF测试本地模型
- 对比优化前后的效果

### 2. 模型选择策略
- **复杂表格**: 优先使用Gemma-3-27B（已优化）
- **文字密集**: 使用nanonets-ocr-s（OCR特化）
- **通用识别**: 使用Qwen2.5-VL-7B（通用优化）

### 3. 效果评估
- **定额编号**: 检查是否正确识别1-22, 1-23等
- **项目名称**: 验证是否完整提取项目名称
- **资源消耗**: 确认是否正确建立对应关系
- **数据完整性**: 检查是否遗漏数据

## 🌟 技术亮点

### 1. 智能适配
- **模型特化**: 针对不同模型的特点进行专门优化
- **自动选择**: 根据模型名称自动选择最适合的策略
- **动态调整**: 根据识别结果动态调整处理方式

### 2. 错误恢复
- **自动修复**: 检测并自动修复常见错误
- **智能过滤**: 过滤掉明显错误的数据
- **完整性保证**: 确保输出数据的结构完整性

### 3. 可扩展性
- **新模型支持**: 易于为新的本地模型添加优化
- **规则扩展**: 可以根据新的错误类型扩展修复规则
- **性能监控**: 详细的日志记录便于性能分析

---

**🌟 通过这套增强方案，本地模型的识别精度应该得到显著提升，能够更好地处理复杂的定额表格结构，减少错误识别，提高数据完整性。**
