#!/usr/bin/env python3
"""
快速配置阿里云通义千问-QVQ模型
"""

import os
from pathlib import Path

def setup_qvq_api_key():
    """配置QVQ API密钥"""
    print("🔑 阿里云通义千问-QVQ模型配置")
    print("=" * 50)
    
    print("📋 配置步骤:")
    print("1. 访问阿里云百炼控制台: https://bailian.console.aliyun.com/")
    print("2. 开通百炼服务（如果未开通）")
    print("3. 创建API密钥")
    print("4. 复制API密钥（格式：sk-xxx）")
    print()
    
    # 检查现有配置
    env_file = Path(".env")
    existing_key = os.getenv("DASHSCOPE_API_KEY")
    
    if existing_key:
        print(f"✅ 检测到现有API密钥: {existing_key[:10]}...")
        choice = input("是否要更新API密钥？(y/n): ").lower().strip()
        if choice != 'y':
            print("保持现有配置")
            return True
    
    # 输入新的API密钥
    print("🔧 请输入您的阿里云百炼API密钥:")
    api_key = input("DASHSCOPE_API_KEY: ").strip()
    
    if not api_key:
        print("❌ 未输入API密钥")
        return False
    
    if not api_key.startswith("sk-"):
        print("⚠️ 警告: API密钥格式可能不正确（通常以sk-开头）")
        choice = input("是否继续？(y/n): ").lower().strip()
        if choice != 'y':
            return False
    
    # 读取现有.env文件内容
    env_content = []
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            env_content = f.readlines()
    
    # 更新或添加DASHSCOPE_API_KEY
    updated = False
    for i, line in enumerate(env_content):
        if line.startswith("DASHSCOPE_API_KEY="):
            env_content[i] = f"DASHSCOPE_API_KEY={api_key}\n"
            updated = True
            break
    
    if not updated:
        env_content.append(f"\n# 阿里云百炼/通义千问-QVQ模型\n")
        env_content.append(f"DASHSCOPE_API_KEY={api_key}\n")
    
    # 写入.env文件
    with open(env_file, 'w', encoding='utf-8') as f:
        f.writelines(env_content)
    
    print("✅ API密钥配置成功！")
    
    # 设置环境变量（当前会话）
    os.environ["DASHSCOPE_API_KEY"] = api_key
    
    return True

def test_qvq_connection():
    """测试QVQ连接"""
    print("\n🧪 测试QVQ模型连接...")
    
    try:
        import sys
        sys.path.insert(0, str(Path(__file__).parent / "src"))
        
        from src.ai_model_processor import AIModelProcessor
        
        processor = AIModelProcessor()
        available_models = processor.get_available_models()
        
        qvq_models = [k for k in available_models.keys() if k.startswith("qwen_qvq")]
        
        if qvq_models:
            print(f"✅ 检测到QVQ模型: {len(qvq_models)} 个")
            for model_id in qvq_models:
                print(f"   • {available_models[model_id]}")
            return True
        else:
            print("❌ 未检测到QVQ模型")
            print("请检查API密钥是否正确")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 阿里云通义千问-QVQ模型快速配置工具")
    print("=" * 60)
    
    # 配置API密钥
    if setup_qvq_api_key():
        # 测试连接
        if test_qvq_connection():
            print("\n🎉 QVQ模型配置完成！")
            print("\n📖 下一步:")
            print("1. 运行: py main.py")
            print("2. 访问: http://localhost:7862")
            print("3. 选择QVQ模型开始使用")
        else:
            print("\n⚠️ 配置可能有问题，请检查API密钥")
    else:
        print("\n❌ 配置失败")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
