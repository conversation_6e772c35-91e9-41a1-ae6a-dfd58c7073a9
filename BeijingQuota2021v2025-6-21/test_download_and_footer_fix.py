#!/usr/bin/env python3
"""
测试下载功能和页脚识别修复
Test download functionality and footer recognition fix
"""

import os
import sys
import pandas as pd
import json
import asyncio

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_footer_recognition():
    """测试页脚识别功能"""
    print("🧪 测试页脚识别功能")
    print("=" * 50)
    
    try:
        from src.intelligent_price_info_processor import IntelligentPriceInfoProcessor
        processor = IntelligentPriceInfoProcessor()
        
        # 测试用例1: 包含页脚时间的JSON格式
        test_json_with_footer = '''
        {
            "page_header": "工程造价信息",
            "page_footer": "2025年6月",
            "chapters": [
                {
                    "chapter_code": "01",
                    "chapter_name": "黑色及有色金属",
                    "remarks": "",
                    "price_items": [
                        {
                            "resource_code": "0101010002-2",
                            "product_name": "热轧光圆钢筋",
                            "specifications": "HPB300 Φ8",
                            "unit": "t",
                            "price_with_tax": "4172.00",
                            "price_without_tax": "3692.00"
                        }
                    ]
                }
            ]
        }
        '''
        
        result = processor._parse_price_info_result(test_json_with_footer, 1)
        
        if result:
            print(f"✅ JSON页脚解析成功，章节数: {len(result)}")
            chapter = result[0]
            print(f"   页眉: {chapter.get('page_header', '')}")
            print(f"   页脚: {chapter.get('page_footer', '')}")
            print(f"   价格条目数: {len(chapter.get('price_items', []))}")
        else:
            print("❌ JSON页脚解析失败")
            return False
        
        # 测试用例2: 文本格式包含页脚时间
        test_text_with_footer = """
        工程造价信息
        1. 黑色及有色金属（编码：01）
        0101010002-2 热轧光圆钢筋 HPB300 Φ8 t 4172.00 3692.00
        
        2025年6月
        """
        
        result2 = processor._parse_price_info_result(test_text_with_footer, 2)
        
        if result2:
            print(f"✅ 文本页脚解析成功，章节数: {len(result2)}")
            chapter2 = result2[0]
            print(f"   页眉: {chapter2.get('page_header', '')}")
            print(f"   页脚: {chapter2.get('page_footer', '')}")
            print(f"   价格条目数: {len(chapter2.get('price_items', []))}")
        else:
            print("❌ 文本页脚解析失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_csv_output_format():
    """测试CSV输出格式"""
    print("\n🧪 测试CSV输出格式")
    print("=" * 50)
    
    try:
        from src.intelligent_price_info_processor import IntelligentPriceInfoProcessor
        processor = IntelligentPriceInfoProcessor()
        
        # 创建包含页脚信息的测试数据
        test_data = [
            {
                'page_number': 1,
                'page_header': '工程造价信息',
                'page_footer': '2025年6月',
                'chapter_code': '01',
                'chapter_name': '黑色及有色金属',
                'remarks': '测试数据',
                'price_items': [
                    {
                        'resource_code': '0101010002-2',
                        'product_name': '热轧光圆钢筋',
                        'specifications': 'HPB300 Φ8',
                        'unit': 't',
                        'price_with_tax': '4172.00',
                        'price_without_tax': '3692.00'
                    },
                    {
                        'resource_code': '0101010003-2',
                        'product_name': '热轧光圆钢筋',
                        'specifications': 'HPB300 Φ10',
                        'unit': 't',
                        'price_with_tax': '4096.00',
                        'price_without_tax': '3625.00'
                    }
                ]
            }
        ]
        
        # 使用临时目录
        import tempfile
        with tempfile.TemporaryDirectory() as temp_dir:
            # 异步生成文件
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                output_files = loop.run_until_complete(
                    processor._generate_output_files(test_data, temp_dir)
                )
                
                if output_files:
                    csv_files = [f for f in output_files if f.endswith('.csv')]
                    if csv_files:
                        # 读取CSV文件检查格式
                        df = pd.read_csv(csv_files[0], encoding='utf-8-sig')
                        print(f"✅ CSV文件生成成功")
                        print(f"   数据行数: {len(df)}")
                        print(f"   列数: {len(df.columns)}")
                        print(f"   列名: {list(df.columns)}")
                        
                        # 检查第一列是否包含页脚信息
                        if '信息价标识' in df.columns:
                            first_value = df['信息价标识'].iloc[0]
                            print(f"   第一列内容: {first_value}")
                            
                            if '2025年6月' in first_value:
                                print("✅ 页脚信息已正确整合到第一列")
                            else:
                                print("❌ 页脚信息未整合到第一列")
                                return False
                        else:
                            print("❌ 未找到'信息价标识'列")
                            return False
                        
                        return True
                    else:
                        print("❌ 未生成CSV文件")
                        return False
                else:
                    print("❌ 未生成任何文件")
                    return False
                    
            finally:
                loop.close()
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_gradio_file_components():
    """测试Gradio文件组件"""
    print("\n🧪 测试Gradio文件组件")
    print("=" * 50)
    
    try:
        from src.intelligent_price_info_interface import IntelligentPriceInfoInterface
        interface = IntelligentPriceInfoInterface()
        
        # 创建界面组件
        components = interface.create_price_info_recognition_interface()
        
        # 检查下载组件
        download_components = [
            'download_csv',
            'download_json',
            'download_output'
        ]
        
        for comp in download_components:
            if comp in components:
                print(f"✅ {comp}: 存在")
                
                # 检查组件属性
                component = components[comp]
                if hasattr(component, 'visible'):
                    print(f"   visible属性: {getattr(component, 'visible', 'N/A')}")
                if hasattr(component, 'interactive'):
                    print(f"   interactive属性: {getattr(component, 'interactive', 'N/A')}")
            else:
                print(f"❌ {comp}: 缺失")
                return False
        
        print("✅ 所有Gradio文件组件都正确配置")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_return_value_format():
    """测试返回值格式"""
    print("\n🧪 测试返回值格式")
    print("=" * 50)
    
    try:
        from src.intelligent_price_info_interface import IntelligentPriceInfoInterface
        import gradio as gr
        
        interface = IntelligentPriceInfoInterface()
        
        # 模拟成功处理的返回值
        test_csv_path = "test.csv"
        test_json_path = "test.json"
        
        # 创建临时测试文件
        import tempfile
        with tempfile.NamedTemporaryFile(suffix='.csv', delete=False) as csv_file:
            csv_file.write(b"test,data\n1,2\n")
            test_csv_path = csv_file.name
        
        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as json_file:
            json_file.write(b'{"test": "data"}')
            test_json_path = json_file.name
        
        try:
            # 测试返回值格式
            result = (
                "<p style='color: green;'>✅ 测试成功</p>",
                "<p>统计信息</p>",
                pd.DataFrame({'test': [1, 2]}),
                gr.update(value=test_csv_path, visible=True),
                gr.update(value=test_json_path, visible=True),
                "<p>下载状态</p>"
            )
            
            print(f"✅ 返回值格式正确，包含 {len(result)} 个元素")
            print(f"   状态信息: 字符串")
            print(f"   统计信息: 字符串")
            print(f"   预览数据: DataFrame ({len(result[2])} 行)")
            print(f"   CSV文件: Gradio更新对象")
            print(f"   JSON文件: Gradio更新对象")
            print(f"   下载状态: 字符串")
            
            return True
            
        finally:
            # 清理临时文件
            try:
                os.unlink(test_csv_path)
                os.unlink(test_json_path)
            except:
                pass
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_config_prompt_update():
    """测试配置提示词更新"""
    print("\n🧪 测试配置提示词更新")
    print("=" * 50)
    
    try:
        from src.config import Config
        config = Config()
        
        prompt = config.PRICE_INFO_EXTRACTION_PROMPT
        
        # 检查是否包含页脚识别要求
        checks = [
            ('页脚识别步骤', '页脚时间信息'),
            ('时间格式示例', '2025年6月'),
            ('JSON格式示例', 'page_footer'),
            ('重要性说明', '非常重要'),
        ]
        
        results = []
        for check_name, pattern in checks:
            if pattern in prompt:
                print(f"✅ {check_name}: 已包含")
                results.append(True)
            else:
                print(f"❌ {check_name}: 未包含")
                results.append(False)
        
        return all(results)
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 下载功能和页脚识别修复测试")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("页脚识别功能", test_footer_recognition),
        ("CSV输出格式", test_csv_output_format),
        ("Gradio文件组件", test_gradio_file_components),
        ("返回值格式", test_return_value_format),
        ("配置提示词更新", test_config_prompt_update),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！下载功能和页脚识别修复成功。")
        print("💡 主要改进:")
        print("   - ✅ 修复了文件下载链接问题")
        print("   - ✅ 增加了页脚时间信息识别")
        print("   - ✅ 页眉和页脚信息整合到第一列")
        print("   - ✅ 使用Gradio原生文件组件")
        print("🌐 重新启动程序后可以测试完整功能")
    elif passed >= total - 1:
        print("✅ 基本功能正常！可能有个别小问题。")
        print("💡 建议重新启动程序测试实际功能。")
    else:
        print("⚠️ 存在多个问题，需要进一步检查。")

if __name__ == "__main__":
    main()
