#!/usr/bin/env python3
"""
测试模型连接测试按钮功能
Test model connection test button functionality
"""

import os
import sys
import asyncio

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_ai_processor_connection():
    """测试AI处理器连接测试功能"""
    print("🧪 测试AI处理器连接测试功能")
    print("=" * 50)
    
    try:
        from src.ai_model_processor import AIModelProcessor
        processor = AIModelProcessor()
        print("✅ AI处理器导入成功")
        
        # 检查是否有test_model_connection方法
        if hasattr(processor, 'test_model_connection'):
            print("✅ test_model_connection 方法存在")
        else:
            print("❌ test_model_connection 方法不存在")
            return False
        
        return True, processor
        
    except Exception as e:
        print(f"❌ AI处理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, None

def test_price_info_processor_connection():
    """测试信息价处理器连接测试功能"""
    print("\n🧪 测试信息价处理器连接测试功能")
    print("=" * 50)
    
    try:
        from src.intelligent_price_info_processor import IntelligentPriceInfoProcessor
        processor = IntelligentPriceInfoProcessor()
        print("✅ 信息价处理器导入成功")
        
        # 检查是否有test_model_connection方法
        if hasattr(processor, 'test_model_connection'):
            print("✅ test_model_connection 方法存在")
        else:
            print("❌ test_model_connection 方法不存在")
            return False
        
        return True, processor
        
    except Exception as e:
        print(f"❌ 信息价处理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, None

async def test_lm_studio_connection(processor):
    """测试LM Studio连接"""
    print("\n🤖 测试LM Studio连接")
    print("=" * 40)
    
    try:
        success, message = await processor.test_model_connection("lm_studio_qwen2_5_vl_7b")
        
        if success:
            print(f"✅ LM Studio连接测试成功")
            print(f"📋 结果: {message}")
        else:
            print(f"❌ LM Studio连接测试失败")
            print(f"📋 错误: {message}")
        
        return success
        
    except Exception as e:
        print(f"❌ LM Studio连接测试异常: {str(e)}")
        return False

async def test_qwen_connection(processor):
    """测试阿里云QVQ连接"""
    print("\n🌟 测试阿里云QVQ连接")
    print("=" * 40)
    
    try:
        success, message = await processor.test_model_connection("qwen_qvq_max")
        
        if success:
            print(f"✅ 阿里云QVQ连接测试成功")
            print(f"📋 结果: {message}")
        else:
            print(f"❌ 阿里云QVQ连接测试失败")
            print(f"📋 错误: {message}")
        
        return success
        
    except Exception as e:
        print(f"❌ 阿里云QVQ连接测试异常: {str(e)}")
        return False

def test_interface_integration():
    """测试界面集成"""
    print("\n🖥️ 测试界面集成")
    print("=" * 40)
    
    try:
        # 检查main.py中是否包含测试按钮
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ('定额识别测试按钮', 'test_quota_model_btn'),
            ('定额识别测试函数', 'test_quota_model_connection'),
            ('定额识别测试事件', 'test_quota_model_btn.click'),
        ]
        
        results = []
        for check_name, pattern in checks:
            if pattern in content:
                print(f"✅ {check_name}: 已集成")
                results.append(True)
            else:
                print(f"❌ {check_name}: 未集成")
                results.append(False)
        
        # 检查信息价识别界面
        try:
            from src.intelligent_price_info_interface import IntelligentPriceInfoInterface
            interface = IntelligentPriceInfoInterface()
            
            if hasattr(interface, 'test_model_connection'):
                print("✅ 信息价识别测试方法: 已集成")
                results.append(True)
            else:
                print("❌ 信息价识别测试方法: 未集成")
                results.append(False)
                
        except Exception as e:
            print(f"❌ 信息价识别界面检查失败: {str(e)}")
            results.append(False)
        
        return all(results)
        
    except Exception as e:
        print(f"❌ 界面集成测试失败: {str(e)}")
        return False

def test_ui_components():
    """测试UI组件"""
    print("\n🎨 测试UI组件")
    print("=" * 40)
    
    try:
        # 检查是否能正确创建界面组件
        from src.intelligent_price_info_interface import IntelligentPriceInfoInterface
        interface = IntelligentPriceInfoInterface()
        
        components = interface.create_price_info_recognition_interface()
        
        # 检查关键组件
        key_components = [
            'test_model_btn',
            'model_test_output',
            'model_type',
            'process_btn'
        ]
        
        missing_components = []
        for comp in key_components:
            if comp in components:
                print(f"✅ {comp}: 存在")
            else:
                print(f"❌ {comp}: 缺失")
                missing_components.append(comp)
        
        if not missing_components:
            print("✅ 所有关键UI组件都存在")
            return True
        else:
            print(f"❌ 缺失组件: {missing_components}")
            return False
        
    except Exception as e:
        print(f"❌ UI组件测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def run_connection_tests():
    """运行连接测试"""
    print("\n🔗 运行连接测试")
    print("=" * 50)
    
    # 测试AI处理器
    ai_success, ai_processor = test_ai_processor_connection()
    if not ai_success:
        return False
    
    # 测试信息价处理器
    price_success, price_processor = test_price_info_processor_connection()
    if not price_success:
        return False
    
    # 运行实际连接测试
    test_results = []
    
    # 测试LM Studio (AI处理器)
    print(f"\n{'='*20} AI处理器 - LM Studio {'='*20}")
    lm_result_ai = await test_lm_studio_connection(ai_processor)
    test_results.append(("AI处理器-LM Studio", lm_result_ai))
    
    # 测试LM Studio (信息价处理器)
    print(f"\n{'='*20} 信息价处理器 - LM Studio {'='*20}")
    lm_result_price = await test_lm_studio_connection(price_processor)
    test_results.append(("信息价处理器-LM Studio", lm_result_price))
    
    # 测试阿里云QVQ (AI处理器)
    print(f"\n{'='*20} AI处理器 - 阿里云QVQ {'='*20}")
    qvq_result_ai = await test_qwen_connection(ai_processor)
    test_results.append(("AI处理器-阿里云QVQ", qvq_result_ai))
    
    # 测试阿里云QVQ (信息价处理器)
    print(f"\n{'='*20} 信息价处理器 - 阿里云QVQ {'='*20}")
    qvq_result_price = await test_qwen_connection(price_processor)
    test_results.append(("信息价处理器-阿里云QVQ", qvq_result_price))
    
    return test_results

def main():
    """主测试函数"""
    print("🚀 模型连接测试按钮功能验证")
    print("=" * 60)
    
    # 运行基础测试
    basic_tests = [
        ("界面集成", test_interface_integration),
        ("UI组件", test_ui_components),
    ]
    
    results = []
    for test_name, test_func in basic_tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 运行连接测试
    print(f"\n{'='*20} 连接测试 {'='*20}")
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        connection_results = loop.run_until_complete(run_connection_tests())
        
        if connection_results:
            for test_name, result in connection_results:
                results.append((test_name, result))
        else:
            results.append(("连接测试", False))
            
    except Exception as e:
        print(f"❌ 连接测试异常: {str(e)}")
        results.append(("连接测试", False))
    finally:
        loop.close()
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed >= total - 1:  # 允许一个测试失败（可能是网络问题）
        print("🎉 模型连接测试按钮功能基本正常！")
        print("💡 现在可以在Web界面中使用模型连接测试功能：")
        print("   1. 定额识别: 在AI模型配置区域点击'🔧 测试连接'")
        print("   2. 信息价识别: 在模型选择区域点击'🔧 测试连接'")
        print("🌐 访问 http://localhost:7864 查看界面")
    else:
        print("⚠️ 存在多个问题，需要进一步检查。")

if __name__ == "__main__":
    main()
