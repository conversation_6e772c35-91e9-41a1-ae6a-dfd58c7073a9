#!/usr/bin/env python3
"""
PDF存储管理器
负责管理上传的PDF文件存储、组织和检索
"""

import os
import shutil
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import hashlib

class PDFStorageManager:
    """PDF存储管理器"""
    
    def __init__(self):
        # 创建存储目录结构
        self.base_dir = Path("stored_pdfs")
        self.quota_dir = self.base_dir / "quota_pdfs"
        self.price_dir = self.base_dir / "price_pdfs"
        self.metadata_file = self.base_dir / "pdf_metadata.json"
        
        # 确保目录存在
        self._ensure_directories()
        
        # 加载元数据
        self.metadata = self._load_metadata()
    
    def _ensure_directories(self):
        """确保存储目录存在"""
        self.base_dir.mkdir(exist_ok=True)
        self.quota_dir.mkdir(exist_ok=True)
        self.price_dir.mkdir(exist_ok=True)
    
    def _load_metadata(self) -> Dict:
        """加载PDF元数据"""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception:
                pass
        
        return {
            "quota_pdfs": {},
            "price_pdfs": {},
            "upload_history": []
        }
    
    def _save_metadata(self):
        """保存PDF元数据"""
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存元数据失败: {e}")
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件MD5哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return ""
    
    def _get_file_info(self, file_path: str) -> Dict:
        """获取文件基本信息"""
        try:
            stat = os.stat(file_path)
            return {
                "size": stat.st_size,
                "size_mb": round(stat.st_size / (1024 * 1024), 2),
                "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat()
            }
        except Exception:
            return {"size": 0, "size_mb": 0, "modified_time": ""}
    
    def store_quota_pdf(self, source_path: str, original_name: str = None) -> Tuple[bool, str, Dict]:
        """
        存储定额PDF文件
        
        Args:
            source_path: 源文件路径
            original_name: 原始文件名
            
        Returns:
            Tuple[bool, str, Dict]: (成功标志, 存储路径, 文件信息)
        """
        try:
            if not os.path.exists(source_path):
                return False, "", {"error": "源文件不存在"}
            
            # 获取原始文件名
            if not original_name:
                original_name = os.path.basename(source_path)
            
            # 计算文件哈希
            file_hash = self._calculate_file_hash(source_path)
            
            # 检查是否已存在相同文件
            for stored_id, info in self.metadata["quota_pdfs"].items():
                if info.get("hash") == file_hash:
                    return True, info["stored_path"], {
                        "message": "文件已存在",
                        "existing_id": stored_id,
                        **info
                    }
            
            # 生成唯一文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_id = f"quota_{timestamp}_{file_hash[:8]}"
            stored_name = f"{file_id}.pdf"
            stored_path = self.quota_dir / stored_name
            
            # 复制文件
            shutil.copy2(source_path, stored_path)
            
            # 获取文件信息
            file_info = self._get_file_info(stored_path)
            
            # 保存元数据
            metadata_entry = {
                "id": file_id,
                "original_name": original_name,
                "stored_name": stored_name,
                "stored_path": str(stored_path),
                "hash": file_hash,
                "upload_time": datetime.now().isoformat(),
                "type": "quota",
                **file_info
            }
            
            self.metadata["quota_pdfs"][file_id] = metadata_entry
            self.metadata["upload_history"].append({
                "id": file_id,
                "type": "quota",
                "name": original_name,
                "time": metadata_entry["upload_time"]
            })
            
            self._save_metadata()
            
            return True, str(stored_path), metadata_entry
            
        except Exception as e:
            return False, "", {"error": f"存储失败: {str(e)}"}
    
    def store_price_pdf(self, source_path: str, original_name: str = None) -> Tuple[bool, str, Dict]:
        """
        存储信息价PDF文件
        
        Args:
            source_path: 源文件路径
            original_name: 原始文件名
            
        Returns:
            Tuple[bool, str, Dict]: (成功标志, 存储路径, 文件信息)
        """
        try:
            if not os.path.exists(source_path):
                return False, "", {"error": "源文件不存在"}
            
            # 获取原始文件名
            if not original_name:
                original_name = os.path.basename(source_path)
            
            # 计算文件哈希
            file_hash = self._calculate_file_hash(source_path)
            
            # 检查是否已存在相同文件
            for stored_id, info in self.metadata["price_pdfs"].items():
                if info.get("hash") == file_hash:
                    return True, info["stored_path"], {
                        "message": "文件已存在",
                        "existing_id": stored_id,
                        **info
                    }
            
            # 生成唯一文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_id = f"price_{timestamp}_{file_hash[:8]}"
            stored_name = f"{file_id}.pdf"
            stored_path = self.price_dir / stored_name
            
            # 复制文件
            shutil.copy2(source_path, stored_path)
            
            # 获取文件信息
            file_info = self._get_file_info(stored_path)
            
            # 保存元数据
            metadata_entry = {
                "id": file_id,
                "original_name": original_name,
                "stored_name": stored_name,
                "stored_path": str(stored_path),
                "hash": file_hash,
                "upload_time": datetime.now().isoformat(),
                "type": "price",
                **file_info
            }
            
            self.metadata["price_pdfs"][file_id] = metadata_entry
            self.metadata["upload_history"].append({
                "id": file_id,
                "type": "price",
                "name": original_name,
                "time": metadata_entry["upload_time"]
            })
            
            self._save_metadata()
            
            return True, str(stored_path), metadata_entry
            
        except Exception as e:
            return False, "", {"error": f"存储失败: {str(e)}"}
    
    def get_stored_pdfs(self, pdf_type: str = "all") -> List[Dict]:
        """
        获取已存储的PDF列表
        
        Args:
            pdf_type: PDF类型 ("quota", "price", "all")
            
        Returns:
            List[Dict]: PDF信息列表
        """
        result = []
        
        if pdf_type in ["quota", "all"]:
            for info in self.metadata["quota_pdfs"].values():
                result.append(info)
        
        if pdf_type in ["price", "all"]:
            for info in self.metadata["price_pdfs"].values():
                result.append(info)
        
        # 按上传时间倒序排列
        result.sort(key=lambda x: x.get("upload_time", ""), reverse=True)
        return result
    
    def get_pdf_info(self, file_id: str) -> Optional[Dict]:
        """获取指定PDF的信息"""
        # 在定额PDF中查找
        if file_id in self.metadata["quota_pdfs"]:
            return self.metadata["quota_pdfs"][file_id]
        
        # 在信息价PDF中查找
        if file_id in self.metadata["price_pdfs"]:
            return self.metadata["price_pdfs"][file_id]
        
        return None
    
    def delete_pdf(self, file_id: str) -> Tuple[bool, str]:
        """
        删除存储的PDF文件
        
        Args:
            file_id: 文件ID
            
        Returns:
            Tuple[bool, str]: (成功标志, 消息)
        """
        try:
            # 查找文件信息
            pdf_info = self.get_pdf_info(file_id)
            if not pdf_info:
                return False, "文件不存在"
            
            # 删除物理文件
            stored_path = Path(pdf_info["stored_path"])
            if stored_path.exists():
                stored_path.unlink()
            
            # 从元数据中删除
            if pdf_info["type"] == "quota":
                del self.metadata["quota_pdfs"][file_id]
            else:
                del self.metadata["price_pdfs"][file_id]
            
            # 从历史记录中删除
            self.metadata["upload_history"] = [
                item for item in self.metadata["upload_history"] 
                if item["id"] != file_id
            ]
            
            self._save_metadata()
            
            return True, "删除成功"
            
        except Exception as e:
            return False, f"删除失败: {str(e)}"
    
    def get_storage_stats(self) -> Dict:
        """获取存储统计信息"""
        quota_count = len(self.metadata["quota_pdfs"])
        price_count = len(self.metadata["price_pdfs"])
        
        # 计算总大小
        total_size = 0
        for info in self.metadata["quota_pdfs"].values():
            total_size += info.get("size", 0)
        for info in self.metadata["price_pdfs"].values():
            total_size += info.get("size", 0)
        
        return {
            "quota_count": quota_count,
            "price_count": price_count,
            "total_count": quota_count + price_count,
            "total_size": total_size,
            "total_size_mb": round(total_size / (1024 * 1024), 2),
            "quota_dir": str(self.quota_dir),
            "price_dir": str(self.price_dir)
        }
    
    def cleanup_orphaned_files(self) -> Tuple[int, List[str]]:
        """清理孤立文件（存在于文件系统但不在元数据中的文件）"""
        orphaned_files = []
        
        # 检查定额PDF目录
        if self.quota_dir.exists():
            for file_path in self.quota_dir.glob("*.pdf"):
                file_name = file_path.name
                found = False
                for info in self.metadata["quota_pdfs"].values():
                    if info["stored_name"] == file_name:
                        found = True
                        break
                if not found:
                    orphaned_files.append(str(file_path))
                    file_path.unlink()  # 删除孤立文件
        
        # 检查信息价PDF目录
        if self.price_dir.exists():
            for file_path in self.price_dir.glob("*.pdf"):
                file_name = file_path.name
                found = False
                for info in self.metadata["price_pdfs"].values():
                    if info["stored_name"] == file_name:
                        found = True
                        break
                if not found:
                    orphaned_files.append(str(file_path))
                    file_path.unlink()  # 删除孤立文件
        
        return len(orphaned_files), orphaned_files
