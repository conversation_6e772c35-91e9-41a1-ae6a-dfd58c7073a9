#!/usr/bin/env python3
"""
PDF处理模块 - MCP服务器1
负责PDF文件处理和表格图片提取
"""

import asyncio
import os
import uuid
from pathlib import Path
from typing import List, Optional
import tempfile

from pdf2image import convert_from_path
import os
from PIL import Image
import cv2
import numpy as np

from .config import Config

class PDFProcessor:
    """PDF处理器"""
    
    def __init__(self):
        self.config = Config()
        # 设置Poppler路径
        self.poppler_path = r"C:\poppler\Library\bin"
        if os.path.exists(self.poppler_path):
            # 临时添加到PATH
            current_path = os.environ.get('PATH', '')
            if self.poppler_path not in current_path:
                os.environ['PATH'] = current_path + os.pathsep + self.poppler_path
    
    async def extract_pages_as_images(
        self, 
        pdf_path: str, 
        start_page: int, 
        end_page: int
    ) -> List[str]:
        """
        将PDF指定页面转换为图片
        
        Args:
            pdf_path: PDF文件路径
            start_page: 起始页码 (1-based)
            end_page: 结束页码 (1-based)
            
        Returns:
            List[str]: 图片文件路径列表
        """
        try:
            # 转换PDF页面为图片
            images = convert_from_path(
                pdf_path,
                dpi=self.config.PDF_DPI,
                first_page=start_page,
                last_page=end_page,
                fmt=self.config.IMAGE_FORMAT.lower(),
                poppler_path=self.poppler_path if os.path.exists(self.poppler_path) else None
            )
            
            image_paths = []
            
            for i, image in enumerate(images):
                # 生成唯一文件名
                filename = f"page_{start_page + i}_{uuid.uuid4().hex[:8]}.png"
                image_path = self.config.get_temp_file_path(filename)
                
                # 保存图片
                image.save(image_path, self.config.IMAGE_FORMAT)
                image_paths.append(str(image_path))
            
            return image_paths
            
        except Exception as e:
            raise Exception(f"PDF转换失败: {str(e)}")
    
    async def detect_table_regions(self, image_path: str) -> List[tuple]:
        """
        检测图片中的表格区域
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            List[tuple]: 表格区域坐标列表 [(x, y, w, h), ...]
        """
        try:
            # 读取图片
            image = cv2.imread(image_path)
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 二值化
            _, binary = cv2.threshold(gray, 128, 255, cv2.THRESH_BINARY_INV)
            
            # 检测水平线
            horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (40, 1))
            horizontal_lines = cv2.morphologyEx(binary, cv2.MORPH_OPEN, horizontal_kernel)
            
            # 检测垂直线
            vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 40))
            vertical_lines = cv2.morphologyEx(binary, cv2.MORPH_OPEN, vertical_kernel)
            
            # 合并线条
            table_mask = cv2.addWeighted(horizontal_lines, 0.5, vertical_lines, 0.5, 0.0)
            
            # 查找轮廓
            contours, _ = cv2.findContours(table_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 筛选表格区域
            table_regions = []
            for contour in contours:
                x, y, w, h = cv2.boundingRect(contour)
                # 过滤太小的区域
                if w > 100 and h > 50:
                    table_regions.append((x, y, w, h))
            
            return table_regions
            
        except Exception as e:
            print(f"表格检测失败: {str(e)}")
            # 如果检测失败，返回整个图片区域
            image = cv2.imread(image_path)
            h, w = image.shape[:2]
            return [(0, 0, w, h)]
    
    async def extract_table_images(
        self, 
        image_path: str, 
        table_regions: List[tuple]
    ) -> List[str]:
        """
        从图片中提取表格区域并保存为单独的图片
        
        Args:
            image_path: 原始图片路径
            table_regions: 表格区域坐标列表
            
        Returns:
            List[str]: 表格图片路径列表
        """
        try:
            image = cv2.imread(image_path)
            table_image_paths = []
            
            for i, (x, y, w, h) in enumerate(table_regions):
                # 提取表格区域
                table_image = image[y:y+h, x:x+w]
                
                # 生成文件名
                base_name = Path(image_path).stem
                filename = f"{base_name}_table_{i}_{uuid.uuid4().hex[:8]}.png"
                table_path = self.config.get_temp_file_path(filename)
                
                # 保存表格图片
                cv2.imwrite(str(table_path), table_image)
                table_image_paths.append(str(table_path))
            
            return table_image_paths
            
        except Exception as e:
            raise Exception(f"表格提取失败: {str(e)}")
    
    async def preprocess_image(self, image_path: str) -> str:
        """
        预处理图片以提高识别准确率
        
        Args:
            image_path: 图片路径
            
        Returns:
            str: 处理后的图片路径
        """
        try:
            # 读取图片
            image = cv2.imread(image_path)
            
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 降噪
            denoised = cv2.fastNlMeansDenoising(gray)
            
            # 增强对比度
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(denoised)
            
            # 锐化
            kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
            sharpened = cv2.filter2D(enhanced, -1, kernel)
            
            # 生成处理后的文件名
            base_name = Path(image_path).stem
            filename = f"{base_name}_processed_{uuid.uuid4().hex[:8]}.png"
            processed_path = self.config.get_temp_file_path(filename)
            
            # 保存处理后的图片
            cv2.imwrite(str(processed_path), sharpened)
            
            return str(processed_path)
            
        except Exception as e:
            print(f"图片预处理失败: {str(e)}")
            # 如果预处理失败，返回原图片路径
            return image_path
