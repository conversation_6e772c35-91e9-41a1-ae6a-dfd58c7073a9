#!/usr/bin/env python3
"""
高级定额管理事件处理器
Advanced Quota Management Event Handlers
"""

import gradio as gr
import pandas as pd
from typing import List, Dict, Any, Tuple, Optional

try:
    from .advanced_quota_manager import AdvancedQuotaManager
except ImportError:
    try:
        from advanced_quota_manager import AdvancedQuotaManager
    except ImportError:
        print("警告: 无法导入AdvancedQuotaManager")
        AdvancedQuotaManager = None

class AdvancedQuotaHandlers:
    """高级定额管理事件处理器类"""
    
    def __init__(self):
        if AdvancedQuotaManager:
            self.manager = AdvancedQuotaManager()
            print("🔧 AdvancedQuotaHandlers 初始化完成，管理器已创建")
        else:
            self.manager = None
            print("❌ AdvancedQuotaHandlers 初始化失败，无法创建管理器")
        self.current_table = None
        self.current_page = 0
        self.page_size = 50

    def update_db_config_visibility(self, db_type: str):
        """根据数据库类型更新配置界面可见性"""
        if db_type == "sqlite":
            return (
                gr.update(visible=True),   # sqlite_config
                gr.update(visible=False),  # other_db_config
                gr.update(value=5432),     # 重置端口为PostgreSQL默认值
            )
        elif db_type == "postgresql":
            return (
                gr.update(visible=False),  # sqlite_config
                gr.update(visible=True),   # other_db_config
                gr.update(value=5432),     # PostgreSQL端口
            )
        elif db_type == "mysql":
            return (
                gr.update(visible=False),  # sqlite_config
                gr.update(visible=True),   # other_db_config
                gr.update(value=3306),     # MySQL端口
            )
        elif db_type == "mongodb":
            return (
                gr.update(visible=False),  # sqlite_config
                gr.update(visible=True),   # other_db_config
                gr.update(value=27017),    # MongoDB端口
            )
        elif db_type == "sql_server":
            return (
                gr.update(visible=False),  # sqlite_config
                gr.update(visible=True),   # other_db_config
                gr.update(value=1433),     # SQL Server端口
            )
        elif db_type == "oracle":
            return (
                gr.update(visible=False),  # sqlite_config
                gr.update(visible=True),   # other_db_config
                gr.update(value=1521),     # Oracle端口
            )
        else:
            return (
                gr.update(visible=True),   # sqlite_config
                gr.update(visible=False),  # other_db_config
                gr.update(value=5432),     # 默认端口
            )

    def test_database_connection(self, db_type: str, db_path: str, host: str, port: float, 
                               database: str, username: str, password: str):
        """测试数据库连接"""
        try:
            if not self.manager:
                return "<p style='color: red;'>❌ 管理器未初始化</p>"
            
            # 构建配置
            if db_type == "sqlite":
                config = {"database_path": db_path}
            else:
                config = {
                    "host": host,
                    "port": int(port) if port else 5432,
                    "database": database,
                    "username": username,
                    "password": password
                }
            
            success, message = self.manager.test_database_connection(db_type, config)
            
            if success:
                return f"<p style='color: green;'>{message}</p>"
            else:
                return f"<p style='color: red;'>{message}</p>"
                
        except Exception as e:
            return f"<p style='color: red;'>❌ 测试连接失败: {str(e)}</p>"

    def connect_to_database(self, db_type: str, db_path: str, host: str, port: float, 
                          database: str, username: str, password: str):
        """连接到数据库"""
        try:
            if not self.manager:
                return (
                    "<p style='color: red;'>❌ 管理器未初始化</p>",
                    gr.update(visible=False),  # disconnect_btn
                    gr.update(visible=True),   # connect_btn
                    gr.update(choices=[]),     # table_selector
                    gr.update(visible=False),  # schema components
                    gr.update(visible=False),  # table_data_display
                    gr.update(visible=False),  # pagination_controls
                    gr.update(visible=False)   # data_operation_btns
                )
            
            # 构建配置
            if db_type == "sqlite":
                config = {"database_path": db_path}
            else:
                config = {
                    "host": host,
                    "port": int(port) if port else 5432,
                    "database": database,
                    "username": username,
                    "password": password
                }
            
            success, message = self.manager.connect_to_database(db_type, config)

            if success:
                # 连接成功后，自动更新配置文件
                self._update_config_file(db_type, host, port, database, username, password, db_path)
                # 获取数据库结构
                schema_success, schema_msg, schema_info = self.manager.get_database_schema()
                
                if schema_success:
                    table_choices = [(f"📋 {table['name']} ({table['record_count']} 条记录)", table['name']) 
                                   for table in schema_info['tables']]
                    
                    return (
                        f"<p style='color: green;'>{message}</p>",
                        gr.update(visible=True),   # disconnect_btn
                        gr.update(visible=False),  # connect_btn
                        gr.update(choices=table_choices, visible=True),  # table_selector
                        gr.update(value=schema_info, visible=True),      # db_schema_tree
                        gr.update(visible=False),  # table_data_display
                        gr.update(visible=False),  # pagination_controls
                        gr.update(visible=False)   # data_operation_btns
                    )
                else:
                    return (
                        f"<p style='color: orange;'>{message}<br/>⚠️ {schema_msg}</p>",
                        gr.update(visible=True),   # disconnect_btn
                        gr.update(visible=False),  # connect_btn
                        gr.update(choices=[], visible=True),  # table_selector
                        gr.update(visible=False),  # db_schema_tree
                        gr.update(visible=False),  # table_data_display
                        gr.update(visible=False),  # pagination_controls
                        gr.update(visible=False)   # data_operation_btns
                    )
            else:
                return (
                    f"<p style='color: red;'>{message}</p>",
                    gr.update(visible=False),  # disconnect_btn
                    gr.update(visible=True),   # connect_btn
                    gr.update(choices=[]),     # table_selector
                    gr.update(visible=False),  # db_schema_tree
                    gr.update(visible=False),  # table_data_display
                    gr.update(visible=False),  # pagination_controls
                    gr.update(visible=False)   # data_operation_btns
                )
                
        except Exception as e:
            return (
                f"<p style='color: red;'>❌ 连接失败: {str(e)}</p>",
                gr.update(visible=False),  # disconnect_btn
                gr.update(visible=True),   # connect_btn
                gr.update(choices=[]),     # table_selector
                gr.update(visible=False),  # db_schema_tree
                gr.update(visible=False),  # table_data_display
                gr.update(visible=False),  # pagination_controls
                gr.update(visible=False)   # data_operation_btns
            )

    def disconnect_from_database(self):
        """断开数据库连接"""
        try:
            if self.manager:
                self.manager.close_connection()
            
            return (
                "<p style='color: #666;'>已断开数据库连接</p>",
                gr.update(visible=False),  # disconnect_btn
                gr.update(visible=True),   # connect_btn
                gr.update(choices=[], visible=False),  # table_selector
                gr.update(visible=False),  # db_schema_tree
                gr.update(visible=False),  # table_data_display
                gr.update(visible=False),  # pagination_controls
                gr.update(visible=False)   # data_operation_btns
            )
            
        except Exception as e:
            return (
                f"<p style='color: red;'>❌ 断开连接失败: {str(e)}</p>",
                gr.update(),  # disconnect_btn
                gr.update(),  # connect_btn
                gr.update(),  # table_selector
                gr.update(),  # db_schema_tree
                gr.update(),  # table_data_display
                gr.update(),  # pagination_controls
                gr.update()   # data_operation_btns
            )

    def _update_config_file(self, db_type, host, port, database, username, password, db_path):
        """连接成功后自动更新配置文件"""
        try:
            from src.config_persistence_manager import ConfigPersistenceManager
            import os

            config_manager = ConfigPersistenceManager()
            config = config_manager.load_config()

            # 更新数据库配置
            if 'database_configs' not in config:
                config['database_configs'] = {}

            if 'quota_db' not in config['database_configs']:
                config['database_configs']['quota_db'] = {}

            quota_db_config = config['database_configs']['quota_db']

            # 更新配置信息
            quota_db_config['db_type'] = db_type

            if db_type == 'sqlite':
                quota_db_config['db_path'] = db_path
                quota_db_config['db_name'] = os.path.basename(db_path) if db_path else ''
            else:
                quota_db_config['host'] = host
                quota_db_config['port'] = str(port)
                quota_db_config['db_name'] = database
                quota_db_config['username'] = username
                # 密码需要加密存储
                if password:
                    quota_db_config['password'] = password

                if db_type == 'postgresql':
                    quota_db_config['default_db'] = 'postgres'
                elif db_type == 'mysql':
                    quota_db_config['default_db'] = 'mysql'

            # 保存配置
            success = config_manager.save_config(config)

            if success:
                print(f"✅ 配置文件已自动更新: {db_type} - {database if database else db_path}")
            else:
                print(f"⚠️ 配置文件更新失败")

        except Exception as e:
            print(f"❌ 更新配置文件异常: {e}")

    def load_table_data(self, table_name: str):
        """加载表数据"""
        try:
            if not self.manager or not table_name:
                return (
                    gr.update(visible=False),  # table_data_display
                    gr.update(visible=False),  # pagination_controls
                    gr.update(visible=False),  # data_operation_btns
                    gr.update(value="第 1 页")  # page_info
                )
            
            self.current_table = table_name
            self.current_page = 0
            
            success, message, df = self.manager.get_table_data(table_name, self.page_size, 0)

            if success and not df.empty:
                # 获取总记录数
                total_success, total_msg, total_count = self.manager.get_table_count(table_name)
                if total_success:
                    total_pages = (total_count + self.page_size - 1) // self.page_size
                    page_info = f"第 {self.current_page + 1} 页 / 共 {total_pages} 页 (总计 {total_count} 条记录)"
                else:
                    page_info = f"第 {self.current_page + 1} 页"

                return (
                    gr.update(value=df, visible=True),   # table_data_display
                    gr.update(visible=True),             # pagination_controls
                    gr.update(visible=True),             # data_operation_btns
                    gr.update(value=page_info)           # page_info
                )
            else:
                return (
                    gr.update(visible=False),  # table_data_display
                    gr.update(visible=False),  # pagination_controls
                    gr.update(visible=False),  # data_operation_btns
                    gr.update(value="无数据")   # page_info
                )
                
        except Exception as e:
            return (
                gr.update(visible=False),  # table_data_display
                gr.update(visible=False),  # pagination_controls
                gr.update(visible=False),  # data_operation_btns
                gr.update(value=f"错误: {str(e)}")  # page_info
            )

    def search_quotas(self, search_term: str):
        """搜索定额项"""
        try:
            if not self.manager or not search_term.strip():
                return (
                    gr.update(visible=False),  # quota_search_results
                    "<p style='color: #666;'>请输入搜索关键词</p>",  # selected_quota_info
                    gr.update(visible=False)   # related_resources_display
                )
            
            success, message, quota_df, resource_df = self.manager.search_quotas_and_resources(search_term.strip())

            if success and (not quota_df.empty or not resource_df.empty):
                # 如果有定额数据，优先显示定额数据
                if not quota_df.empty:
                    display_df = quota_df
                    info_message = f"<p style='color: green;'>{message}</p><p style='color: blue;'>📊 显示定额项数据</p>"
                else:
                    display_df = resource_df
                    info_message = f"<p style='color: green;'>{message}</p><p style='color: blue;'>🔧 显示资源数据</p>"

                return (
                    gr.update(value=display_df, visible=True),  # quota_search_results
                    info_message,  # selected_quota_info
                    gr.update(value=resource_df, visible=not resource_df.empty)  # related_resources_display
                )
            else:
                return (
                    gr.update(visible=False),  # quota_search_results
                    f"<p style='color: orange;'>{message}</p>",  # selected_quota_info
                    gr.update(visible=False)   # related_resources_display
                )
                
        except Exception as e:
            return (
                gr.update(visible=False),  # quota_search_results
                f"<p style='color: red;'>❌ 搜索失败: {str(e)}</p>",  # selected_quota_info
                gr.update(visible=False)   # related_resources_display
            )

    def load_unique_resources(self):
        """加载唯一资源列表"""
        try:
            if not self.manager:
                return (
                    gr.update(visible=False),  # unique_resources_display
                    "<p style='color: red;'>❌ 管理器未初始化</p>"  # price_update_status
                )
            
            success, message, df = self.manager.get_unique_resources()
            
            if success and not df.empty:
                return (
                    gr.update(value=df, visible=True),  # unique_resources_display
                    f"<p style='color: green;'>{message}</p>"  # price_update_status
                )
            else:
                return (
                    gr.update(visible=False),  # unique_resources_display
                    f"<p style='color: orange;'>{message}</p>"  # price_update_status
                )
                
        except Exception as e:
            return (
                gr.update(visible=False),  # unique_resources_display
                f"<p style='color: red;'>❌ 加载失败: {str(e)}</p>"  # price_update_status
            )

    def update_resource_price(self, resource_code: str, new_price: float):
        """更新资源价格"""
        try:
            if not self.manager:
                return "<p style='color: red;'>❌ 管理器未初始化</p>"
            
            if not resource_code or new_price is None or new_price < 0:
                return "<p style='color: orange;'>⚠️ 请提供有效的资源编号和价格</p>"
            
            success, message = self.manager.update_resource_price(resource_code, new_price)
            
            if success:
                return f"<p style='color: green;'>{message}</p>"
            else:
                return f"<p style='color: red;'>{message}</p>"
                
        except Exception as e:
            return f"<p style='color: red;'>❌ 更新失败: {str(e)}</p>"

    def recalculate_quota_prices(self):
        """重新计算定额价格"""
        try:
            print("🔄 重新计算定额价格按钮被点击")

            if not self.manager:
                print("❌ 管理器未初始化")
                return "<p style='color: red;'>❌ 管理器未初始化</p>"

            if not self.manager.connection:
                print("❌ 数据库未连接")
                return "<p style='color: red;'>❌ 请先连接到数据库</p>"

            print("🔄 开始重新计算定额价格...")
            success, message = self.manager.recalculate_quota_prices()

            if success:
                print(f"✅ 重新计算成功: {message}")
                return f"<p style='color: green;'>{message}</p>"
            else:
                print(f"❌ 重新计算失败: {message}")
                return f"<p style='color: red;'>{message}</p>"

        except Exception as e:
            print(f"❌ 重新计算异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return f"<p style='color: red;'>❌ 重新计算失败: {str(e)}</p>"

    def select_resource_for_price_update(self, evt: gr.SelectData, dataframe_data):
        """选择资源进行价格更新"""
        try:
            if not evt or evt.index is None:
                return (
                    "<p style='color: #666;'>请选择一个资源项进行价格更新</p>",  # selected_resource_info
                    0,  # new_price_input value
                    ""     # resource_code (hidden)
                )

            # 获取选中的行索引
            if isinstance(evt.index, (list, tuple)):
                row_index = evt.index[0] if len(evt.index) > 0 else 0
            else:
                row_index = evt.index

            # 确保row_index是整数
            try:
                row_index = int(row_index)
            except (ValueError, TypeError):
                return (
                    "<p style='color: orange;'>⚠️ 无效的行索引</p>",
                    0,
                    ""
                )

            # 处理不同类型的dataframe数据
            if dataframe_data is None:
                return (
                    "<p style='color: orange;'>⚠️ 数据为空</p>",
                    0,
                    ""
                )

            # 转换pandas DataFrame或其他格式为列表
            import pandas as pd
            if isinstance(dataframe_data, pd.DataFrame):
                # 如果是pandas DataFrame，转换为列表
                data_list = dataframe_data.values.tolist()
            elif hasattr(dataframe_data, 'values'):
                # 如果有values属性，尝试转换
                data_list = dataframe_data.values.tolist()
            elif isinstance(dataframe_data, list):
                data_list = dataframe_data
            else:
                # 尝试直接转换为列表
                try:
                    data_list = list(dataframe_data)
                except:
                    return (
                        f"<p style='color: orange;'>⚠️ 无法处理数据格式 (类型: {type(dataframe_data)})</p>",
                        0,
                        ""
                    )

            # 检查数据长度
            if len(data_list) <= row_index or row_index < 0:
                return (
                    f"<p style='color: orange;'>⚠️ 无法获取选中行数据 (索引: {row_index}, 数据长度: {len(data_list)})</p>",
                    0,
                    ""
                )

            selected_row = data_list[row_index]

            if selected_row and len(selected_row) > 0:
                # 提取资源信息
                resource_code = str(selected_row[0]) if len(selected_row) > 0 else ""
                resource_name = str(selected_row[1]) if len(selected_row) > 1 else ""
                category = str(selected_row[2]) if len(selected_row) > 2 else ""
                unit = str(selected_row[3]) if len(selected_row) > 3 else ""
                avg_price = selected_row[4] if len(selected_row) > 4 else 0
                min_price = selected_row[5] if len(selected_row) > 5 else 0
                max_price = selected_row[6] if len(selected_row) > 6 else 0
                usage_count = selected_row[7] if len(selected_row) > 7 else 0

                # 确保价格是数值类型
                try:
                    avg_price = float(avg_price) if avg_price else 0
                    min_price = float(min_price) if min_price else 0
                    max_price = float(max_price) if max_price else 0
                    usage_count = int(usage_count) if usage_count else 0
                except (ValueError, TypeError):
                    avg_price = min_price = max_price = 0
                    usage_count = 0

                # 构建显示信息
                info_html = f"""
                <div style="padding: 15px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 10px; border: 1px solid rgba(102, 126, 234, 0.2);">
                    <h4 style="color: #667eea; margin-bottom: 10px;">📋 选中资源信息</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                        <div><strong>资源编号:</strong> {resource_code}</div>
                        <div><strong>资源名称:</strong> {resource_name}</div>
                        <div><strong>类别:</strong> {category}</div>
                        <div><strong>单位:</strong> {unit}</div>
                        <div><strong>平均单价:</strong> ¥{avg_price:.2f}</div>
                        <div><strong>使用次数:</strong> {usage_count}</div>
                    </div>
                    <div style="margin-top: 10px; padding: 8px; background: rgba(255, 255, 255, 0.7); border-radius: 5px;">
                        <small><strong>价格范围:</strong> ¥{min_price:.2f} ~ ¥{max_price:.2f}</small>
                    </div>
                </div>
                """

                return (
                    info_html,      # selected_resource_info
                    avg_price,      # new_price_input value
                    resource_code   # resource_code (for update function)
                )

            return (
                "<p style='color: orange;'>⚠️ 无法获取选中资源的详细信息</p>",
                0,
                ""
            )

        except Exception as e:
            return (
                f"<p style='color: red;'>❌ 选择资源失败: {str(e)}</p>",
                None,
                ""
            )

    def update_selected_resource_price(self, resource_code, new_price):
        """更新选中资源的价格"""
        try:
            print(f"💰 更新价格按钮被点击: 资源编号={resource_code}, 新价格={new_price}")

            if not resource_code:
                print("⚠️ 未选择资源")
                return "<p style='color: orange;'>⚠️ 请先选择一个资源项</p>"

            if not self.manager:
                print("❌ 管理器未初始化")
                return "<p style='color: red;'>❌ 管理器未初始化</p>"

            if not self.manager.connection:
                print("❌ 数据库未连接")
                return "<p style='color: red;'>❌ 请先连接到数据库</p>"

            return self.update_resource_price(resource_code, new_price)

        except Exception as e:
            print(f"❌ 更新价格异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return f"<p style='color: red;'>❌ 更新失败: {str(e)}</p>"

    def scan_price_info_databases(self):
        """扫描可用的信息价数据库文件和PostgreSQL数据库"""
        try:
            import os
            import glob

            # 扫描output目录下的信息价相关文件
            price_db_files = []

            # 1. 扫描PostgreSQL数据库服务器
            print("🔍 扫描PostgreSQL数据库服务器...")
            postgresql_dbs = self._scan_postgresql_databases()
            price_db_files.extend(postgresql_dbs)

            # 2. 扫描SQLite数据库文件
            sqlite_patterns = [
                "output/*price*.db",
                "output/*信息价*.db",
                "output/price_info*.db"
            ]

            for pattern in sqlite_patterns:
                files = glob.glob(pattern)
                for file in files:
                    if os.path.exists(file):
                        price_db_files.append(("SQLite: " + os.path.basename(file), file))

            # 3. 扫描MongoDB JSON文件
            json_patterns = [
                "output/*price*.json",
                "output/*信息价*.json",
                "output/price_info*.json"
            ]

            for pattern in json_patterns:
                files = glob.glob(pattern)
                for file in files:
                    if os.path.exists(file):
                        price_db_files.append(("MongoDB: " + os.path.basename(file), file))

            # 4. 扫描CSV文件
            csv_patterns = [
                "output/*price*.csv",
                "output/*信息价*.csv",
                "output/price_info*.csv"
            ]

            for pattern in csv_patterns:
                files = glob.glob(pattern)
                for file in files:
                    if os.path.exists(file):
                        price_db_files.append(("CSV: " + os.path.basename(file), file))

            print(f"📋 扫描完成，找到 {len(price_db_files)} 个信息价数据源")

            if price_db_files:
                return gr.update(choices=price_db_files, value=price_db_files[0][1] if price_db_files else None)
            else:
                return gr.update(choices=[("未找到信息价数据库文件", "")], value="")

        except Exception as e:
            print(f"❌ 扫描失败: {str(e)}")
            return gr.update(choices=[("扫描失败: " + str(e), "")], value="")

    def _scan_postgresql_databases(self):
        """扫描PostgreSQL数据库服务器中的信息价数据库"""
        try:
            # 从配置中获取PostgreSQL连接信息
            from .config_persistence_manager import ConfigPersistenceManager
            config_manager = ConfigPersistenceManager()
            price_db_config = config_manager.get_database_config("price")

            # 如果配置的数据库类型不是PostgreSQL，跳过扫描
            if price_db_config.get("db_type") != "postgresql":
                print("💡 当前配置的信息价数据库类型不是PostgreSQL，跳过服务器扫描")
                return []

            try:
                import psycopg2

                # 连接到PostgreSQL服务器
                conn = psycopg2.connect(
                    host=price_db_config.get('host', 'localhost'),
                    port=int(price_db_config.get('port', 5432)),
                    user=price_db_config.get('username', 'postgres'),
                    password=price_db_config.get('password', ''),
                    database=price_db_config.get('default_db', 'postgres'),
                    connect_timeout=5
                )

                cursor = conn.cursor()

                # 获取所有数据库列表
                cursor.execute("""
                    SELECT datname FROM pg_database
                    WHERE datistemplate = false
                    AND datname NOT IN ('postgres', 'template0', 'template1')
                """)

                databases = cursor.fetchall()
                postgresql_dbs = []

                for (db_name,) in databases:
                    # 检查数据库是否包含信息价相关的表
                    if self._check_price_info_tables(cursor, db_name, price_db_config):
                        # 构建PostgreSQL连接字符串
                        pg_connection_string = f"postgresql://{price_db_config.get('username', 'postgres')}:{price_db_config.get('password', '')}@{price_db_config.get('host', 'localhost')}:{price_db_config.get('port', 5432)}/{db_name}"
                        postgresql_dbs.append((f"PostgreSQL: {db_name}", pg_connection_string))
                        print(f"✅ 找到PostgreSQL信息价数据库: {db_name}")

                cursor.close()
                conn.close()

                return postgresql_dbs

            except ImportError:
                print("⚠️ 未安装psycopg2模块，无法扫描PostgreSQL数据库")
                return []
            except Exception as e:
                print(f"⚠️ PostgreSQL数据库扫描失败: {str(e)}")
                return []

        except Exception as e:
            print(f"⚠️ PostgreSQL配置获取失败: {str(e)}")
            return []

    def _check_price_info_tables(self, cursor, db_name, config):
        """检查数据库是否包含信息价相关的表"""
        try:
            # 切换到目标数据库
            cursor.connection.close()

            import psycopg2
            conn = psycopg2.connect(
                host=config.get('host', 'localhost'),
                port=int(config.get('port', 5432)),
                user=config.get('username', 'postgres'),
                password=config.get('password', ''),
                database=db_name,
                connect_timeout=5
            )

            cursor = conn.cursor()

            # 查找包含信息价关键词的表
            cursor.execute("""
                SELECT table_name FROM information_schema.tables
                WHERE table_schema = 'public'
                AND (table_name ILIKE '%price%'
                     OR table_name ILIKE '%信息价%'
                     OR table_name ILIKE '%价格%')
            """)

            tables = cursor.fetchall()
            cursor.close()
            conn.close()

            return len(tables) > 0

        except Exception as e:
            print(f"检查数据库 {db_name} 的表结构失败: {str(e)}")
            return False

    def load_price_info_data(self, selected_db_path: str):
        """载入信息价数据并智能匹配资源编号"""
        try:
            import os

            if not self.manager or not self.manager.connection:
                return (
                    "<p style='color: red;'>❌ 请先连接到定额数据库</p>",
                    ""
                )

            if not selected_db_path or selected_db_path == "":
                return (
                    "<p style='color: orange;'>⚠️ 请先选择信息价数据库文件</p>",
                    ""
                )

            # 检查是否是PostgreSQL连接字符串
            if not selected_db_path.startswith('postgresql://') and not os.path.exists(selected_db_path):
                return (
                    "<p style='color: red;'>❌ 选择的信息价数据库文件不存在</p>",
                    ""
                )

            # 根据文件类型载入信息价数据
            price_data = self._load_price_data_from_file(selected_db_path)
            if not price_data:
                return (
                    "<p style='color: red;'>❌ 无法从选择的文件中载入信息价数据</p>",
                    ""
                )

            # 获取当前数据库中的资源列表
            resource_data = self._get_current_resources()
            if not resource_data:
                return (
                    "<p style='color: red;'>❌ 无法获取当前数据库中的资源列表</p>",
                    ""
                )

            # 智能匹配资源名称并更新价格
            match_results = self._match_and_update_prices_by_name(resource_data, price_data)

            # 生成结果报告
            status_html, stats_html = self._generate_price_update_report(match_results)

            return status_html, stats_html

        except Exception as e:
            return (
                f"<p style='color: red;'>❌ 载入信息价失败: {str(e)}</p>",
                ""
            )

    def _load_price_data_from_file(self, file_path: str) -> list:
        """从文件或数据库连接中载入信息价数据"""
        try:
            import pandas as pd
            import json
            import sqlite3
            import os

            # 检查是否是PostgreSQL连接字符串
            if file_path.startswith('postgresql://'):
                return self._load_price_data_from_postgresql(file_path)

            file_ext = os.path.splitext(file_path)[1].lower()

            if file_ext == '.csv':
                # 从CSV文件载入
                df = pd.read_csv(file_path, encoding='utf-8-sig')
                return df.to_dict('records')

            elif file_ext == '.json':
                # 从MongoDB JSON文件载入
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 提取文档数据
                price_records = []
                if 'collections' in data:
                    for collection_name, collection_data in data['collections'].items():
                        documents = collection_data.get('documents', [])
                        price_records.extend(documents)

                return price_records

            elif file_ext == '.db':
                # 从SQLite数据库载入
                conn = sqlite3.connect(file_path)

                # 获取所有表名
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()

                price_records = []
                for table_name, in tables:
                    df = pd.read_sql_query(f"SELECT * FROM `{table_name}`", conn)
                    price_records.extend(df.to_dict('records'))

                conn.close()
                return price_records

            return []

        except Exception as e:
            print(f"载入信息价数据失败: {str(e)}")
            return []

    def _load_price_data_from_postgresql(self, connection_string: str) -> list:
        """从PostgreSQL数据库载入信息价数据"""
        try:
            import psycopg2
            from urllib.parse import urlparse

            # 解析连接字符串
            parsed = urlparse(connection_string)

            conn = psycopg2.connect(
                host=parsed.hostname,
                port=parsed.port or 5432,
                user=parsed.username,
                password=parsed.password,
                database=parsed.path[1:],  # 移除开头的 '/'
                connect_timeout=10
            )

            cursor = conn.cursor()

            # 获取所有包含信息价的表
            cursor.execute("""
                SELECT table_name FROM information_schema.tables
                WHERE table_schema = 'public'
                AND (table_name ILIKE '%price%'
                     OR table_name ILIKE '%信息价%'
                     OR table_name ILIKE '%价格%')
            """)

            tables = cursor.fetchall()
            price_records = []

            for (table_name,) in tables:
                try:
                    cursor.execute(f'SELECT * FROM "{table_name}"')
                    columns = [desc[0] for desc in cursor.description]
                    rows = cursor.fetchall()

                    for row in rows:
                        record = dict(zip(columns, row))
                        price_records.append(record)

                    print(f"📊 从表 {table_name} 载入 {len(rows)} 条信息价记录")

                except Exception as e:
                    print(f"⚠️ 载入表 {table_name} 失败: {str(e)}")
                    continue

            cursor.close()
            conn.close()

            print(f"✅ 从PostgreSQL载入总计 {len(price_records)} 条信息价记录")
            return price_records

        except Exception as e:
            print(f"❌ PostgreSQL信息价数据载入失败: {str(e)}")
            return []

    def _get_current_resources(self) -> list:
        """获取当前数据库中的资源列表"""
        try:
            # 使用正确的方法名获取资源数据
            success, message, resources_df = self.manager.get_unique_resources()

            if success and not resources_df.empty:
                # 将DataFrame转换为字典列表
                resources_data = resources_df.to_dict('records')
                print(f"📋 获取到 {len(resources_data)} 个定额资源")
                return resources_data

            print(f"⚠️ 获取资源失败: {message}")
            return []

        except Exception as e:
            print(f"获取当前资源列表失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return []

    def _match_and_update_prices_by_name(self, resource_data: list, price_data: list) -> dict:
        """智能匹配资源名称并更新价格"""
        try:
            match_results = {
                'matched': [],
                'updated': [],
                'not_matched': [],
                'errors': []
            }

            print(f"🔍 开始匹配资源: 定额资源 {len(resource_data)} 个, 信息价 {len(price_data)} 个")

            # 创建信息价数据的名称索引（支持模糊匹配）
            price_name_index = []
            for price_item in price_data:
                # 获取产品名称
                product_name = None
                for field in ['产品名称', '资源名称', '名称', 'name', 'product_name']:
                    if field in price_item and price_item[field]:
                        product_name = str(price_item[field]).strip()
                        break

                if not product_name:
                    continue

                # 获取规格信息
                specifications = None
                for field in ['规格型号及特征', '规格', '型号', '规格型号', 'specifications']:
                    if field in price_item and price_item[field]:
                        specifications = str(price_item[field]).strip()
                        break
                    # 智能选择价格字段，优先使用不含税价格
                    price_value = None
                    price_type = None
                    selected_field = None

                    # 定义价格字段优先级（不含税价格优先）
                    price_fields_priority = [
                        # 第一优先级：明确的不含税价格
                        ('市场参考价（不含税）', 'no_tax'),
                        ('不含税价格', 'no_tax'),
                        ('不含税单价', 'no_tax'),
                        ('市场价（不含税）', 'no_tax'),
                        ('参考价（不含税）', 'no_tax'),
                        ('price_no_tax', 'no_tax'),
                        ('unit_price_no_tax', 'no_tax'),

                        # 第二优先级：通用价格字段（可能是不含税）
                        ('单价', 'general'),
                        ('price', 'general'),
                        ('unit_price', 'general'),
                        ('市场价', 'general'),
                        ('参考价', 'general'),

                        # 第三优先级：市场参考价（通常是含税）
                        ('市场参考价', 'market'),
                        ('市场参考价格', 'market'),

                        # 最后选择：含税价格（作为备选）
                        ('市场参考价（含税）', 'with_tax'),
                        ('含税价格', 'with_tax'),
                        ('含税单价', 'with_tax'),
                        ('市场价（含税）', 'with_tax'),
                        ('参考价（含税）', 'with_tax'),
                        ('price_with_tax', 'with_tax'),
                        ('unit_price_with_tax', 'with_tax')
                    ]

                    # 按优先级查找价格字段
                    for field_name, field_type in price_fields_priority:
                        if field_name in price_item and price_item[field_name]:
                            try:
                                price_str = str(price_item[field_name]).replace(',', '').replace('¥', '').replace('￥', '').strip()
                                if price_str and price_str != '' and price_str.lower() != 'null':
                                    price_value = float(price_str)
                                    if price_value > 0:
                                        price_type = field_type
                                        selected_field = field_name
                                        break
                            except (ValueError, TypeError):
                                continue

                    if price_value and price_value > 0:
                        price_index[resource_code] = {
                            'price': price_value,
                            'price_type': price_type,
                            'price_field': selected_field,
                            'name': price_item.get('产品名称', price_item.get('资源名称', '')),
                            'unit': price_item.get('计量单位', price_item.get('单位', '')),
                            'source': price_item
                        }

            # 匹配当前资源
            for resource in resource_data:
                resource_code = resource.get('资源编号', '')

                if resource_code in price_index:
                    # 找到匹配的信息价
                    price_info = price_index[resource_code]
                    match_results['matched'].append({
                        'resource_code': resource_code,
                        'resource_name': resource.get('资源名称', ''),
                        'old_price': resource.get('平均单价', 0),
                        'new_price': price_info['price'],
                        'price_type': price_info['price_type'],
                        'price_field': price_info['price_field'],
                        'price_source': price_info['name']
                    })

                    # 尝试更新价格
                    try:
                        success, message = self.manager.update_resource_price(resource_code, price_info['price'])
                        if success:
                            match_results['updated'].append({
                                'resource_code': resource_code,
                                'resource_name': resource.get('资源名称', ''),
                                'new_price': price_info['price'],
                                'price_type': price_info['price_type'],
                                'price_field': price_info['price_field'],
                                'message': message
                            })
                        else:
                            match_results['errors'].append({
                                'resource_code': resource_code,
                                'error': message
                            })
                    except Exception as e:
                        match_results['errors'].append({
                            'resource_code': resource_code,
                            'error': str(e)
                        })
                else:
                    # 未找到匹配的信息价
                    match_results['not_matched'].append({
                        'resource_code': resource_code,
                        'resource_name': resource.get('资源名称', '')
                    })

            return match_results

        except Exception as e:
            return {
                'matched': [],
                'updated': [],
                'not_matched': [],
                'errors': [{'resource_code': 'SYSTEM', 'error': str(e)}]
            }

    def _generate_price_update_report(self, match_results: dict) -> tuple[str, str]:
        """生成价格更新报告"""
        try:
            updated_count = len(match_results['updated'])
            matched_count = len(match_results['matched'])
            not_matched_count = len(match_results['not_matched'])
            error_count = len(match_results['errors'])

            # 统计价格类型
            price_type_stats = {}
            for item in match_results['updated']:
                price_type = item.get('price_type', 'unknown')
                price_type_stats[price_type] = price_type_stats.get(price_type, 0) + 1

            # 生成价格类型说明
            price_type_names = {
                'no_tax': '不含税价格',
                'general': '通用价格',
                'market': '市场参考价',
                'with_tax': '含税价格',
                'unknown': '未知类型'
            }

            price_type_info = []
            for price_type, count in price_type_stats.items():
                type_name = price_type_names.get(price_type, price_type)
                price_type_info.append(f"{type_name}: {count}个")

            # 生成状态HTML
            if updated_count > 0:
                price_type_summary = "、".join(price_type_info) if price_type_info else "未分类"
                status_html = f"""
                <div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 15px; margin: 10px 0;">
                    <h4 style="color: #155724; margin: 0 0 10px 0;">✅ 信息价载入成功！</h4>
                    <p style="color: #155724; margin: 0;">
                        成功匹配并更新了 <strong>{updated_count}</strong> 个资源项的价格<br>
                        价格类型分布: {price_type_summary}
                    </p>
                </div>
                """
            else:
                status_html = f"""
                <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 10px 0;">
                    <h4 style="color: #856404; margin: 0 0 10px 0;">⚠️ 未找到匹配的资源</h4>
                    <p style="color: #856404; margin: 0;">
                        没有找到可以匹配的资源编号，请检查信息价数据格式
                    </p>
                </div>
                """

            # 生成统计HTML
            stats_html = f"""
            <div style="border: 1px solid #ddd; border-radius: 8px; padding: 16px; background: #f9f9f9;">
                <h4 style="margin-top: 0; color: #333;">📊 载入统计信息</h4>

                <div style="margin-bottom: 16px; padding: 12px; background: #e8f4fd; border-radius: 6px;">
                    <p style="margin: 4px 0;"><strong>📈 匹配统计:</strong></p>
                    <p style="margin: 4px 0;">• 成功匹配: {matched_count} 个资源</p>
                    <p style="margin: 4px 0;">• 成功更新: {updated_count} 个资源</p>
                    <p style="margin: 4px 0;">• 未匹配: {not_matched_count} 个资源</p>
                    <p style="margin: 4px 0;">• 更新失败: {error_count} 个资源</p>
                </div>

                <div style="margin-bottom: 16px; padding: 12px; background: #f8f9fa; border-radius: 6px;">
                    <p style="margin: 4px 0;"><strong>💰 价格类型统计:</strong></p>"""

            for price_type, count in price_type_stats.items():
                type_name = price_type_names.get(price_type, price_type)
                type_icon = {
                    'no_tax': '💰',
                    'general': '💵',
                    'market': '🏪',
                    'with_tax': '🧾',
                    'unknown': '❓'
                }.get(price_type, '💵')

                type_desc = {
                    'no_tax': '（推荐，定额计算标准）',
                    'general': '（通用价格字段）',
                    'market': '（市场参考价格）',
                    'with_tax': '（含税价格，已使用）',
                    'unknown': '（未知类型）'
                }.get(price_type, '')

                stats_html += f"<p style='margin: 4px 0;'>• {type_icon} {type_name}: {count} 个资源 {type_desc}</p>"

            stats_html += """
                </div>
            """

            # 添加成功更新的详细信息
            if match_results['updated']:
                stats_html += """
                <div style="margin-bottom: 16px; padding: 12px; background: #d4edda; border-radius: 6px;">
                    <p style="margin: 4px 0;"><strong>✅ 成功更新的资源:</strong></p>
                """
                for item in match_results['updated'][:10]:  # 只显示前10个
                    price_type = item.get('price_type', 'unknown')
                    price_field = item.get('price_field', '未知字段')
                    type_name = price_type_names.get(price_type, price_type)

                    # 根据价格类型显示不同的图标
                    type_icon = {
                        'no_tax': '💰',  # 不含税价格（推荐）
                        'general': '💵',  # 通用价格
                        'market': '🏪',   # 市场参考价
                        'with_tax': '🧾', # 含税价格
                        'unknown': '❓'   # 未知类型
                    }.get(price_type, '💵')

                    stats_html += f"<p style='margin: 2px 0; font-size: 0.9em;'>• {item['resource_code']}: {item['resource_name']} → ¥{item['new_price']:.2f} {type_icon}({type_name})</p>"

                if len(match_results['updated']) > 10:
                    stats_html += f"<p style='margin: 2px 0; font-size: 0.9em; color: #666;'>... 还有 {len(match_results['updated']) - 10} 个资源已更新</p>"

                stats_html += "</div>"

            # 添加未匹配的资源信息
            if match_results['not_matched']:
                stats_html += """
                <div style="margin-bottom: 16px; padding: 12px; background: #fff3cd; border-radius: 6px;">
                    <p style="margin: 4px 0;"><strong>⚠️ 未匹配的资源 (编号不匹配):</strong></p>
                """
                for item in match_results['not_matched'][:5]:  # 只显示前5个
                    stats_html += f"<p style='margin: 2px 0; font-size: 0.9em;'>• {item['resource_code']}: {item['resource_name']}</p>"

                if len(match_results['not_matched']) > 5:
                    stats_html += f"<p style='margin: 2px 0; font-size: 0.9em; color: #666;'>... 还有 {len(match_results['not_matched']) - 5} 个资源未匹配</p>"

                stats_html += "</div>"

            stats_html += "</div>"

            return status_html, stats_html

        except Exception as e:
            return (
                f"<p style='color: red;'>❌ 生成报告失败: {str(e)}</p>",
                ""
            )

    def next_page(self):
        """下一页"""
        try:
            if not self.manager or not self.current_table:
                return (
                    gr.update(),  # table_data_display
                    gr.update(value="无数据")  # page_info
                )

            # 获取总记录数
            total_success, total_msg, total_count = self.manager.get_table_count(self.current_table)
            if not total_success:
                return (
                    gr.update(),  # table_data_display
                    gr.update(value="获取总数失败")  # page_info
                )

            # 计算总页数
            total_pages = (total_count + self.page_size - 1) // self.page_size

            # 检查是否可以翻页
            if self.current_page < total_pages - 1:
                self.current_page += 1
                offset = self.current_page * self.page_size

                success, message, df = self.manager.get_table_data(self.current_table, self.page_size, offset)

                if success and not df.empty:
                    return (
                        gr.update(value=df),  # table_data_display
                        gr.update(value=f"第 {self.current_page + 1} 页 / 共 {total_pages} 页")  # page_info
                    )

            return (
                gr.update(),  # table_data_display
                gr.update(value=f"第 {self.current_page + 1} 页 / 共 {total_pages} 页")  # page_info
            )

        except Exception as e:
            return (
                gr.update(),  # table_data_display
                gr.update(value=f"翻页错误: {str(e)}")  # page_info
            )

    def prev_page(self):
        """上一页"""
        try:
            if not self.manager or not self.current_table:
                return (
                    gr.update(),  # table_data_display
                    gr.update(value="无数据")  # page_info
                )

            # 获取总记录数
            total_success, total_msg, total_count = self.manager.get_table_count(self.current_table)
            if not total_success:
                return (
                    gr.update(),  # table_data_display
                    gr.update(value="获取总数失败")  # page_info
                )

            # 计算总页数
            total_pages = (total_count + self.page_size - 1) // self.page_size

            # 检查是否可以翻页
            if self.current_page > 0:
                self.current_page -= 1
                offset = self.current_page * self.page_size

                success, message, df = self.manager.get_table_data(self.current_table, self.page_size, offset)

                if success and not df.empty:
                    return (
                        gr.update(value=df),  # table_data_display
                        gr.update(value=f"第 {self.current_page + 1} 页 / 共 {total_pages} 页")  # page_info
                    )

            return (
                gr.update(),  # table_data_display
                gr.update(value=f"第 {self.current_page + 1} 页 / 共 {total_pages} 页")  # page_info
            )

        except Exception as e:
            return (
                gr.update(),  # table_data_display
                gr.update(value=f"翻页错误: {str(e)}")  # page_info
            )

    def _match_and_update_prices(self, resource_data: list, price_data: list) -> dict:
        """基于名称关键词智能匹配资源并更新价格"""
        try:
            match_results = {
                'matched': [],
                'updated': [],
                'not_matched': [],
                'errors': []
            }

            print(f"🔍 开始基于名称匹配资源: 定额资源 {len(resource_data)} 个, 信息价 {len(price_data)} 个")

            # 创建信息价数据的名称索引
            price_name_index = []
            for price_item in price_data:
                # 获取产品名称
                product_name = None
                for field in ['产品名称', '资源名称', '名称', 'name', 'product_name']:
                    if field in price_item and price_item[field]:
                        product_name = str(price_item[field]).strip()
                        break

                if not product_name:
                    continue

                # 获取规格信息
                specifications = None
                for field in ['规格型号及特征', '规格', '型号', '规格型号', 'specifications']:
                    if field in price_item and price_item[field]:
                        specifications = str(price_item[field]).strip()
                        break

                # 查找价格字段
                price_value = None
                price_type = 'unknown'
                selected_field = None

                # 按优先级查找价格字段
                price_fields_priority = [
                    ('市场参考价（不含税）', 'no_tax'),
                    ('不含税价格', 'no_tax'),
                    ('市场参考价（含税）', 'with_tax'),
                    ('含税价格', 'with_tax'),
                    ('单价', 'general'),
                    ('价格', 'general'),
                    ('市场参考价', 'market'),
                    ('参考价', 'market'),
                    ('price', 'general'),
                    ('unit_price', 'general')
                ]

                for field_name, field_type in price_fields_priority:
                    if field_name in price_item and price_item[field_name]:
                        try:
                            price_str = str(price_item[field_name]).replace(',', '').replace('¥', '').replace('￥', '').strip()
                            if price_str and price_str != '' and price_str.lower() != 'null':
                                price_value = float(price_str)
                                if price_value > 0:
                                    price_type = field_type
                                    selected_field = field_name
                                    break
                        except (ValueError, TypeError):
                            continue

                if price_value and price_value > 0:
                    # 提取关键词
                    keywords = self._extract_keywords(product_name, specifications)

                    price_name_index.append({
                        'name': product_name,
                        'specifications': specifications or '',
                        'price': price_value,
                        'price_type': price_type,
                        'price_field': selected_field,
                        'unit': price_item.get('计量单位', price_item.get('单位', '')),
                        'source': price_item,
                        'keywords': keywords,
                        'full_name': f"{product_name} {specifications or ''}".strip()
                    })

            print(f"📋 建立信息价索引: {len(price_name_index)} 个有效信息价项")

            # 匹配定额资源
            for resource in resource_data:
                resource_code = resource.get('资源编号', '')
                resource_name = resource.get('资源名称', '')

                if not resource_name:
                    match_results['not_matched'].append({
                        'resource_code': resource_code,
                        'resource_name': resource_name,
                        'reason': '资源名称为空'
                    })
                    continue

                # 基于名称关键词匹配
                best_match = self._find_best_name_match(resource_name, price_name_index)

                if best_match:
                    match_results['matched'].append({
                        'resource_code': resource_code,
                        'resource_name': resource_name,
                        'old_price': resource.get('平均单价', 0),
                        'new_price': best_match['price'],
                        'price_type': best_match['price_type'],
                        'price_field': best_match['price_field'],
                        'price_source': best_match['name'],
                        'match_score': best_match['score']
                    })

                    # 尝试更新价格
                    try:
                        success, message = self.manager.update_resource_price(resource_code, best_match['price'])
                        if success:
                            match_results['updated'].append({
                                'resource_code': resource_code,
                                'resource_name': resource_name,
                                'new_price': best_match['price'],
                                'price_type': best_match['price_type'],
                                'price_field': best_match['price_field'],
                                'price_source': best_match['name'],
                                'match_score': best_match['score'],
                                'message': message
                            })
                            print(f"✅ 更新成功: {resource_name} -> {best_match['name']} (¥{best_match['price']})")
                        else:
                            match_results['errors'].append({
                                'resource_code': resource_code,
                                'error': message
                            })
                    except Exception as e:
                        match_results['errors'].append({
                            'resource_code': resource_code,
                            'error': str(e)
                        })
                else:
                    match_results['not_matched'].append({
                        'resource_code': resource_code,
                        'resource_name': resource_name,
                        'reason': '未找到匹配的信息价'
                    })

            print(f"🎯 匹配完成: 匹配 {len(match_results['matched'])} 个, 更新 {len(match_results['updated'])} 个")
            return match_results

        except Exception as e:
            print(f"❌ 匹配过程异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                'matched': [],
                'updated': [],
                'not_matched': [],
                'errors': [{'resource_code': 'SYSTEM', 'error': str(e)}]
            }

    def _extract_keywords(self, product_name: str, specifications: str = None) -> list:
        """提取产品名称和规格中的关键词"""
        import re

        keywords = []

        # 合并产品名称和规格
        full_text = f"{product_name} {specifications or ''}".strip()

        # 移除常见的无意义词汇
        stop_words = {'的', '和', '或', '及', '与', '等', '类', '型', '种', '个', '只', '件', '套', '根', '条', '块', '片', '张', '米', '厘米', '毫米', 'mm', 'cm', 'm'}

        # 提取中文词汇（2-8个字符）
        chinese_words = re.findall(r'[\u4e00-\u9fff]{2,8}', full_text)
        for word in chinese_words:
            if word not in stop_words and len(word) >= 2:
                keywords.append(word)

        # 提取英文词汇
        english_words = re.findall(r'[A-Za-z]{2,}', full_text)
        for word in english_words:
            if len(word) >= 2:
                keywords.append(word.lower())

        # 提取数字+单位组合
        number_units = re.findall(r'\d+(?:\.\d+)?[A-Za-z\u4e00-\u9fff]+', full_text)
        keywords.extend(number_units)

        return list(set(keywords))  # 去重

    def _find_best_name_match(self, resource_name: str, price_index: list) -> dict:
        """基于名称关键词找到最佳匹配"""
        if not resource_name or not price_index:
            return None

        # 提取资源名称的关键词
        resource_keywords = self._extract_keywords(resource_name)

        best_match = None
        best_score = 0

        for price_item in price_index:
            score = self._calculate_match_score(resource_keywords, price_item)

            if score > best_score and score >= 0.3:  # 最低匹配阈值
                best_score = score
                best_match = price_item.copy()
                best_match['score'] = score

        if best_match:
            print(f"🎯 找到匹配: {resource_name} -> {best_match['name']} (得分: {best_score:.2f})")

        return best_match

    def _calculate_match_score(self, resource_keywords: list, price_item: dict) -> float:
        """计算匹配得分"""
        if not resource_keywords:
            return 0

        price_keywords = price_item.get('keywords', [])
        if not price_keywords:
            return 0

        # 计算关键词重叠度
        common_keywords = set(resource_keywords) & set(price_keywords)

        if not common_keywords:
            return 0

        # 基础得分：重叠关键词数量 / 资源关键词总数
        overlap_score = len(common_keywords) / len(resource_keywords)

        # 加权得分：考虑重要关键词
        weighted_score = 0
        for keyword in common_keywords:
            # 长词汇权重更高
            weight = min(len(keyword) / 10, 1.0)
            weighted_score += weight

        weighted_score = weighted_score / len(resource_keywords)

        # 最终得分：基础得分 * 0.6 + 加权得分 * 0.4
        final_score = overlap_score * 0.6 + weighted_score * 0.4

        return min(final_score, 1.0)
