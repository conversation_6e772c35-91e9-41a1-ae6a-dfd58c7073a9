#!/usr/bin/env python3
"""
配置持久化管理器
负责保存和加载用户的API密钥、数据库配置等设置信息
"""

import os
import json
import base64
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
import logging
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

class ConfigPersistenceManager:
    """配置持久化管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 配置文件路径
        self.config_dir = Path("user_config")
        self.config_file = self.config_dir / "user_settings.json"
        self.key_file = self.config_dir / "encryption.key"
        
        # 确保配置目录存在
        self._ensure_config_directory()
        
        # 初始化加密
        self.cipher_suite = self._init_encryption()
        
        # 默认配置结构
        self.default_config = {
            "api_keys": {
                "dashscope_key": "",
                "openai_key": "",
                "last_updated": ""
            },
            "database_configs": {
                "quota_db": {
                    "db_type": "sqlite",
                    "db_name": "quota_database.db",
                    "host": "localhost",
                    "port": "3306",
                    "username": "",
                    "password": "",
                    "default_db": "quota_db"
                },
                "price_db": {
                    "db_type": "sqlite",
                    "db_name": "price_database.db",
                    "host": "localhost",
                    "port": "3306",
                    "username": "",
                    "password": "",
                    "default_db": "price_db",
                    "merge_strategy": "replace"
                }
            },
            "ui_preferences": {
                "last_model_type": "dashscope",
                "default_start_page": "1",
                "default_end_page": "10",
                "default_volume_code": "04",
                "default_chapter_codes": "01"
            },
            "system_info": {
                "created_time": "",
                "last_access_time": "",
                "access_count": 0
            }
        }
    
    def _ensure_config_directory(self):
        """确保配置目录存在"""
        try:
            self.config_dir.mkdir(exist_ok=True)
            self.logger.info(f"📁 配置目录已准备: {self.config_dir}")
        except Exception as e:
            self.logger.error(f"❌ 创建配置目录失败: {e}")
    
    def _init_encryption(self) -> Optional[Fernet]:
        """初始化加密功能"""
        try:
            # 检查是否已有加密密钥
            if self.key_file.exists():
                with open(self.key_file, 'rb') as f:
                    key = f.read()
            else:
                # 生成新的加密密钥
                key = self._generate_encryption_key()
                with open(self.key_file, 'wb') as f:
                    f.write(key)
                self.logger.info("🔐 生成新的加密密钥")
            
            return Fernet(key)
            
        except Exception as e:
            self.logger.error(f"❌ 初始化加密功能失败: {e}")
            return None
    
    def _generate_encryption_key(self) -> bytes:
        """生成加密密钥"""
        try:
            # 使用机器特征生成密钥
            import platform
            import getpass
            
            # 组合机器信息作为密码
            machine_info = f"{platform.node()}-{platform.system()}-{getpass.getuser()}"
            password = machine_info.encode()
            
            # 使用固定的盐值（在实际应用中应该随机生成并保存）
            salt = b'quota_tool_salt_2025'
            
            # 生成密钥
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(password))
            return key
            
        except Exception as e:
            self.logger.error(f"❌ 生成加密密钥失败: {e}")
            # 如果加密失败，使用简单的固定密钥
            return Fernet.generate_key()
    
    def _encrypt_sensitive_data(self, data: str) -> str:
        """加密敏感数据"""
        try:
            if self.cipher_suite and data:
                encrypted_data = self.cipher_suite.encrypt(data.encode())
                return base64.urlsafe_b64encode(encrypted_data).decode()
            return data
        except Exception as e:
            self.logger.error(f"❌ 加密数据失败: {e}")
            return data
    
    def _decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """解密敏感数据"""
        try:
            if self.cipher_suite and encrypted_data:
                decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
                decrypted_data = self.cipher_suite.decrypt(decoded_data)
                return decrypted_data.decode()
            return encrypted_data
        except Exception as e:
            self.logger.error(f"❌ 解密数据失败: {e}")
            return encrypted_data
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置"""
        try:
            if not self.config_file.exists():
                self.logger.info("📋 配置文件不存在，使用默认配置")
                return self.default_config.copy()
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 解密敏感数据
            if 'api_keys' in config:
                for key in ['dashscope_key', 'openai_key']:
                    if key in config['api_keys'] and config['api_keys'][key]:
                        config['api_keys'][key] = self._decrypt_sensitive_data(config['api_keys'][key])
            
            if 'database_configs' in config:
                for db_name, db_config in config['database_configs'].items():
                    if 'password' in db_config and db_config['password']:
                        db_config['password'] = self._decrypt_sensitive_data(db_config['password'])
            
            # 合并默认配置（处理新增字段）
            merged_config = self._merge_with_defaults(config)
            
            # 更新访问信息
            self._update_access_info(merged_config)
            
            self.logger.info("✅ 成功加载用户配置")
            return merged_config
            
        except Exception as e:
            self.logger.error(f"❌ 加载配置失败: {e}")
            return self.default_config.copy()
    
    def save_config(self, config: Dict[str, Any]) -> bool:
        """保存配置"""
        try:
            # 创建配置副本用于保存
            save_config = json.loads(json.dumps(config))
            
            # 加密敏感数据
            if 'api_keys' in save_config:
                for key in ['dashscope_key', 'openai_key']:
                    if key in save_config['api_keys'] and save_config['api_keys'][key]:
                        save_config['api_keys'][key] = self._encrypt_sensitive_data(save_config['api_keys'][key])
            
            if 'database_configs' in save_config:
                for db_name, db_config in save_config['database_configs'].items():
                    if 'password' in db_config and db_config['password']:
                        db_config['password'] = self._encrypt_sensitive_data(db_config['password'])
            
            # 更新时间戳
            from datetime import datetime
            save_config['system_info']['last_access_time'] = datetime.now().isoformat()
            
            # 保存到文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(save_config, f, ensure_ascii=False, indent=2)
            
            self.logger.info("✅ 成功保存用户配置")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 保存配置失败: {e}")
            return False
    
    def _merge_with_defaults(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """合并默认配置"""
        def merge_dict(default: Dict, user: Dict) -> Dict:
            result = default.copy()
            for key, value in user.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = merge_dict(result[key], value)
                else:
                    result[key] = value
            return result
        
        return merge_dict(self.default_config, config)
    
    def _update_access_info(self, config: Dict[str, Any]):
        """更新访问信息"""
        try:
            from datetime import datetime
            now = datetime.now().isoformat()
            
            if 'system_info' not in config:
                config['system_info'] = {}
            
            # 设置创建时间（如果是第一次）
            if not config['system_info'].get('created_time'):
                config['system_info']['created_time'] = now
            
            # 更新访问时间和次数
            config['system_info']['last_access_time'] = now
            config['system_info']['access_count'] = config['system_info'].get('access_count', 0) + 1
            
        except Exception as e:
            self.logger.error(f"❌ 更新访问信息失败: {e}")
    
    def save_api_keys(self, dashscope_key: str = None, openai_key: str = None) -> bool:
        """保存API密钥"""
        try:
            config = self.load_config()
            
            if dashscope_key is not None:
                config['api_keys']['dashscope_key'] = dashscope_key
            
            if openai_key is not None:
                config['api_keys']['openai_key'] = openai_key
            
            from datetime import datetime
            config['api_keys']['last_updated'] = datetime.now().isoformat()
            
            return self.save_config(config)
            
        except Exception as e:
            self.logger.error(f"❌ 保存API密钥失败: {e}")
            return False
    
    def get_api_keys(self) -> Tuple[str, str]:
        """获取API密钥"""
        try:
            config = self.load_config()
            dashscope_key = config.get('api_keys', {}).get('dashscope_key', '')
            openai_key = config.get('api_keys', {}).get('openai_key', '')
            return dashscope_key, openai_key
        except Exception as e:
            self.logger.error(f"❌ 获取API密钥失败: {e}")
            return '', ''
    
    def save_database_config(self, db_type: str, config_data: Dict[str, Any]) -> bool:
        """保存数据库配置"""
        try:
            config = self.load_config()
            
            if db_type == "quota":
                config['database_configs']['quota_db'].update(config_data)
            elif db_type == "price":
                config['database_configs']['price_db'].update(config_data)
            else:
                self.logger.error(f"❌ 未知的数据库类型: {db_type}")
                return False
            
            return self.save_config(config)
            
        except Exception as e:
            self.logger.error(f"❌ 保存数据库配置失败: {e}")
            return False
    
    def get_database_config(self, db_type: str) -> Dict[str, Any]:
        """获取数据库配置"""
        try:
            config = self.load_config()
            
            if db_type == "quota":
                return config.get('database_configs', {}).get('quota_db', {})
            elif db_type == "price":
                return config.get('database_configs', {}).get('price_db', {})
            else:
                self.logger.error(f"❌ 未知的数据库类型: {db_type}")
                return {}
                
        except Exception as e:
            self.logger.error(f"❌ 获取数据库配置失败: {e}")
            return {}
    
    def save_ui_preferences(self, preferences: Dict[str, Any]) -> bool:
        """保存UI偏好设置"""
        try:
            config = self.load_config()
            config['ui_preferences'].update(preferences)
            return self.save_config(config)
        except Exception as e:
            self.logger.error(f"❌ 保存UI偏好设置失败: {e}")
            return False
    
    def get_ui_preferences(self) -> Dict[str, Any]:
        """获取UI偏好设置"""
        try:
            config = self.load_config()
            return config.get('ui_preferences', {})
        except Exception as e:
            self.logger.error(f"❌ 获取UI偏好设置失败: {e}")
            return {}
    
    def clear_config(self) -> bool:
        """清除所有配置"""
        try:
            if self.config_file.exists():
                self.config_file.unlink()
            if self.key_file.exists():
                self.key_file.unlink()
            self.logger.info("✅ 成功清除所有配置")
            return True
        except Exception as e:
            self.logger.error(f"❌ 清除配置失败: {e}")
            return False
    
    def export_config(self, export_path: str, include_sensitive: bool = False) -> bool:
        """导出配置"""
        try:
            config = self.load_config()
            
            if not include_sensitive:
                # 移除敏感信息
                export_config = json.loads(json.dumps(config))
                export_config['api_keys'] = {
                    "dashscope_key": "***已隐藏***" if config['api_keys']['dashscope_key'] else "",
                    "openai_key": "***已隐藏***" if config['api_keys']['openai_key'] else "",
                    "last_updated": config['api_keys']['last_updated']
                }
                
                for db_name, db_config in export_config['database_configs'].items():
                    if db_config.get('password'):
                        db_config['password'] = "***已隐藏***"
            else:
                export_config = config
            
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(export_config, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"✅ 成功导出配置到: {export_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 导出配置失败: {e}")
            return False
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        try:
            config = self.load_config()
            
            summary = {
                "api_keys_configured": {
                    "dashscope": bool(config['api_keys']['dashscope_key']),
                    "openai": bool(config['api_keys']['openai_key']),
                    "last_updated": config['api_keys']['last_updated']
                },
                "database_configs": {
                    "quota_db_type": config['database_configs']['quota_db']['db_type'],
                    "price_db_type": config['database_configs']['price_db']['db_type']
                },
                "system_info": config['system_info'],
                "config_file_exists": self.config_file.exists(),
                "config_file_size": self.config_file.stat().st_size if self.config_file.exists() else 0
            }
            
            return summary
            
        except Exception as e:
            self.logger.error(f"❌ 获取配置摘要失败: {e}")
            return {}
