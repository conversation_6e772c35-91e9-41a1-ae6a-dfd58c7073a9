#!/usr/bin/env python3
"""
日志管理器 - 统一的日志记录和显示系统
Log Manager - Unified logging and display system
"""

import logging
import os
import sys
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
import threading
import queue
import json

class LogManager:
    """统一日志管理器"""
    
    def __init__(self, log_level=logging.INFO):
        self.log_level = log_level
        self.log_queue = queue.Queue()
        self.log_history = []
        self.max_history = 1000  # 最多保存1000条日志
        
        # 创建日志目录
        self.log_dir = "logs"
        os.makedirs(self.log_dir, exist_ok=True)
        
        # 设置日志文件
        self.log_file = os.path.join(self.log_dir, f"app_{datetime.now().strftime('%Y%m%d')}.log")
        
        # 配置日志记录器
        self.setup_logger()
        
        # 启动日志处理线程
        self.log_thread = threading.Thread(target=self._process_logs, daemon=True)
        self.log_thread.start()
        
        self.logger.info("日志管理器初始化完成")
    
    def setup_logger(self):
        """设置日志记录器"""
        # 创建logger
        self.logger = logging.getLogger('QuotaCreationTool')
        self.logger.setLevel(self.log_level)
        
        # 清除现有的处理器
        self.logger.handlers.clear()
        
        # 创建文件处理器
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(self.log_level)
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(self.log_level)
        
        # 创建队列处理器（用于界面显示）
        queue_handler = QueueHandler(self.log_queue)
        queue_handler.setLevel(self.log_level)
        
        # 设置格式
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)s | %(name)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        queue_handler.setFormatter(formatter)
        
        # 添加处理器
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        self.logger.addHandler(queue_handler)
    
    def _process_logs(self):
        """处理日志队列"""
        while True:
            try:
                record = self.log_queue.get(timeout=1)
                if record is None:
                    break
                
                # 格式化日志记录
                log_entry = {
                    'timestamp': datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S'),
                    'level': record.levelname,
                    'module': record.name,
                    'message': record.getMessage(),
                    'raw_record': record
                }
                
                # 添加到历史记录
                self.log_history.append(log_entry)
                
                # 保持历史记录大小
                if len(self.log_history) > self.max_history:
                    self.log_history.pop(0)
                    
            except queue.Empty:
                continue
            except Exception as e:
                print(f"日志处理错误: {e}")
    
    def get_recent_logs(self, count: int = 50) -> List[Dict]:
        """获取最近的日志记录"""
        return self.log_history[-count:] if self.log_history else []
    
    def get_logs_by_level(self, level: str, count: int = 50) -> List[Dict]:
        """按级别获取日志记录"""
        filtered_logs = [log for log in self.log_history if log['level'] == level.upper()]
        return filtered_logs[-count:] if filtered_logs else []
    
    def get_logs_by_module(self, module: str, count: int = 50) -> List[Dict]:
        """按模块获取日志记录"""
        filtered_logs = [log for log in self.log_history if module.lower() in log['module'].lower()]
        return filtered_logs[-count:] if filtered_logs else []
    
    def format_logs_for_display(self, logs: List[Dict]) -> str:
        """格式化日志用于界面显示"""
        if not logs:
            return "暂无日志记录"
        
        formatted_lines = []
        for log in logs:
            level_color = self._get_level_color(log['level'])
            line = f"<span style='color: #666;'>{log['timestamp']}</span> | " \
                   f"<span style='color: {level_color}; font-weight: bold;'>{log['level']}</span> | " \
                   f"<span style='color: #333;'>{log['module']}</span> | " \
                   f"<span>{log['message']}</span>"
            formatted_lines.append(line)
        
        return "<br>".join(formatted_lines)
    
    def _get_level_color(self, level: str) -> str:
        """获取日志级别对应的颜色"""
        colors = {
            'DEBUG': '#6c757d',
            'INFO': '#17a2b8',
            'WARNING': '#ffc107',
            'ERROR': '#dc3545',
            'CRITICAL': '#6f42c1'
        }
        return colors.get(level, '#333')
    
    def clear_logs(self):
        """清空日志历史"""
        self.log_history.clear()
        self.logger.info("日志历史已清空")
    
    def export_logs(self, filename: Optional[str] = None) -> str:
        """导出日志到文件"""
        if not filename:
            filename = f"logs_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        export_path = os.path.join(self.log_dir, filename)
        
        with open(export_path, 'w', encoding='utf-8') as f:
            json.dump(self.log_history, f, ensure_ascii=False, indent=2, default=str)
        
        self.logger.info(f"日志已导出到: {export_path}")
        return export_path
    
    def get_logger(self, name: str = None) -> logging.Logger:
        """获取日志记录器"""
        if name:
            return logging.getLogger(f'QuotaCreationTool.{name}')
        return self.logger


class QueueHandler(logging.Handler):
    """队列日志处理器"""
    
    def __init__(self, log_queue):
        super().__init__()
        self.log_queue = log_queue
    
    def emit(self, record):
        """发送日志记录到队列"""
        try:
            self.log_queue.put(record)
        except Exception:
            self.handleError(record)


# 全局日志管理器实例
_log_manager = None

def get_log_manager() -> LogManager:
    """获取全局日志管理器实例"""
    global _log_manager
    if _log_manager is None:
        _log_manager = LogManager()
    return _log_manager

def get_logger(name: str = None) -> logging.Logger:
    """获取日志记录器的便捷函数"""
    return get_log_manager().get_logger(name)
