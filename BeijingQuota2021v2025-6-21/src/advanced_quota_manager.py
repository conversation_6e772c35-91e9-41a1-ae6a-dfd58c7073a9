#!/usr/bin/env python3
"""
定额创建工具 v3.0
Quota Creation Tool v3.0
集成MCP工具，实现完整的数据库操作、查询、管理功能
"""

import os
import sqlite3
import pandas as pd
import json
import re
import warnings
from typing import List, Dict, Optional, Tuple, Any
import logging
from datetime import datetime

# 抑制pandas SQLAlchemy警告
warnings.filterwarnings('ignore', message='pandas only supports SQLAlchemy connectable')

# 尝试多种导入方式以适应不同的运行环境
try:
    from .mcp_database_converter import MCPDatabaseConverter
except ImportError:
    try:
        from mcp_database_converter import MCPDatabaseConverter
    except ImportError:
        print("警告: 无法导入MCPDatabaseConverter，某些功能可能不可用")
        MCPDatabaseConverter = None

try:
    from .intelligent_quota_processor import IntelligentQuotaProcessor
except ImportError:
    try:
        from intelligent_quota_processor import IntelligentQuotaProcessor
    except ImportError:
        IntelligentQuotaProcessor = None

try:
    from .quota_revision_processor import QuotaRevisionProcessor
except ImportError:
    try:
        from quota_revision_processor import QuotaRevisionProcessor
    except ImportError:
        QuotaRevisionProcessor = None

class AdvancedQuotaManager:
    """定额创建工具类（基于MCP工具的完整实现）"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.db_path = None
        self.db_type = None
        self.connection = None
        self.connection_config = {}
        
        # 集成MCP数据库转换工具
        if MCPDatabaseConverter:
            self.mcp_converter = MCPDatabaseConverter()
        else:
            self.mcp_converter = None
            self.logger.warning("MCP数据库转换工具未找到")

        # 集成智能定额处理器
        if IntelligentQuotaProcessor:
            self.intelligent_processor = IntelligentQuotaProcessor()
        else:
            self.intelligent_processor = None
            self.logger.warning("智能定额处理器未找到")

        # 集成定额修订处理器
        if QuotaRevisionProcessor:
            self.revision_processor = QuotaRevisionProcessor(logger=self.logger, quota_manager=self)
        else:
            self.revision_processor = None
            self.logger.warning("定额修订处理器未找到")

        # 支持的数据库类型
        self.supported_db_types = {
            'sqlite': {
                'name': 'SQLite本地数据库',
                'icon': '📱',
                'description': '适合本地测试和小型项目',
                'connection_fields': ['database_path']
            },
            'mysql': {
                'name': 'MySQL数据库',
                'icon': '🐬',
                'description': '适合中大型企业应用',
                'connection_fields': ['host', 'port', 'database', 'username', 'password']
            },
            'postgresql': {
                'name': 'PostgreSQL数据库',
                'icon': '🐘',
                'description': '适合高性能企业级应用',
                'connection_fields': ['host', 'port', 'database', 'username', 'password']
            },
            'mongodb': {
                'name': 'MongoDB数据库',
                'icon': '🍃',
                'description': '适合文档型数据存储',
                'connection_fields': ['host', 'port', 'database', 'username', 'password']
            },
            'sql_server': {
                'name': 'SQL Server数据库',
                'icon': '🏢',
                'description': '适合Windows企业环境',
                'connection_fields': ['host', 'port', 'database', 'username', 'password']
            },
            'oracle': {
                'name': 'Oracle数据库',
                'icon': '🔶',
                'description': '适合大型企业级应用',
                'connection_fields': ['host', 'port', 'database', 'username', 'password']
            }
        }

    def get_database_types(self) -> List[Dict[str, str]]:
        """获取支持的数据库类型列表"""
        return [
            {
                'value': db_type,
                'label': f"{info['icon']} {info['name']}",
                'description': info['description']
            }
            for db_type, info in self.supported_db_types.items()
        ]

    def get_connection_fields(self, db_type: str) -> List[str]:
        """获取指定数据库类型需要的连接字段"""
        return self.supported_db_types.get(db_type, {}).get('connection_fields', [])

    def test_database_connection(self, db_type: str, config: Dict[str, str]) -> Tuple[bool, str]:
        """测试数据库连接"""
        try:
            if db_type == 'sqlite':
                db_path = config.get('database_path', '')
                if not db_path:
                    return False, "请指定SQLite数据库文件路径"
                
                # 确保目录存在
                db_dir = os.path.dirname(db_path)
                if db_dir and not os.path.exists(db_dir):
                    os.makedirs(db_dir, exist_ok=True)

                # SQLite会自动创建数据库文件，不需要检查是否存在
                
                # 尝试连接
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                conn.close()
                
                return True, f"✅ SQLite连接成功，找到 {len(tables)} 个表"
                
            elif db_type == 'postgresql':
                import psycopg2
                
                host = config.get('host', 'localhost')
                port = int(config.get('port', 5432))
                database = config.get('database', 'postgres')
                username = config.get('username', 'postgres')
                password = config.get('password', '')
                
                conn = psycopg2.connect(
                    host=host,
                    port=port,
                    database=database,
                    user=username,
                    password=password
                )
                
                cursor = conn.cursor()
                cursor.execute("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'")
                tables = cursor.fetchall()
                conn.close()
                
                return True, f"✅ PostgreSQL连接成功，找到 {len(tables)} 个表"
                
            elif db_type == 'mysql':
                import pymysql
                
                host = config.get('host', 'localhost')
                port = int(config.get('port', 3306))
                database = config.get('database', 'mysql')
                username = config.get('username', 'root')
                password = config.get('password', '')
                
                conn = pymysql.connect(
                    host=host,
                    port=port,
                    database=database,
                    user=username,
                    password=password,
                    charset='utf8mb4',
                    autocommit=True,
                    connect_timeout=30,
                    read_timeout=30,
                    write_timeout=30
                )
                
                cursor = conn.cursor()
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                conn.close()
                
                return True, f"✅ MySQL连接成功，找到 {len(tables)} 个表"
                
            elif db_type == 'mongodb':
                from pymongo import MongoClient
                
                host = config.get('host', 'localhost')
                port = int(config.get('port', 27017))
                database = config.get('database', 'test')
                username = config.get('username', '')
                password = config.get('password', '')
                
                if username and password:
                    connection_string = f"mongodb://{username}:{password}@{host}:{port}/"
                else:
                    connection_string = f"mongodb://{host}:{port}/"
                
                client = MongoClient(connection_string)
                db = client[database]
                
                # 验证连接
                client.admin.command('ping')
                collections = db.list_collection_names()
                client.close()
                
                return True, f"✅ MongoDB连接成功，找到 {len(collections)} 个集合"
                
            else:
                return False, f"暂不支持 {db_type} 数据库的连接测试"
                
        except Exception as e:
            return False, f"❌ 连接失败: {str(e)}"

    def connect_to_database(self, db_type: str, config: Dict[str, str]) -> Tuple[bool, str]:
        """连接到数据库"""
        try:
            # 先测试连接
            success, message = self.test_database_connection(db_type, config)
            if not success:
                return False, message
            
            # 保存连接配置
            self.db_type = db_type
            self.connection_config = config.copy()
            
            if db_type == 'sqlite':
                self.db_path = config.get('database_path', '')
                self.connection = sqlite3.connect(self.db_path)
                
            elif db_type == 'postgresql':
                import psycopg2
                self.connection = psycopg2.connect(
                    host=config.get('host', 'localhost'),
                    port=int(config.get('port', 5432)),
                    database=config.get('database', 'postgres'),
                    user=config.get('username', 'postgres'),
                    password=config.get('password', ''),
                    client_encoding='utf8'
                )
                self.db_path = f"{config.get('host')}:{config.get('port')}/{config.get('database')}"
                
            elif db_type == 'mysql':
                import pymysql
                self.connection = pymysql.connect(
                    host=config.get('host', 'localhost'),
                    port=int(config.get('port', 3306)),
                    database=config.get('database', 'mysql'),
                    user=config.get('username', 'root'),
                    password=config.get('password', ''),
                    charset='utf8mb4',
                    autocommit=True,
                    connect_timeout=30,
                    read_timeout=30,
                    write_timeout=30
                )
                self.db_path = f"{config.get('host')}:{config.get('port')}/{config.get('database')}"
                
            elif db_type == 'mongodb':
                from pymongo import MongoClient
                
                host = config.get('host', 'localhost')
                port = int(config.get('port', 27017))
                database = config.get('database', 'test')
                username = config.get('username', '')
                password = config.get('password', '')
                
                if username and password:
                    connection_string = f"mongodb://{username}:{password}@{host}:{port}/"
                else:
                    connection_string = f"mongodb://{host}:{port}/"
                
                self.connection = MongoClient(connection_string)
                self.db_path = f"{host}:{port}/{database}"
            
            return True, f"✅ 成功连接到 {self.supported_db_types[db_type]['name']}"
            
        except Exception as e:
            return False, f"❌ 连接失败: {str(e)}"

    def close_connection(self):
        """关闭数据库连接"""
        if self.connection:
            try:
                if self.db_type == 'mongodb':
                    self.connection.close()
                else:
                    self.connection.close()
                self.connection = None
                self.db_type = None
                self.db_path = None
                self.connection_config = {}
            except Exception as e:
                self.logger.error(f"关闭数据库连接失败: {e}")

    def get_database_schema(self) -> Tuple[bool, str, Dict[str, Any]]:
        """获取数据库结构信息"""
        try:
            if not self.connection:
                return False, "未连接到数据库", {}
            
            schema_info = {
                'database_type': self.db_type,
                'database_path': self.db_path,
                'tables': [],
                'total_tables': 0,
                'total_records': 0
            }
            
            if self.db_type == 'sqlite':
                cursor = self.connection.cursor()
                
                # 获取表列表
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                table_names = [row[0] for row in cursor.fetchall()]
                
                for table_name in table_names:
                    # 获取表结构
                    cursor.execute(f"PRAGMA table_info(`{table_name}`)")
                    columns = [
                        {
                            'name': col[1],
                            'type': col[2],
                            'nullable': not col[3],
                            'primary_key': bool(col[5])
                        }
                        for col in cursor.fetchall()
                    ]
                    
                    # 获取记录数
                    cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                    record_count = cursor.fetchone()[0]
                    
                    schema_info['tables'].append({
                        'name': table_name,
                        'columns': columns,
                        'record_count': record_count
                    })
                    
                    schema_info['total_records'] += record_count
                
                schema_info['total_tables'] = len(table_names)
                
            elif self.db_type == 'postgresql':
                cursor = self.connection.cursor()
                
                # 获取表列表
                cursor.execute("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public'
                """)
                table_names = [row[0] for row in cursor.fetchall()]
                
                for table_name in table_names:
                    # 获取表结构
                    cursor.execute("""
                        SELECT column_name, data_type, is_nullable
                        FROM information_schema.columns
                        WHERE table_name = %s
                        ORDER BY ordinal_position
                    """, (table_name,))
                    
                    columns = [
                        {
                            'name': col[0],
                            'type': col[1],
                            'nullable': col[2] == 'YES',
                            'primary_key': False  # 需要额外查询
                        }
                        for col in cursor.fetchall()
                    ]
                    
                    # 获取记录数
                    cursor.execute(f'SELECT COUNT(*) FROM "{table_name}"')
                    record_count = cursor.fetchone()[0]
                    
                    schema_info['tables'].append({
                        'name': table_name,
                        'columns': columns,
                        'record_count': record_count
                    })
                    
                    schema_info['total_records'] += record_count
                
                schema_info['total_tables'] = len(table_names)

            elif self.db_type == 'mysql':
                cursor = self.connection.cursor()

                # 获取表列表
                cursor.execute("SHOW TABLES")
                table_names = [row[0] for row in cursor.fetchall()]

                for table_name in table_names:
                    # 获取表结构
                    cursor.execute(f"DESCRIBE `{table_name}`")
                    table_info = cursor.fetchall()

                    columns = [
                        {
                            'name': col[0],  # Field
                            'type': col[1],  # Type
                            'nullable': col[2] == 'YES',  # Null
                            'primary_key': col[3] == 'PRI'  # Key
                        }
                        for col in table_info
                    ]

                    # 获取记录数
                    cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                    record_count = cursor.fetchone()[0]

                    schema_info['tables'].append({
                        'name': table_name,
                        'columns': columns,
                        'record_count': record_count
                    })

                    schema_info['total_records'] += record_count

                schema_info['total_tables'] = len(table_names)

            elif self.db_type == 'mongodb':
                database_name = self.connection_config.get('database', 'test')
                db = self.connection[database_name]
                
                collection_names = db.list_collection_names()
                
                for collection_name in collection_names:
                    collection = db[collection_name]
                    
                    # 获取文档数量
                    record_count = collection.count_documents({})
                    
                    # 获取字段信息（从第一个文档推断）
                    sample_doc = collection.find_one()
                    columns = []
                    if sample_doc:
                        for key, value in sample_doc.items():
                            columns.append({
                                'name': key,
                                'type': type(value).__name__,
                                'nullable': True,
                                'primary_key': key == '_id'
                            })
                    
                    schema_info['tables'].append({
                        'name': collection_name,
                        'columns': columns,
                        'record_count': record_count
                    })
                    
                    schema_info['total_records'] += record_count
                
                schema_info['total_tables'] = len(collection_names)
            
            return True, "数据库结构获取成功", schema_info

        except Exception as e:
            return False, f"获取数据库结构失败: {str(e)}", {}

    def get_table_data(self, table_name: str, limit: int = 100, offset: int = 0) -> Tuple[bool, str, pd.DataFrame]:
        """获取表数据"""
        try:
            if not self.connection:
                return False, "未连接到数据库", pd.DataFrame()

            if self.db_type == 'sqlite':
                query = f"SELECT * FROM `{table_name}` LIMIT {limit} OFFSET {offset}"
                df = pd.read_sql_query(query, self.connection)

            elif self.db_type == 'postgresql':
                query = f'SELECT * FROM "{table_name}" LIMIT {limit} OFFSET {offset}'
                df = self._execute_postgresql_query(query)

            elif self.db_type == 'mysql':
                query = f"SELECT * FROM `{table_name}` LIMIT {limit} OFFSET {offset}"
                df = pd.read_sql_query(query, self.connection)

            elif self.db_type == 'mongodb':
                database_name = self.connection_config.get('database', 'test')
                db = self.connection[database_name]
                collection = db[table_name]

                # 获取文档
                documents = list(collection.find().skip(offset).limit(limit))

                if documents:
                    df = pd.DataFrame(documents)
                    # 将ObjectId转换为字符串
                    if '_id' in df.columns:
                        df['_id'] = df['_id'].astype(str)
                else:
                    df = pd.DataFrame()

            return True, f"成功获取 {len(df)} 条记录", df

        except Exception as e:
            return False, f"获取表数据失败: {str(e)}", pd.DataFrame()

    def get_table_count(self, table_name: str) -> Tuple[bool, str, int]:
        """获取表记录总数"""
        try:
            if not self.connection:
                return False, "未连接到数据库", 0

            if self.db_type == 'sqlite':
                query = f"SELECT COUNT(*) FROM `{table_name}`"
                cursor = self.connection.cursor()
                cursor.execute(query)
                count = cursor.fetchone()[0]

            elif self.db_type == 'postgresql':
                query = f'SELECT COUNT(*) FROM "{table_name}"'
                # 使用SQLAlchemy方式执行查询
                try:
                    from sqlalchemy import create_engine, text
                    host = self.connection_config.get('host', 'localhost')
                    port = self.connection_config.get('port', 5432)
                    database = self.connection_config.get('database', 'postgres')
                    username = self.connection_config.get('username', 'postgres')
                    password = self.connection_config.get('password', '')

                    engine = create_engine(f'postgresql://{username}:{password}@{host}:{port}/{database}')
                    with engine.connect() as conn:
                        result = conn.execute(text(query))
                        count = result.fetchone()[0]
                    engine.dispose()
                except ImportError:
                    # 回退到原生连接
                    cursor = self.connection.cursor()
                    cursor.execute(query)
                    count = cursor.fetchone()[0]

            elif self.db_type == 'mysql':
                query = f"SELECT COUNT(*) FROM `{table_name}`"
                cursor = self.connection.cursor()
                cursor.execute(query)
                count = cursor.fetchone()[0]

            elif self.db_type == 'mongodb':
                database_name = self.connection_config.get('database', 'test')
                db = self.connection[database_name]
                collection = db[table_name]
                count = collection.count_documents({})

            else:
                return False, f"不支持的数据库类型: {self.db_type}", 0

            return True, f"表 {table_name} 共有 {count} 条记录", count

        except Exception as e:
            return False, f"获取表记录数失败: {str(e)}", 0

    def search_quotas_and_resources(self, search_term: str, limit: int = 100) -> Tuple[bool, str, pd.DataFrame, pd.DataFrame]:
        """高级定额搜索功能 - 同时搜索定额表和资源表"""
        try:
            if not self.connection:
                return False, "未连接到数据库", pd.DataFrame(), pd.DataFrame()

            # 获取数据库结构信息
            schema_success, schema_msg, schema_info = self.get_database_schema()
            if not schema_success:
                return False, schema_msg, pd.DataFrame(), pd.DataFrame()

            # 自动识别定额表和资源表
            quota_tables = []
            resource_tables = []

            for table in schema_info['tables']:
                table_name_lower = table['name'].lower()
                if any(keyword in table_name_lower for keyword in ['parent', 'quota', '定额']):
                    quota_tables.append(table['name'])
                elif any(keyword in table_name_lower for keyword in ['child', 'resource', '资源']):
                    resource_tables.append(table['name'])

            if not quota_tables:
                return False, "未找到定额数据表", pd.DataFrame(), pd.DataFrame()

            if not resource_tables:
                return False, "未找到资源数据表", pd.DataFrame(), pd.DataFrame()

            quota_table = quota_tables[0]
            resource_table = resource_tables[0]

            quota_df = pd.DataFrame()
            resource_df = pd.DataFrame()

            # 搜索定额表
            quota_df = self._search_table(quota_table, search_term, schema_info, limit)

            # 搜索资源表
            resource_df = self._search_table(resource_table, search_term, schema_info, limit)

            # 处理搜索结果
            quota_count = len(quota_df) if not quota_df.empty else 0
            resource_count = len(resource_df) if not resource_df.empty else 0

            # 确保DataFrame数据格式正确
            if not quota_df.empty:
                quota_df = quota_df.reset_index(drop=True)
                for col in quota_df.columns:
                    if quota_df[col].dtype == 'object':
                        quota_df[col] = quota_df[col].astype(str)

            if not resource_df.empty:
                resource_df = resource_df.reset_index(drop=True)
                for col in resource_df.columns:
                    if resource_df[col].dtype == 'object':
                        resource_df[col] = resource_df[col].astype(str)

            message = f"找到 {quota_count} 个定额项，{resource_count} 个相关资源"
            print(f"🔍 搜索结果: {message}")

            return True, message, quota_df, resource_df

        except Exception as e:
            print(f"❌ 搜索错误详情: {str(e)}")
            import traceback
            traceback.print_exc()
            return False, f"搜索失败: {str(e)}", pd.DataFrame(), pd.DataFrame()

    def search_quotas_advanced(self, search_term: str, table_name: str = None, limit: int = 100) -> Tuple[bool, str, pd.DataFrame]:
        """保持向后兼容的搜索方法"""
        success, message, quota_df, resource_df = self.search_quotas_and_resources(search_term, limit)

        # 如果有定额数据就返回定额数据，否则返回资源数据
        if not quota_df.empty:
            return success, message, quota_df
        elif not resource_df.empty:
            return success, message, resource_df
        else:
            return success, message, pd.DataFrame()

    def get_related_resources_by_quota(self, quota_code: str, limit: int = 100) -> Tuple[bool, str, pd.DataFrame]:
        """根据定额编号获取相关资源"""
        try:
            if not self.connection:
                return False, "未连接到数据库", pd.DataFrame()

            # 获取数据库结构信息
            schema_success, schema_msg, schema_info = self.get_database_schema()
            if not schema_success:
                return False, schema_msg, pd.DataFrame()

            # 查找资源表
            resource_tables = []
            for table in schema_info['tables']:
                table_name_lower = table['name'].lower()
                if any(keyword in table_name_lower for keyword in ['child', 'resource', '资源']):
                    resource_tables.append(table['name'])

            if not resource_tables:
                return False, "未找到资源数据表", pd.DataFrame()

            resource_table = resource_tables[0]

            # 搜索与定额编号相关的资源
            if self.db_type == 'sqlite':
                query = f"""
                    SELECT * FROM `{resource_table}`
                    WHERE `编号` LIKE '%{quota_code}%' OR `定额编号` LIKE '%{quota_code}%'
                    LIMIT {limit}
                """
                df = pd.read_sql_query(query, self.connection)

            elif self.db_type == 'postgresql':
                query = f"""
                    SELECT * FROM "{resource_table}"
                    WHERE "编号" ILIKE '%{quota_code}%' OR "定额编号" ILIKE '%{quota_code}%'
                    LIMIT {limit}
                """
                df = self._execute_postgresql_query(query)

            else:  # MySQL
                query = f"""
                    SELECT * FROM `{resource_table}`
                    WHERE `编号` LIKE '%{quota_code}%' OR `定额编号` LIKE '%{quota_code}%'
                    LIMIT {limit}
                """
                df = pd.read_sql_query(query, self.connection)

            # 确保DataFrame数据格式正确
            if not df.empty:
                df = df.reset_index(drop=True)
                for col in df.columns:
                    if df[col].dtype == 'object':
                        df[col] = df[col].astype(str)

            return True, f"找到 {len(df)} 个相关资源", df

        except Exception as e:
            print(f"❌ 获取相关资源失败: {str(e)}")
            return False, f"获取相关资源失败: {str(e)}", pd.DataFrame()

    def _detect_field_names(self, table_name: str, schema_info: dict) -> dict:
        """智能检测字段名"""
        field_mapping = {
            'resource_code': None,
            'resource_name': None,
            'category': None,
            'unit': None,
            'unit_price': None,
            'quantity': None,
            'total_price': None
        }

        # 获取表的列信息
        table_columns = []
        for table in schema_info['tables']:
            if table['name'] == table_name:
                table_columns = [col['name'] for col in table['columns']]
                break

        if not table_columns:
            return field_mapping

        # 智能匹配字段名（优先级从高到低）
        for col_name in table_columns:
            col_lower = col_name.lower()

            # 资源编号（优先匹配明确的资源编号字段）
            if '资源编号' in col_lower and not field_mapping['resource_code']:
                field_mapping['resource_code'] = col_name
            elif 'resource_code' in col_lower and not field_mapping['resource_code']:
                field_mapping['resource_code'] = col_name

            # 资源名称
            elif any(keyword in col_lower for keyword in ['资源名称', 'resource_name', '子项名称']) and not field_mapping['resource_name']:
                field_mapping['resource_name'] = col_name
            elif '名称' in col_lower and '定额' not in col_lower and not field_mapping['resource_name']:
                field_mapping['resource_name'] = col_name

            # 类别
            elif any(keyword in col_lower for keyword in ['类别', 'category', '类型']) and not field_mapping['category']:
                field_mapping['category'] = col_name

            # 单位
            elif any(keyword in col_lower for keyword in ['资源单位', 'unit']) and not field_mapping['unit']:
                field_mapping['unit'] = col_name
            elif '单位' in col_lower and '定额' not in col_lower and not field_mapping['unit']:
                field_mapping['unit'] = col_name

            # 单价
            elif any(keyword in col_lower for keyword in ['单价', 'unit_price', 'price']) and not field_mapping['unit_price']:
                field_mapping['unit_price'] = col_name

            # 消耗量
            elif any(keyword in col_lower for keyword in ['消耗量', 'quantity', '数量']) and not field_mapping['quantity']:
                field_mapping['quantity'] = col_name

            # 合价
            elif any(keyword in col_lower for keyword in ['合价', 'total_price', '总价']) and not field_mapping['total_price']:
                field_mapping['total_price'] = col_name

        # 如果没有找到明确的资源编号字段，再考虑通用的"编号"字段
        if not field_mapping['resource_code']:
            for col_name in table_columns:
                col_lower = col_name.lower()
                if '编号' in col_lower and '定额' not in col_lower:
                    field_mapping['resource_code'] = col_name
                    break

        return field_mapping

    def get_unique_resources(self) -> Tuple[bool, str, pd.DataFrame]:
        """获取定额项的关联资源"""
        try:
            if not self.connection:
                return False, "未连接到数据库", pd.DataFrame()

            # 自动识别资源表
            schema_success, schema_msg, schema_info = self.get_database_schema()
            if not schema_success:
                return False, schema_msg, pd.DataFrame()

            # 查找包含资源数据的表
            resource_tables = []
            for table in schema_info['tables']:
                table_name_lower = table['name'].lower()
                if any(keyword in table_name_lower for keyword in ['child', 'resource', '资源']):
                    resource_tables.append(table['name'])

            if not resource_tables:
                return False, "未找到资源数据表", pd.DataFrame()

            resource_table = resource_tables[0]  # 使用第一个找到的资源表

            if self.db_type in ['sqlite', 'postgresql', 'mysql']:
                # SQL数据库查询
                if self.db_type == 'sqlite':
                    query = f"""
                        SELECT * FROM `{resource_table}`
                        WHERE `定额编号` = '{quota_code}' OR `编号` = '{quota_code}'
                    """
                elif self.db_type == 'postgresql':
                    query = f"""
                        SELECT * FROM "{resource_table}"
                        WHERE "定额编号" = '{quota_code}' OR "编号" = '{quota_code}'
                    """
                else:  # MySQL
                    query = f"""
                        SELECT * FROM `{resource_table}`
                        WHERE `定额编号` = '{quota_code}' OR `编号` = '{quota_code}'
                    """

                df = pd.read_sql_query(query, self.connection)

            elif self.db_type == 'mongodb':
                database_name = self.connection_config.get('database', 'test')
                db = self.connection[database_name]
                collection = db[resource_table]

                # MongoDB查询
                search_filter = {
                    "$or": [
                        {"定额编号": quota_code},
                        {"编号": quota_code}
                    ]
                }

                documents = list(collection.find(search_filter))

                if documents:
                    df = pd.DataFrame(documents)
                    # 将ObjectId转换为字符串
                    if '_id' in df.columns:
                        df['_id'] = df['_id'].astype(str)
                else:
                    df = pd.DataFrame()

            return True, f"找到 {len(df)} 个关联资源", df

        except Exception as e:
            return False, f"获取关联资源失败: {str(e)}", pd.DataFrame()

    def _detect_field_names(self, table_name: str, schema_info: Dict) -> Dict[str, str]:
        """智能检测表中的字段名称"""
        field_mapping = {
            'resource_code': None,
            'resource_name': None,
            'category': None,
            'unit': None,
            'unit_price': None,
            'quantity': None,
            'total_price': None
        }

        # 找到对应的表信息
        table_info = None
        for table in schema_info['tables']:
            if table['name'] == table_name:
                table_info = table
                break

        if not table_info:
            return field_mapping

        # 获取所有字段名
        column_names = [col['name'] for col in table_info['columns']]

        # 智能匹配字段名（优先级从高到低）
        for col_name in column_names:
            col_lower = col_name.lower()

            # 资源编号（优先匹配明确的资源编号字段）
            if '资源编号' in col_lower and not field_mapping['resource_code']:
                field_mapping['resource_code'] = col_name
            elif 'resource_code' in col_lower and not field_mapping['resource_code']:
                field_mapping['resource_code'] = col_name

            # 资源名称
            elif any(keyword in col_lower for keyword in ['资源名称', 'resource_name', '子项名称']) and not field_mapping['resource_name']:
                field_mapping['resource_name'] = col_name
            elif '名称' in col_lower and '定额' not in col_lower and not field_mapping['resource_name']:
                field_mapping['resource_name'] = col_name

            # 类别
            elif any(keyword in col_lower for keyword in ['类别', 'category', '类型']) and not field_mapping['category']:
                field_mapping['category'] = col_name

            # 单位
            elif any(keyword in col_lower for keyword in ['资源单位', 'unit']) and not field_mapping['unit']:
                field_mapping['unit'] = col_name
            elif '单位' in col_lower and '定额' not in col_lower and not field_mapping['unit']:
                field_mapping['unit'] = col_name

            # 单价
            elif any(keyword in col_lower for keyword in ['单价', 'unit_price', 'price']) and not field_mapping['unit_price']:
                field_mapping['unit_price'] = col_name

            # 消耗量
            elif any(keyword in col_lower for keyword in ['消耗量', 'quantity', '数量']) and not field_mapping['quantity']:
                field_mapping['quantity'] = col_name

            # 合价
            elif any(keyword in col_lower for keyword in ['合价', 'total_price', '总价']) and not field_mapping['total_price']:
                field_mapping['total_price'] = col_name

        # 如果没有找到明确的资源编号字段，再考虑通用的"编号"字段
        if not field_mapping['resource_code']:
            for col_name in column_names:
                col_lower = col_name.lower()
                if '编号' in col_lower and '定额' not in col_lower:
                    field_mapping['resource_code'] = col_name
                    break

        return field_mapping

    def _execute_postgresql_query(self, query: str) -> pd.DataFrame:
        """执行PostgreSQL查询的辅助方法"""
        try:
            from sqlalchemy import create_engine, text

            host = self.connection_config.get('host', 'localhost')
            port = self.connection_config.get('port', 5432)
            database = self.connection_config.get('database', 'postgres')
            username = self.connection_config.get('username', 'postgres')
            password = self.connection_config.get('password', '')

            engine = create_engine(f'postgresql://{username}:{password}@{host}:{port}/{database}')

            # 使用text()包装查询并手动构建DataFrame
            with engine.connect() as conn:
                result = conn.execute(text(query))
                columns = result.keys()
                rows = []
                for row in result:
                    # 将immutabledict转换为普通字典
                    row_dict = dict(row._mapping) if hasattr(row, '_mapping') else dict(row)
                    rows.append(row_dict)

                if rows:
                    df = pd.DataFrame(rows, columns=columns)
                else:
                    df = pd.DataFrame()

            engine.dispose()
            return df

        except ImportError:
            return pd.read_sql_query(query, self.connection)
        except Exception as e:
            print(f"❌ SQLAlchemy查询失败: {str(e)}")
            return pd.read_sql_query(query, self.connection)

    def _search_table(self, table_name: str, search_term: str, schema_info: dict, limit: int = 100) -> pd.DataFrame:
        """搜索单个表的辅助方法 - 支持精确和模糊搜索"""
        try:
            # 获取表的搜索字段
            search_fields = []
            for table in schema_info['tables']:
                if table['name'] == table_name:
                    for col in table['columns']:
                        col_lower = col['name'].lower()
                        if any(keyword in col_lower for keyword in ['编号', '名称', '内容', 'code', 'name', 'content']):
                            search_fields.append(col['name'])
                    break

            if not search_fields:
                return pd.DataFrame()

            # 构建智能搜索条件
            where_conditions = []

            # 检查是否是定额编号格式（如1-1, 2-3-4等）
            is_quota_code = self._is_quota_code_format(search_term)

            for field in search_fields:
                field_lower = field.lower()

                if is_quota_code and ('编号' in field_lower or 'code' in field_lower):
                    # 对于定额编号字段，使用精确匹配和前缀匹配
                    if self.db_type == 'sqlite':
                        # 精确匹配 OR 作为前缀匹配（后面跟-或结束）
                        where_conditions.append(f"(`{field}` = '{search_term}' OR `{field}` LIKE '{search_term}-%')")
                    elif self.db_type == 'postgresql':
                        where_conditions.append(f'("{field}" = \'{search_term}\' OR "{field}" LIKE \'{search_term}-%\')')
                    else:  # MySQL
                        where_conditions.append(f"(`{field}` = '{search_term}' OR `{field}` LIKE '{search_term}-%')")
                else:
                    # 对于其他字段或非定额编号，使用模糊匹配
                    if self.db_type == 'sqlite':
                        where_conditions.append(f"`{field}` LIKE '%{search_term}%'")
                    elif self.db_type == 'postgresql':
                        where_conditions.append(f'"{field}" ILIKE \'%{search_term}%\'')
                    else:  # MySQL
                        where_conditions.append(f"`{field}` LIKE '%{search_term}%'")

            where_clause = " OR ".join(where_conditions)

            # 根据数据库类型构建查询
            if self.db_type == 'sqlite':
                query = f"""
                    SELECT * FROM `{table_name}`
                    WHERE {where_clause}
                    ORDER BY
                        CASE
                            WHEN `{search_fields[0]}` = '{search_term}' THEN 1
                            WHEN `{search_fields[0]}` LIKE '{search_term}-%' THEN 2
                            ELSE 3
                        END,
                        `{search_fields[0]}`
                    LIMIT {limit}
                """
                return pd.read_sql_query(query, self.connection)

            elif self.db_type == 'postgresql':
                query = f"""
                    SELECT * FROM "{table_name}"
                    WHERE {where_clause}
                    ORDER BY
                        CASE
                            WHEN "{search_fields[0]}" = '{search_term}' THEN 1
                            WHEN "{search_fields[0]}" LIKE '{search_term}-%' THEN 2
                            ELSE 3
                        END,
                        "{search_fields[0]}"
                    LIMIT {limit}
                """
                return self._execute_postgresql_query(query)

            else:  # MySQL
                query = f"""
                    SELECT * FROM `{table_name}`
                    WHERE {where_clause}
                    ORDER BY
                        CASE
                            WHEN `{search_fields[0]}` = '{search_term}' THEN 1
                            WHEN `{search_fields[0]}` LIKE '{search_term}-%' THEN 2
                            ELSE 3
                        END,
                        `{search_fields[0]}`
                    LIMIT {limit}
                """
                return pd.read_sql_query(query, self.connection)

        except Exception as e:
            print(f"❌ 搜索表 {table_name} 失败: {str(e)}")
            return pd.DataFrame()

    def _is_quota_code_format(self, search_term: str) -> bool:
        """判断搜索词是否为定额编号格式"""
        import re
        # 定额编号格式：数字-数字 或 数字-数字-数字 等
        pattern = r'^\d+(-\d+)*$'
        return bool(re.match(pattern, search_term.strip()))

    def get_unique_resources(self) -> Tuple[bool, str, pd.DataFrame]:
        """获取所有唯一的资源项（按资源编号合并）"""
        try:
            if not self.connection:
                return False, "未连接到数据库", pd.DataFrame()

            # 自动识别资源表
            schema_success, schema_msg, schema_info = self.get_database_schema()
            if not schema_success:
                return False, schema_msg, pd.DataFrame()

            # 查找包含资源数据的表
            resource_tables = []
            for table in schema_info['tables']:
                table_name_lower = table['name'].lower()
                if any(keyword in table_name_lower for keyword in ['child', 'resource', '资源']):
                    resource_tables.append(table['name'])

            if not resource_tables:
                return False, "未找到资源数据表", pd.DataFrame()

            resource_table = resource_tables[0]

            # 智能检测字段名
            field_mapping = self._detect_field_names(resource_table, schema_info)

            # 调试信息：显示检测到的字段映射
            print(f"🔍 字段检测结果:")
            for key, value in field_mapping.items():
                if value:
                    print(f"   {key}: {value}")

            # 检查必要字段是否存在
            if not field_mapping['resource_code']:
                # 显示所有可用字段以便调试
                table_info = None
                for table in schema_info['tables']:
                    if table['name'] == resource_table:
                        table_info = table
                        break

                available_fields = [col['name'] for col in table_info['columns']] if table_info else []
                return False, f"未找到资源编号字段。可用字段: {', '.join(available_fields)}", pd.DataFrame()

            # 构建字段名（使用实际检测到的字段名）
            resource_code_field = field_mapping['resource_code']
            resource_name_field = field_mapping['resource_name'] or resource_code_field
            category_field = field_mapping['category'] or "'未知'"
            unit_field = field_mapping['unit'] or "''"
            unit_price_field = field_mapping['unit_price'] or "0"

            if self.db_type in ['sqlite', 'postgresql', 'mysql']:
                # SQL数据库查询 - 按资源编号分组
                if self.db_type == 'sqlite':
                    query = f"""
                        SELECT
                            `{resource_code_field}` as `资源编号`,
                            `{resource_name_field}` as `资源名称`,
                            {category_field} as `类别`,
                            {unit_field} as `资源单位`,
                            AVG({unit_price_field}) as `平均单价`,
                            MIN({unit_price_field}) as `最低单价`,
                            MAX({unit_price_field}) as `最高单价`,
                            COUNT(*) as `使用次数`
                        FROM `{resource_table}`
                        WHERE `{resource_code_field}` IS NOT NULL AND `{resource_code_field}` != ''
                        GROUP BY `{resource_code_field}`, `{resource_name_field}`, {category_field}, {unit_field}
                        ORDER BY `{resource_code_field}`
                    """
                elif self.db_type == 'postgresql':
                    # 使用SQLAlchemy连接来避免pandas警告
                    try:
                        from sqlalchemy import create_engine, text

                        # 构建SQLAlchemy连接字符串
                        host = self.connection_config.get('host', 'localhost')
                        port = self.connection_config.get('port', 5432)
                        database = self.connection_config.get('database', 'postgres')
                        username = self.connection_config.get('username', 'postgres')
                        password = self.connection_config.get('password', '')

                        engine = create_engine(f'postgresql://{username}:{password}@{host}:{port}/{database}')

                        query = f"""
                            SELECT
                                "{resource_code_field}" as "资源编号",
                                "{resource_name_field}" as "资源名称",
                                {category_field} as "类别",
                                {unit_field} as "资源单位",
                                AVG({unit_price_field}) as "平均单价",
                                MIN({unit_price_field}) as "最低单价",
                                MAX({unit_price_field}) as "最高单价",
                                COUNT(*) as "使用次数"
                            FROM "{resource_table}"
                            WHERE "{resource_code_field}" IS NOT NULL AND "{resource_code_field}" != ''
                            GROUP BY "{resource_code_field}", "{resource_name_field}", {category_field}, {unit_field}
                            ORDER BY "{resource_code_field}"
                        """

                        # 使用text()包装查询并手动构建DataFrame
                        with engine.connect() as conn:
                            result = conn.execute(text(query))
                            columns = result.keys()
                            rows = []
                            for row in result:
                                # 将immutabledict转换为普通字典
                                row_dict = dict(row._mapping) if hasattr(row, '_mapping') else dict(row)
                                rows.append(row_dict)

                            if rows:
                                df = pd.DataFrame(rows, columns=columns)
                            else:
                                df = pd.DataFrame()

                        engine.dispose()

                    except ImportError:
                        # 如果没有SQLAlchemy，使用原始连接（会有警告但能工作）
                        query = f"""
                            SELECT
                                "{resource_code_field}" as "资源编号",
                                "{resource_name_field}" as "资源名称",
                                {category_field} as "类别",
                                {unit_field} as "资源单位",
                                AVG({unit_price_field}) as "平均单价",
                                MIN({unit_price_field}) as "最低单价",
                                MAX({unit_price_field}) as "最高单价",
                                COUNT(*) as "使用次数"
                            FROM "{resource_table}"
                            WHERE "{resource_code_field}" IS NOT NULL AND "{resource_code_field}" != ''
                            GROUP BY "{resource_code_field}", "{resource_name_field}", {category_field}, {unit_field}
                            ORDER BY "{resource_code_field}"
                        """

                        df = pd.read_sql_query(query, self.connection)
                    except Exception as e:
                        print(f"❌ SQLAlchemy查询失败: {str(e)}")
                        # 回退到原始连接
                        query = f"""
                            SELECT
                                "{resource_code_field}" as "资源编号",
                                "{resource_name_field}" as "资源名称",
                                {category_field} as "类别",
                                {unit_field} as "资源单位",
                                AVG({unit_price_field}) as "平均单价",
                                MIN({unit_price_field}) as "最低单价",
                                MAX({unit_price_field}) as "最高单价",
                                COUNT(*) as "使用次数"
                            FROM "{resource_table}"
                            WHERE "{resource_code_field}" IS NOT NULL AND "{resource_code_field}" != ''
                            GROUP BY "{resource_code_field}", "{resource_name_field}", {category_field}, {unit_field}
                            ORDER BY "{resource_code_field}"
                        """

                        df = pd.read_sql_query(query, self.connection)

                else:  # MySQL
                    query = f"""
                        SELECT
                            `{resource_code_field}` as `资源编号`,
                            `{resource_name_field}` as `资源名称`,
                            {category_field} as `类别`,
                            {unit_field} as `资源单位`,
                            AVG({unit_price_field}) as `平均单价`,
                            MIN({unit_price_field}) as `最低单价`,
                            MAX({unit_price_field}) as `最高单价`,
                            COUNT(*) as `使用次数`
                        FROM `{resource_table}`
                        WHERE `{resource_code_field}` IS NOT NULL AND `{resource_code_field}` != ''
                        GROUP BY `{resource_code_field}`, `{resource_name_field}`, {category_field}, {unit_field}
                        ORDER BY `{resource_code_field}`
                    """

                    df = pd.read_sql_query(query, self.connection)

            elif self.db_type == 'mongodb':
                database_name = self.connection_config.get('database', 'test')
                db = self.connection[database_name]
                collection = db[resource_table]

                # MongoDB聚合查询
                pipeline = [
                    {
                        "$match": {
                            "资源编号": {"$exists": True, "$ne": ""}
                        }
                    },
                    {
                        "$group": {
                            "_id": {
                                "资源编号": "$资源编号",
                                "资源名称": "$资源名称",
                                "类别": "$类别",
                                "资源单位": "$资源单位"
                            },
                            "平均单价": {"$avg": "$单价"},
                            "最低单价": {"$min": "$单价"},
                            "最高单价": {"$max": "$单价"},
                            "使用次数": {"$sum": 1}
                        }
                    },
                    {
                        "$project": {
                            "_id": 0,
                            "资源编号": "$_id.资源编号",
                            "资源名称": "$_id.资源名称",
                            "类别": "$_id.类别",
                            "资源单位": "$_id.资源单位",
                            "平均单价": 1,
                            "最低单价": 1,
                            "最高单价": 1,
                            "使用次数": 1
                        }
                    },
                    {
                        "$sort": {"资源编号": 1}
                    }
                ]

                documents = list(collection.aggregate(pipeline))
                df = pd.DataFrame(documents) if documents else pd.DataFrame()

            return True, f"找到 {len(df)} 个唯一资源项", df

        except Exception as e:
            return False, f"获取唯一资源失败: {str(e)}", pd.DataFrame()

    def update_resource_price(self, resource_code: str, new_price: float) -> Tuple[bool, str]:
        """更新资源单价"""
        try:
            if not self.connection:
                return False, "未连接到数据库"

            # 自动识别资源表
            schema_success, schema_msg, schema_info = self.get_database_schema()
            if not schema_success:
                return False, schema_msg

            # 查找包含资源数据的表
            resource_tables = []
            for table in schema_info['tables']:
                table_name_lower = table['name'].lower()
                if any(keyword in table_name_lower for keyword in ['child', 'resource', '资源']):
                    resource_tables.append(table['name'])

            if not resource_tables:
                return False, "未找到资源数据表"

            resource_table = resource_tables[0]

            # 智能检测字段名
            field_mapping = self._detect_field_names(resource_table, schema_info)

            # 检查必要字段是否存在
            if not field_mapping['resource_code']:
                return False, "未找到资源编号字段"

            if not field_mapping['unit_price']:
                return False, "未找到单价字段"

            resource_code_field = field_mapping['resource_code']
            unit_price_field = field_mapping['unit_price']
            quantity_field = field_mapping['quantity']
            total_price_field = field_mapping['total_price']

            updated_count = 0

            if self.db_type in ['sqlite', 'postgresql', 'mysql']:
                cursor = self.connection.cursor()

                if self.db_type == 'sqlite':
                    # 更新单价
                    cursor.execute(f"""
                        UPDATE `{resource_table}`
                        SET `{unit_price_field}` = ?
                        WHERE `{resource_code_field}` = ?
                    """, (new_price, resource_code))

                    updated_count = cursor.rowcount

                    # 重新计算合价（如果字段存在）
                    if quantity_field and total_price_field:
                        cursor.execute(f"""
                            UPDATE `{resource_table}`
                            SET `{total_price_field}` = `{quantity_field}` * `{unit_price_field}`
                            WHERE `{resource_code_field}` = ?
                        """, (resource_code,))

                elif self.db_type == 'postgresql':
                    # 更新单价
                    cursor.execute(f"""
                        UPDATE "{resource_table}"
                        SET "{unit_price_field}" = %s
                        WHERE "{resource_code_field}" = %s
                    """, (new_price, resource_code))

                    updated_count = cursor.rowcount

                    # 重新计算合价（如果字段存在）
                    if quantity_field and total_price_field:
                        cursor.execute(f"""
                            UPDATE "{resource_table}"
                            SET "{total_price_field}" = "{quantity_field}" * "{unit_price_field}"
                            WHERE "{resource_code_field}" = %s
                        """, (resource_code,))

                else:  # MySQL
                    # 更新单价
                    cursor.execute(f"""
                        UPDATE `{resource_table}`
                        SET `{unit_price_field}` = %s
                        WHERE `{resource_code_field}` = %s
                    """, (new_price, resource_code))

                    updated_count = cursor.rowcount

                    # 重新计算合价（如果字段存在）
                    if quantity_field and total_price_field:
                        cursor.execute(f"""
                            UPDATE `{resource_table}`
                            SET `{total_price_field}` = `{quantity_field}` * `{unit_price_field}`
                            WHERE `{resource_code_field}` = %s
                        """, (resource_code,))

                self.connection.commit()

            elif self.db_type == 'mongodb':
                database_name = self.connection_config.get('database', 'test')
                db = self.connection[database_name]
                collection = db[resource_table]

                # 更新单价
                result = collection.update_many(
                    {"资源编号": resource_code},
                    {
                        "$set": {"单价": new_price},
                        "$mul": {"合价": 0}  # 先清零
                    }
                )

                updated_count = result.modified_count

                # 重新计算合价
                collection.update_many(
                    {"资源编号": resource_code},
                    [
                        {
                            "$set": {
                                "合价": {"$multiply": ["$消耗量", "$单价"]}
                            }
                        }
                    ]
                )

            return True, f"✅ 成功更新 {updated_count} 条资源记录的价格"

        except Exception as e:
            return False, f"更新资源价格失败: {str(e)}"

    def recalculate_quota_prices(self) -> Tuple[bool, str]:
        """重新计算所有定额项的价格"""
        try:
            if not self.connection:
                return False, "未连接到数据库"

            # 获取数据库结构
            schema_success, schema_msg, schema_info = self.get_database_schema()
            if not schema_success:
                return False, schema_msg

            # 查找定额表和资源表
            quota_table = None
            resource_table = None

            for table in schema_info['tables']:
                table_name_lower = table['name'].lower()
                if any(keyword in table_name_lower for keyword in ['parent', 'quota', '定额']):
                    quota_table = table['name']
                elif any(keyword in table_name_lower for keyword in ['child', 'resource', '资源']):
                    resource_table = table['name']

            if not quota_table or not resource_table:
                return False, "未找到完整的定额表和资源表"

            updated_count = 0

            if self.db_type in ['sqlite', 'postgresql', 'mysql']:
                cursor = self.connection.cursor()

                # 获取所有定额编号
                if self.db_type == 'sqlite':
                    cursor.execute(f"SELECT DISTINCT `编号` FROM `{quota_table}` WHERE `编号` IS NOT NULL")
                elif self.db_type == 'postgresql':
                    cursor.execute(f'SELECT DISTINCT "编号" FROM "{quota_table}" WHERE "编号" IS NOT NULL')
                else:  # MySQL
                    cursor.execute(f"SELECT DISTINCT `编号` FROM `{quota_table}` WHERE `编号` IS NOT NULL")

                quota_codes = [row[0] for row in cursor.fetchall()]

                for quota_code in quota_codes:
                    # 计算该定额项的总价
                    if self.db_type == 'sqlite':
                        cursor.execute(f"""
                            SELECT SUM(`合价`) FROM `{resource_table}`
                            WHERE `定额编号` = ?
                        """, (quota_code,))
                    elif self.db_type == 'postgresql':
                        cursor.execute(f"""
                            SELECT SUM("合价") FROM "{resource_table}"
                            WHERE "定额编号" = %s
                        """, (quota_code,))
                    else:  # MySQL
                        cursor.execute(f"""
                            SELECT SUM(`合价`) FROM `{resource_table}`
                            WHERE `定额编号` = %s
                        """, (quota_code,))

                    result = cursor.fetchone()
                    total_price = result[0] if result and result[0] else 0

                    # 获取定额表的字段映射
                    quota_field_mapping = self._detect_field_names(quota_table, schema_info)

                    # 查找价格字段（可能的字段名）
                    price_field = None
                    for table in schema_info['tables']:
                        if table['name'] == quota_table:
                            for col in table['columns']:
                                col_lower = col['name'].lower()
                                if any(keyword in col_lower for keyword in ['综合单价', '单价', '合计', '总价', 'total_price', 'price']):
                                    price_field = col['name']
                                    break
                            break

                    if not price_field:
                        print(f"⚠️ 未找到价格字段，跳过定额项 {quota_code}")
                        continue

                    # 查找编号字段
                    code_field = None
                    for table in schema_info['tables']:
                        if table['name'] == quota_table:
                            for col in table['columns']:
                                col_lower = col['name'].lower()
                                if any(keyword in col_lower for keyword in ['编号', 'code']) and '资源' not in col_lower:
                                    code_field = col['name']
                                    break
                            break

                    if not code_field:
                        print(f"⚠️ 未找到编号字段，跳过定额项 {quota_code}")
                        continue

                    # 更新定额项总价
                    if self.db_type == 'sqlite':
                        cursor.execute(f"""
                            UPDATE `{quota_table}`
                            SET `{price_field}` = ?
                            WHERE `{code_field}` = ?
                        """, (total_price, quota_code))
                    elif self.db_type == 'postgresql':
                        cursor.execute(f"""
                            UPDATE "{quota_table}"
                            SET "{price_field}" = %s
                            WHERE "{code_field}" = %s
                        """, (total_price, quota_code))
                    else:  # MySQL
                        cursor.execute(f"""
                            UPDATE `{quota_table}`
                            SET `{price_field}` = %s
                            WHERE `{code_field}` = %s
                        """, (total_price, quota_code))

                    if cursor.rowcount > 0:
                        updated_count += 1

                self.connection.commit()

            elif self.db_type == 'mongodb':
                database_name = self.connection_config.get('database', 'test')
                db = self.connection[database_name]
                quota_collection = db[quota_table]
                resource_collection = db[resource_table]

                # 获取所有定额编号
                quota_codes = resource_collection.distinct("定额编号")

                for quota_code in quota_codes:
                    # 计算该定额项的总价
                    pipeline = [
                        {"$match": {"定额编号": quota_code}},
                        {"$group": {"_id": None, "total": {"$sum": "$合价"}}}
                    ]

                    result = list(resource_collection.aggregate(pipeline))
                    total_price = result[0]['total'] if result else 0

                    # 更新定额项总价
                    update_result = quota_collection.update_many(
                        {"编号": quota_code},
                        {"$set": {"综合单价": total_price}}
                    )

                    updated_count += update_result.modified_count

            return True, f"✅ 成功重新计算 {updated_count} 个定额项的价格"

        except Exception as e:
            error_msg = f"重新计算定额价格失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def create_price_info_tables(self) -> Tuple[bool, str]:
        """创建信息价数据表"""
        try:
            if not self.connection:
                return False, "未连接到数据库"

            if self.db_type == 'sqlite':
                cursor = self.connection.cursor()

                # 创建信息价主表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS price_info (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        page_header TEXT,
                        chapter_code TEXT,
                        chapter_name TEXT,
                        resource_code TEXT,
                        product_name TEXT,
                        specifications TEXT,
                        unit TEXT,
                        price_with_tax REAL,
                        price_without_tax REAL,
                        remarks TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(resource_code, chapter_code)
                    )
                """)

                # 创建索引
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_price_info_resource_code ON price_info(resource_code)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_price_info_chapter ON price_info(chapter_code)")

                self.connection.commit()

            elif self.db_type == 'mysql':
                cursor = self.connection.cursor()

                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS price_info (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        page_header TEXT,
                        chapter_code VARCHAR(50),
                        chapter_name TEXT,
                        resource_code VARCHAR(50),
                        product_name TEXT,
                        specifications TEXT,
                        unit VARCHAR(20),
                        price_with_tax DECIMAL(10,2),
                        price_without_tax DECIMAL(10,2),
                        remarks TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE KEY unique_resource_chapter (resource_code, chapter_code)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                """)

                self.connection.commit()

            elif self.db_type == 'postgresql':
                cursor = self.connection.cursor()

                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS price_info (
                        id SERIAL PRIMARY KEY,
                        page_header TEXT,
                        chapter_code VARCHAR(50),
                        chapter_name TEXT,
                        resource_code VARCHAR(50),
                        product_name TEXT,
                        specifications TEXT,
                        unit VARCHAR(20),
                        price_with_tax DECIMAL(10,2),
                        price_without_tax DECIMAL(10,2),
                        remarks TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(resource_code, chapter_code)
                    )
                """)

                cursor.execute("CREATE INDEX IF NOT EXISTS idx_price_info_resource_code ON price_info(resource_code)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_price_info_chapter ON price_info(chapter_code)")

                self.connection.commit()

            elif self.db_type == 'mongodb':
                database_name = self.connection_config.get('database', 'test')
                db = self.connection[database_name]

                # MongoDB会自动创建集合，我们只需要创建索引
                price_collection = db['price_info']
                price_collection.create_index([("resource_code", 1), ("chapter_code", 1)], unique=True)
                price_collection.create_index("resource_code")
                price_collection.create_index("chapter_code")

            return True, "✅ 信息价数据表创建成功"

        except Exception as e:
            error_msg = f"创建信息价数据表失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def import_price_info_data(self, csv_file_path: str) -> Tuple[bool, str]:
        """导入信息价数据到数据库"""
        try:
            if not self.connection:
                return False, "未连接到数据库"

            # 首先创建表（如果不存在）
            table_success, table_msg = self.create_price_info_tables()
            if not table_success:
                return False, f"创建表失败: {table_msg}"

            # 读取CSV数据
            df = pd.read_csv(csv_file_path, encoding='utf-8-sig')

            if df.empty:
                return False, "CSV文件为空"

            # 验证必需的列
            required_columns = ['资源编号', '产品名称', '计量单位']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                return False, f"CSV文件缺少必需的列: {missing_columns}"

            imported_count = 0
            updated_count = 0
            error_count = 0

            if self.db_type in ['sqlite', 'mysql', 'postgresql']:
                cursor = self.connection.cursor()

                for _, row in df.iterrows():
                    try:
                        # 准备数据
                        data = {
                            'page_header': str(row.get('页眉标识', '')),
                            'chapter_code': str(row.get('章节编号', '')),
                            'chapter_name': str(row.get('章节名称', '')),
                            'resource_code': str(row.get('资源编号', '')),
                            'product_name': str(row.get('产品名称', '')),
                            'specifications': str(row.get('规格型号及特征', '')),
                            'unit': str(row.get('计量单位', '')),
                            'price_with_tax': self._safe_float(row.get('市场参考价（含税）', 0)),
                            'price_without_tax': self._safe_float(row.get('市场参考价（不含税）', 0)),
                            'remarks': str(row.get('备注', ''))
                        }

                        # 检查是否已存在
                        if self.db_type == 'sqlite':
                            cursor.execute(
                                "SELECT id FROM price_info WHERE resource_code = ? AND chapter_code = ?",
                                (data['resource_code'], data['chapter_code'])
                            )
                        elif self.db_type == 'mysql':
                            cursor.execute(
                                "SELECT id FROM price_info WHERE resource_code = %s AND chapter_code = %s",
                                (data['resource_code'], data['chapter_code'])
                            )
                        elif self.db_type == 'postgresql':
                            cursor.execute(
                                "SELECT id FROM price_info WHERE resource_code = %s AND chapter_code = %s",
                                (data['resource_code'], data['chapter_code'])
                            )

                        existing = cursor.fetchone()

                        if existing:
                            # 更新现有记录
                            if self.db_type == 'sqlite':
                                cursor.execute("""
                                    UPDATE price_info SET
                                    page_header = ?, chapter_name = ?, product_name = ?,
                                    specifications = ?, unit = ?, price_with_tax = ?,
                                    price_without_tax = ?, remarks = ?
                                    WHERE resource_code = ? AND chapter_code = ?
                                """, (
                                    data['page_header'], data['chapter_name'], data['product_name'],
                                    data['specifications'], data['unit'], data['price_with_tax'],
                                    data['price_without_tax'], data['remarks'],
                                    data['resource_code'], data['chapter_code']
                                ))
                            else:  # MySQL, PostgreSQL
                                cursor.execute("""
                                    UPDATE price_info SET
                                    page_header = %s, chapter_name = %s, product_name = %s,
                                    specifications = %s, unit = %s, price_with_tax = %s,
                                    price_without_tax = %s, remarks = %s
                                    WHERE resource_code = %s AND chapter_code = %s
                                """, (
                                    data['page_header'], data['chapter_name'], data['product_name'],
                                    data['specifications'], data['unit'], data['price_with_tax'],
                                    data['price_without_tax'], data['remarks'],
                                    data['resource_code'], data['chapter_code']
                                ))
                            updated_count += 1
                        else:
                            # 插入新记录
                            if self.db_type == 'sqlite':
                                cursor.execute("""
                                    INSERT INTO price_info
                                    (page_header, chapter_code, chapter_name, resource_code, product_name,
                                     specifications, unit, price_with_tax, price_without_tax, remarks)
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                """, (
                                    data['page_header'], data['chapter_code'], data['chapter_name'],
                                    data['resource_code'], data['product_name'], data['specifications'],
                                    data['unit'], data['price_with_tax'], data['price_without_tax'], data['remarks']
                                ))
                            else:  # MySQL, PostgreSQL
                                cursor.execute("""
                                    INSERT INTO price_info
                                    (page_header, chapter_code, chapter_name, resource_code, product_name,
                                     specifications, unit, price_with_tax, price_without_tax, remarks)
                                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                                """, (
                                    data['page_header'], data['chapter_code'], data['chapter_name'],
                                    data['resource_code'], data['product_name'], data['specifications'],
                                    data['unit'], data['price_with_tax'], data['price_without_tax'], data['remarks']
                                ))
                            imported_count += 1

                    except Exception as e:
                        self.logger.error(f"导入行数据失败: {str(e)}")
                        error_count += 1
                        continue

                self.connection.commit()

            elif self.db_type == 'mongodb':
                database_name = self.connection_config.get('database', 'test')
                db = self.connection[database_name]
                collection = db['price_info']

                for _, row in df.iterrows():
                    try:
                        data = {
                            'page_header': str(row.get('页眉标识', '')),
                            'chapter_code': str(row.get('章节编号', '')),
                            'chapter_name': str(row.get('章节名称', '')),
                            'resource_code': str(row.get('资源编号', '')),
                            'product_name': str(row.get('产品名称', '')),
                            'specifications': str(row.get('规格型号及特征', '')),
                            'unit': str(row.get('计量单位', '')),
                            'price_with_tax': self._safe_float(row.get('市场参考价（含税）', 0)),
                            'price_without_tax': self._safe_float(row.get('市场参考价（不含税）', 0)),
                            'remarks': str(row.get('备注', '')),
                            'created_at': datetime.now()
                        }

                        # 使用upsert操作
                        result = collection.replace_one(
                            {
                                'resource_code': data['resource_code'],
                                'chapter_code': data['chapter_code']
                            },
                            data,
                            upsert=True
                        )

                        if result.upserted_id:
                            imported_count += 1
                        elif result.modified_count > 0:
                            updated_count += 1

                    except Exception as e:
                        self.logger.error(f"导入行数据失败: {str(e)}")
                        error_count += 1
                        continue

            success_msg = f"✅ 信息价数据导入完成！\n"
            success_msg += f"📊 导入统计:\n"
            success_msg += f"   - 新增记录: {imported_count}\n"
            success_msg += f"   - 更新记录: {updated_count}\n"
            if error_count > 0:
                success_msg += f"   - 错误记录: {error_count}\n"

            return True, success_msg

        except Exception as e:
            error_msg = f"导入信息价数据失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def _safe_float(self, value) -> float:
        """安全转换为浮点数"""
        try:
            if pd.isna(value) or value == '' or value is None:
                return 0.0
            return float(str(value).replace(',', ''))
        except (ValueError, TypeError):
            return 0.0

        except Exception as e:
            return False, f"重新计算定额价格失败: {str(e)}"
