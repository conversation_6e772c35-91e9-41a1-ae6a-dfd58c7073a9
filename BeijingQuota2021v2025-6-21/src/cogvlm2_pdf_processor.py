#!/usr/bin/env python3
"""
CogVLM2优化的PDF预处理器
基于RTX 5090 D + PyTorch 2.9.0 + CUDA 12.8技术突破
专门为CogVLM2模型优化图片预处理
"""

import os
import uuid
import asyncio
from pathlib import Path
from typing import List, Optional, Tuple
import tempfile

from pdf2image import convert_from_path
from PIL import Image, ImageEnhance, ImageFilter
import cv2
import numpy as np

from .config import Config
from .log_manager import get_logger

class CogVLM2PDFProcessor:
    """CogVLM2优化的PDF处理器"""
    
    def __init__(self):
        self.logger = get_logger('CogVLM2PDFProcessor')
        self.config = Config()
        
        # CogVLM2模型的最佳配置
        self.cogvlm2_config = {
            'target_size': (1344, 1344),  # CogVLM2的最佳输入尺寸
            'dpi': 200,  # 高质量DPI设置
            'image_format': 'PNG',  # 无损格式
            'quality': 95,  # 高质量设置
            'color_mode': 'RGB',  # RGB颜色模式
        }
        
        # 设置Poppler路径
        self.poppler_path = r"C:\poppler\Library\bin"
        if os.path.exists(self.poppler_path):
            current_path = os.environ.get('PATH', '')
            if self.poppler_path not in current_path:
                os.environ['PATH'] = current_path + os.pathsep + self.poppler_path
        
        self.logger.info("🎯 CogVLM2优化PDF处理器初始化完成")
        self.logger.info(f"📐 目标尺寸: {self.cogvlm2_config['target_size']}")
        self.logger.info(f"🔧 DPI设置: {self.cogvlm2_config['dpi']}")
    
    async def extract_pages_for_cogvlm2(
        self, 
        pdf_path: str, 
        start_page: int, 
        end_page: int,
        enhance_quality: bool = True
    ) -> List[str]:
        """
        为CogVLM2提取并优化PDF页面
        
        Args:
            pdf_path: PDF文件路径
            start_page: 起始页码 (1-based)
            end_page: 结束页码 (1-based)
            enhance_quality: 是否进行质量增强
            
        Returns:
            List[str]: 优化后的图片文件路径列表
        """
        try:
            self.logger.info(f"🔄 开始为CogVLM2提取PDF页面: {pdf_path}")
            self.logger.info(f"📄 页面范围: {start_page}-{end_page}")
            
            # 使用高DPI转换PDF页面
            images = convert_from_path(
                pdf_path,
                dpi=self.cogvlm2_config['dpi'],
                first_page=start_page,
                last_page=end_page,
                fmt='PNG',
                poppler_path=self.poppler_path if os.path.exists(self.poppler_path) else None
            )
            
            optimized_image_paths = []
            
            for i, image in enumerate(images):
                page_num = start_page + i
                self.logger.info(f"🖼️ 处理第{page_num}页...")
                
                # 转换为RGB模式
                if image.mode != self.cogvlm2_config['color_mode']:
                    image = image.convert(self.cogvlm2_config['color_mode'])
                
                # 质量增强
                if enhance_quality:
                    image = await self._enhance_image_quality(image)
                
                # 调整到CogVLM2最佳尺寸
                image = await self._resize_for_cogvlm2(image)
                
                # 生成优化的文件名
                filename = f"cogvlm2_page_{page_num}_{uuid.uuid4().hex[:8]}.png"
                image_path = self.config.get_temp_file_path(filename)
                
                # 保存高质量图片
                image.save(
                    image_path, 
                    self.cogvlm2_config['image_format'],
                    quality=self.cogvlm2_config['quality'],
                    optimize=True
                )
                
                optimized_image_paths.append(str(image_path))
                self.logger.info(f"✅ 第{page_num}页优化完成: {image.size}")
            
            self.logger.info(f"🎉 CogVLM2图片预处理完成，共处理{len(optimized_image_paths)}页")
            return optimized_image_paths
            
        except Exception as e:
            self.logger.error(f"❌ CogVLM2 PDF预处理失败: {str(e)}")
            raise Exception(f"CogVLM2 PDF预处理失败: {str(e)}")
    
    async def _enhance_image_quality(self, image: Image.Image) -> Image.Image:
        """增强图片质量以提高CogVLM2识别效果"""
        try:
            # 1. 锐化处理
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.2)  # 轻微锐化
            
            # 2. 对比度增强
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.1)  # 轻微增强对比度
            
            # 3. 亮度调整
            enhancer = ImageEnhance.Brightness(image)
            image = enhancer.enhance(1.05)  # 轻微提亮
            
            # 4. 去噪处理（使用OpenCV）
            image_array = np.array(image)
            denoised = cv2.fastNlMeansDenoisingColored(image_array, None, 10, 10, 7, 21)
            image = Image.fromarray(denoised)
            
            return image
            
        except Exception as e:
            self.logger.warning(f"⚠️ 图片质量增强失败，使用原图: {e}")
            return image
    
    async def _resize_for_cogvlm2(self, image: Image.Image) -> Image.Image:
        """调整图片尺寸以适配CogVLM2模型"""
        try:
            target_width, target_height = self.cogvlm2_config['target_size']
            
            # 计算缩放比例，保持宽高比
            width_ratio = target_width / image.width
            height_ratio = target_height / image.height
            scale_ratio = min(width_ratio, height_ratio)
            
            # 计算新尺寸
            new_width = int(image.width * scale_ratio)
            new_height = int(image.height * scale_ratio)
            
            # 高质量缩放
            resized_image = image.resize(
                (new_width, new_height), 
                Image.Resampling.LANCZOS
            )
            
            # 创建1344x1344的白色背景
            background = Image.new(
                self.cogvlm2_config['color_mode'], 
                self.cogvlm2_config['target_size'], 
                'white'
            )
            
            # 居中放置缩放后的图片
            x_offset = (target_width - new_width) // 2
            y_offset = (target_height - new_height) // 2
            background.paste(resized_image, (x_offset, y_offset))
            
            return background
            
        except Exception as e:
            self.logger.error(f"❌ 图片尺寸调整失败: {e}")
            # 如果调整失败，直接缩放到目标尺寸
            return image.resize(self.cogvlm2_config['target_size'], Image.Resampling.LANCZOS)
    
    async def detect_table_regions_optimized(self, image_path: str) -> List[Tuple[int, int, int, int]]:
        """
        优化的表格区域检测，专门为定额表格优化
        
        Returns:
            List[Tuple[int, int, int, int]]: 表格区域坐标列表 [(x, y, w, h), ...]
        """
        try:
            # 读取图片
            image = cv2.imread(image_path)
            if image is None:
                return []
            
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 自适应二值化
            binary = cv2.adaptiveThreshold(
                gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 11, 2
            )
            
            # 检测水平线（定额表格的特征）
            horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (50, 1))
            horizontal_lines = cv2.morphologyEx(binary, cv2.MORPH_OPEN, horizontal_kernel)
            
            # 检测垂直线
            vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 30))
            vertical_lines = cv2.morphologyEx(binary, cv2.MORPH_OPEN, vertical_kernel)
            
            # 合并水平线和垂直线
            table_structure = cv2.addWeighted(horizontal_lines, 0.5, vertical_lines, 0.5, 0.0)
            
            # 查找轮廓
            contours, _ = cv2.findContours(table_structure, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 筛选表格区域
            table_regions = []
            min_area = 10000  # 最小表格面积
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > min_area:
                    x, y, w, h = cv2.boundingRect(contour)
                    # 过滤掉过小或过窄的区域
                    if w > 200 and h > 100 and w/h > 1.5:
                        table_regions.append((x, y, w, h))
            
            self.logger.info(f"🔍 检测到{len(table_regions)}个表格区域")
            return table_regions
            
        except Exception as e:
            self.logger.error(f"❌ 表格区域检测失败: {e}")
            return []
    
    def get_optimization_stats(self) -> dict:
        """获取优化统计信息"""
        return {
            'target_size': self.cogvlm2_config['target_size'],
            'dpi': self.cogvlm2_config['dpi'],
            'format': self.cogvlm2_config['image_format'],
            'quality': self.cogvlm2_config['quality'],
            'color_mode': self.cogvlm2_config['color_mode'],
            'rtx5090d_optimized': True,
            'pytorch_version': '2.9.0.dev20250629+cu128',
            'cuda_version': '12.8'
        }
    
    async def batch_process_for_cogvlm2(
        self, 
        pdf_paths: List[str], 
        page_ranges: List[Tuple[int, int]] = None
    ) -> List[List[str]]:
        """
        批量处理多个PDF文件
        
        Args:
            pdf_paths: PDF文件路径列表
            page_ranges: 页面范围列表，如果为None则处理全部页面
            
        Returns:
            List[List[str]]: 每个PDF对应的优化图片路径列表
        """
        try:
            results = []
            
            for i, pdf_path in enumerate(pdf_paths):
                if page_ranges and i < len(page_ranges):
                    start_page, end_page = page_ranges[i]
                else:
                    # 如果没有指定范围，处理前10页
                    start_page, end_page = 1, 10
                
                self.logger.info(f"🔄 批量处理PDF {i+1}/{len(pdf_paths)}: {pdf_path}")
                
                try:
                    optimized_images = await self.extract_pages_for_cogvlm2(
                        pdf_path, start_page, end_page
                    )
                    results.append(optimized_images)
                    
                except Exception as e:
                    self.logger.error(f"❌ 处理PDF失败 {pdf_path}: {e}")
                    results.append([])
            
            self.logger.info(f"🎉 批量处理完成，共处理{len(pdf_paths)}个PDF文件")
            return results
            
        except Exception as e:
            self.logger.error(f"❌ 批量处理失败: {e}")
            return []
