#!/usr/bin/env python3
"""
浏览器自动化模块 - MCP服务器2
负责与DeepSeek网站交互，上传图片并获取识别结果
"""

import asyncio
import time
import json
from typing import Dict, Any, Optional
from pathlib import Path

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

from .config import Config

class BrowserAutomation:
    """浏览器自动化处理器"""
    
    def __init__(self):
        self.config = Config()
        self.driver = None
        self.wait = None
    
    async def initialize_browser(self):
        """初始化浏览器"""
        try:
            chrome_options = Options()

            if self.config.BROWSER_HEADLESS:
                chrome_options.add_argument("--headless")

            # 添加其他选项
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")

            # 禁用图片加载以提高速度（除了我们要上传的图片）
            prefs = {
                "profile.managed_default_content_settings.images": 2
            }
            chrome_options.add_experimental_option("prefs", prefs)

            # 使用webdriver-manager自动管理ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.wait = WebDriverWait(self.driver, self.config.BROWSER_TIMEOUT)

            print("浏览器初始化成功")

        except Exception as e:
            raise Exception(f"浏览器初始化失败: {str(e)}")
    
    async def navigate_to_deepseek(self):
        """导航到DeepSeek网站"""
        try:
            self.driver.get(self.config.DEEPSEEK_URL)
            
            # 等待页面加载
            await asyncio.sleep(2)
            
            print("已导航到DeepSeek网站")
            
        except Exception as e:
            raise Exception(f"导航到DeepSeek失败: {str(e)}")
    
    async def upload_image(self, image_path: str) -> bool:
        """
        上传图片到DeepSeek
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            bool: 上传是否成功
        """
        try:
            # 查找文件上传按钮或拖拽区域
            # 这里需要根据DeepSeek网站的实际DOM结构来调整选择器
            
            # 方法1: 查找文件输入框
            try:
                file_input = self.wait.until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='file']"))
                )
                file_input.send_keys(str(Path(image_path).absolute()))
                print(f"通过文件输入框上传图片: {image_path}")
                return True
                
            except TimeoutException:
                pass
            
            # 方法2: 查找上传按钮
            try:
                upload_button = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), '上传') or contains(text(), 'Upload')]"))
                )
                upload_button.click()
                
                # 等待文件选择对话框，然后发送文件路径
                await asyncio.sleep(1)
                file_input = self.driver.find_element(By.CSS_SELECTOR, "input[type='file']")
                file_input.send_keys(str(Path(image_path).absolute()))
                print(f"通过上传按钮上传图片: {image_path}")
                return True
                
            except (TimeoutException, NoSuchElementException):
                pass
            
            # 方法3: 拖拽上传
            try:
                drop_zone = self.wait.until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "[data-testid='drop-zone'], .drop-zone, .upload-area"))
                )
                
                # 使用JavaScript模拟文件拖拽
                with open(image_path, 'rb') as f:
                    file_content = f.read()
                
                js_script = """
                var dropZone = arguments[0];
                var file = new File([arguments[1]], arguments[2], {type: 'image/png'});
                var dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);
                
                var dropEvent = new DragEvent('drop', {
                    dataTransfer: dataTransfer,
                    bubbles: true,
                    cancelable: true
                });
                
                dropZone.dispatchEvent(dropEvent);
                """
                
                self.driver.execute_script(js_script, drop_zone, file_content, Path(image_path).name)
                print(f"通过拖拽上传图片: {image_path}")
                return True
                
            except (TimeoutException, NoSuchElementException):
                pass
            
            print("未找到合适的上传方式")
            return False
            
        except Exception as e:
            print(f"图片上传失败: {str(e)}")
            return False
    
    async def send_extraction_prompt(self) -> bool:
        """
        发送定额信息提取指令
        
        Returns:
            bool: 发送是否成功
        """
        try:
            # 查找文本输入框
            text_input = self.wait.until(
                EC.presence_of_element_located((
                    By.CSS_SELECTOR, 
                    "textarea, input[type='text'], .chat-input, [contenteditable='true']"
                ))
            )
            
            # 清空输入框并输入提取指令
            text_input.clear()
            text_input.send_keys(self.config.QUOTA_EXTRACTION_PROMPT)
            
            # 查找发送按钮
            send_button = self.wait.until(
                EC.element_to_be_clickable((
                    By.XPATH, 
                    "//button[contains(text(), '发送') or contains(text(), 'Send') or contains(@aria-label, 'Send')]"
                ))
            )
            
            send_button.click()
            print("已发送定额信息提取指令")
            return True
            
        except Exception as e:
            print(f"发送提取指令失败: {str(e)}")
            return False
    
    async def wait_for_response(self, timeout: int = 60) -> Optional[str]:
        """
        等待AI响应
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            Optional[str]: AI响应内容
        """
        try:
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                try:
                    # 查找最新的AI响应
                    # 这里需要根据DeepSeek网站的实际DOM结构来调整选择器
                    response_elements = self.driver.find_elements(
                        By.CSS_SELECTOR, 
                        ".message.assistant, .ai-message, .response-message, [data-role='assistant']"
                    )
                    
                    if response_elements:
                        latest_response = response_elements[-1]
                        response_text = latest_response.text.strip()
                        
                        # 检查响应是否完整（不包含加载指示器）
                        if response_text and not any(indicator in response_text.lower() for indicator in 
                                                   ['正在思考', 'thinking', '加载中', 'loading', '...']):
                            print("获取到AI响应")
                            return response_text
                    
                    await asyncio.sleep(2)
                    
                except Exception as e:
                    print(f"检查响应时出错: {str(e)}")
                    await asyncio.sleep(2)
            
            print("等待AI响应超时")
            return None
            
        except Exception as e:
            print(f"等待响应失败: {str(e)}")
            return None
    
    async def process_image(self, image_path: str) -> Optional[str]:
        """
        处理单张图片：上传图片并获取识别结果
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            Optional[str]: 识别结果
        """
        try:
            # 初始化浏览器（如果还未初始化）
            if not self.driver:
                await self.initialize_browser()
                await self.navigate_to_deepseek()
            
            # 上传图片
            upload_success = await self.upload_image(image_path)
            if not upload_success:
                return None
            
            # 等待图片上传完成
            await asyncio.sleep(3)
            
            # 发送提取指令
            prompt_success = await self.send_extraction_prompt()
            if not prompt_success:
                return None
            
            # 等待AI响应
            response = await self.wait_for_response()
            return response
            
        except Exception as e:
            print(f"处理图片失败: {str(e)}")
            return None
    
    async def close_browser(self):
        """关闭浏览器"""
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
                print("浏览器已关闭")
        except Exception as e:
            print(f"关闭浏览器失败: {str(e)}")
    
    def __del__(self):
        """析构函数，确保浏览器被关闭"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
