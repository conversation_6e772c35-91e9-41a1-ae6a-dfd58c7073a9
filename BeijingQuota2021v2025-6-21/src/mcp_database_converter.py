#!/usr/bin/env python3
"""
MCP数据库转换工具
Multi-CSV Processing (MCP) Database Converter for converting CSV files to various database formats
"""

import os
import pandas as pd
import sqlite3
import re
from datetime import datetime
from typing import List, Dict, Optional, Tuple, Any
import logging

class MCPDatabaseConverter:
    """MCP数据库转换工具类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 支持的数据库格式
        self.supported_formats = {
            'sqlite': 'SQLite数据库文件',
            'mysql': 'MySQL SQL脚本',
            'postgresql': 'PostgreSQL SQL脚本',
            'sql_server': 'SQL Server SQL脚本',
            'oracle': 'Oracle SQL脚本',
            'mongodb': 'MongoDB JSON导出'
        }
        
        # 数据类型映射
        self.type_mappings = {
            'sqlite': {
                'int64': 'INTEGER',
                'float64': 'REAL',
                'object': 'TEXT',
                'bool': 'INTEGER',
                'datetime64[ns]': 'DATETIME'
            },
            'mysql': {
                'int64': 'BIGINT',  # MySQL 8.0推荐使用BIGINT
                'float64': 'DECIMAL(15,4)',  # 增加精度以支持更大的数值
                'object': 'VARCHAR(500)',  # 增加长度以支持更长的中文文本
                'bool': 'BOOLEAN',
                'datetime64[ns]': 'DATETIME(6)'  # MySQL 8.0支持微秒精度
            },
            'postgresql': {
                'int64': 'INTEGER',
                'float64': 'DECIMAL(10,2)',
                'object': 'VARCHAR(255)',
                'bool': 'BOOLEAN',
                'datetime64[ns]': 'TIMESTAMP'
            },
            'sql_server': {
                'int64': 'INT',
                'float64': 'DECIMAL(10,2)',
                'object': 'NVARCHAR(255)',
                'bool': 'BIT',
                'datetime64[ns]': 'DATETIME'
            },
            'oracle': {
                'int64': 'NUMBER',
                'float64': 'NUMBER(10,2)',
                'object': 'VARCHAR2(255)',
                'bool': 'NUMBER(1)',
                'datetime64[ns]': 'DATE'
            }
        }
    
    def analyze_csv_structure(self, csv_path: str) -> Dict[str, Any]:
        """分析CSV文件结构"""
        try:
            df = pd.read_csv(csv_path, encoding='utf-8-sig')
            
            # 分析数据类型
            column_info = {}
            for col in df.columns:
                dtype = str(df[col].dtype)
                sample_values = df[col].dropna().head(5).tolist()
                null_count = df[col].isnull().sum()
                unique_count = df[col].nunique()
                
                # 检测是否为主键候选
                is_primary_key = (
                    unique_count == len(df) and 
                    null_count == 0 and 
                    dtype in ['int64', 'object']
                )
                
                # 检测文本长度
                max_length = 50
                if dtype == 'object':
                    max_length = max(df[col].astype(str).str.len().max(), 50)
                    max_length = min(max_length, 1000)  # 限制最大长度
                
                column_info[col] = {
                    'dtype': dtype,
                    'max_length': max_length,
                    'null_count': null_count,
                    'unique_count': unique_count,
                    'is_primary_key': is_primary_key,
                    'sample_values': sample_values
                }
            
            return {
                'filename': os.path.basename(csv_path),
                'row_count': len(df),
                'column_count': len(df.columns),
                'columns': column_info,
                'dataframe': df
            }
            
        except Exception as e:
            self.logger.error(f"分析CSV文件失败 {csv_path}: {e}")
            return None
    
    def generate_table_name(self, filename: str) -> str:
        """生成合适的表名"""
        # 移除文件扩展名
        table_name = os.path.splitext(filename)[0]
        
        # 替换特殊字符
        table_name = re.sub(r'[^a-zA-Z0-9_]', '_', table_name)
        
        # 确保以字母开头
        if not table_name[0].isalpha():
            table_name = 'table_' + table_name
        
        return table_name.lower()
    
    def generate_create_table_sql(
        self, 
        structure: Dict[str, Any], 
        db_format: str,
        table_name: str = None
    ) -> str:
        """生成CREATE TABLE SQL语句"""
        
        if not table_name:
            table_name = self.generate_table_name(structure['filename'])
        
        type_mapping = self.type_mappings.get(db_format, self.type_mappings['sqlite'])
        
        # 生成列定义
        column_definitions = []
        primary_keys = []
        used_column_names = set()

        for col_name, col_info in structure['columns'].items():
            # 清理列名（支持中文列名）
            if db_format == 'postgresql':
                # PostgreSQL支持中文列名，用双引号包围
                clean_col_name = f'"{col_name}"'
            else:
                # 其他数据库使用原来的清理逻辑
                clean_col_name = re.sub(r'[^a-zA-Z0-9_]', '_', col_name)
                if not clean_col_name[0].isalpha():
                    clean_col_name = 'col_' + clean_col_name

            # 确保列名唯一
            original_clean_name = clean_col_name
            counter = 1
            while clean_col_name in used_column_names:
                if db_format == 'postgresql':
                    clean_col_name = f'"{col_name}_{counter}"'
                else:
                    clean_col_name = f"{original_clean_name}_{counter}"
                counter += 1
            used_column_names.add(clean_col_name)
            
            # 确定数据类型
            dtype = col_info['dtype']
            sql_type = type_mapping.get(dtype, 'TEXT')
            
            # 对于文本类型，使用实际长度
            if dtype == 'object' and db_format in ['mysql', 'postgresql', 'sql_server']:
                max_len = col_info['max_length']
                if db_format == 'mysql':
                    sql_type = f'VARCHAR({max_len})'
                elif db_format == 'postgresql':
                    sql_type = f'VARCHAR({max_len})'
                elif db_format == 'sql_server':
                    sql_type = f'NVARCHAR({max_len})'
            
            # 添加约束
            constraints = []
            if col_info['null_count'] == 0:
                constraints.append('NOT NULL')
            
            if col_info['is_primary_key']:
                primary_keys.append(clean_col_name)
            
            column_def = f'    {clean_col_name} {sql_type}'
            if constraints:
                column_def += ' ' + ' '.join(constraints)
            
            column_definitions.append(column_def)
        
        # 添加主键约束
        if primary_keys:
            column_definitions.append(f'    PRIMARY KEY ({", ".join(primary_keys)})')
        
        # 生成完整的CREATE TABLE语句
        sql = f'CREATE TABLE {table_name} (\n'
        sql += ',\n'.join(column_definitions)
        sql += '\n);'
        
        return sql
    
    def generate_insert_sql(
        self,
        structure: Dict[str, Any],
        table_name: str,
        batch_size: int = 1000,
        db_format: str = 'sqlite'
    ) -> List[str]:
        """生成INSERT SQL语句"""
        
        df = structure['dataframe']
        
        # 清理列名并确保唯一性（支持中文列名）
        clean_columns = []
        used_names = set()
        for col in df.columns:
            # 对于PostgreSQL，保留中文列名但用双引号包围
            if db_format == 'postgresql':
                # PostgreSQL支持中文列名，用双引号包围
                clean_col = f'"{col}"'
            else:
                # 其他数据库使用原来的清理逻辑
                clean_col = re.sub(r'[^a-zA-Z0-9_]', '_', col)
                if not clean_col[0].isalpha():
                    clean_col = 'col_' + clean_col

            # 确保列名唯一
            original_clean = clean_col
            counter = 1
            while clean_col in used_names:
                if db_format == 'postgresql':
                    clean_col = f'"{col}_{counter}"'
                else:
                    clean_col = f"{original_clean}_{counter}"
                counter += 1
            used_names.add(clean_col)
            clean_columns.append(clean_col)
        
        insert_statements = []
        
        # 分批处理数据
        for i in range(0, len(df), batch_size):
            batch_df = df.iloc[i:i+batch_size]
            
            values_list = []
            for _, row in batch_df.iterrows():
                values = []
                for value in row:
                    if pd.isna(value):
                        values.append('NULL')
                    elif isinstance(value, str):
                        # 完整的SQL字符串转义
                        escaped_value = self._escape_sql_string(value)
                        values.append(f"'{escaped_value}'")
                    else:
                        values.append(str(value))
                
                values_list.append(f"({', '.join(values)})")
            
            if values_list:
                columns_str = ', '.join(clean_columns)
                values_str = ',\n    '.join(values_list)
                
                insert_sql = f'INSERT INTO {table_name} ({columns_str})\nVALUES\n    {values_str};'
                insert_statements.append(insert_sql)
        
        return insert_statements

    def _escape_sql_string(self, value: str) -> str:
        """完整的SQL字符串转义"""
        if not isinstance(value, str):
            return str(value)

        # 转义特殊字符
        escaped = value.replace("\\", "\\\\")  # 反斜杠
        escaped = escaped.replace("'", "''")   # 单引号
        escaped = escaped.replace('"', '""')   # 双引号
        escaped = escaped.replace('\n', '\\n') # 换行符
        escaped = escaped.replace('\r', '\\r') # 回车符
        escaped = escaped.replace('\t', '\\t') # 制表符
        escaped = escaped.replace('\0', '\\0') # 空字符

        # 处理可能导致SQL语法错误的字符
        # 移除或替换控制字符
        import re
        escaped = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f]', '', escaped)

        return escaped

    def _clean_column_name(self, column_name: str) -> str:
        """清理列名，使其符合SQL标准，保留中文字符"""
        if not column_name or pd.isna(column_name):
            return 'unnamed_column'

        # 转换为字符串并去除首尾空格
        clean_name = str(column_name).strip()

        # 如果为空，使用默认名称
        if not clean_name:
            return 'unnamed_column'

        # 对于MySQL，中文列名是支持的，只需要用反引号包围
        # 只替换真正有问题的字符，保留中文
        clean_name = re.sub(r'[`\'"\\]', '_', clean_name)  # 只替换引号和反斜杠
        clean_name = re.sub(r'\s+', '_', clean_name)       # 空格替换为下划线

        # 移除首尾的下划线
        clean_name = clean_name.strip('_')

        # 如果清理后为空，使用默认名称
        if not clean_name:
            clean_name = 'unnamed_column'

        return clean_name

    def _generate_create_table_sql_from_df(self, df, table_name: str, db_format: str) -> str:
        """从DataFrame生成CREATE TABLE SQL语句"""
        type_mapping = self.type_mappings.get(db_format, self.type_mappings['sqlite'])

        # 生成列定义
        column_definitions = []
        clean_columns = []
        used_names = set()

        for col in df.columns:
            # 清理列名
            clean_col = self._clean_column_name(col)

            # 处理重复列名
            counter = 1
            original_clean = clean_col
            while clean_col in used_names:
                clean_col = f"{original_clean}_{counter}"
                counter += 1
            used_names.add(clean_col)
            clean_columns.append(clean_col)

            # 确定数据类型
            dtype = str(df[col].dtype)
            if dtype.startswith('int'):
                sql_type = type_mapping.get('int64', 'INT')
            elif dtype.startswith('float'):
                sql_type = type_mapping.get('float64', 'DECIMAL(15,4)')
            elif dtype == 'bool':
                sql_type = type_mapping.get('bool', 'BOOLEAN')
            elif 'datetime' in dtype:
                sql_type = type_mapping.get('datetime64[ns]', 'DATETIME')
            else:
                # 对于文本类型，计算最大长度
                max_len = df[col].astype(str).str.len().max()
                if pd.isna(max_len):
                    max_len = 255
                else:
                    max_len = min(max(int(max_len) + 50, 255), 1000)  # 限制在255-1000之间

                if db_format == 'mysql':
                    sql_type = f'VARCHAR({max_len})'
                else:
                    sql_type = type_mapping.get('object', 'TEXT')

            column_definitions.append(f'    `{clean_col}` {sql_type}')

        # 生成完整的CREATE TABLE语句
        sql = f'CREATE TABLE `{table_name}` (\n'
        sql += ',\n'.join(column_definitions)
        sql += '\n);'

        return sql

    def insert_data_to_mysql(self, csv_paths: List[str], mysql_cursor, mysql_connection) -> Tuple[bool, str, Dict]:
        """直接使用参数化查询插入数据到MySQL"""
        try:
            import pandas as pd
            import os

            conversion_stats = {
                'total_files': len(csv_paths),
                'tables_created': 0,
                'total_records': 0,
                'tables': []
            }

            for csv_path in csv_paths:
                if not os.path.exists(csv_path):
                    continue

                # 读取CSV文件
                df = pd.read_csv(csv_path, encoding='utf-8-sig')
                if df.empty:
                    continue

                # 生成表名
                table_name = self.generate_table_name(os.path.basename(csv_path))

                # 创建表结构
                create_table_sql = self._generate_create_table_sql_from_df(df, table_name, 'mysql')
                mysql_cursor.execute(create_table_sql)
                print(f"✅ 创建表: {table_name}")

                # 使用参数化查询插入数据
                columns = [self._clean_column_name(col) for col in df.columns]
                # 处理重复列名
                clean_columns = []
                used_names = set()
                for col in columns:
                    counter = 1
                    original_col = col
                    while col in used_names:
                        col = f"{original_col}_{counter}"
                        counter += 1
                    used_names.add(col)
                    clean_columns.append(f'`{col}`')

                placeholders = ', '.join(['%s'] * len(clean_columns))
                insert_sql = f"INSERT INTO `{table_name}` ({', '.join(clean_columns)}) VALUES ({placeholders})"

                # 批量插入数据
                batch_size = 1000
                total_inserted = 0

                for i in range(0, len(df), batch_size):
                    batch_df = df.iloc[i:i+batch_size]
                    batch_data = []

                    for _, row in batch_df.iterrows():
                        row_data = []
                        for value in row:
                            if pd.isna(value):
                                row_data.append(None)
                            else:
                                row_data.append(value)
                        batch_data.append(tuple(row_data))

                    mysql_cursor.executemany(insert_sql, batch_data)
                    total_inserted += len(batch_data)
                    print(f"📊 插入数据: {table_name} - {total_inserted}/{len(df)} 行")

                mysql_connection.commit()

                conversion_stats['tables_created'] += 1
                conversion_stats['total_records'] += len(df)
                conversion_stats['tables'].append({
                    'name': table_name,
                    'rows': len(df),
                    'source_file': os.path.basename(csv_path)
                })

            return True, "数据插入成功", conversion_stats

        except Exception as e:
            return False, f"数据插入失败: {str(e)}", conversion_stats

    def convert_to_sqlite(
        self,
        csv_paths: List[str],
        output_path: str,
        merge_strategy: str = "smart_merge"
    ) -> Tuple[bool, str, Dict]:
        """转换为SQLite数据库"""
        try:
            # 创建SQLite数据库
            conn = sqlite3.connect(output_path)
            cursor = conn.cursor()
            
            conversion_stats = {
                'total_files': len(csv_paths),
                'successful_files': 0,
                'total_rows': 0,
                'tables': []
            }
            
            for csv_path in csv_paths:
                if not os.path.exists(csv_path):
                    continue
                
                # 分析CSV结构
                structure = self.analyze_csv_structure(csv_path)
                if not structure:
                    continue
                
                # 生成表名
                table_name = self.generate_table_name(structure['filename'])
                
                # 创建表
                create_sql = self.generate_create_table_sql(structure, 'sqlite', table_name)
                cursor.execute(create_sql)
                
                # 插入数据
                df = structure['dataframe']
                
                # 清理列名并确保唯一性
                clean_columns = []
                used_names = set()
                for col in df.columns:
                    clean_col = re.sub(r'[^a-zA-Z0-9_]', '_', col)
                    if not clean_col[0].isalpha():
                        clean_col = 'col_' + clean_col

                    # 确保列名唯一
                    original_clean = clean_col
                    counter = 1
                    while clean_col in used_names:
                        clean_col = f"{original_clean}_{counter}"
                        counter += 1
                    used_names.add(clean_col)
                    clean_columns.append(clean_col)
                
                # 重命名DataFrame列
                df.columns = clean_columns
                
                # 根据合并策略插入数据到SQLite
                if merge_strategy == "smart_merge":
                    self._smart_insert_to_sqlite(df, table_name, conn)
                elif merge_strategy == "replace":
                    df.to_sql(table_name, conn, if_exists='replace', index=False)
                    self.logger.info(f"完全替换表 {table_name}，插入 {len(df)} 条记录")
                else:  # timestamp策略在外层已经处理了文件名
                    self._smart_insert_to_sqlite(df, table_name, conn)
                
                conversion_stats['successful_files'] += 1
                conversion_stats['total_rows'] += len(df)
                conversion_stats['tables'].append({
                    'name': table_name,
                    'source_file': structure['filename'],
                    'rows': len(df),
                    'columns': len(df.columns)
                })
            
            conn.commit()
            conn.close()
            
            success_msg = f"✅ 成功转换 {conversion_stats['successful_files']} 个文件到SQLite数据库"
            return True, success_msg, conversion_stats
            
        except Exception as e:
            error_msg = f"❌ SQLite转换失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, {}

    def _smart_insert_to_sqlite(self, df: pd.DataFrame, table_name: str, conn):
        """智能插入数据到SQLite，支持增量更新和重复数据处理"""
        cursor = None
        try:
            cursor = conn.cursor()

            # 检查表是否存在
            cursor.execute("""
                SELECT name FROM sqlite_master
                WHERE type='table' AND name=?
            """, (table_name,))

            table_exists = cursor.fetchone() is not None

            if not table_exists:
                # 表不存在，直接插入所有数据
                df.to_sql(table_name, conn, if_exists='replace', index=False)
                self.logger.info(f"创建新表 {table_name}，插入 {len(df)} 条记录")
                return

            # 表已存在，需要智能合并
            # 获取现有数据
            existing_df = pd.read_sql_query(f"SELECT * FROM {table_name}", conn)

            # 确定主键列（通常是定额编号相关的列）
            primary_key_col = self._identify_primary_key_column(df)

            if primary_key_col:
                # 基于主键进行智能合并
                merged_df = self._merge_dataframes_by_key(existing_df, df, primary_key_col)

                # 替换整个表
                merged_df.to_sql(table_name, conn, if_exists='replace', index=False)

                new_count = len(df)
                existing_count = len(existing_df)
                final_count = len(merged_df)
                updated_count = existing_count + new_count - final_count

                self.logger.info(f"智能合并表 {table_name}: 原有{existing_count}条, 新增{new_count}条, 合并后{final_count}条, 更新{updated_count}条")
            else:
                # 没有明确的主键，简单追加不重复的数据
                # 基于所有列进行去重
                combined_df = pd.concat([existing_df, df], ignore_index=True)
                deduplicated_df = combined_df.drop_duplicates()

                deduplicated_df.to_sql(table_name, conn, if_exists='replace', index=False)

                self.logger.info(f"追加数据到表 {table_name}: 原有{len(existing_df)}条, 新增{len(df)}条, 去重后{len(deduplicated_df)}条")

        except Exception as e:
            self.logger.warning(f"智能插入失败，使用替换模式: {str(e)}")
            # 出错时回退到替换模式
            df.to_sql(table_name, conn, if_exists='replace', index=False)
        finally:
            if cursor:
                cursor.close()

    def _identify_primary_key_column(self, df: pd.DataFrame) -> str:
        """识别主键列（定额编号相关列）"""
        # 常见的定额编号列名
        key_patterns = [
            '定额编号', '编号', '定额号', 'quota_code', 'code', 'id',
            '项目编号', '序号', '定额项编号', '资源编号'
        ]

        for col in df.columns:
            col_lower = col.lower()
            for pattern in key_patterns:
                if pattern.lower() in col_lower or pattern in col:
                    # 检查这一列是否有足够的唯一值
                    unique_ratio = df[col].nunique() / len(df) if len(df) > 0 else 0
                    if unique_ratio > 0.8:  # 80%以上的值是唯一的
                        self.logger.debug(f"识别主键列: {col} (唯一性: {unique_ratio:.2%})")
                        return col

        self.logger.debug("未找到合适的主键列，将使用全列去重")
        return None

    def _merge_dataframes_by_key(self, existing_df: pd.DataFrame, new_df: pd.DataFrame, key_col: str) -> pd.DataFrame:
        """基于主键合并数据框，新数据优先"""
        try:
            # 确保两个数据框都有相同的列
            all_columns = list(set(existing_df.columns) | set(new_df.columns))

            # 为缺失的列添加空值
            for col in all_columns:
                if col not in existing_df.columns:
                    existing_df[col] = None
                if col not in new_df.columns:
                    new_df[col] = None

            # 重新排序列
            existing_df = existing_df[all_columns]
            new_df = new_df[all_columns]

            # 合并数据，新数据优先（suffixes参数控制重复列的后缀）
            merged_df = pd.merge(
                existing_df, new_df,
                on=key_col,
                how='outer',
                suffixes=('_old', '_new')
            )

            # 处理重复列，优先使用新数据
            final_columns = []
            for col in all_columns:
                if col == key_col:
                    final_columns.append(col)
                elif f"{col}_new" in merged_df.columns:
                    # 新数据优先，如果新数据为空则使用旧数据
                    merged_df[col] = merged_df[f"{col}_new"].fillna(merged_df[f"{col}_old"])
                    final_columns.append(col)
                elif col in merged_df.columns:
                    final_columns.append(col)

            # 只保留最终需要的列
            result_df = merged_df[final_columns]

            return result_df

        except Exception as e:
            self.logger.warning(f"智能合并失败: {str(e)}")
            # 合并失败时，简单连接并去重
            combined_df = pd.concat([existing_df, new_df], ignore_index=True)
            return combined_df.drop_duplicates(subset=[key_col], keep='last')

    def _smart_merge_mongodb_data(self, existing_file_path: str, new_data: dict) -> dict:
        """智能合并MongoDB数据"""
        try:
            import json
            # 读取现有数据
            with open(existing_file_path, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)

            # 合并元数据
            merged_data = {
                'metadata': {
                    'export_date': new_data['metadata']['export_date'],
                    'source_files': existing_data['metadata'].get('source_files', 0) + new_data['metadata']['source_files'],
                    'total_documents': 0  # 将在后面计算
                },
                'collections': {}
            }

            # 合并集合数据
            all_collections = set(existing_data.get('collections', {}).keys()) | set(new_data.get('collections', {}).keys())

            total_documents = 0
            for collection_name in all_collections:
                existing_collection = existing_data.get('collections', {}).get(collection_name, {})
                new_collection = new_data.get('collections', {}).get(collection_name, {})

                # 合并文档
                existing_docs = existing_collection.get('documents', [])
                new_docs = new_collection.get('documents', [])

                # 尝试基于某个字段进行智能合并
                merged_docs = self._merge_mongodb_documents(existing_docs, new_docs)

                # 更新集合元数据
                merged_collection = {
                    'metadata': {
                        'document_count': len(merged_docs),
                        'source_files': list(set(
                            existing_collection.get('metadata', {}).get('source_files', []) +
                            new_collection.get('metadata', {}).get('source_files', [])
                        )),
                        'last_updated': new_data['metadata']['export_date']
                    },
                    'documents': merged_docs
                }

                merged_data['collections'][collection_name] = merged_collection
                total_documents += len(merged_docs)

            # 更新总文档数
            merged_data['metadata']['total_documents'] = total_documents

            self.logger.info(f"MongoDB智能合并完成: {len(all_collections)}个集合, 总计{total_documents}个文档")
            return merged_data

        except Exception as e:
            self.logger.warning(f"MongoDB智能合并失败，使用新数据: {str(e)}")
            return new_data

    def _merge_mongodb_documents(self, existing_docs: list, new_docs: list) -> list:
        """合并MongoDB文档，新文档优先"""
        try:
            if not existing_docs:
                return new_docs
            if not new_docs:
                return existing_docs

            # 尝试找到合适的唯一标识字段
            key_field = None
            for doc in new_docs:
                for field in doc.keys():
                    if any(pattern in field.lower() for pattern in ['编号', 'code', 'id', '序号']):
                        key_field = field
                        break
                if key_field:
                    break

            if key_field:
                # 基于键字段合并
                existing_dict = {doc.get(key_field): doc for doc in existing_docs if doc.get(key_field)}
                new_dict = {doc.get(key_field): doc for doc in new_docs if doc.get(key_field)}

                # 合并字典，新数据优先
                merged_dict = {**existing_dict, **new_dict}

                # 添加没有键字段的文档
                merged_docs = list(merged_dict.values())
                for doc in existing_docs + new_docs:
                    if not doc.get(key_field):
                        merged_docs.append(doc)

                return merged_docs
            else:
                # 没有找到合适的键字段，简单合并并去重
                all_docs = existing_docs + new_docs
                # 基于文档内容去重（转换为字符串比较）
                seen = set()
                unique_docs = []
                for doc in all_docs:
                    doc_str = json.dumps(doc, sort_keys=True, default=str)
                    if doc_str not in seen:
                        seen.add(doc_str)
                        unique_docs.append(doc)

                return unique_docs

        except Exception as e:
            self.logger.warning(f"MongoDB文档合并失败，使用简单连接: {str(e)}")
            return existing_docs + new_docs

    def convert_to_sql_script(
        self, 
        csv_paths: List[str], 
        output_path: str,
        db_format: str = 'mysql'
    ) -> Tuple[bool, str, Dict]:
        """转换为SQL脚本文件"""
        try:
            conversion_stats = {
                'total_files': len(csv_paths),
                'successful_files': 0,
                'total_rows': 0,
                'tables': []
            }
            
            sql_content = []
            
            # 添加文件头注释
            sql_content.append(f'-- {db_format.upper()} SQL Script')
            sql_content.append(f'-- Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
            sql_content.append(f'-- Source files: {len(csv_paths)} CSV files')
            sql_content.append('')
            
            for csv_path in csv_paths:
                if not os.path.exists(csv_path):
                    continue
                
                # 分析CSV结构
                structure = self.analyze_csv_structure(csv_path)
                if not structure:
                    continue
                
                # 生成表名
                table_name = self.generate_table_name(structure['filename'])
                
                # 添加表注释
                sql_content.append(f'-- Table: {table_name}')
                sql_content.append(f'-- Source: {structure["filename"]}')
                sql_content.append(f'-- Rows: {structure["row_count"]}')
                sql_content.append('')
                
                # 生成CREATE TABLE语句
                create_sql = self.generate_create_table_sql(structure, db_format, table_name)
                sql_content.append(create_sql)
                sql_content.append('')
                
                # 生成INSERT语句
                insert_statements = self.generate_insert_sql(structure, table_name, db_format=db_format)
                sql_content.extend(insert_statements)
                sql_content.append('')
                
                conversion_stats['successful_files'] += 1
                conversion_stats['total_rows'] += structure['row_count']
                conversion_stats['tables'].append({
                    'name': table_name,
                    'source_file': structure['filename'],
                    'rows': structure['row_count'],
                    'columns': structure['column_count']
                })
            
            # 写入SQL文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(sql_content))
            
            success_msg = f"✅ 成功生成 {db_format.upper()} SQL脚本"
            return True, success_msg, conversion_stats

        except Exception as e:
            error_msg = f"❌ SQL脚本生成失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, {}

    def preview_database_file(self, db_path: str, file_type: str = 'auto') -> str:
        """预览数据库文件内容"""
        try:
            if file_type == 'auto':
                # 根据文件扩展名判断类型
                ext = os.path.splitext(db_path)[1].lower()
                if ext == '.db' or ext == '.sqlite':
                    file_type = 'sqlite'
                elif ext == '.sql':
                    file_type = 'sql'
                elif ext == '.json':
                    file_type = 'mongodb'
                else:
                    file_type = 'sql'

            if file_type == 'sqlite':
                return self._preview_sqlite_database(db_path)
            elif file_type == 'sql':
                return self._preview_sql_file(db_path)
            elif file_type == 'mongodb':
                return self._preview_mongodb_file(db_path)
            else:
                return "<p style='color: red;'>❌ 不支持的文件类型</p>"

        except Exception as e:
            return f"<p style='color: red;'>❌ 预览失败: {str(e)}</p>"

    def _preview_sqlite_database(self, db_path: str) -> str:
        """预览SQLite数据库"""
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 获取所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()

            if not tables:
                conn.close()
                return "<p>数据库中没有表</p>"

            html_content = f"""
            <div style="border: 1px solid #ddd; border-radius: 8px; padding: 16px; background: #f9f9f9;">
                <h3 style="margin-top: 0; color: #333;">🗄️ SQLite数据库预览</h3>
                <div style="margin-bottom: 16px; padding: 12px; background: #e8f4fd; border-radius: 6px;">
                    <p style="margin: 4px 0;"><strong>📊 数据库统计:</strong></p>
                    <p style="margin: 4px 0;">• 文件大小: {os.path.getsize(db_path) / 1024:.2f} KB</p>
                    <p style="margin: 4px 0;">• 表数量: {len(tables)} 个</p>
                </div>
            """

            # 预览每个表
            for table_name, in tables:
                # 获取表结构
                cursor.execute(f"PRAGMA table_info({table_name});")
                columns = cursor.fetchall()

                # 获取行数
                cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
                row_count = cursor.fetchone()[0]

                # 获取前20行数据
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 20;")
                rows = cursor.fetchall()

                # 生成表预览HTML
                html_content += f"""
                <div style="margin-bottom: 20px; border: 1px solid #ccc; border-radius: 6px; overflow: hidden;">
                    <div style="background: #f8f9fa; padding: 12px; border-bottom: 1px solid #ccc;">
                        <h4 style="margin: 0; color: #333;">📋 表: {table_name}</h4>
                        <p style="margin: 4px 0 0 0; font-size: 12px; color: #666;">
                            总行数: {row_count} | 列数: {len(columns)} | 预览: {min(len(rows), 20)} 行
                        </p>
                    </div>
                    <div style="overflow: auto; max-height: 400px; background: white;">
                        <table class="db-preview-table">
                            <thead>
                                <tr>
                """

                # 添加表头
                for col in columns:
                    col_name = col[1]
                    col_type = col[2]
                    html_content += f"<th>{col_name}<br><small>({col_type})</small></th>"

                html_content += "</tr></thead><tbody>"

                # 添加数据行
                for row in rows:
                    html_content += "<tr>"
                    for value in row:
                        if value is None:
                            html_content += "<td style='color: #999; font-style: italic;'>NULL</td>"
                        else:
                            # 限制显示长度
                            display_value = str(value)
                            if len(display_value) > 50:
                                display_value = display_value[:47] + "..."
                            html_content += f"<td>{display_value}</td>"
                    html_content += "</tr>"

                html_content += "</tbody></table></div></div>"

            conn.close()

            # 添加样式
            html_content += """
                <style>
                .db-preview-table {
                    font-size: 11px;
                    border-collapse: collapse;
                    width: 100%;
                    margin: 0;
                }
                .db-preview-table th, .db-preview-table td {
                    border: 1px solid #ddd;
                    padding: 6px 8px;
                    text-align: left;
                    vertical-align: top;
                }
                .db-preview-table th {
                    background-color: #f8f9fa;
                    font-weight: bold;
                    position: sticky;
                    top: 0;
                    z-index: 5;
                }
                .db-preview-table tbody tr:nth-child(even) {
                    background-color: #f9f9f9;
                }
                .db-preview-table tbody tr:hover {
                    background-color: #e8f4fd;
                }
                .db-preview-table td {
                    max-width: 200px;
                    word-wrap: break-word;
                    white-space: normal;
                }
                </style>
            </div>
            """

            return html_content

        except Exception as e:
            return f"<p style='color: red;'>❌ SQLite预览失败: {str(e)}</p>"

    def _preview_sql_file(self, sql_path: str) -> str:
        """预览SQL文件"""
        try:
            with open(sql_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 分析SQL内容
            lines = content.split('\n')
            total_lines = len(lines)

            # 统计CREATE TABLE和INSERT语句
            create_tables = len([line for line in lines if line.strip().upper().startswith('CREATE TABLE')])
            insert_statements = len([line for line in lines if line.strip().upper().startswith('INSERT INTO')])

            # 获取前100行用于预览
            preview_lines = lines[:100]
            preview_content = '\n'.join(preview_lines)

            # 如果内容被截断，添加提示
            if total_lines > 100:
                preview_content += f"\n\n-- ... 还有 {total_lines - 100} 行内容 ..."

            html_content = f"""
            <div style="border: 1px solid #ddd; border-radius: 8px; padding: 16px; background: #f9f9f9;">
                <h3 style="margin-top: 0; color: #333;">📄 SQL文件预览</h3>
                <div style="margin-bottom: 16px; padding: 12px; background: #e8f4fd; border-radius: 6px;">
                    <p style="margin: 4px 0;"><strong>📊 文件统计:</strong></p>
                    <p style="margin: 4px 0;">• 文件大小: {os.path.getsize(sql_path) / 1024:.2f} KB</p>
                    <p style="margin: 4px 0;">• 总行数: {total_lines} 行</p>
                    <p style="margin: 4px 0;">• CREATE TABLE语句: {create_tables} 个</p>
                    <p style="margin: 4px 0;">• INSERT语句: {insert_statements} 个</p>
                </div>

                <h4 style="color: #333; margin-bottom: 12px;">📋 SQL内容预览:</h4>
                <div style="overflow: auto; max-height: 500px; border: 1px solid #ccc; border-radius: 4px; background: #f8f8f8; padding: 12px;">
                    <pre style="margin: 0; font-family: 'Courier New', monospace; font-size: 11px; line-height: 1.4; white-space: pre-wrap;">{preview_content}</pre>
                </div>

                <div style="margin-top: 12px; padding: 8px; background: #f0f8ff; border-radius: 4px; font-size: 12px; color: #666;">
                    💡 提示: 显示前100行内容，完整内容包含 {total_lines} 行SQL语句
                </div>
            </div>
            """

            return html_content

        except Exception as e:
            return f"<p style='color: red;'>❌ SQL文件预览失败: {str(e)}</p>"

    def convert_to_mongodb(
        self,
        csv_paths: List[str],
        output_path: str,
        merge_strategy: str = "smart_merge"
    ) -> Tuple[bool, str, Dict]:
        """转换为MongoDB JSON导出文件"""
        try:
            import json

            conversion_stats = {
                'total_files': len(csv_paths),
                'successful_files': 0,
                'total_rows': 0,
                'collections': []
            }

            mongodb_export = {
                'metadata': {
                    'export_date': datetime.now().isoformat(),
                    'source_files': len(csv_paths),
                    'format': 'MongoDB JSON Export',
                    'description': 'CSV to MongoDB collection export'
                },
                'collections': {}
            }

            for csv_path in csv_paths:
                if not os.path.exists(csv_path):
                    continue

                # 分析CSV结构
                structure = self.analyze_csv_structure(csv_path)
                if not structure:
                    continue

                # 生成集合名
                collection_name = self.generate_table_name(structure['filename'])

                # 转换数据为MongoDB文档
                df = structure['dataframe']
                documents = []

                for index, row in df.iterrows():
                    document = {}
                    for col_name, value in row.items():
                        # 清理字段名（MongoDB字段名规则）
                        # 对于中文字段名，保持原样，只替换特殊字符
                        clean_field_name = str(col_name)
                        # 只替换可能导致问题的字符，保留中文
                        clean_field_name = re.sub(r'[.$]', '_', clean_field_name)
                        # 如果字段名以数字开头，添加前缀
                        if clean_field_name and clean_field_name[0].isdigit():
                            clean_field_name = 'field_' + clean_field_name
                        # 如果字段名为空或只有特殊字符，使用默认名称
                        if not clean_field_name or clean_field_name.isspace():
                            clean_field_name = f'field_{len(document)}'

                        # 处理值
                        if pd.isna(value):
                            document[clean_field_name] = None
                        elif isinstance(value, (int, float)):
                            # 保持数值类型
                            if pd.isna(value):
                                document[clean_field_name] = None
                            else:
                                document[clean_field_name] = value
                        else:
                            # 字符串类型
                            document[clean_field_name] = str(value)

                    # 添加元数据
                    document['_source_file'] = structure['filename']
                    document['_import_date'] = datetime.now().isoformat()
                    document['_row_index'] = index

                    documents.append(document)

                # 添加到导出数据
                mongodb_export['collections'][collection_name] = {
                    'metadata': {
                        'source_file': structure['filename'],
                        'document_count': len(documents),
                        'fields': list(df.columns),
                        'collection_name': collection_name
                    },
                    'documents': documents
                }

                conversion_stats['successful_files'] += 1
                conversion_stats['total_rows'] += len(documents)
                conversion_stats['collections'].append({
                    'name': collection_name,
                    'source_file': structure['filename'],
                    'documents': len(documents),
                    'fields': len(df.columns)
                })

            # 根据合并策略处理现有文件
            if merge_strategy == "smart_merge" and os.path.exists(output_path):
                mongodb_export = self._smart_merge_mongodb_data(output_path, mongodb_export)
            elif merge_strategy == "replace" or not os.path.exists(output_path):
                # 直接覆盖或文件不存在时直接写入
                pass

            # 写入JSON文件
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(mongodb_export, f, ensure_ascii=False, indent=2, default=str)

            success_msg = f"✅ 成功生成 MongoDB JSON 导出文件"
            return True, success_msg, conversion_stats

        except Exception as e:
            error_msg = f"❌ MongoDB导出失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, {}

    def _preview_mongodb_file(self, json_path: str) -> str:
        """预览MongoDB JSON文件"""
        try:
            import json

            with open(json_path, 'r', encoding='utf-8') as f:
                mongodb_data = json.load(f)

            metadata = mongodb_data.get('metadata', {})
            collections = mongodb_data.get('collections', {})

            html_content = f"""
            <div style="border: 1px solid #ddd; border-radius: 8px; padding: 16px; background: #f9f9f9;">
                <h3 style="margin-top: 0; color: #333;">🍃 MongoDB JSON导出预览</h3>
                <div style="margin-bottom: 16px; padding: 12px; background: #e8f4fd; border-radius: 6px;">
                    <p style="margin: 4px 0;"><strong>📊 导出统计:</strong></p>
                    <p style="margin: 4px 0;">• 文件大小: {os.path.getsize(json_path) / 1024:.2f} KB</p>
                    <p style="margin: 4px 0;">• 导出时间: {metadata.get('export_date', 'N/A')}</p>
                    <p style="margin: 4px 0;">• 源文件数: {metadata.get('source_files', 0)} 个</p>
                    <p style="margin: 4px 0;">• 集合数量: {len(collections)} 个</p>
                </div>
            """

            # 显示每个集合的信息
            for collection_name, collection_data in collections.items():
                collection_metadata = collection_data.get('metadata', {})
                documents = collection_data.get('documents', [])

                html_content += f"""
                <div style="margin-bottom: 20px; border: 1px solid #ccc; border-radius: 6px; overflow: hidden;">
                    <div style="background: #f8f9fa; padding: 12px; border-bottom: 1px solid #ccc;">
                        <h4 style="margin: 0; color: #333;">🗂️ 集合: {collection_name}</h4>
                        <p style="margin: 4px 0 0 0; font-size: 12px; color: #666;">
                            源文件: {collection_metadata.get('source_file', 'N/A')} |
                            文档数: {collection_metadata.get('document_count', 0)} |
                            字段数: {len(collection_metadata.get('fields', []))}
                        </p>
                    </div>
                    <div style="padding: 12px; background: white;">
                        <p style="margin: 0 0 8px 0; font-weight: bold; color: #333;">字段列表:</p>
                        <p style="margin: 0 0 12px 0; font-size: 12px; color: #666;">
                            {', '.join(collection_metadata.get('fields', []))}
                        </p>
                """

                # 显示前3个文档作为示例
                if documents:
                    html_content += """
                        <p style="margin: 0 0 8px 0; font-weight: bold; color: #333;">文档示例:</p>
                        <div style="overflow: auto; max-height: 300px; border: 1px solid #ddd; border-radius: 4px; background: #f8f8f8; padding: 8px;">
                    """

                    for i, doc in enumerate(documents[:3]):
                        # 移除内部元数据字段用于显示
                        display_doc = {k: v for k, v in doc.items() if not k.startswith('_')}
                        doc_json = json.dumps(display_doc, ensure_ascii=False, indent=2)

                        html_content += f"""
                            <div style="margin-bottom: 12px; padding: 8px; background: white; border-radius: 4px; border: 1px solid #eee;">
                                <p style="margin: 0 0 4px 0; font-size: 11px; color: #888;">文档 #{i+1}</p>
                                <pre style="margin: 0; font-family: 'Courier New', monospace; font-size: 10px; line-height: 1.3; white-space: pre-wrap;">{doc_json}</pre>
                            </div>
                        """

                    if len(documents) > 3:
                        html_content += f"""
                            <div style="text-align: center; padding: 8px; color: #666; font-size: 11px;">
                                ... 还有 {len(documents) - 3} 个文档
                            </div>
                        """

                    html_content += "</div>"

                html_content += "</div></div>"

            html_content += """
                <div style="margin-top: 12px; padding: 8px; background: #f0f8ff; border-radius: 4px; font-size: 12px; color: #666;">
                    💡 提示: 此文件可以直接导入到MongoDB数据库中，每个集合对应一个CSV文件
                </div>
            </div>
            """

            return html_content

        except Exception as e:
            return f"<p style='color: red;'>❌ MongoDB文件预览失败: {str(e)}</p>"
