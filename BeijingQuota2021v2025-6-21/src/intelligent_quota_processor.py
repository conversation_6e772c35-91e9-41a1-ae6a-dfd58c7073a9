#!/usr/bin/env python3
"""
智能定额数据处理器
Intelligent Quota Data Processor
基于MCP工具，智能处理定额项和资源项的合并、关联和价格计算
"""

import os
import pandas as pd
import re
from typing import List, Dict, Optional, Tuple, Any
import logging
from datetime import datetime
from .mcp_file_merger import MCPFileMerger
from .price_calculator import PriceCalculator

class IntelligentQuotaProcessor:
    """智能定额数据处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.file_merger = MCPFileMerger()
        self.price_calculator = PriceCalculator()

    def _sort_quota_code(self, quota_code: str) -> tuple:
        """
        智能排序定额编号
        将 "1-1", "1-2", "1-11" 等转换为可正确排序的元组
        确保 1-1, 1-2, 1-3, ..., 1-10, 1-11 的正确顺序
        """
        try:
            # 分割定额编号，如 "1-1" -> ["1", "1"]
            parts = quota_code.split('-')
            # 转换为整数元组用于排序，确保数字比较而不是字符串比较
            numeric_parts = []
            for part in parts:
                if part.isdigit():
                    numeric_parts.append(int(part))
                else:
                    # 如果不是纯数字，尝试提取数字部分
                    import re
                    numbers = re.findall(r'\d+', part)
                    if numbers:
                        numeric_parts.append(int(numbers[0]))
                    else:
                        # 如果没有数字，使用ASCII值
                        numeric_parts.append(ord(part[0]) if part else 0)

            return tuple(numeric_parts)
        except (ValueError, AttributeError, IndexError):
            # 如果转换失败，返回一个大的数值，让它排在最后
            return (999999, 999999)
        
    def process_quota_files(
        self,
        selected_files: List[str],
        output_dir: str = "output"
    ) -> Tuple[bool, str, Dict]:
        """
        智能处理定额文件
        
        Args:
            selected_files: 选择的文件列表（包含parent_quotas和child_resources文件）
            output_dir: 输出目录
            
        Returns:
            (成功标志, 状态消息, 处理统计)
        """
        try:
            # 1. 智能分类文件
            file_classification = self._classify_files(selected_files)
            
            if not file_classification['parent_files'] and not file_classification['child_files']:
                return False, "❌ 未找到有效的定额项或资源文件", {}
            
            # 2. 分别合并同类型文件
            merge_results = self._merge_classified_files(file_classification, output_dir)
            
            if not merge_results['success']:
                return False, merge_results['message'], {}
            
            # 3. 建立关联关系并计算价格
            association_results = self._establish_associations_and_calculate_prices(
                merge_results['merged_files'], output_dir
            )
            
            if not association_results['success']:
                return False, association_results['message'], merge_results['stats']
            
            # 4. 生成最终统计信息
            final_stats = {
                'classification': file_classification,
                'merge_results': merge_results['stats'],
                'association_results': association_results['stats'],
                'final_files': association_results['output_files']
            }
            
            success_message = f"""✅ 智能定额数据处理完成！

📊 文件分类结果:
- 定额项文件: {len(file_classification['parent_files'])} 个
- 资源文件: {len(file_classification['child_files'])} 个

🔄 合并处理结果:
- 合并后定额项记录: {merge_results['stats'].get('parent_rows', 0)} 条
- 合并后资源记录: {merge_results['stats'].get('child_rows', 0)} 条

🔗 关联计算结果:
- 成功关联的定额项: {association_results['stats'].get('associated_quotas', 0)} 个
- 价格计算完成的定额项: {association_results['stats'].get('calculated_quotas', 0)} 个

📁 输出文件:
{chr(10).join([f"- {f}" for f in association_results['output_files']])}

💡 数据已准备就绪，可用于数据库创建"""
            
            return True, success_message, final_stats
            
        except Exception as e:
            error_msg = f"❌ 智能处理失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, {}
    
    def _classify_files(self, file_paths: List[str]) -> Dict[str, List[str]]:
        """智能分类文件"""
        classification = {
            'parent_files': [],
            'child_files': [],
            'unknown_files': []
        }
        
        for file_path in file_paths:
            if not os.path.exists(file_path):
                continue
                
            filename = os.path.basename(file_path)
            
            # 根据文件名模式分类（支持多种命名模式）
            if (re.match(r'parent_quotas.*\.csv$', filename, re.IGNORECASE) or
                re.match(r'.*parent_quotas.*\.csv$', filename, re.IGNORECASE) or
                re.match(r'merged_parent.*\.csv$', filename, re.IGNORECASE) or
                re.match(r'processed_parent.*\.csv$', filename, re.IGNORECASE)):
                classification['parent_files'].append(file_path)
                self.logger.info(f"✅ 识别为定额项文件: {filename}")
            elif (re.match(r'child_resources.*\.csv$', filename, re.IGNORECASE) or
                  re.match(r'.*child_resources.*\.csv$', filename, re.IGNORECASE) or
                  re.match(r'merged_child.*\.csv$', filename, re.IGNORECASE) or
                  re.match(r'processed_child.*\.csv$', filename, re.IGNORECASE)):
                classification['child_files'].append(file_path)
                self.logger.info(f"✅ 识别为资源文件: {filename}")
            else:
                # 尝试通过文件内容分类
                file_type = self._classify_by_content(file_path)
                if file_type == 'parent':
                    classification['parent_files'].append(file_path)
                elif file_type == 'child':
                    classification['child_files'].append(file_path)
                else:
                    classification['unknown_files'].append(file_path)
                    self.logger.warning(f"⚠️ 无法分类文件: {filename}")

        # 输出分类结果统计
        self.logger.info(f"📊 文件分类完成:")
        self.logger.info(f"   - 定额项文件: {len(classification['parent_files'])} 个")
        self.logger.info(f"   - 资源文件: {len(classification['child_files'])} 个")
        self.logger.info(f"   - 未知文件: {len(classification['unknown_files'])} 个")

        if classification['parent_files']:
            self.logger.info(f"📋 定额项文件列表:")
            for f in classification['parent_files']:
                self.logger.info(f"   - {os.path.basename(f)}")

        if classification['child_files']:
            self.logger.info(f"🔧 资源文件列表:")
            for f in classification['child_files']:
                self.logger.info(f"   - {os.path.basename(f)}")

        return classification
    
    def _classify_by_content(self, file_path: str) -> str:
        """通过文件内容分类"""
        try:
            # 读取文件头部几行来判断类型
            df = pd.read_csv(file_path, encoding='utf-8-sig', nrows=5)
            columns = [col.lower() for col in df.columns]
            
            # 定额项文件特征：包含定额编号、定额名称、工作内容等
            parent_indicators = ['定额编号', '定额名称', '定额项名称', '工作内容', '单位', '综合单价', '编号']
            parent_score = sum(1 for indicator in parent_indicators
                             if any(indicator in col for col in columns))

            # 资源文件特征：包含资源编号、资源名称、消耗量等
            child_indicators = ['资源编号', '资源名称', '消耗量', '类别', '子项名称', '定额编号']
            child_score = sum(1 for indicator in child_indicators
                            if any(indicator in col for col in columns))

            # 特殊检查：如果同时包含"定额编号"和"资源编号"，很可能是资源文件
            has_quota_code = any('定额编号' in col for col in columns)
            has_resource_code = any('资源编号' in col for col in columns)
            has_consumption = any('消耗量' in col for col in columns)

            if has_quota_code and has_resource_code and has_consumption:
                child_score += 2  # 增加资源文件的权重
            
            self.logger.info(f"📊 文件内容分析 {os.path.basename(file_path)}: parent_score={parent_score}, child_score={child_score}")
            self.logger.info(f"📋 文件列名: {columns}")

            if parent_score > child_score and parent_score >= 2:
                self.logger.info(f"✅ 通过内容识别为定额项文件: {os.path.basename(file_path)}")
                return 'parent'
            elif child_score > parent_score and child_score >= 2:
                self.logger.info(f"✅ 通过内容识别为资源文件: {os.path.basename(file_path)}")
                return 'child'
            else:
                self.logger.warning(f"⚠️ 无法确定文件类型: {os.path.basename(file_path)}")
                return 'unknown'
                
        except Exception as e:
            self.logger.warning(f"无法通过内容分类文件 {file_path}: {e}")
            return 'unknown'
    
    def _merge_classified_files(
        self, 
        classification: Dict[str, List[str]], 
        output_dir: str
    ) -> Dict[str, Any]:
        """合并分类后的文件"""
        try:
            merged_files = {}
            merge_stats = {}
            
            # 合并定额项文件
            if classification['parent_files']:
                parent_output = os.path.join(output_dir, f"merged_parent_quotas_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
                success, message, stats = self.file_merger.merge_csv_files(
                    classification['parent_files'], 
                    parent_output, 
                    merge_mode="by_filename"
                )
                
                if success:
                    merged_files['parent'] = parent_output
                    merge_stats['parent_rows'] = stats.get('total_rows', 0)
                    merge_stats['parent_files'] = stats.get('total_files', 0)
                else:
                    return {'success': False, 'message': f"定额项文件合并失败: {message}"}
            
            # 合并资源文件
            if classification['child_files']:
                child_output = os.path.join(output_dir, f"merged_child_resources_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
                success, message, stats = self.file_merger.merge_csv_files(
                    classification['child_files'], 
                    child_output, 
                    merge_mode="by_filename"
                )
                
                if success:
                    merged_files['child'] = child_output
                    merge_stats['child_rows'] = stats.get('total_rows', 0)
                    merge_stats['child_files'] = stats.get('total_files', 0)
                else:
                    return {'success': False, 'message': f"资源文件合并失败: {message}"}
            
            return {
                'success': True,
                'message': '文件合并成功',
                'merged_files': merged_files,
                'stats': merge_stats
            }
            
        except Exception as e:
            return {'success': False, 'message': f"文件合并过程失败: {str(e)}"}
    
    def _establish_associations_and_calculate_prices(
        self, 
        merged_files: Dict[str, str], 
        output_dir: str
    ) -> Dict[str, Any]:
        """建立关联关系并计算价格"""
        try:
            output_files = []
            stats = {
                'associated_quotas': 0,
                'calculated_quotas': 0,
                'total_parent_records': 0,
                'total_child_records': 0
            }
            
            # 读取合并后的文件
            parent_data = []
            child_data = []
            
            if 'parent' in merged_files:
                parent_df = pd.read_csv(merged_files['parent'], encoding='utf-8-sig')
                parent_data = parent_df.to_dict('records')
                stats['total_parent_records'] = len(parent_data)
                
                # 标准化列名
                parent_data = self._standardize_parent_columns(parent_data)
            
            if 'child' in merged_files:
                child_df = pd.read_csv(merged_files['child'], encoding='utf-8-sig')
                child_data = child_df.to_dict('records')
                stats['total_child_records'] = len(child_data)
                
                # 标准化列名
                child_data = self._standardize_child_columns(child_data)
            
            # 建立关联关系
            if parent_data and child_data:
                associations = self._build_associations(parent_data, child_data)
                stats['associated_quotas'] = len(associations)

                # 计算价格
                calculated_data = self._calculate_comprehensive_prices(associations)
                stats['calculated_quotas'] = len([q for q in calculated_data if q.get('price_calculated', False)])

                # 输出分离的定额项和资源文件（用于数据库创建）
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

                try:
                    # 输出处理后的定额项文件
                    processed_parent_output = os.path.join(output_dir, f"processed_parent_quotas_{timestamp}.csv")
                    self.logger.info(f"正在导出定额项文件: {processed_parent_output}")
                    self._export_parent_data(calculated_data, processed_parent_output)
                    output_files.append(processed_parent_output)
                    self.logger.info(f"定额项文件导出成功: {len(calculated_data)} 条记录")

                    # 输出处理后的资源文件
                    processed_child_output = os.path.join(output_dir, f"processed_child_resources_{timestamp}.csv")
                    self.logger.info(f"正在导出资源文件: {processed_child_output}")
                    self._export_child_data(calculated_data, processed_child_output)
                    output_files.append(processed_child_output)

                    # 计算资源记录总数
                    total_resources = sum(len(record.get('resource_details', [])) for record in calculated_data)
                    self.logger.info(f"资源文件导出成功: {total_resources} 条记录")

                    # 同时输出关联后的汇总数据（用于查看）
                    associated_output = os.path.join(output_dir, f"associated_quota_data_{timestamp}.csv")
                    self.logger.info(f"正在导出汇总文件: {associated_output}")
                    self._export_associated_data(calculated_data, associated_output)
                    output_files.append(associated_output)
                    self.logger.info(f"汇总文件导出成功")

                except Exception as e:
                    self.logger.error(f"导出文件时发生错误: {e}")
                    # 如果分离文件导出失败，至少保证汇总文件可用
                    associated_output = os.path.join(output_dir, f"associated_quota_data_{timestamp}.csv")
                    self._export_associated_data(calculated_data, associated_output)
                    output_files.append(associated_output)
            
            # 如果只有一种类型的文件，直接输出
            if parent_data and not child_data:
                parent_output = os.path.join(output_dir, f"processed_parent_quotas_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
                pd.DataFrame(parent_data).to_csv(parent_output, index=False, encoding='utf-8-sig')
                output_files.append(parent_output)
            
            if child_data and not parent_data:
                child_output = os.path.join(output_dir, f"processed_child_resources_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv")
                pd.DataFrame(child_data).to_csv(child_output, index=False, encoding='utf-8-sig')
                output_files.append(child_output)
            
            return {
                'success': True,
                'message': '关联和价格计算完成',
                'stats': stats,
                'output_files': output_files
            }
            
        except Exception as e:
            return {'success': False, 'message': f"关联和价格计算失败: {str(e)}"}

    def _standardize_parent_columns(self, data: List[Dict]) -> List[Dict]:
        """标准化定额项数据列名"""
        standardized_data = []

        for record in data:
            standardized_record = {}

            # 标准化字段映射
            field_mapping = {
                'quota_code': ['编号', '定额编号', 'quota_code', '定额号'],
                'quota_name': ['定额项名称', '定额名称', 'quota_name', '名称'],
                'work_content': ['工作内容', 'work_content', '内容'],
                'unit': ['单位', 'unit'],
                'comprehensive_price': ['综合单价（元/单位）', '综合单价', 'comprehensive_price', '单价'],
                'source_file': ['文件来源', 'source_file', '来源文件']
            }

            # 应用字段映射
            for std_field, possible_names in field_mapping.items():
                value = None
                for possible_name in possible_names:
                    if possible_name in record:
                        value = record[possible_name]
                        break

                # 数值字段处理
                if std_field == 'comprehensive_price' and value is not None:
                    try:
                        standardized_record[std_field] = float(value) if value != '' else 0.0
                    except (ValueError, TypeError):
                        standardized_record[std_field] = 0.0
                else:
                    standardized_record[std_field] = str(value) if value is not None else ''

            # 保留原始数据中的其他字段
            for key, value in record.items():
                if key not in [name for names in field_mapping.values() for name in names]:
                    standardized_record[key] = value

            standardized_data.append(standardized_record)

        return standardized_data

    def _standardize_child_columns(self, data: List[Dict]) -> List[Dict]:
        """标准化资源数据列名"""
        standardized_data = []

        for record in data:
            standardized_record = {}

            # 标准化字段映射
            field_mapping = {
                'quota_code': ['定额编号', 'quota_code', '编号'],
                'resource_code': ['资源编号', 'resource_code', '资源号'],
                'category': ['类别', 'category', '分类'],
                'resource_name': ['子项名称', '资源名称', 'resource_name', '名称'],
                'unit': ['单位', 'unit'],
                'consumption': ['消耗量', 'consumption', '用量'],
                'unit_price': ['单价', 'unit_price', '价格'],
                'total_price': ['合价', 'total_price', '总价'],
                'source_file': ['文件来源', 'source_file', '来源文件']
            }

            # 应用字段映射
            for std_field, possible_names in field_mapping.items():
                value = None
                for possible_name in possible_names:
                    if possible_name in record:
                        value = record[possible_name]
                        break

                # 数值字段处理
                if std_field in ['consumption', 'unit_price', 'total_price'] and value is not None:
                    try:
                        standardized_record[std_field] = float(value) if value != '' else 0.0
                    except (ValueError, TypeError):
                        standardized_record[std_field] = 0.0
                else:
                    standardized_record[std_field] = str(value) if value is not None else ''

            # 保留原始数据中的其他字段
            for key, value in record.items():
                if key not in [name for names in field_mapping.values() for name in names]:
                    standardized_record[key] = value

            standardized_data.append(standardized_record)

        return standardized_data

    def _build_associations(self, parent_data: List[Dict], child_data: List[Dict]) -> Dict[str, Dict]:
        """建立定额项与资源的关联关系"""
        associations = {}

        # 按定额编号分组
        for parent_record in parent_data:
            quota_code = parent_record.get('quota_code', '').strip()
            if quota_code:
                associations[quota_code] = {
                    'parent': parent_record,
                    'children': [],
                    'price_calculated': False
                }

        # 关联资源数据
        for child_record in child_data:
            quota_code = child_record.get('quota_code', '').strip()
            if quota_code in associations:
                associations[quota_code]['children'].append(child_record)

        return associations

    def _calculate_comprehensive_prices(self, associations: Dict[str, Dict]) -> List[Dict]:
        """计算综合单价"""
        calculated_data = []

        for quota_code, association in associations.items():
            parent = association['parent'].copy()
            children = association['children']

            if children:
                # 计算总价
                total_price = 0.0
                category_totals = {'人工': 0.0, '材料': 0.0, '机械': 0.0, '其他': 0.0}

                for child in children:
                    consumption = child.get('consumption', 0.0)
                    unit_price = child.get('unit_price', 0.0)

                    # 如果没有单价，尝试从价格计算器获取
                    if unit_price == 0.0:
                        resource_code = child.get('resource_code', '')
                        category = child.get('category', '')
                        unit_price = self.price_calculator.get_unit_price(resource_code, category)
                        child['unit_price'] = unit_price

                    # 计算合价
                    child_total = consumption * unit_price
                    child['total_price'] = child_total
                    total_price += child_total

                    # 按类别统计
                    category = child.get('category', '其他')
                    if category in category_totals:
                        category_totals[category] += child_total
                    else:
                        category_totals['其他'] += child_total

                # 更新定额项的综合单价
                parent['comprehensive_price'] = total_price
                parent['labor_cost'] = category_totals.get('人工', 0.0)
                parent['material_cost'] = category_totals.get('材料', 0.0)
                parent['machinery_cost'] = category_totals.get('机械', 0.0)
                parent['other_cost'] = category_totals.get('其他', 0.0)
                parent['price_calculated'] = True

                # 添加关联的资源信息
                parent['associated_resources'] = len(children)
                parent['resource_details'] = children
            else:
                parent['price_calculated'] = False
                parent['associated_resources'] = 0
                parent['resource_details'] = []

            calculated_data.append(parent)

        return calculated_data

    def _export_associated_data(self, data: List[Dict], output_path: str):
        """导出关联后的数据"""
        try:
            # 准备导出数据
            export_data = []

            for record in data:
                # 基本定额项信息
                base_record = {
                    '定额编号': record.get('quota_code', ''),
                    '定额名称': record.get('quota_name', ''),
                    '工作内容': record.get('work_content', ''),
                    '单位': record.get('unit', ''),
                    '综合单价': record.get('comprehensive_price', 0.0),
                    '人工费': record.get('labor_cost', 0.0),
                    '材料费': record.get('material_cost', 0.0),
                    '机械费': record.get('machinery_cost', 0.0),
                    '其他费用': record.get('other_cost', 0.0),
                    '关联资源数': record.get('associated_resources', 0),
                    '价格已计算': '是' if record.get('price_calculated', False) else '否',
                    '来源文件': record.get('source_file', '')
                }

                export_data.append(base_record)

            # 保存到CSV
            df = pd.DataFrame(export_data)
            df.to_csv(output_path, index=False, encoding='utf-8-sig')

        except Exception as e:
            self.logger.error(f"导出关联数据失败: {e}")
            raise

    def _export_parent_data(self, data: List[Dict], output_path: str):
        """导出处理后的定额项数据（用于数据库创建）"""
        try:
            export_data = []

            for record in data:
                parent_record = {
                    '编号': record.get('quota_code', ''),
                    '定额项名称': record.get('quota_name', ''),
                    '工作内容': record.get('work_content', ''),
                    '单位': record.get('unit', ''),
                    '综合单价（元/单位）': record.get('comprehensive_price', 0.0),
                    '人工费': record.get('labor_cost', 0.0),
                    '材料费': record.get('material_cost', 0.0),
                    '机械费': record.get('machinery_cost', 0.0),
                    '其他费用': record.get('other_cost', 0.0),
                    '文件来源': record.get('source_file', ''),
                    '关联资源数': record.get('associated_resources', 0),
                    '价格已计算': '是' if record.get('price_calculated', False) else '否'
                }
                export_data.append(parent_record)

            # 按定额编号智能排序
            export_data.sort(key=lambda x: self._sort_quota_code(x['编号']))

            df = pd.DataFrame(export_data)
            df.to_csv(output_path, index=False, encoding='utf-8-sig')

        except Exception as e:
            self.logger.error(f"导出定额项数据失败: {e}")
            raise

    def _export_child_data(self, data: List[Dict], output_path: str):
        """导出处理后的资源数据（用于数据库创建）"""
        try:
            export_data = []

            for record in data:
                quota_code = record.get('quota_code', '')
                resource_details = record.get('resource_details', [])

                if not resource_details:
                    self.logger.warning(f"定额项 {quota_code} 没有关联的资源详情")
                    continue

                for resource in resource_details:
                    child_record = {
                        '定额编号': quota_code,
                        '资源编号': resource.get('resource_code', ''),
                        '类别': resource.get('category', ''),
                        '子项名称': resource.get('resource_name', ''),
                        '单位': resource.get('unit', ''),
                        '消耗量': resource.get('consumption', 0.0),
                        '单价': resource.get('unit_price', 0.0),
                        '合价': resource.get('total_price', 0.0),
                        '文件来源': resource.get('source_file', '')
                    }
                    export_data.append(child_record)

            if not export_data:
                self.logger.warning("没有资源数据可导出，创建空文件")
                # 创建空的DataFrame但保留列结构
                df = pd.DataFrame(columns=['定额编号', '资源编号', '类别', '子项名称', '单位', '消耗量', '单价', '合价', '文件来源'])
            else:
                # 按定额编号智能排序，然后按资源编号排序
                export_data.sort(key=lambda x: (self._sort_quota_code(x['定额编号']), x['资源编号']))
                df = pd.DataFrame(export_data)

            df.to_csv(output_path, index=False, encoding='utf-8-sig')
            self.logger.info(f"资源数据导出完成: {len(export_data)} 条记录")

        except Exception as e:
            self.logger.error(f"导出资源数据失败: {e}")
            raise
