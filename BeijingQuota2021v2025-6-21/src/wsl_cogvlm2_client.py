"""
WSL ModelScope CogVLM2客户端
用于从Windows主应用调用WSL中的CogVLM2服务
支持自动启动WSL服务
"""

import requests
import base64
import json
import logging
import subprocess
import time
import threading
from typing import Optional, Dict, Any
from PIL import Image
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import io
import os

logger = logging.getLogger(__name__)

class WSLCogVLM2Client:
    """WSL CogVLM2服务客户端"""
    
    def __init__(self, host='localhost', port=8765):
        self.base_url = f"http://{host}:{port}"
        self.wsl_process = None  # WSL服务进程
        self.auto_start_enabled = True  # 是否启用自动启动

        # 创建带有重试机制和连接池的会话
        self.session = requests.Session()
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "POST"]
        )
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=10,
            pool_maxsize=20
        )
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        # 设置超时 - 适合大模型推理
        self.session.timeout = 900  # 15分钟超时
    
    def start_wsl_service(self) -> bool:
        """启动WSL中的RTX 5090 D + CogVLM2服务"""
        try:
            logger.info("🚀 启动RTX 5090 D + PyTorch 2.9.0 + CogVLM2服务...")

            # 检查WSL是否可用
            result = subprocess.run(['wsl', '--list', '--verbose'],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                logger.error("❌ WSL不可用")
                return False

            # 检查我们的优化服务脚本是否存在
            check_script = subprocess.run([
                'wsl', 'test', '-f', '~/cogvlm2_final_solution.py'
            ], capture_output=True, timeout=10)

            if check_script.returncode != 0:
                logger.warning("⚠️ RTX 5090 D优化服务脚本不存在，尝试复制...")
                # 复制我们的优化服务脚本到WSL
                copy_result = subprocess.run([
                    'wsl', 'cp',
                    '/mnt/c/Users/<USER>/Desktop/dev/cogvlm2_final_solution.py',
                    '~/cogvlm2_final_solution.py'
                ], capture_output=True, timeout=30)

                if copy_result.returncode != 0:
                    logger.error("❌ 无法复制RTX 5090 D优化服务脚本到WSL")
                    return False

            # 在后台启动我们的优化WSL服务
            logger.info("🔄 启动RTX 5090 D + CUDA 12.8 + CogVLM2服务...")
            self.wsl_process = subprocess.Popen([
                'wsl', 'bash', '-c',
                'source ~/cogvlm2_env/bin/activate && export PYTHONPATH=~/fake_xformers:$PYTHONPATH && python3 ~/cogvlm2_final_solution.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)

            # 等待服务启动（模型加载需要更长时间）
            logger.info("⏳ 等待RTX 5090 D + CogVLM2服务启动（模型加载约4-5分钟）...")
            for i in range(300):  # 等待最多5分钟
                time.sleep(1)
                if self.health_check():
                    logger.info("✅ RTX 5090 D + CogVLM2服务启动成功！")
                    logger.info("🎯 世界首个RTX 5090 D + PyTorch 2.9.0 + CUDA 12.8 + CogVLM2服务已就绪")
                    return True
                if i % 30 == 0:  # 每30秒显示一次进度
                    logger.info(f"⏳ 等待模型加载... ({i+1}/300秒)")

            logger.error("❌ RTX 5090 D + CogVLM2服务启动超时")
            return False

        except Exception as e:
            logger.error(f"❌ 启动RTX 5090 D + CogVLM2服务失败: {e}")
            return False
    
    def stop_wsl_service(self):
        """停止WSL服务"""
        if self.wsl_process:
            try:
                self.wsl_process.terminate()
                self.wsl_process.wait(timeout=10)
                logger.info("✅ WSL服务已停止")
            except Exception as e:
                logger.error(f"⚠️ 停止WSL服务时出错: {e}")
            finally:
                self.wsl_process = None
    
    def health_check(self) -> bool:
        """检查WSL服务健康状态"""
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                return data.get('status') == 'healthy' and data.get('model_loaded', False)
            return False
        except Exception:
            return False

    def check_service_health(self) -> bool:
        """检查WSL服务健康状态（别名方法）"""
        return self.health_check()

    def get_service_status(self) -> Optional[Dict[str, Any]]:
        """获取WSL服务详细状态"""
        try:
            response = self.session.get(f"{self.base_url}/status", timeout=10)
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            logger.error(f"获取服务状态失败: {e}")
            return None
    
    def ensure_service_running(self) -> bool:
        """确保WSL服务正在运行"""
        # 首先检查服务是否已经运行
        if self.health_check():
            return True
        
        # 如果启用自动启动，尝试启动服务
        if self.auto_start_enabled:
            logger.info("🔄 WSL服务未运行，尝试自动启动...")
            return self.start_wsl_service()
        
        return False
    
    def load_model(self) -> bool:
        """手动触发模型加载"""
        try:
            if not self.ensure_service_running():
                return False
                
            response = self.session.post(f"{self.base_url}/load_model")
            if response.status_code == 200:
                data = response.json()
                return data.get('success', False)
            return False
        except Exception as e:
            logger.error(f"WSL服务模型加载失败: {e}")
            return False
    
    def process_image(self, image_path: str, prompt: str = None) -> Optional[str]:
        """使用RTX 5090 D + CogVLM2处理图片"""
        try:
            if not self.ensure_service_running():
                logger.error("❌ RTX 5090 D + CogVLM2服务不可用")
                return None

            # 检查图片是否已经是1344x1344格式
            from PIL import Image
            with Image.open(image_path) as img:
                if img.size != (1344, 1344):
                    logger.info(f"📐 图片尺寸{img.size}，将自动调整为CogVLM2最佳尺寸1344x1344")
                    # 调整图片尺寸以适配CogVLM2
                    img = self._resize_for_cogvlm2(img)
                    # 保存临时文件
                    import tempfile
                    temp_path = tempfile.mktemp(suffix='.png')
                    img.save(temp_path, 'PNG', quality=95)
                    image_path = temp_path
                else:
                    logger.info(f"✅ 图片尺寸{img.size}，完美适配CogVLM2")

            # 读取并编码图片
            with open(image_path, 'rb') as f:
                image_data = f.read()

            image_base64 = base64.b64encode(image_data).decode('utf-8')

            # 优化的定额识别提示词
            if prompt is None:
                prompt = """请仔细分析这张定额表格图片，提取其中的定额信息。

这是一张1344x1344像素的高质量定额表格图片，请使用RTX 5090 D + CogVLM2的强大视觉理解能力进行精确识别。

要求：
1. 识别所有定额项目的名称、编号、单位
2. 识别每个定额项目对应的资源消耗量
3. 特别注意表格中的数字、单位和中文文字
4. 按照以下JSON格式输出：

{
  "parent_quotas": [
    {
      "quota_code": "定额编号",
      "quota_name": "定额名称",
      "unit": "单位",
      "resource_consumption": [
        {
          "resource_name": "资源名称",
          "consumption": "消耗量",
          "unit": "单位"
        }
      ]
    }
  ]
}

请确保输出格式严格按照上述JSON结构，不要包含其他解释文字。"""

            # 发送请求
            payload = {
                "image": image_base64,
                "prompt": prompt
            }

            logger.info(f"🔄 发送1344x1344图片到RTX 5090 D + CogVLM2服务: {image_path}")

            response = self.session.post(
                f"{self.base_url}/process_image",
                json=payload
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    result = data.get('result', '')
                    process_time = data.get('process_time', 0)
                    device = data.get('device', 'unknown')
                    mode = data.get('mode', 'unknown')

                    logger.info(f"✅ RTX 5090 D + CogVLM2处理成功！")
                    logger.info(f"   🔧 处理时间: {process_time:.2f}秒")
                    logger.info(f"   🖥️ 设备: {device}")
                    logger.info(f"   🚀 模式: {mode}")
                    logger.info(f"   📝 结果长度: {len(result)}")

                    return result
                else:
                    error = data.get('error', '未知错误')
                    logger.error(f"❌ RTX 5090 D + CogVLM2处理失败: {error}")
                    return None
            else:
                logger.error(f"❌ RTX 5090 D + CogVLM2请求失败: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"❌ RTX 5090 D + CogVLM2客户端处理失败: {e}")
            return None

    def _resize_for_cogvlm2(self, image):
        """调整图片尺寸以适配CogVLM2模型"""
        from PIL import Image

        target_width, target_height = 1344, 1344

        # 计算缩放比例，保持宽高比
        width_ratio = target_width / image.width
        height_ratio = target_height / image.height
        scale_ratio = min(width_ratio, height_ratio)

        # 计算新尺寸
        new_width = int(image.width * scale_ratio)
        new_height = int(image.height * scale_ratio)

        # 高质量缩放
        resized_image = image.resize(
            (new_width, new_height),
            Image.Resampling.LANCZOS
        )

        # 创建1344x1344的白色背景
        background = Image.new('RGB', (target_width, target_height), 'white')

        # 居中放置缩放后的图片
        x_offset = (target_width - new_width) // 2
        y_offset = (target_height - new_height) // 2
        background.paste(resized_image, (x_offset, y_offset))

        return background
    
    def is_available(self) -> bool:
        """检查WSL服务是否可用"""
        return self.ensure_service_running()

# 全局客户端实例
wsl_client = WSLCogVLM2Client()

def process_image_with_wsl_cogvlm2(image_path: str, volume_code: str = "", chapter_codes: str = "") -> Optional[str]:
    """
    使用WSL CogVLM2服务处理图片
    这是主应用调用的接口函数
    """
    try:
        # 构建提示词
        prompt = f"""请仔细分析这张定额表格图片，提取其中的定额信息。

卷册代码: {volume_code}
章节代码: {chapter_codes}

要求：
1. 识别所有定额项目的名称、编号、单位
2. 识别每个定额项目对应的资源消耗量
3. 按照以下JSON格式输出：

{{
  "parent_quotas": [
    {{
      "quota_code": "定额编号",
      "quota_name": "定额名称", 
      "unit": "单位",
      "resource_consumption": [
        {{
          "resource_name": "资源名称",
          "consumption": "消耗量",
          "unit": "单位"
        }}
      ]
    }}
  ]
}}

请确保输出格式严格按照上述JSON结构，不要包含其他解释文字。"""
        
        # 调用WSL服务
        result = wsl_client.process_image(image_path, prompt)
        
        if result:
            logger.info(f"✅ WSL CogVLM2处理完成: {image_path}")
            return result
        else:
            logger.error(f"❌ WSL CogVLM2处理失败: {image_path}")
            return None
            
    except Exception as e:
        logger.error(f"❌ WSL CogVLM2客户端错误: {e}")
        return None

def test_wsl_cogvlm2_connection() -> tuple[bool, str]:
    """测试WSL CogVLM2连接"""
    try:
        if wsl_client.is_available():
            return True, "✅ WSL CogVLM2服务连接成功"
        else:
            return False, "❌ WSL CogVLM2服务不可用，请检查WSL环境和服务状态"
    except Exception as e:
        return False, f"❌ WSL CogVLM2连接测试失败: {str(e)}"

def start_wsl_service_if_needed():
    """应用启动时自动启动WSL服务"""
    try:
        logger.info("🔍 检查WSL CogVLM2服务状态...")
        if not wsl_client.health_check():
            logger.info("🚀 自动启动WSL CogVLM2服务...")
            success = wsl_client.start_wsl_service()
            if success:
                logger.info("✅ WSL CogVLM2服务自动启动成功")
            else:
                logger.warning("⚠️ WSL CogVLM2服务自动启动失败")
        else:
            logger.info("✅ WSL CogVLM2服务已在运行")
    except Exception as e:
        logger.error(f"❌ WSL服务自动启动检查失败: {e}")

# 应用启动时在后台线程中检查并启动WSL服务
def init_wsl_service():
    """初始化WSL服务（在后台线程中运行）"""
    thread = threading.Thread(target=start_wsl_service_if_needed, daemon=True)
    thread.start()
