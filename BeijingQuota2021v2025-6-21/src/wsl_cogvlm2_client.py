"""
WSL ModelScope CogVLM2客户端
用于从Windows主应用调用WSL中的CogVLM2服务
支持自动启动WSL服务
"""

import requests
import base64
import json
import logging
import subprocess
import time
import threading
from typing import Optional
from PIL import Image
import io
import os

logger = logging.getLogger(__name__)

class WSLCogVLM2Client:
    """WSL CogVLM2服务客户端"""
    
    def __init__(self, host='localhost', port=8765):
        self.base_url = f"http://{host}:{port}"
        self.session = requests.Session()
        self.session.timeout = 300  # 5分钟超时
        self.wsl_process = None  # WSL服务进程
        self.auto_start_enabled = True  # 是否启用自动启动
    
    def start_wsl_service(self) -> bool:
        """启动WSL中的CogVLM2服务"""
        try:
            logger.info("🚀 尝试启动WSL CogVLM2服务...")
            
            # 检查WSL是否可用
            result = subprocess.run(['wsl', '--list', '--verbose'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                logger.error("❌ WSL不可用")
                return False
            
            # 检查服务脚本是否存在
            check_script = subprocess.run([
                'wsl', 'test', '-f', '~/wsl_cogvlm2_service.py'
            ], capture_output=True, timeout=10)
            
            if check_script.returncode != 0:
                logger.warning("⚠️ WSL服务脚本不存在，尝试复制...")
                # 复制服务脚本到WSL
                copy_result = subprocess.run([
                    'wsl', 'cp', 
                    '/mnt/c/Users/<USER>/Desktop/dev/BeijingQuota2021v2025-6-21/wsl_cogvlm2_service.py',
                    '~/wsl_cogvlm2_service.py'
                ], capture_output=True, timeout=30)
                
                if copy_result.returncode != 0:
                    logger.error("❌ 无法复制服务脚本到WSL")
                    return False
            
            # 在后台启动WSL服务
            logger.info("🔄 在WSL中启动CogVLM2服务...")
            self.wsl_process = subprocess.Popen([
                'wsl', 'bash', '-c', 
                'cd ~ && python3 wsl_cogvlm2_service.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # 等待服务启动
            logger.info("⏳ 等待服务启动...")
            for i in range(30):  # 等待最多30秒
                time.sleep(1)
                if self.health_check():
                    logger.info("✅ WSL CogVLM2服务启动成功")
                    return True
                logger.info(f"⏳ 等待服务启动... ({i+1}/30)")
            
            logger.error("❌ WSL服务启动超时")
            return False
            
        except Exception as e:
            logger.error(f"❌ 启动WSL服务失败: {e}")
            return False
    
    def stop_wsl_service(self):
        """停止WSL服务"""
        if self.wsl_process:
            try:
                self.wsl_process.terminate()
                self.wsl_process.wait(timeout=10)
                logger.info("✅ WSL服务已停止")
            except Exception as e:
                logger.error(f"⚠️ 停止WSL服务时出错: {e}")
            finally:
                self.wsl_process = None
    
    def health_check(self) -> bool:
        """检查WSL服务健康状态"""
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                return data.get('status') == 'healthy' and data.get('model_loaded', False)
            return False
        except Exception:
            return False
    
    def ensure_service_running(self) -> bool:
        """确保WSL服务正在运行"""
        # 首先检查服务是否已经运行
        if self.health_check():
            return True
        
        # 如果启用自动启动，尝试启动服务
        if self.auto_start_enabled:
            logger.info("🔄 WSL服务未运行，尝试自动启动...")
            return self.start_wsl_service()
        
        return False
    
    def load_model(self) -> bool:
        """手动触发模型加载"""
        try:
            if not self.ensure_service_running():
                return False
                
            response = self.session.post(f"{self.base_url}/load_model")
            if response.status_code == 200:
                data = response.json()
                return data.get('success', False)
            return False
        except Exception as e:
            logger.error(f"WSL服务模型加载失败: {e}")
            return False
    
    def process_image(self, image_path: str, prompt: str = None) -> Optional[str]:
        """处理图片"""
        try:
            if not self.ensure_service_running():
                logger.error("❌ WSL服务不可用")
                return None
            
            # 读取并编码图片
            with open(image_path, 'rb') as f:
                image_data = f.read()
            
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            # 默认提示词
            if prompt is None:
                prompt = """请仔细分析这张定额表格图片，提取其中的定额信息。

要求：
1. 识别所有定额项目的名称、编号、单位
2. 识别每个定额项目对应的资源消耗量
3. 按照以下JSON格式输出：

{
  "parent_quotas": [
    {
      "quota_code": "定额编号",
      "quota_name": "定额名称", 
      "unit": "单位",
      "resource_consumption": [
        {
          "resource_name": "资源名称",
          "consumption": "消耗量",
          "unit": "单位"
        }
      ]
    }
  ]
}

请确保输出格式严格按照上述JSON结构，不要包含其他解释文字。"""
            
            # 发送请求
            payload = {
                "image": image_base64,
                "prompt": prompt
            }
            
            logger.info(f"🔄 发送图片到WSL服务处理: {image_path}")
            
            response = self.session.post(
                f"{self.base_url}/process_image",
                json=payload
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    result = data.get('result', '')
                    logger.info(f"✅ WSL服务处理成功，结果长度: {len(result)}")
                    return result
                else:
                    error = data.get('error', '未知错误')
                    logger.error(f"❌ WSL服务处理失败: {error}")
                    return None
            else:
                logger.error(f"❌ WSL服务请求失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"❌ WSL客户端处理失败: {e}")
            return None
    
    def is_available(self) -> bool:
        """检查WSL服务是否可用"""
        return self.ensure_service_running()

# 全局客户端实例
wsl_client = WSLCogVLM2Client()

def process_image_with_wsl_cogvlm2(image_path: str, volume_code: str = "", chapter_codes: str = "") -> Optional[str]:
    """
    使用WSL CogVLM2服务处理图片
    这是主应用调用的接口函数
    """
    try:
        # 构建提示词
        prompt = f"""请仔细分析这张定额表格图片，提取其中的定额信息。

卷册代码: {volume_code}
章节代码: {chapter_codes}

要求：
1. 识别所有定额项目的名称、编号、单位
2. 识别每个定额项目对应的资源消耗量
3. 按照以下JSON格式输出：

{{
  "parent_quotas": [
    {{
      "quota_code": "定额编号",
      "quota_name": "定额名称", 
      "unit": "单位",
      "resource_consumption": [
        {{
          "resource_name": "资源名称",
          "consumption": "消耗量",
          "unit": "单位"
        }}
      ]
    }}
  ]
}}

请确保输出格式严格按照上述JSON结构，不要包含其他解释文字。"""
        
        # 调用WSL服务
        result = wsl_client.process_image(image_path, prompt)
        
        if result:
            logger.info(f"✅ WSL CogVLM2处理完成: {image_path}")
            return result
        else:
            logger.error(f"❌ WSL CogVLM2处理失败: {image_path}")
            return None
            
    except Exception as e:
        logger.error(f"❌ WSL CogVLM2客户端错误: {e}")
        return None

def test_wsl_cogvlm2_connection() -> tuple[bool, str]:
    """测试WSL CogVLM2连接"""
    try:
        if wsl_client.is_available():
            return True, "✅ WSL CogVLM2服务连接成功"
        else:
            return False, "❌ WSL CogVLM2服务不可用，请检查WSL环境和服务状态"
    except Exception as e:
        return False, f"❌ WSL CogVLM2连接测试失败: {str(e)}"

def start_wsl_service_if_needed():
    """应用启动时自动启动WSL服务"""
    try:
        logger.info("🔍 检查WSL CogVLM2服务状态...")
        if not wsl_client.health_check():
            logger.info("🚀 自动启动WSL CogVLM2服务...")
            success = wsl_client.start_wsl_service()
            if success:
                logger.info("✅ WSL CogVLM2服务自动启动成功")
            else:
                logger.warning("⚠️ WSL CogVLM2服务自动启动失败")
        else:
            logger.info("✅ WSL CogVLM2服务已在运行")
    except Exception as e:
        logger.error(f"❌ WSL服务自动启动检查失败: {e}")

# 应用启动时在后台线程中检查并启动WSL服务
def init_wsl_service():
    """初始化WSL服务（在后台线程中运行）"""
    thread = threading.Thread(target=start_wsl_service_if_needed, daemon=True)
    thread.start()
