#!/usr/bin/env python3
"""
信息价识别处理器
Price Information Recognition Processor
专门处理北京市造价信息PDF的识别、提取和数据库存储
"""

import os
import pandas as pd
import json
import re
import asyncio
from typing import List, Dict, Optional, Tuple, Any
import logging
from datetime import datetime
from pathlib import Path

from .config import Config
from .pdf_processor import PDFProcessor
from .ai_model_processor import AIModelProcessor

class PriceInfoProcessor:
    """信息价识别处理器"""
    
    def __init__(self):
        self.config = Config()
        self.logger = logging.getLogger(__name__)
        self.pdf_processor = PDFProcessor()
        self.ai_processor = AIModelProcessor()
        
    async def process_price_info_pdf(
        self,
        pdf_path: str,
        start_page: int = 1,
        end_page: int = None,
        model_type: str = "qwen_qvq_max",
        output_dir: str = "output"
    ) -> Tuple[bool, str, Dict]:
        """
        处理信息价PDF文件
        
        Args:
            pdf_path: PDF文件路径
            start_page: 开始页码
            end_page: 结束页码
            model_type: AI模型类型
            output_dir: 输出目录
            
        Returns:
            (成功标志, 状态消息, 处理结果)
        """
        try:
            self.logger.info(f"开始处理信息价PDF: {pdf_path}")
            
            # 1. 转换PDF为图片
            self.logger.info("转换PDF页面为图片...")
            image_paths = await self.pdf_processor.extract_pages_as_images(
                pdf_path, start_page, end_page or start_page
            )
            
            if not image_paths:
                return False, "PDF转换失败，未生成图片", {}
            
            # 2. 逐页识别信息价数据
            all_price_data = []
            processing_stats = {
                'total_pages': len(image_paths),
                'processed_pages': 0,
                'total_chapters': 0,
                'total_price_items': 0,
                'failed_pages': []
            }
            
            for i, image_path in enumerate(image_paths):
                page_num = start_page + i
                self.logger.info(f"处理第 {page_num} 页...")
                
                try:
                    # 使用AI模型识别信息价数据
                    recognition_result = await self.ai_processor.process_image_with_price_info_prompt(
                        image_path, model_type
                    )
                    
                    if recognition_result:
                        # 解析识别结果
                        page_data = self._parse_price_info_result(recognition_result, page_num)
                        if page_data:
                            all_price_data.extend(page_data)
                            processing_stats['processed_pages'] += 1
                            processing_stats['total_chapters'] += len(page_data)
                            processing_stats['total_price_items'] += sum(
                                len(chapter.get('price_items', [])) for chapter in page_data
                            )
                        else:
                            processing_stats['failed_pages'].append(page_num)
                    else:
                        processing_stats['failed_pages'].append(page_num)
                        
                except Exception as e:
                    self.logger.error(f"处理第 {page_num} 页时出错: {str(e)}")
                    processing_stats['failed_pages'].append(page_num)
            
            # 3. 生成输出文件
            if all_price_data:
                output_files = await self._generate_output_files(all_price_data, output_dir)
                processing_stats['output_files'] = output_files
                
                success_msg = f"✅ 信息价识别完成！\n"
                success_msg += f"📊 处理统计:\n"
                success_msg += f"   - 总页数: {processing_stats['total_pages']}\n"
                success_msg += f"   - 成功页数: {processing_stats['processed_pages']}\n"
                success_msg += f"   - 识别章节: {processing_stats['total_chapters']}\n"
                success_msg += f"   - 价格条目: {processing_stats['total_price_items']}\n"
                
                if processing_stats['failed_pages']:
                    success_msg += f"   - 失败页面: {processing_stats['failed_pages']}\n"
                
                success_msg += f"📁 输出文件: {len(output_files)} 个"
                
                return True, success_msg, processing_stats
            else:
                return False, "未识别到有效的信息价数据", processing_stats
                
        except Exception as e:
            error_msg = f"信息价处理失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, {}
    
    def _parse_price_info_result(self, recognition_result: str, page_num: int) -> List[Dict]:
        """
        解析AI识别结果

        Args:
            recognition_result: AI识别的原始结果
            page_num: 页码

        Returns:
            解析后的价格信息列表
        """
        try:
            # 首先尝试JSON解析
            json_data = self._try_parse_json(recognition_result, page_num)
            if json_data:
                return json_data

            # 如果JSON解析失败，尝试文本解析
            self.logger.info(f"第 {page_num} 页JSON解析失败，尝试文本解析...")
            text_data = self._parse_text_result(recognition_result, page_num)
            if text_data:
                return text_data

            # 如果都失败了，记录原始结果用于调试
            self.logger.warning(f"第 {page_num} 页解析失败，原始结果: {recognition_result[:500]}...")
            return []

        except Exception as e:
            self.logger.error(f"第 {page_num} 页数据解析失败: {str(e)}")
            return []

    def _try_parse_json(self, recognition_result: str, page_num: int) -> List[Dict]:
        """尝试JSON解析"""
        try:
            # 清理结果文本
            cleaned_result = recognition_result.strip()

            # 尝试直接解析
            if cleaned_result.startswith('{'):
                data = json.loads(cleaned_result)
            else:
                # 尝试提取JSON部分
                json_patterns = [
                    r'\{[^{}]*"chapters"[^{}]*\[[^\]]*\][^{}]*\}',  # 简单JSON
                    r'\{.*?"chapters".*?\[.*?\].*?\}',  # 包含chapters的JSON
                    r'\{.*\}',  # 任何JSON对象
                ]

                data = None
                for pattern in json_patterns:
                    json_match = re.search(pattern, cleaned_result, re.DOTALL)
                    if json_match:
                        try:
                            data = json.loads(json_match.group())
                            break
                        except json.JSONDecodeError:
                            continue

                if not data:
                    return None

            # 验证数据结构
            if not isinstance(data, dict):
                return None

            # 如果没有chapters字段，尝试构建
            if 'chapters' not in data:
                # 检查是否有其他可用的数据结构
                if 'price_items' in data:
                    # 直接包含价格条目的情况
                    data = {
                        'page_header': data.get('page_header', '工程造价信息'),
                        'chapters': [{
                            'chapter_code': '01',
                            'chapter_name': '信息价数据',
                            'remarks': '',
                            'price_items': data['price_items']
                        }]
                    }
                else:
                    return None

            # 处理数据
            page_header = data.get('page_header', '工程造价信息')
            chapters = data.get('chapters', [])

            # 为每个章节添加页面和页眉信息
            for chapter in chapters:
                chapter['page_number'] = page_num
                chapter['page_header'] = page_header

                # 验证价格条目数据
                price_items = chapter.get('price_items', [])
                validated_items = []

                for item in price_items:
                    if self._validate_price_item(item):
                        validated_items.append(item)

                chapter['price_items'] = validated_items

            return chapters

        except json.JSONDecodeError as e:
            self.logger.debug(f"第 {page_num} 页JSON解析失败: {str(e)}")
            return None
        except Exception as e:
            self.logger.debug(f"第 {page_num} 页JSON处理失败: {str(e)}")
            return None

    def _parse_text_result(self, recognition_result: str, page_num: int) -> List[Dict]:
        """解析文本格式的识别结果"""
        try:
            # 初始化结果
            chapters = []
            current_chapter = {
                'page_number': page_num,
                'page_header': '工程造价信息',
                'chapter_code': '01',
                'chapter_name': '信息价数据',
                'remarks': '',
                'price_items': []
            }

            # 按行分析文本
            lines = recognition_result.split('\n')

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 尝试识别页眉信息
                if any(keyword in line for keyword in ['工程造价信息', '市场参考价', '厂家参考价']):
                    current_chapter['page_header'] = line
                    continue

                # 尝试识别章节信息
                chapter_match = re.search(r'(\d+)\.?\s*([^（]+)（编码[：:]\s*(\d+)\）', line)
                if chapter_match:
                    current_chapter['chapter_code'] = chapter_match.group(3)
                    current_chapter['chapter_name'] = chapter_match.group(2).strip()
                    continue

                # 尝试识别价格条目
                price_item = self._extract_price_item_from_line(line)
                if price_item:
                    current_chapter['price_items'].append(price_item)

            # 如果找到了价格条目，添加到结果中
            if current_chapter['price_items']:
                chapters.append(current_chapter)

            return chapters

        except Exception as e:
            self.logger.error(f"第 {page_num} 页文本解析失败: {str(e)}")
            return []

    def _extract_price_item_from_line(self, line: str) -> Dict:
        """从文本行中提取价格条目信息"""
        try:
            # 清理行内容
            line = line.strip()
            if not line or len(line) < 10:
                return None

            # 尝试匹配常见的价格信息格式
            patterns = [
                # 格式1: 10位资源编号 产品名称 规格型号 单位 含税价 不含税价
                r'(\d{10})\s+([^\d]+?)\s+([^\d]+?)\s+(\S+?)\s+(\d+\.?\d*)\s+(\d+\.?\d*)',
                # 格式2: 10位资源编号 产品名称 规格型号 单位 价格
                r'(\d{10})\s+([^\d]+?)\s+([^\d]+?)\s+(\S+?)\s+(\d+\.?\d*)',
                # 格式3: 资源编号 产品名称 单位 含税价 不含税价
                r'(\d{8,})\s+([^\d]+?)\s+(\S+?)\s+(\d+\.?\d*)\s+(\d+\.?\d*)',
                # 格式4: 简单格式 - 编号 名称 单位 价格
                r'(\d{6,})\s+([^\d]+?)\s+(\S+?)\s+(\d+\.?\d*)',
                # 格式5: 带分隔符的格式
                r'(\d+)\s*[：:]\s*([^，,\d]+)[，,]?\s*([^，,\d]*)[，,]?\s*(\S+?)[，,]?\s*(\d+\.?\d*)',
            ]

            for i, pattern in enumerate(patterns, 1):
                match = re.search(pattern, line)
                if match:
                    groups = match.groups()
                    self.logger.debug(f"使用模式 {i} 匹配成功: {groups}")

                    if len(groups) >= 4:
                        # 根据匹配的组数确定字段映射
                        if len(groups) == 6:  # 完整格式：编号 名称 规格 单位 含税价 不含税价
                            return {
                                'resource_code': groups[0],
                                'product_name': groups[1].strip(),
                                'specifications': groups[2].strip(),
                                'unit': groups[3].strip(),
                                'price_with_tax': groups[4],
                                'price_without_tax': groups[5]
                            }
                        elif len(groups) == 5:  # 5个字段
                            if i <= 2:  # 前两个模式：编号 名称 规格 单位 价格
                                return {
                                    'resource_code': groups[0],
                                    'product_name': groups[1].strip(),
                                    'specifications': groups[2].strip(),
                                    'unit': groups[3].strip(),
                                    'price_with_tax': groups[4],
                                    'price_without_tax': ''
                                }
                            else:  # 后面的模式：编号 名称 单位 含税价 不含税价
                                return {
                                    'resource_code': groups[0],
                                    'product_name': groups[1].strip(),
                                    'specifications': '',
                                    'unit': groups[2].strip(),
                                    'price_with_tax': groups[3],
                                    'price_without_tax': groups[4]
                                }
                        elif len(groups) == 4:  # 4个字段：编号 名称 单位 价格
                            return {
                                'resource_code': groups[0],
                                'product_name': groups[1].strip(),
                                'specifications': '',
                                'unit': groups[2].strip(),
                                'price_with_tax': groups[3],
                                'price_without_tax': ''
                            }

            # 如果所有模式都不匹配，尝试简单的空格分割
            parts = line.split()
            if len(parts) >= 4:
                # 检查第一个部分是否是数字（资源编号）
                if parts[0].isdigit() and len(parts[0]) >= 6:
                    # 尝试识别价格（通常是最后的数字）
                    prices = []
                    non_price_parts = []

                    for part in parts[1:]:  # 跳过资源编号
                        if re.match(r'^\d+\.?\d*$', part):
                            prices.append(part)
                        else:
                            non_price_parts.append(part)

                    if prices and non_price_parts:
                        # 假设最后一个非价格部分是单位
                        unit = non_price_parts[-1] if non_price_parts else ''
                        # 其余部分组成产品名称
                        product_name = ' '.join(non_price_parts[:-1]) if len(non_price_parts) > 1 else ''

                        return {
                            'resource_code': parts[0],
                            'product_name': product_name,
                            'specifications': '',
                            'unit': unit,
                            'price_with_tax': prices[0] if len(prices) > 0 else '',
                            'price_without_tax': prices[1] if len(prices) > 1 else ''
                        }

            return None

        except Exception as e:
            self.logger.debug(f"提取价格条目失败: {str(e)}")
            return None
    
    def _validate_price_item(self, item: Dict) -> bool:
        """
        验证价格条目数据的有效性
        
        Args:
            item: 价格条目数据
            
        Returns:
            是否有效
        """
        required_fields = ['resource_code', 'product_name', 'unit']
        
        # 检查必需字段
        for field in required_fields:
            if not item.get(field):
                return False
        
        # 检查资源编号格式（应该是数字）
        resource_code = str(item.get('resource_code', '')).strip()
        if not resource_code or not resource_code.isdigit():
            return False
        
        return True
    
    async def _generate_output_files(self, price_data: List[Dict], output_dir: str) -> List[str]:
        """
        生成输出文件
        
        Args:
            price_data: 价格数据列表
            output_dir: 输出目录
            
        Returns:
            生成的文件路径列表
        """
        try:
            os.makedirs(output_dir, exist_ok=True)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_files = []
            
            # 1. 生成CSV文件
            csv_data = []
            
            for chapter in price_data:
                page_header = chapter.get('page_header', '')
                chapter_code = chapter.get('chapter_code', '')
                chapter_name = chapter.get('chapter_name', '')
                remarks = chapter.get('remarks', '')
                
                for item in chapter.get('price_items', []):
                    csv_row = {
                        '页眉标识': page_header,
                        '章节编号': chapter_code,
                        '章节名称': chapter_name,
                        '资源编号': item.get('resource_code', ''),
                        '产品名称': item.get('product_name', ''),
                        '规格型号及特征': item.get('specifications', ''),
                        '计量单位': item.get('unit', ''),
                        '市场参考价（含税）': item.get('price_with_tax', ''),
                        '市场参考价（不含税）': item.get('price_without_tax', ''),
                        '备注': remarks
                    }
                    csv_data.append(csv_row)
            
            if csv_data:
                csv_file = os.path.join(output_dir, f"price_info_result_{timestamp}.csv")
                df = pd.DataFrame(csv_data)
                df.to_csv(csv_file, index=False, encoding='utf-8-sig')
                output_files.append(csv_file)
                self.logger.info(f"生成CSV文件: {csv_file}")
            
            # 2. 生成JSON文件
            json_file = os.path.join(output_dir, f"price_info_result_{timestamp}.json")
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'metadata': {
                        'export_date': datetime.now().isoformat(),
                        'total_chapters': len(price_data),
                        'total_items': sum(len(ch.get('price_items', [])) for ch in price_data),
                        'format': 'Price Information Export'
                    },
                    'data': price_data
                }, f, ensure_ascii=False, indent=2)
            output_files.append(json_file)
            self.logger.info(f"生成JSON文件: {json_file}")
            
            return output_files
            
        except Exception as e:
            self.logger.error(f"生成输出文件失败: {str(e)}")
            return []
    
    def merge_price_info_with_quotas(
        self,
        price_info_file: str,
        quota_resources_file: str,
        output_dir: str = "output"
    ) -> Tuple[bool, str, str]:
        """
        将信息价数据与定额资源数据进行智能合并
        
        Args:
            price_info_file: 信息价CSV文件路径
            quota_resources_file: 定额资源CSV文件路径
            output_dir: 输出目录
            
        Returns:
            (成功标志, 状态消息, 输出文件路径)
        """
        try:
            # 读取数据文件
            price_df = pd.read_csv(price_info_file, encoding='utf-8-sig')
            quota_df = pd.read_csv(quota_resources_file, encoding='utf-8-sig')
            
            # 基于资源编号进行匹配
            merged_data = []
            match_count = 0
            
            for _, quota_row in quota_df.iterrows():
                quota_resource_code = str(quota_row.get('资源编号', '')).strip()
                
                # 查找匹配的信息价数据
                price_matches = price_df[price_df['资源编号'].astype(str).str.strip() == quota_resource_code]
                
                if not price_matches.empty:
                    # 找到匹配的信息价
                    price_row = price_matches.iloc[0]
                    match_count += 1
                    
                    # 合并数据
                    merged_row = quota_row.to_dict()
                    merged_row.update({
                        '信息价_产品名称': price_row.get('产品名称', ''),
                        '信息价_规格型号': price_row.get('规格型号及特征', ''),
                        '信息价_含税价格': price_row.get('市场参考价（含税）', ''),
                        '信息价_不含税价格': price_row.get('市场参考价（不含税）', ''),
                        '信息价_章节': price_row.get('章节名称', ''),
                        '匹配状态': '已匹配'
                    })
                else:
                    # 未找到匹配的信息价
                    merged_row = quota_row.to_dict()
                    merged_row.update({
                        '信息价_产品名称': '',
                        '信息价_规格型号': '',
                        '信息价_含税价格': '',
                        '信息价_不含税价格': '',
                        '信息价_章节': '',
                        '匹配状态': '未匹配'
                    })
                
                merged_data.append(merged_row)
            
            # 生成输出文件
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = os.path.join(output_dir, f"merged_quota_price_info_{timestamp}.csv")
            
            merged_df = pd.DataFrame(merged_data)
            merged_df.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            success_msg = f"✅ 信息价与定额数据合并完成！\n"
            success_msg += f"📊 合并统计:\n"
            success_msg += f"   - 定额资源总数: {len(quota_df)}\n"
            success_msg += f"   - 匹配成功数量: {match_count}\n"
            success_msg += f"   - 匹配率: {match_count/len(quota_df)*100:.1f}%\n"
            success_msg += f"📁 输出文件: {output_file}"
            
            return True, success_msg, output_file
            
        except Exception as e:
            error_msg = f"合并失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, ""
