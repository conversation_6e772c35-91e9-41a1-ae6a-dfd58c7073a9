#!/usr/bin/env python3
"""
定额创建工具界面组件
Quota Creation Tool Interface Components
"""

import gradio as gr
import pandas as pd
from typing import List, Dict, Any, Tuple, Optional

try:
    from .advanced_quota_manager import AdvancedQuotaManager
    from .price_info_interface import PriceInfoInterface
except ImportError:
    try:
        from advanced_quota_manager import AdvancedQuotaManager
        from price_info_interface import PriceInfoInterface
    except ImportError:
        print("警告: 无法导入AdvancedQuotaManager或PriceInfoInterface")
        AdvancedQuotaManager = None
        PriceInfoInterface = None

class AdvancedQuotaInterface:
    """定额创建工具界面类"""

    def __init__(self, quota_manager=None, pdf_storage=None):
        if quota_manager:
            self.manager = quota_manager
            self.quota_manager = quota_manager  # 为了兼容性
        elif AdvancedQuotaManager:
            self.manager = AdvancedQuotaManager()
            self.quota_manager = self.manager
        else:
            self.manager = None
            self.quota_manager = None

        # 保存PDF存储管理器的引用
        self.pdf_storage = pdf_storage

        if PriceInfoInterface:
            self.price_info_interface = PriceInfoInterface()
        else:
            self.price_info_interface = None

        self.current_table = None
        self.current_page = 0
        self.page_size = 50

    def create_database_connection_interface(self):
        """创建数据库连接界面"""
        with gr.Group(elem_classes="feature-card"):
            gr.HTML("""
                <h3 style="color: #667eea; margin-bottom: 15px;">
                    <span class="icon">🔗</span>高级数据库连接
                </h3>
                <p style="color: #666; margin-bottom: 15px;">
                    支持多种数据库类型的连接和管理
                </p>
            """)
            
            # 数据库类型选择
            db_type_dropdown = gr.Dropdown(
                label="🗄️ 数据库类型",
                choices=[
                    ("📱 SQLite本地数据库", "sqlite"),
                    ("🐘 PostgreSQL数据库", "postgresql"),
                    ("🐬 MySQL数据库", "mysql"),
                    ("🍃 MongoDB数据库", "mongodb"),
                    ("🏢 SQL Server数据库", "sql_server"),
                    ("🔶 Oracle数据库", "oracle")
                ],
                value="sqlite",
                info="选择要连接的数据库类型",
                interactive=True,
                elem_id="advanced_db_type_dropdown"
            )
            
            # 动态连接配置区域
            with gr.Group() as connection_config:
                # SQLite配置
                with gr.Group(visible=True) as sqlite_config:
                    db_path_input = gr.Textbox(
                        label="📂 数据库文件路径",
                        placeholder="output/enterprise_quota.db",
                        value="output/enterprise_quota.db",
                        info="SQLite数据库文件的完整路径"
                    )
                
                # 其他数据库配置
                with gr.Group(visible=False) as other_db_config:
                    with gr.Row():
                        db_host_input = gr.Textbox(
                            label="🌐 主机地址",
                            placeholder="localhost",
                            value="localhost"
                        )
                        db_port_input = gr.Number(
                            label="🔌 端口号",
                            value=5432,
                            precision=0
                        )
                    
                    with gr.Row():
                        db_name_input = gr.Textbox(
                            label="🗄️ 数据库名",
                            placeholder="enterprise_quota"
                        )
                        db_user_input = gr.Textbox(
                            label="👤 用户名",
                            placeholder="postgres"
                        )
                    
                    db_password_input = gr.Textbox(
                        label="🔐 密码",
                        type="password",
                        placeholder="请输入密码"
                    )
            
            # 连接操作按钮
            with gr.Row():
                test_connection_btn = gr.Button("🧪 测试连接", elem_classes="btn-secondary")
                connect_db_btn = gr.Button("🔌 连接数据库", elem_classes="btn-primary")
                disconnect_db_btn = gr.Button("🔌 断开连接", elem_classes="btn-danger", visible=False)
            
            # 连接状态显示
            connection_status = gr.HTML(
                value="<p style='color: #666;'>等待连接数据库...</p>",
                label="连接状态"
            )
        
        return {
            'db_type_dropdown': db_type_dropdown,
            'sqlite_config': sqlite_config,
            'other_db_config': other_db_config,
            'db_path_input': db_path_input,
            'db_host_input': db_host_input,
            'db_port_input': db_port_input,
            'db_name_input': db_name_input,
            'db_user_input': db_user_input,
            'db_password_input': db_password_input,
            'test_connection_btn': test_connection_btn,
            'connect_db_btn': connect_db_btn,
            'disconnect_db_btn': disconnect_db_btn,
            'connection_status': connection_status
        }

    def create_quota_revision_interface(self):
        """创建定额核对修订界面"""
        with gr.Group(elem_classes="feature-card"):
            gr.HTML("""
                <h3 style="color: #667eea; margin-bottom: 15px;">
                    <span class="icon">📝</span>识别定额修订
                </h3>
                <p style="color: #666; margin-bottom: 15px;">
                    对比PDF原文，核对和修订AI识别的定额数据，支持实时增删查改
                </p>
            """)

            # 数据库状态和加载
            with gr.Row():
                with gr.Column(scale=2):
                    revision_db_status = gr.HTML(
                        value="<p style='color: #666;'>正在检查数据库连接状态...</p>",
                        label="数据库连接状态"
                    )
                with gr.Column(scale=2):
                    revision_connection_info = gr.HTML(
                        value="<p style='color: #666;'>暂无连接信息</p>",
                        label="连接详情"
                    )
                with gr.Column(scale=1):
                    load_revision_data_btn = gr.Button("📥 加载数据", elem_classes="btn-primary")
                    refresh_revision_data_btn = gr.Button("🔄 刷新状态", elem_classes="btn-secondary")
                    check_connection_btn = gr.Button("🔍 检查连接", elem_classes="btn-info")
                    test_btn = gr.Button("🧪 测试按钮", elem_classes="btn-warning")

            # 主要内容区域
            with gr.Row():
                # 左侧：定额数据编辑区（Excel风格）
                with gr.Column(scale=2):
                    gr.HTML("<h4 style='color: #667eea; margin: 10px 0;'>📊 定额数据表格（Excel风格编辑）</h4>")

                    # 数据统计信息
                    data_stats = gr.HTML(
                        value="<p style='color: #666;'>暂无数据</p>",
                        label="数据统计"
                    )

                    # 主数据表格 - Excel风格
                    quota_data_table = gr.Dataframe(
                        label="📋 定额与资源数据表",
                        headers=[
                            "类型", "定额编码", "定额名称", "单位", "工作内容",
                            "资源编码", "资源名称", "资源类别", "资源单位", "消耗量"
                        ],
                        datatype=[
                            "str", "str", "str", "str", "str",
                            "str", "str", "str", "str", "number"
                        ],
                        interactive=True,
                        wrap=True,
                        row_count=(1, "dynamic"),
                        col_count=(10, "fixed")
                    )

                    # 数据操作按钮
                    with gr.Row():
                        add_quota_btn = gr.Button("➕ 添加定额", elem_classes="btn-success", scale=1)
                        add_resource_btn = gr.Button("🔧 添加资源", elem_classes="btn-info", scale=1)
                        save_all_btn = gr.Button("💾 保存全部", elem_classes="btn-primary", scale=1)
                        delete_selected_btn = gr.Button("🗑️ 删除选中", elem_classes="btn-danger", scale=1)
                        export_excel_btn = gr.Button("📤 导出Excel", elem_classes="btn-secondary", scale=1)

                # 右侧：PDF预览区
                with gr.Column(scale=1):
                    gr.HTML("<h4 style='color: #667eea; margin: 10px 0;'>📄 PDF原文对比</h4>")

                    # PDF文件选择
                    with gr.Row():
                        # 获取初始PDF列表
                        try:
                            initial_pdf_choices = self._handle_refresh_pdf_list()
                        except Exception as e:
                            print(f"获取初始PDF列表失败: {e}")
                            initial_pdf_choices = []

                        pdf_file_selector = gr.Dropdown(
                            label="📁 选择PDF文件",
                            choices=initial_pdf_choices,
                            info="选择要对比的PDF文件",
                            scale=2
                        )
                        refresh_pdf_list_btn = gr.Button("🔄 刷新", elem_classes="btn-secondary", scale=1)

                    # PDF预览控制
                    with gr.Row():
                        pdf_page_input = gr.Number(
                            label="页码",
                            value=1,
                            minimum=1,
                            precision=0,
                            scale=1
                        )
                        prev_pdf_page_btn = gr.Button("⬅️", elem_classes="btn-secondary", scale=1)
                        next_pdf_page_btn = gr.Button("➡️", elem_classes="btn-secondary", scale=1)
                        zoom_pdf_btn = gr.Button("🔍 缩放", elem_classes="btn-secondary", scale=1)

                    # PDF显示区域
                    pdf_preview = gr.Image(
                        label="PDF预览",
                        type="filepath",
                        interactive=False,
                        height=600
                    )

            # 状态信息和操作日志
            with gr.Row():
                revision_status = gr.HTML(
                    value="<p style='color: #666;'>请先连接数据库并加载数据</p>",
                    label="操作状态"
                )
                revision_log = gr.Textbox(
                    label="📝 操作日志",
                    lines=3,
                    interactive=False,
                    placeholder="操作记录将显示在这里..."
                )

        return {
            'revision_db_status': revision_db_status,
            'revision_connection_info': revision_connection_info,
            'load_revision_data_btn': load_revision_data_btn,
            'refresh_revision_data_btn': refresh_revision_data_btn,
            'check_connection_btn': check_connection_btn,
            'test_btn': test_btn,
            'data_stats': data_stats,
            'quota_data_table': quota_data_table,
            'add_quota_btn': add_quota_btn,
            'add_resource_btn': add_resource_btn,
            'save_all_btn': save_all_btn,
            'delete_selected_btn': delete_selected_btn,
            'export_excel_btn': export_excel_btn,
            'pdf_file_selector': pdf_file_selector,
            'refresh_pdf_list_btn': refresh_pdf_list_btn,
            'pdf_page_input': pdf_page_input,
            'prev_pdf_page_btn': prev_pdf_page_btn,
            'next_pdf_page_btn': next_pdf_page_btn,
            'zoom_pdf_btn': zoom_pdf_btn,
            'pdf_preview': pdf_preview,
            'revision_status': revision_status,
            'revision_log': revision_log
        }

    def create_quota_search_interface(self):
        """创建定额搜索界面"""
        with gr.Group(elem_classes="feature-card"):
            gr.HTML("""
                <h3 style="color: #667eea; margin-bottom: 15px;">
                    <span class="icon">🔍</span>智能定额搜索
                </h3>
                <p style="color: #666; margin-bottom: 15px;">
                    高级定额搜索，支持关联资源查看和价格分析
                </p>
            """)
            
            # 搜索区域
            with gr.Row():
                search_input = gr.Textbox(
                    label="🔎 搜索关键词",
                    placeholder="输入定额编号、名称或工作内容...",
                    scale=3,
                    info="支持模糊搜索"
                )
                search_btn = gr.Button("🔍 搜索", elem_classes="btn-primary", scale=1)
            
            # 搜索结果
            quota_search_results = gr.Dataframe(
                label="📋 定额项搜索结果",
                interactive=True,
                visible=False,
                wrap=True
            )
            
            # 关联资源显示
            with gr.Row():
                with gr.Column(scale=1):
                    selected_quota_info = gr.HTML(
                        value="<p style='color: #666;'>请选择一个定额项查看详情</p>",
                        label="定额项详情"
                    )
                
                with gr.Column(scale=2):
                    related_resources_display = gr.Dataframe(
                        label="🔧 关联资源",
                        interactive=False,
                        visible=False,
                        wrap=True
                    )
        
        return {
            'search_input': search_input,
            'search_btn': search_btn,
            'quota_search_results': quota_search_results,
            'selected_quota_info': selected_quota_info,
            'related_resources_display': related_resources_display
        }

    def create_resource_price_interface(self):
        """创建资源价格管理界面"""
        with gr.Group(elem_classes="feature-card"):
            gr.HTML("""
                <h3 style="color: #667eea; margin-bottom: 15px;">
                    <span class="icon">💰</span>资源价格管理
                </h3>
                <p style="color: #666; margin-bottom: 15px;">
                    智能资源价格填报，自动更新定额项总价
                </p>
            """)
            
            # 资源列表
            unique_resources_display = gr.Dataframe(
                label="📊 唯一资源列表",
                headers=["资源编号", "资源名称", "类别", "单位", "平均单价", "最低单价", "最高单价", "使用次数"],
                interactive=True,
                visible=False,
                wrap=True
            )
            
            # 价格更新区域
            with gr.Row():
                with gr.Column(scale=2):
                    selected_resource_info = gr.HTML(
                        value="<p style='color: #666;'>请选择一个资源项进行价格更新</p>",
                        label="选中资源信息"
                    )
                
                with gr.Column(scale=1):
                    new_price_input = gr.Number(
                        label="💰 新单价",
                        minimum=0,
                        step=0.01,
                        info="输入资源的新单价"
                    )
                    
                    update_price_btn = gr.Button("💰 更新价格", elem_classes="btn-warning")
                    recalculate_btn = gr.Button("🔄 重新计算定额价格", elem_classes="btn-success")
            
            # 操作按钮
            with gr.Row():
                load_resources_btn = gr.Button("📊 加载资源列表", elem_classes="btn-primary")
                export_resources_btn = gr.Button("📤 导出资源列表", elem_classes="btn-info")

            # 信息价载入功能区域
            gr.HTML("<hr style='margin: 30px 0;'>")

            with gr.Group(elem_classes="price-info-section"):
                gr.HTML("""
                    <h4 style="color: #667eea; margin-bottom: 15px;">
                        <span class="icon">📊</span>信息价载入功能
                    </h4>
                    <p style="color: #666; margin-bottom: 20px;">
                        智能从数据库中找到信息价数据，自动匹配资源编号并更新价格
                    </p>
                """)

                # 信息价数据库选择
                with gr.Row():
                    with gr.Column(scale=2):
                        price_info_databases = gr.Dropdown(
                            label="🗄️ 选择信息价数据库",
                            choices=[],
                            value=None,
                            info="系统将自动扫描可用的信息价数据库文件",
                            interactive=True
                        )

                    with gr.Column(scale=1):
                        scan_price_db_btn = gr.Button("🔍 扫描信息价数据库", elem_classes="btn-secondary")

                # 载入操作
                with gr.Row():
                    load_price_info_btn = gr.Button("📥 载入信息价", elem_classes="btn-success", size="lg")

                # 载入状态和结果
                price_info_load_status = gr.HTML(
                    value="<p style='color: #666;'>请先扫描并选择信息价数据库</p>",
                    label="载入状态"
                )

                # 匹配结果统计
                price_match_stats = gr.HTML(
                    value="",
                    label="匹配统计"
                )
            
            # 操作结果
            price_update_status = gr.HTML(
                value="<p style='color: #666;'>等待操作...</p>",
                label="操作状态"
            )

            # 隐藏状态变量
            selected_resource_code = gr.Textbox(
                value="",
                visible=False,
                interactive=False
            )

        return {
            'unique_resources_display': unique_resources_display,
            'selected_resource_info': selected_resource_info,
            'new_price_input': new_price_input,
            'update_price_btn': update_price_btn,
            'recalculate_btn': recalculate_btn,
            'load_resources_btn': load_resources_btn,
            'export_resources_btn': export_resources_btn,
            'price_update_status': price_update_status,
            'selected_resource_code': selected_resource_code,
            # 信息价载入相关组件
            'price_info_databases': price_info_databases,
            'scan_price_db_btn': scan_price_db_btn,
            'load_price_info_btn': load_price_info_btn,
            'price_info_load_status': price_info_load_status,
            'price_match_stats': price_match_stats
        }

    def create_complete_interface(self):
        """创建完整的高级定额管理界面"""
        with gr.Tab("🔗 数据库连接"):
            connection_components = self.create_database_connection_interface()
        
        with gr.Tab("📝 识别定额修订"):
            revision_components = self.create_quota_revision_interface()
        
        with gr.Tab("🔍 定额搜索"):
            search_components = self.create_quota_search_interface()
        
        with gr.Tab("💰 资源价格"):
            price_components = self.create_resource_price_interface()

        with gr.Tab("📊 信息价识别"):
            price_info_components = self.create_price_info_interface()

        # 绑定定额修订事件
        self._bind_revision_events(revision_components)

        # 页面加载时自动检查连接状态
        self._initialize_revision_status(revision_components)

        return {
            'connection': connection_components,
            'revision': revision_components,
            'search': search_components,
            'price': price_components,
            'price_info': price_info_components
        }

    def create_price_info_interface(self, api_key_input=None):
        """创建信息价识别界面"""
        if not self.price_info_interface:
            with gr.Group():
                gr.HTML("""
                    <div style="text-align: center; padding: 20px; color: #666;">
                        <h3>⚠️ 信息价识别功能不可用</h3>
                        <p>PriceInfoInterface模块未正确加载</p>
                    </div>
                """)
            return {}

        # 信息价识别区域
        recognition_components = self.price_info_interface.create_price_info_recognition_interface()

        # 信息价合并区域
        merge_components = self.price_info_interface.create_price_merge_interface()

        # 绑定PDF预览相关事件
        self._bind_price_info_events(recognition_components, api_key_input)

        return {
            'recognition': recognition_components,
            'merge': merge_components
        }

    def _bind_price_info_events(self, components, api_key_input=None):
        """绑定信息价识别相关事件"""
        try:
            # PDF文件上传事件
            components['pdf_input'].upload(
                fn=self.price_info_interface.handle_pdf_upload,
                inputs=[components['pdf_input']],
                outputs=[
                    components['pdf_viewer'],
                    components['pdf_viewer'],  # 控制可见性
                    components['current_page'],
                    components['total_pages']
                ]
            )

            # 页面导航事件
            components['current_page'].change(
                fn=self.price_info_interface.show_page,
                inputs=[components['pdf_input'], components['current_page']],
                outputs=[components['pdf_viewer']]
            )

            components['prev_page_btn'].click(
                fn=self.price_info_interface.prev_page,
                inputs=[components['pdf_input'], components['current_page']],
                outputs=[components['current_page'], components['pdf_viewer']]
            )

            components['next_page_btn'].click(
                fn=self.price_info_interface.next_page,
                inputs=[
                    components['pdf_input'],
                    components['current_page'],
                    components['total_pages']
                ],
                outputs=[components['current_page'], components['pdf_viewer']]
            )

            components['zoom_btn'].click(
                fn=self.price_info_interface.zoom_page,
                inputs=[components['pdf_input'], components['current_page']],
                outputs=[components['pdf_viewer']]
            )

            # 处理按钮事件 - 传递API密钥
            if api_key_input:
                components['process_btn'].click(
                    fn=self.price_info_interface.process_price_info,
                    inputs=[
                        components['pdf_input'],
                        components['start_page'],
                        components['end_page'],
                        components['model_type'],
                        api_key_input  # 传递API密钥
                    ],
                    outputs=[
                        components['status_output'],
                        components['stats_output'],
                        components['preview_output'],
                        components['download_output']
                    ]
                )
            else:
                components['process_btn'].click(
                    fn=self.price_info_interface.process_price_info,
                    inputs=[
                        components['pdf_input'],
                        components['start_page'],
                        components['end_page'],
                        components['model_type']
                    ],
                    outputs=[
                        components['status_output'],
                        components['stats_output'],
                        components['preview_output'],
                        components['download_output']
                    ]
                )

        except Exception as e:
            print(f"绑定信息价识别事件失败: {str(e)}")

    def _bind_revision_events(self, components):
        """绑定定额修订相关事件"""
        try:
            if not self.quota_manager or not self.quota_manager.revision_processor:
                print("定额修订处理器未初始化")
                return

            revision_processor = self.quota_manager.revision_processor

            # 检查连接状态事件
            components['check_connection_btn'].click(
                fn=self._handle_check_connection,
                inputs=[],
                outputs=[
                    components['revision_db_status'],
                    components['revision_connection_info'],
                    components['revision_log']
                ]
            )

            # 测试按钮事件
            components['test_btn'].click(
                fn=self._handle_test_button,
                inputs=[],
                outputs=[components['revision_log']]
            )

            # 加载数据事件
            components['load_revision_data_btn'].click(
                fn=self._handle_load_revision_data,
                inputs=[],
                outputs=[
                    components['revision_db_status'],
                    components['revision_connection_info'],
                    components['data_stats'],
                    components['quota_data_table'],
                    components['revision_status'],
                    components['revision_log']
                ]
            )

            # 刷新状态事件
            components['refresh_revision_data_btn'].click(
                fn=self._handle_refresh_status,
                inputs=[],
                outputs=[
                    components['revision_db_status'],
                    components['revision_connection_info'],
                    components['data_stats'],
                    components['quota_data_table'],
                    components['revision_status'],
                    components['revision_log']
                ]
            )

            # 保存全部数据事件
            components['save_all_btn'].click(
                fn=self._handle_save_all_data,
                inputs=[components['quota_data_table']],
                outputs=[
                    components['revision_status'],
                    components['revision_log']
                ]
            )

            # 添加定额事件
            components['add_quota_btn'].click(
                fn=self._handle_add_quota,
                inputs=[components['quota_data_table']],
                outputs=[
                    components['quota_data_table'],
                    components['revision_log']
                ]
            )

            # 添加资源事件
            components['add_resource_btn'].click(
                fn=self._handle_add_resource,
                inputs=[components['quota_data_table']],
                outputs=[
                    components['quota_data_table'],
                    components['revision_log']
                ]
            )

            # PDF相关事件
            components['refresh_pdf_list_btn'].click(
                fn=self._handle_refresh_pdf_list,
                inputs=[],
                outputs=[components['pdf_file_selector']]
            )

            components['pdf_file_selector'].change(
                fn=self._handle_pdf_selection,
                inputs=[
                    components['pdf_file_selector'],
                    components['pdf_page_input']
                ],
                outputs=[components['pdf_preview']]
            )

            components['pdf_page_input'].change(
                fn=self._handle_pdf_page_change,
                inputs=[
                    components['pdf_file_selector'],
                    components['pdf_page_input']
                ],
                outputs=[components['pdf_preview']]
            )

            components['prev_pdf_page_btn'].click(
                fn=self._handle_prev_pdf_page,
                inputs=[
                    components['pdf_file_selector'],
                    components['pdf_page_input']
                ],
                outputs=[
                    components['pdf_page_input'],
                    components['pdf_preview']
                ]
            )

            components['next_pdf_page_btn'].click(
                fn=self._handle_next_pdf_page,
                inputs=[
                    components['pdf_file_selector'],
                    components['pdf_page_input']
                ],
                outputs=[
                    components['pdf_page_input'],
                    components['pdf_preview']
                ]
            )

        except Exception as e:
            print(f"绑定定额修订事件失败: {str(e)}")

    def _initialize_revision_status(self, components):
        """初始化定额修订状态"""
        try:
            # 页面加载时自动检查连接状态
            def check_initial_status():
                return self._handle_check_connection()

            # 设置初始状态检查
            components['revision_db_status'].value, components['revision_connection_info'].value, _ = check_initial_status()

        except Exception as e:
            print(f"初始化定额修订状态失败: {str(e)}")

    def _handle_test_button(self):
        """处理测试按钮点击"""
        print(f"🧪 [DEBUG] 测试按钮被点击了！")
        return "🧪 测试按钮点击成功！"

    def _handle_check_connection(self):
        """处理检查连接状态"""
        try:
            if not self.quota_manager:
                return (
                    "<p style='color: #f56565;'>❌ 定额管理器未初始化</p>",
                    "<p style='color: #666;'>暂无连接信息</p>",
                    "❌ 管理器未初始化"
                )

            # 检查数据库连接状态
            if self.quota_manager.connection:
                try:
                    # 测试连接是否有效
                    cursor = self.quota_manager.connection.cursor()
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                    cursor.close()

                    # 获取连接信息
                    db_info = {
                        'path': getattr(self.quota_manager, 'db_path', '未知'),
                        'type': getattr(self.quota_manager, 'db_type', '未知')
                    }

                    status_html = f"<p style='color: #48bb78;'>✅ 数据库已连接</p>"

                    info_html = f"""
                    <div style='background: #f0fff4; padding: 10px; border-radius: 5px; border-left: 4px solid #48bb78;'>
                        <p style='margin: 0; color: #2d3748;'><strong>连接信息:</strong></p>
                        <p style='margin: 5px 0; color: #2d3748;'>📂 路径: {db_info['path']}</p>
                        <p style='margin: 5px 0; color: #2d3748;'>🗄️ 类型: {db_info['type']}</p>
                        <p style='margin: 5px 0; color: #2d3748;'>🔗 状态: 连接正常</p>
                    </div>
                    """

                    log_message = f"✅ 数据库连接检查通过: {db_info['path']}"

                except Exception as e:
                    status_html = f"<p style='color: #f56565;'>❌ 数据库连接异常: {str(e)}</p>"
                    info_html = "<p style='color: #666;'>连接异常，请重新连接数据库</p>"
                    log_message = f"❌ 连接检查失败: {str(e)}"
            else:
                status_html = "<p style='color: #ed8936;'>⚠️ 未连接数据库</p>"
                info_html = """
                <div style='background: #fffaf0; padding: 10px; border-radius: 5px; border-left: 4px solid #ed8936;'>
                    <p style='margin: 0; color: #2d3748;'><strong>提示:</strong></p>
                    <p style='margin: 5px 0; color: #2d3748;'>请先在"数据库连接"页面连接数据库</p>
                    <p style='margin: 5px 0; color: #2d3748;'>支持SQLite和PostgreSQL数据库</p>
                </div>
                """
                log_message = "⚠️ 未连接数据库，请先连接数据库"

            return status_html, info_html, log_message

        except Exception as e:
            error_msg = f"❌ 检查连接状态异常: {str(e)}"
            return (
                f"<p style='color: #f56565;'>{error_msg}</p>",
                "<p style='color: #666;'>检查过程中发生错误</p>",
                error_msg
            )

    def _handle_refresh_status(self):
        """处理刷新状态"""
        try:
            # 先检查连接状态
            status_html, info_html, log_message = self._handle_check_connection()

            # 如果连接正常，尝试加载数据
            if self.quota_manager and self.quota_manager.connection:
                try:
                    success, message, quota_data, stats = self.quota_manager.revision_processor.load_from_connected_database()

                    if success:
                        data_stats_html = f"""
                        <div style='background: #f7fafc; padding: 10px; border-radius: 5px; margin: 5px 0;'>
                            <p style='margin: 0; color: #2d3748;'>
                                📊 数据统计: {stats.get('quota_count', 0)} 个定额项,
                                {stats.get('resource_count', 0)} 个资源项,
                                共 {stats.get('total_rows', 0)} 行数据
                            </p>
                        </div>
                        """
                        revision_status = f"✅ 数据刷新成功: {message}"
                        log_message += f" | 数据: {stats.get('quota_count', 0)} 个定额项"
                    else:
                        data_stats_html = "<p style='color: #666;'>暂无数据</p>"
                        quota_data = []
                        revision_status = f"❌ 数据加载失败: {message}"
                        log_message += f" | {message}"

                except Exception as e:
                    data_stats_html = "<p style='color: #666;'>数据加载异常</p>"
                    quota_data = []
                    revision_status = f"❌ 数据加载异常: {str(e)}"
                    log_message += f" | 异常: {str(e)}"
            else:
                data_stats_html = "<p style='color: #666;'>暂无数据</p>"
                quota_data = []
                revision_status = "⚠️ 未连接数据库"

            return status_html, info_html, data_stats_html, quota_data, revision_status, log_message

        except Exception as e:
            error_msg = f"❌ 刷新状态异常: {str(e)}"
            return (
                f"<p style='color: #f56565;'>{error_msg}</p>",
                "<p style='color: #666;'>刷新过程中发生错误</p>",
                "<p style='color: #666;'>暂无数据</p>",
                [],
                error_msg,
                error_msg
            )

    def _handle_load_revision_data(self):
        """处理加载修订数据"""
        try:
            print(f"🔍 [DEBUG] 开始加载修订数据...")
            print(f"🔍 [DEBUG] quota_manager存在: {self.quota_manager is not None}")

            if not self.quota_manager or not self.quota_manager.revision_processor:
                print(f"🔍 [DEBUG] 处理器检查失败:")
                print(f"  quota_manager: {self.quota_manager is not None}")
                print(f"  revision_processor: {self.quota_manager.revision_processor is not None if self.quota_manager else 'N/A'}")
                return (
                    "<p style='color: #f56565;'>❌ 定额修订处理器未初始化</p>",
                    "<p style='color: #666;'>暂无连接信息</p>",
                    "<p style='color: #666;'>暂无数据</p>",
                    [],
                    "❌ 处理器未初始化",
                    "❌ 处理器未初始化"
                )

            print(f"🔍 [DEBUG] revision_processor存在: {self.quota_manager.revision_processor is not None}")
            print(f"🔍 [DEBUG] quota_manager.connection存在: {hasattr(self.quota_manager, 'connection') and self.quota_manager.connection is not None}")

            # 检查数据库连接状态
            if not self.quota_manager.connection:
                status_html = "<p style='color: #f56565;'>❌ 未连接数据库</p>"
                info_html = """
                <div style='background: #fffaf0; padding: 10px; border-radius: 5px; border-left: 4px solid #ed8936;'>
                    <p style='margin: 0; color: #2d3748;'><strong>提示:</strong></p>
                    <p style='margin: 5px 0; color: #2d3748;'>请先在"数据库连接"页面连接数据库</p>
                    <p style='margin: 5px 0; color: #2d3748;'>支持SQLite和PostgreSQL数据库</p>
                </div>
                """
                return (
                    status_html,
                    info_html,
                    "<p style='color: #666;'>暂无数据</p>",
                    [],
                    "❌ 未连接数据库",
                    "❌ 请先连接数据库"
                )

            print(f"🔍 [DEBUG] 开始调用load_from_connected_database...")
            success, message, quota_data, stats = self.quota_manager.revision_processor.load_from_connected_database()

            print(f"🔍 [DEBUG] 数据加载结果:")
            print(f"  成功: {success}")
            print(f"  消息: {message}")
            print(f"  统计: {stats}")
            print(f"  数据行数: {len(quota_data) if quota_data else 0}")

            if success:
                # 获取连接信息
                db_info = {
                    'path': getattr(self.quota_manager, 'db_path', '未知'),
                    'type': getattr(self.quota_manager, 'db_type', '未知')
                }

                status_html = f"<p style='color: #48bb78;'>✅ 数据加载成功</p>"

                info_html = f"""
                <div style='background: #f0fff4; padding: 10px; border-radius: 5px; border-left: 4px solid #48bb78;'>
                    <p style='margin: 0; color: #2d3748;'><strong>连接信息:</strong></p>
                    <p style='margin: 5px 0; color: #2d3748;'>📂 路径: {db_info['path']}</p>
                    <p style='margin: 5px 0; color: #2d3748;'>🗄️ 类型: {db_info['type']}</p>
                    <p style='margin: 5px 0; color: #2d3748;'>📊 定额项: {stats.get('quota_count', 0)} 个</p>
                    <p style='margin: 5px 0; color: #2d3748;'>🔧 资源项: {stats.get('resource_count', 0)} 个</p>
                </div>
                """

                data_stats_html = f"""
                <div style='background: #f7fafc; padding: 10px; border-radius: 5px; margin: 5px 0;'>
                    <p style='margin: 0; color: #2d3748;'>
                        📊 数据统计: {stats.get('quota_count', 0)} 个定额项,
                        {stats.get('resource_count', 0)} 个资源项,
                        共 {stats.get('total_rows', 0)} 行数据
                    </p>
                </div>
                """
                log_message = f"✅ 成功加载数据: {stats.get('quota_count', 0)} 个定额项"
            else:
                status_html = f"<p style='color: #f56565;'>❌ 加载失败: {message}</p>"
                info_html = "<p style='color: #666;'>加载失败，请检查数据库连接</p>"
                data_stats_html = "<p style='color: #666;'>暂无数据</p>"
                quota_data = []
                log_message = f"❌ 加载失败: {message}"

            return status_html, info_html, data_stats_html, quota_data, message, log_message

        except Exception as e:
            error_msg = f"❌ 加载数据异常: {str(e)}"
            return (
                f"<p style='color: #f56565;'>{error_msg}</p>",
                "<p style='color: #666;'>加载过程中发生错误</p>",
                "<p style='color: #666;'>暂无数据</p>",
                [],
                error_msg,
                error_msg
            )

    def _handle_save_all_data(self, table_data):
        """处理保存全部数据"""
        try:
            if not self.quota_manager or not self.quota_manager.revision_processor:
                return "❌ 处理器未初始化", "❌ 处理器未初始化"

            if not table_data:
                return "❌ 没有数据需要保存", "❌ 表格为空"

            success, message = self.quota_manager.revision_processor.save_excel_data(table_data)

            log_message = f"💾 保存全部数据: {message}"
            return message, log_message

        except Exception as e:
            error_msg = f"❌ 保存数据异常: {str(e)}"
            return error_msg, error_msg

    def _handle_add_quota(self, current_data):
        """处理添加定额"""
        try:
            if not current_data:
                current_data = []

            # 添加新的定额行
            new_row = [
                "定额项",  # 类型
                "",  # 定额编码
                "",  # 定额名称
                "",  # 单位
                "",  # 工作内容
                "",  # 资源编码
                "",  # 资源名称
                "",  # 资源类别
                "",  # 资源单位
                ""   # 消耗量
            ]

            current_data.append(new_row)

            return current_data, "➕ 已添加新定额行"

        except Exception as e:
            return current_data, f"❌ 添加定额失败: {str(e)}"

    def _handle_add_resource(self, current_data):
        """处理添加资源"""
        try:
            if not current_data:
                current_data = []

            # 添加新的资源行
            new_row = [
                "资源项",  # 类型
                "",  # 定额编码（需要手动填写关联）
                "",  # 定额名称（空）
                "",  # 单位（空）
                "",  # 工作内容（空）
                "",  # 资源编码
                "",  # 资源名称
                "",  # 资源类别
                "",  # 资源单位
                ""   # 消耗量
            ]

            current_data.append(new_row)

            return current_data, "🔧 已添加新资源行"

        except Exception as e:
            return current_data, f"❌ 添加资源失败: {str(e)}"

    def _handle_save_quota_info(self, quota_code, quota_name, unit, work_content):
        """处理保存定额信息"""
        try:
            if not self.quota_manager or not self.quota_manager.revision_processor:
                return "❌ 处理器未初始化", "❌ 处理器未初始化"

            if not quota_code:
                return "❌ 定额编码不能为空", "❌ 定额编码为空"

            success, message = self.quota_manager.revision_processor.save_quota_info(
                quota_code, quota_name, unit, work_content
            )

            log_message = f"💾 保存定额信息: {quota_code} - {message}"
            return message, log_message

        except Exception as e:
            error_msg = f"❌ 保存定额信息异常: {str(e)}"
            return error_msg, error_msg

    def _handle_save_resources(self, quota_code, resources_data):
        """处理保存资源数据"""
        try:
            if not self.quota_manager or not self.quota_manager.revision_processor:
                return "❌ 处理器未初始化", "❌ 处理器未初始化"

            if not quota_code:
                return "❌ 定额编码不能为空", "❌ 定额编码为空"

            success, message = self.quota_manager.revision_processor.save_resources_data(
                quota_code, resources_data
            )

            log_message = f"💾 保存资源数据: {quota_code} - {message}"
            return message, log_message

        except Exception as e:
            error_msg = f"❌ 保存资源数据异常: {str(e)}"
            return error_msg, error_msg

    def _handle_delete_quota(self, quota_code):
        """处理删除定额"""
        try:
            if not self.quota_manager or not self.quota_manager.revision_processor:
                return "❌ 处理器未初始化", [], "❌ 处理器未初始化"

            if not quota_code:
                return "❌ 定额编码不能为空", [], "❌ 定额编码为空"

            success, message = self.quota_manager.revision_processor.delete_quota(quota_code)

            if success:
                # 刷新定额列表
                quota_list = self.quota_manager.revision_processor._get_quota_list()
                log_message = f"🗑️ 删除定额: {quota_code} - {message}"
                return message, quota_list, log_message
            else:
                log_message = f"❌ 删除失败: {quota_code} - {message}"
                return message, [], log_message

        except Exception as e:
            error_msg = f"❌ 删除定额异常: {str(e)}"
            return error_msg, [], error_msg

    def _handle_refresh_pdf_list(self):
        """处理刷新PDF列表"""
        try:
            # 使用PDFStorageManager获取已存储的PDF列表，确保与其他模块一致
            if hasattr(self, 'pdf_storage') and self.pdf_storage:
                stored_pdfs = self.pdf_storage.get_stored_pdfs("quota")
                pdf_choices = []
                for pdf_info in stored_pdfs:
                    display_name = f"{pdf_info.get('original_name', 'Unknown')} ({pdf_info.get('size_mb', 0)} MB)"
                    # 使用文件ID作为值，与其他模块保持一致
                    file_id = pdf_info.get('id', '')
                    pdf_choices.append((display_name, file_id))

                print(f"🔄 识别定额修订模块刷新PDF列表: 找到 {len(pdf_choices)} 个已存储PDF")
                return pdf_choices

            # 备用方案：使用原有的文件系统扫描方式
            if not self.quota_manager or not self.quota_manager.revision_processor:
                return []

            pdf_files = self.quota_manager.revision_processor.get_available_pdfs()
            # 转换为选择项格式
            pdf_choices = [(pdf_file, pdf_file) for pdf_file in pdf_files]
            return pdf_choices

        except Exception as e:
            print(f"刷新PDF列表失败: {str(e)}")
            return []

    def _handle_pdf_selection(self, pdf_id_or_path, page_num):
        """处理PDF文件选择"""
        try:
            if not self.quota_manager or not self.quota_manager.revision_processor:
                print("❌ 定额修订处理器未初始化")
                return None

            if not pdf_id_or_path:
                print("❌ 未选择PDF文件")
                return None

            # 确定实际的PDF文件路径
            actual_pdf_path = self._resolve_pdf_path(pdf_id_or_path)
            if not actual_pdf_path:
                print(f"❌ 无法解析PDF路径: {pdf_id_or_path}")
                return None

            print(f"🔍 渲染PDF: {actual_pdf_path}, 页码: {page_num}")
            success, message, image_path = self.quota_manager.revision_processor.render_pdf_page(
                actual_pdf_path, int(page_num) if page_num else 1
            )

            if success:
                print(f"✅ PDF渲染成功: {message}")
                return image_path
            else:
                print(f"❌ PDF渲染失败: {message}")
                return None

        except Exception as e:
            print(f"PDF选择失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

    def _resolve_pdf_path(self, pdf_id_or_path):
        """解析PDF路径"""
        try:
            # 如果有PDF存储管理器，尝试通过ID获取路径
            if hasattr(self, 'pdf_storage') and self.pdf_storage:
                pdf_info = self.pdf_storage.get_pdf_info(pdf_id_or_path)
                if pdf_info:
                    stored_path = pdf_info.get('stored_path', '')
                    # 确保路径格式正确（统一使用正斜杠）
                    normalized_path = stored_path.replace('\\', '/')
                    print(f"📁 通过ID解析PDF路径: {pdf_id_or_path} -> {normalized_path}")
                    return normalized_path

            # 如果不是ID，直接作为路径使用
            print(f"📁 直接使用PDF路径: {pdf_id_or_path}")
            return pdf_id_or_path

        except Exception as e:
            print(f"解析PDF路径失败: {str(e)}")
            return pdf_id_or_path

    def _handle_pdf_page_change(self, pdf_path, page_num):
        """处理PDF页面变化"""
        return self._handle_pdf_selection(pdf_path, page_num)

    def _handle_prev_pdf_page(self, pdf_path, current_page):
        """处理上一页"""
        try:
            if not pdf_path:
                return current_page, None

            new_page = max(1, int(current_page) - 1)
            image_path = self._handle_pdf_selection(pdf_path, new_page)

            return new_page, image_path

        except Exception as e:
            print(f"上一页失败: {str(e)}")
            return current_page, None

    def _handle_next_pdf_page(self, pdf_path, current_page):
        """处理下一页"""
        try:
            if not self.quota_manager or not self.quota_manager.revision_processor:
                return current_page, None

            if not pdf_path:
                return current_page, None

            # 获取PDF总页数
            total_pages = self.quota_manager.revision_processor.get_pdf_page_count(pdf_path)
            new_page = min(total_pages, int(current_page) + 1)
            image_path = self._handle_pdf_selection(pdf_path, new_page)

            return new_page, image_path

        except Exception as e:
            print(f"下一页失败: {str(e)}")
            return current_page, None
