#!/usr/bin/env python3
"""
AI模型处理器 - 支持多种AI模型API进行图像识别
替代浏览器自动化方案
"""

import asyncio
import base64
import json
import os
from typing import Dict, Any, Optional, List
from pathlib import Path
import requests
import time
import urllib3
from .log_manager import get_logger
from .lm_studio_manager import LMStudioManager

# 禁用SSL警告（用于解决SSL问题）
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

try:
    import openai
except ImportError:
    openai = None

try:
    import anthropic
except ImportError:
    anthropic = None

try:
    import google.generativeai as genai
except ImportError:
    genai = None

from .config import Config

class AIModelProcessor:
    """AI模型处理器"""
    
    def __init__(self):
        self.logger = get_logger('AIModelProcessor')
        self.config = Config()

        # 初始化LM Studio管理器
        self.lm_studio_manager = LMStudioManager()

        # 基础模型支持
        self.base_models = {
            "qwen_qvq_max": "阿里通义千问-QVQ-Max",
            "modelscope_cogvlm2": "ModelScope-CogVLM2-Llama3-Chinese-19B-Int4"
        }

        # 动态支持的模型（包含LM Studio模型）
        self.supported_models = self.base_models.copy()

        # 从环境变量加载API密钥
        self.api_keys = {
            "dashscope": os.getenv("DASHSCOPE_API_KEY")  # 阿里云百炼API密钥
        }

        # 配置网络请求session
        self.session = requests.Session()
        self._configure_session()

        # 初始化时刷新LM Studio模型列表
        self.refresh_lm_studio_models()

        self.logger.info("AI模型处理器初始化完成")
    
    def _configure_session(self):
        """配置网络请求session，处理代理和SSL问题"""
        # 配置重试策略
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS", "POST"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # 检查并处理代理设置
        # 移除可能导致问题的代理设置
        if 'http_proxy' in os.environ:
            del os.environ['http_proxy']
        if 'https_proxy' in os.environ:
            del os.environ['https_proxy']
        if 'HTTP_PROXY' in os.environ:
            del os.environ['HTTP_PROXY']
        if 'HTTPS_PROXY' in os.environ:
            del os.environ['HTTPS_PROXY']
            
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept-Charset': 'utf-8'
        })

        # SSL配置 - 对于有SSL问题的情况，可以临时禁用SSL验证
        # 注意：这仅用于调试，生产环境建议修复SSL配置
        self.session.verify = True  # 默认启用SSL验证

        # 设置编码
        self.session.encoding = 'utf-8'
        
    def _make_request_with_retry(self, url, **kwargs):
        """带重试的网络请求"""
        max_retries = 3
        backoff_factor = 2
        
        for attempt in range(max_retries):
            try:
                # 如果SSL验证失败，尝试禁用SSL验证
                if attempt > 0:
                    kwargs['verify'] = False
                    print(f"第{attempt + 1}次重试，尝试禁用SSL验证...")
                
                response = self.session.request(**kwargs, url=url)
                # 确保响应使用UTF-8编码
                if response.encoding is None or response.encoding.lower() == 'iso-8859-1':
                    response.encoding = 'utf-8'
                return response
            except requests.exceptions.SSLError as e:
                print(f"SSL错误 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(backoff_factor ** attempt)
                    continue
                else:
                    raise
            except requests.exceptions.ProxyError as e:
                print(f"代理错误 (尝试 {attempt + 1}/{max_retries}): {e}")
                # 移除代理设置
                if 'proxies' in kwargs:
                    del kwargs['proxies']
                if attempt < max_retries - 1:
                    time.sleep(backoff_factor ** attempt)
                    continue
                else:
                    raise
            except requests.exceptions.ConnectionError as e:
                print(f"连接错误 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(backoff_factor ** attempt)
                    continue
                else:
                    raise
            except Exception as e:
                print(f"请求错误 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(backoff_factor ** attempt)
                    continue
                else:
                    raise
        
        return None
    
    def _initialize_clients(self):
        """初始化AI模型客户端 - 简化版本"""
        # 不需要初始化客户端，直接使用requests调用API
        pass
    
    def get_available_models(self) -> Dict[str, str]:
        """获取可用的模型列表 - 简化版本"""
        available = {}

        # 阿里云通义千问-QVQ-Max (如果有API密钥)
        if self.api_keys["dashscope"]:
            available["qwen_qvq_max"] = self.supported_models["qwen_qvq_max"]

        # ModelScope CogVLM2模型 (检查是否已下载)
        if self._check_modelscope_cogvlm2_available():
            available["modelscope_cogvlm2"] = self.supported_models["modelscope_cogvlm2"]

        # LM Studio模型 (检查是否运行且有模型)
        if self.lm_studio_manager.is_server_running():
            # 刷新模型列表
            self.refresh_lm_studio_models()

            # 添加所有LM Studio模型
            for model_key, model_name in self.supported_models.items():
                if model_key.startswith("lm_studio_"):
                    available[model_key] = model_name

        return available

    def reload_api_keys(self):
        """重新加载API密钥"""
        self.api_keys = {
            "dashscope": os.getenv("DASHSCOPE_API_KEY")
        }

    def _check_lm_studio_qwen_available(self) -> bool:
        """检查LM Studio是否运行且有qwen2.5-vl-7b模型（保持向后兼容）"""
        try:
            return self.lm_studio_manager.is_server_running() and len(self.lm_studio_manager.get_vision_models()) > 0
        except Exception:
            return False
    

    
    def _encode_image_to_base64(self, image_path: str) -> str:
        """将图片编码为base64"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    def _compress_image_base64(self, image_path: str, max_size: int = 5 * 1024 * 1024) -> str:
        """压缩图片并编码为base64"""
        try:
            from PIL import Image
            import io

            # 打开图片
            with Image.open(image_path) as img:
                # 转换为RGB模式（如果需要）
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # 计算压缩比例
                original_size = img.size
                quality = 85

                while True:
                    # 创建内存缓冲区
                    buffer = io.BytesIO()

                    # 保存压缩图片
                    img.save(buffer, format='JPEG', quality=quality, optimize=True)

                    # 检查大小
                    buffer_size = buffer.tell()

                    if buffer_size <= max_size or quality <= 20:
                        break

                    # 降低质量
                    quality -= 10
                    buffer.seek(0)
                    buffer.truncate()

                # 编码为base64
                buffer.seek(0)
                return base64.b64encode(buffer.read()).decode('utf-8')

        except Exception as e:
            print(f"图片压缩失败: {e}")
            # 如果压缩失败，返回原始图片
            return self._encode_image_to_base64(image_path)
    


    async def process_image_with_lm_studio(self, image_path: str, model_name: str = "monkeyocr-recognition", volume_code: str = "", chapter_codes: str = "") -> Optional[str]:
        """使用LM Studio模型处理图片"""
        try:
            base64_image = self._encode_image_to_base64(image_path)

            headers = {
                "Content-Type": "application/json"
            }

            # 构建包含分册章节信息的提示词
            # 对于所有LM Studio模型使用优化的提示词
            prompt_text = self._get_lm_studio_optimized_prompt(model_name, volume_code, chapter_codes)
            if volume_code and chapter_codes:
                chapters = [ch.strip() for ch in chapter_codes.split(',') if ch.strip()]
                if chapters:
                    volume_chapter_instruction = f"""

**🔥 重要：分册章节编号规则**：
- 本次识别的分册编号为：{volume_code}
- 章节编号包括：{', '.join(chapters)}
- 对于识别出的每个定额编号，必须按以下规则添加前缀：
  * 原编号"1-1"应输出为："{volume_code}-{chapters[0]}-1-1"
  * 原编号"1-2"应输出为："{volume_code}-{chapters[0]}-1-2"
  * 如果表格涉及多个章节，请根据内容判断属于哪个章节
  * 格式：分册编号-章节编号-原定额编号
- 示例：如果识别到定额编号"1-1"，输出应为"{volume_code}-{chapters[0]}-1-1"
"""
                    prompt_text = prompt_text + volume_chapter_instruction

            data = {
                "model": model_name,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt_text
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                "max_tokens": 4000,
                "temperature": 0.1
            }

            # 统一设置为900秒超时时间，支持大规模本地模型测试
            timeout_seconds = 900  # 15分钟超时，支持所有大规模模型
            self.logger.info(f"🕒 设置模型 {model_name} 超时时间为 {timeout_seconds} 秒（15分钟）")

            response = requests.post(
                "http://127.0.0.1:1234/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=timeout_seconds
            )

            if response.status_code == 200:
                result = response.json()

                # 检查响应格式
                if "choices" in result and len(result["choices"]) > 0:
                    choice = result["choices"][0]
                    if "message" in choice and "content" in choice["message"]:
                        content = choice["message"]["content"]
                        if content and content.strip():
                            self.logger.info(f"✅ LM Studio模型 {model_name} 响应成功")

                            # 验证和修复JSON格式
                            cleaned_content = self._validate_and_fix_json_response(content, model_name)

                            # 对本地模型进行额外的数据验证和修复
                            if "qwen" in model_name.lower() or "gemma" in model_name.lower() or "ocr" in model_name.lower():
                                cleaned_content = self._validate_and_fix_local_model_result(cleaned_content, model_name)

                            return cleaned_content
                        else:
                            self.logger.error(f"❌ LM Studio模型 {model_name} 返回空内容")
                            return None
                    else:
                        self.logger.error(f"❌ LM Studio模型 {model_name} 响应格式错误: 缺少message.content")
                        self.logger.error(f"   响应结构: {json.dumps(result, indent=2, ensure_ascii=False)}")
                        return None
                else:
                    self.logger.error(f"❌ LM Studio模型 {model_name} 响应格式错误: 缺少choices")
                    self.logger.error(f"   响应结构: {json.dumps(result, indent=2, ensure_ascii=False)}")
                    return None
            else:
                self.logger.error(f"❌ LM Studio模型 {model_name} 请求失败: {response.status_code}")
                try:
                    error_detail = response.json()
                    self.logger.error(f"   错误详情: {json.dumps(error_detail, indent=2, ensure_ascii=False)}")
                except:
                    self.logger.error(f"   响应内容: {response.text}")
                return None

        except Exception as e:
            self.logger.error(f"❌ LM Studio模型 {model_name} 处理异常: {str(e)}")
            import traceback
            self.logger.error(f"   异常详情: {traceback.format_exc()}")
            return None



    async def process_image_with_qwen_qvq(self, image_path: str, model_name: str = "qvq-max", volume_code: str = "", chapter_codes: str = "") -> Optional[str]:
        """
        使用阿里云通义千问-QVQ模型处理图片

        Args:
            image_path: 图片文件路径
            model_name: 模型名称 (qvq-max 或 qvq-plus)

        Returns:
            Optional[str]: 识别结果
        """
        try:
            if not self.api_keys["dashscope"]:
                return None

            # 将图片编码为base64
            base64_image = self._encode_image_to_base64(image_path)

            # 构建请求数据 - 使用OpenAI兼容格式
            headers = {
                "Authorization": f"Bearer {self.api_keys['dashscope']}",
                "Content-Type": "application/json; charset=utf-8"
            }

            # 构建分册章节编号指令
            volume_chapter_instruction = ""
            if volume_code and chapter_codes:
                chapters = [ch.strip() for ch in chapter_codes.split(',') if ch.strip()]
                if chapters:
                    volume_chapter_instruction = f"""

**🔥 重要：分册章节编号规则**：
- 本次识别的分册编号为：{volume_code}
- 章节编号包括：{', '.join(chapters)}
- 对于识别出的每个定额编号，必须按以下规则添加前缀：
  * 原编号"1-1"应输出为："{volume_code}-{chapters[0]}-1-1"
  * 原编号"1-2"应输出为："{volume_code}-{chapters[0]}-1-2"
  * 如果表格涉及多个章节，请根据内容判断属于哪个章节
  * 格式：分册编号-章节编号-原定额编号
- 示例：如果识别到定额编号"1-1"，输出应为"{volume_code}-{chapters[0]}-1-1"
"""

            # QVQ模型的特殊提示词，针对定额表格优化
            qvq_prompt = f"""
请仔细分析这张北京市消耗定额表格图片。我需要你按照以下步骤进行分析：

1. **识别表格结构**：
   - 确定这是否为标准的北京市消耗定额表格
   - 识别表格上半部分的定额编号列（如：1-11、1-12、1-13）
   - 识别表格下半部分的资源消耗明细

2. **提取每个定额项的信息**（表格上半部分）：
   - 定额编号（如：1-1、1-2、1-3）{volume_chapter_instruction}
   - 定额项名称：需要组合主项名称和差异描述
     * 主项名称（如：人工挖一般土方）
     * 差异描述（如：一、二类土、三类土、四类土）
     * 完整名称（如：人工挖一般土方 一、二类土）
   - 工作内容（如：挖土、余土清理、修整底边等）
   - 单位（如：m³）

3. **提取资源消耗信息**（表格下半部分的每一行）：
   - 资源编号（如：00010701）
   - 类别（如：人工、机械、其他费用）
   - 子项名称（如：综合用工三类、电动打钉机、其他机具费占人工费）
   - 单位（如：工日、台班、%）
   - 消耗量（如：0.200、0.0039、1.50）

4. **特别注意**：
   - 图片中可能有多个定额项（如1-1、1-2、1-3），每个都要单独提取
   - 定额项名称要完整：主项名称 + 差异描述（如：人工挖一般土方 一、二类土）
   - 仔细观察表格中每列的差异描述文字，将其合并到定额项名称中
   - 每个定额项通常有相同的资源消耗明细，但消耗量可能不同
   - 单位为"%"的资源项是特殊的百分比费用项
   - 确保提取所有资源消耗行
   - 保留消耗量的小数精度

请以JSON格式返回结果（支持多个定额项）：
{{
    "quotas": [
        {{
            "parent_quota": {{
                "code": "定额编号",
                "name": "定额项名称",
                "work_content": "工作内容",
                "unit": "单位"
            }},
            "resource_consumption": [
                {{
                    "resource_code": "资源编号",
                    "category": "类别",
                    "name": "子项名称",
                    "unit": "单位",
                    "consumption": "消耗量"
                }}
            ]
        }}
    ]
}}

如果只有一个定额项，quotas数组中只包含一个元素。
如果有多个定额项（如1-11、1-12、1-13），quotas数组中包含多个元素。
"""

            # 模型回退策略 - 如果QVQ模型不可用，使用通用模型
            fallback_models = [model_name, "qwen-vl-max", "qwen-max", "qwen-vl-plus"]
            
            for try_model in fallback_models:
                print(f"尝试使用模型: {try_model}")
                
                data = {
                    "model": try_model,
                    "messages": [
                        {
                            "role": "user", 
                            "content": [
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/png;base64,{base64_image}"
                                    }
                                },
                                {
                                    "type": "text",
                                    "text": qvq_prompt
                                }
                            ]
                        }
                    ],
                    "stream": True if try_model.startswith("qvq") else False  # QVQ模型支持流式，其他不一定
                }

                # 确保数据编码正确
                data = self._ensure_utf8_encoding(data)
                headers = self._ensure_utf8_encoding(headers)

                # 发送请求到阿里云百炼API - 使用重试机制
                response = self._make_request_with_retry(
                    "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions",
                    method='POST',
                    headers=headers,
                    json=data,
                    stream=data["stream"],
                    timeout=900  # 15分钟超时，支持大规模模型
                )

                if response.status_code == 200:
                    print(f"✅ 模型 {try_model} 连接成功")
                    break
                else:
                    error_text = response.text if hasattr(response, 'text') else str(response)
                    print(f"❌ 模型 {try_model} 失败: {response.status_code} - {error_text[:100]}")
                    if try_model == fallback_models[-1]:  # 最后一个模型也失败了
                        print(f"所有模型都失败了")
                        return None
                    continue
            else:
                print(f"所有模型都失败了") 
                return None

            # 处理响应 - 支持流式和非流式
            if data["stream"]:
                # 处理流式响应 (QVQ模型)
                reasoning_content = ""  # 思考过程
                answer_content = ""     # 最终回答
                is_answering = False    # 是否开始回答阶段

                for line in response.iter_lines():
                    if line:
                        line_text = line.decode('utf-8')
                        if line_text.startswith('data: '):
                            data_text = line_text[6:]  # 移除 'data: ' 前缀

                            if data_text.strip() == '[DONE]':
                                break

                            try:
                                chunk_data = json.loads(data_text)
                                if 'choices' in chunk_data and chunk_data['choices']:
                                    delta = chunk_data['choices'][0].get('delta', {})

                                    # 处理思考过程
                                    if 'reasoning_content' in delta and delta['reasoning_content']:
                                        reasoning_content += delta['reasoning_content']

                                    # 处理最终回答
                                    elif 'content' in delta and delta['content']:
                                        if not is_answering:
                                            is_answering = True
                                        answer_content += delta['content']

                            except json.JSONDecodeError:
                                continue

                # 返回最终回答内容
                if answer_content:
                    print(f"思考过程长度: {len(reasoning_content)} 字符")
                    print(f"回答内容长度: {len(answer_content)} 字符")
                    return answer_content
                else:
                    print("流式模型未返回有效回答")
                    return None
            else:
                # 处理非流式响应 (通用模型)
                try:
                    result = response.json()
                    if 'choices' in result and result['choices']:
                        content = result['choices'][0]['message']['content']
                        print(f"回答内容长度: {len(content)} 字符")
                        return content
                    else:
                        print("非流式模型未返回有效回答")
                        return None
                except json.JSONDecodeError as e:
                    print(f"JSON解析错误: {e}")
                    return None

        except Exception as e:
            print(f"QVQ模型处理失败: {e}")
            return None
    
    async def process_image(self, image_path: str, model_type: str, **kwargs) -> Optional[str]:
        """
        使用指定模型处理图片 - 简化版本

        Args:
            image_path: 图片路径
            model_type: 模型类型
            **kwargs: 额外参数（包含volume_code和chapter_codes）

        Returns:
            Optional[str]: 识别结果
        """
        print(f"使用 {self.supported_models.get(model_type, model_type)} 处理图片: {image_path}")

        # 提取分册章节信息
        volume_code = kwargs.get('volume_code', '')
        chapter_codes = kwargs.get('chapter_codes', '')

        try:
            if model_type == "qwen_qvq_max":
                return await self.process_image_with_qwen_qvq(image_path, "qvq-max", volume_code, chapter_codes)
            elif model_type == "modelscope_cogvlm2":
                return await self.process_image_with_modelscope_cogvlm2(image_path, volume_code, chapter_codes)
            elif model_type.startswith("lm_studio_"):
                # 获取实际的模型ID
                actual_model_id = self.get_model_id_from_key(model_type)
                return await self.process_image_with_lm_studio(image_path, actual_model_id, volume_code, chapter_codes)
            else:
                print(f"不支持的模型类型: {model_type}")
                return None

        except Exception as e:
            print(f"处理图片时发生错误: {e}")
            return None

    # 信息价识别功能已移至独立模块 intelligent_price_info_processor.py

    async def test_model_connection(self, model_type: str) -> tuple[bool, str]:
        """
        测试AI模型连接

        Args:
            model_type: 模型类型

        Returns:
            (是否成功, 状态消息)
        """
        try:
            if model_type == "qwen_qvq_max":
                return await self._test_qwen_qvq_connection()
            elif model_type == "modelscope_cogvlm2":
                return await self._test_modelscope_cogvlm2_connection()
            elif model_type.startswith("lm_studio_"):
                # 获取实际的模型ID并测试
                actual_model_id = self.get_model_id_from_key(model_type)
                success, message = self.test_lm_studio_model(actual_model_id)
                return success, message
            else:
                return False, f"不支持的模型类型: {model_type}"

        except Exception as e:
            return False, f"连接测试失败: {str(e)}"

    async def _test_qwen_qvq_connection(self) -> tuple[bool, str]:
        """测试阿里云QVQ模型连接"""
        try:
            if not self.api_keys["dashscope"]:
                return False, "❌ 未设置DASHSCOPE_API_KEY"

            # 尝试使用DashScope SDK
            try:
                import dashscope
                from dashscope import Generation

                dashscope.api_key = self.api_keys["dashscope"]

                # 简单的文本生成测试
                response = Generation.call(
                    model='qwen-max',
                    prompt='Hello',
                    max_tokens=5
                )

                if response.status_code == 200:
                    return True, "✅ 阿里云DashScope SDK连接正常"
                else:
                    return False, f"❌ DashScope SDK调用失败: {response.message}"

            except ImportError:
                return False, "❌ 未安装dashscope SDK，请运行: pip install dashscope"
            except Exception as e:
                # 如果SDK失败，尝试HTTP测试
                return await self._test_qwen_http_connection()

        except Exception as e:
            return False, f"❌ 阿里云模型连接测试失败: {str(e)}"

    async def _test_qwen_http_connection(self) -> tuple[bool, str]:
        """测试阿里云HTTP连接"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_keys['dashscope']}",
                "Content-Type": "application/json; charset=utf-8"
            }

            # 简单的文本生成测试
            data = {
                "model": "qwen-max",
                "messages": [{"role": "user", "content": "Hello"}],
                "max_tokens": 5
            }

            # 确保数据编码正确
            data = self._ensure_utf8_encoding(data)
            headers = self._ensure_utf8_encoding(headers)

            response = requests.post(
                "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=900,  # 15分钟超时，支持大规模模型
                proxies={},
                verify=True
            )

            if response.status_code == 200:
                return True, "✅ 阿里云HTTP API连接正常"
            else:
                error_detail = ""
                try:
                    error_info = response.json()
                    error_detail = error_info.get('error', {}).get('message', '')
                except:
                    error_detail = response.text[:100]

                return False, f"❌ HTTP API调用失败 ({response.status_code}): {error_detail}"

        except Exception as e:
            return False, f"❌ HTTP连接测试失败: {str(e)}"

    async def _test_lm_studio_connection(self) -> tuple[bool, str]:
        """测试LM Studio连接"""
        try:
            # 检查LM Studio服务状态
            try:
                response = requests.get("http://127.0.0.1:1234/v1/models", timeout=5)
                if response.status_code != 200:
                    return False, "❌ LM Studio服务未运行或不可访问"
            except Exception as e:
                return False, f"❌ 无法连接到LM Studio: {str(e)}"

            # 获取可用模型列表
            models = response.json()
            model_list = [model.get('id', '') for model in models.get('data', [])]

            if not model_list:
                return False, "❌ LM Studio未加载任何模型"

            # 检查是否有视觉语言模型
            vision_models = [m for m in model_list if 'vl' in m.lower() or 'vision' in m.lower()]

            if not vision_models:
                return False, f"❌ 未找到视觉语言模型，当前模型: {', '.join(model_list[:3])}"

            # 尝试简单的文本生成测试
            test_data = {
                "model": model_list[0],
                "messages": [{"role": "user", "content": "测试"}],
                "max_tokens": 5
            }

            test_response = requests.post(
                "http://127.0.0.1:1234/v1/chat/completions",
                json=test_data,
                timeout=10
            )

            if test_response.status_code == 200:
                return True, f"✅ LM Studio连接正常，可用视觉模型: {', '.join(vision_models)}"
            else:
                return False, f"❌ LM Studio模型调用失败 ({test_response.status_code})"

        except Exception as e:
            return False, f"❌ LM Studio连接测试失败: {str(e)}"

    def refresh_lm_studio_models(self) -> bool:
        """刷新LM Studio模型列表"""
        try:
            # 重置为基础模型
            self.supported_models = self.base_models.copy()

            # 获取LM Studio模型
            success, message, models = self.lm_studio_manager.refresh_models()

            if success and models:
                # 添加LM Studio模型到支持列表
                for model in models:
                    model_key = f"lm_studio_{model['id'].replace('/', '_').replace('.', '_').replace('-', '_')}"
                    model_name = f"LM Studio: {model['name']}"
                    self.supported_models[model_key] = model_name

                self.logger.info(f"成功刷新LM Studio模型: {len(models)} 个模型")
                return True
            else:
                self.logger.warning(f"LM Studio模型刷新失败: {message}")
                return False

        except Exception as e:
            self.logger.error(f"刷新LM Studio模型异常: {str(e)}")
            return False

    def get_lm_studio_models(self) -> List[Dict[str, str]]:
        """获取LM Studio模型列表"""
        try:
            return self.lm_studio_manager.get_vision_models()
        except Exception as e:
            self.logger.error(f"获取LM Studio模型失败: {str(e)}")
            return []

    def get_lm_studio_status(self) -> Dict[str, any]:
        """获取LM Studio状态"""
        try:
            return self.lm_studio_manager.get_server_status()
        except Exception as e:
            self.logger.error(f"获取LM Studio状态失败: {str(e)}")
            return {
                "running": False,
                "message": f"状态检查失败: {str(e)}",
                "models_count": 0,
                "vision_models_count": 0
            }

    def test_lm_studio_model(self, model_id: str) -> tuple[bool, str]:
        """测试特定的LM Studio模型"""
        try:
            return self.lm_studio_manager.test_model_connection(model_id)
        except Exception as e:
            return False, f"模型测试失败: {str(e)}"

    def _check_modelscope_cogvlm2_available(self) -> bool:
        """检查ModelScope CogVLM2模型是否可用"""
        try:
            # 检查是否安装了必要的依赖
            import torch
            from modelscope import AutoTokenizer, AutoModel

            # 检查模型是否已下载到本地
            model_id = "ZhipuAI/cogvlm2-llama3-chinese-chat-19B-int4"

            try:
                # 尝试加载tokenizer来检查模型是否存在
                tokenizer = AutoTokenizer.from_pretrained(model_id, trust_remote_code=True)
                return True
            except Exception:
                # 模型未下载，但ModelScope可用
                print(f"💡 ModelScope CogVLM2模型未下载，可通过以下命令下载：")
                print(f"   modelscope download --model {model_id}")
                return False

        except ImportError as e:
            print(f"⚠️ ModelScope依赖未安装: {e}")
            print("请安装: pip install modelscope torch transformers")
            return False
        except Exception as e:
            print(f"⚠️ ModelScope CogVLM2检查失败: {e}")
            return False

    async def _test_modelscope_cogvlm2_connection(self) -> tuple[bool, str]:
        """测试ModelScope CogVLM2模型连接"""
        try:
            if not self._check_modelscope_cogvlm2_available():
                return False, "ModelScope CogVLM2模型不可用，请检查安装和下载状态"

            # 尝试加载模型进行简单测试
            import torch
            from modelscope import AutoTokenizer, AutoModel

            model_id = "ZhipuAI/cogvlm2-llama3-chinese-chat-19B-int4"

            # 检查GPU可用性
            device = "cuda" if torch.cuda.is_available() else "cpu"

            try:
                # 加载tokenizer
                tokenizer = AutoTokenizer.from_pretrained(model_id, trust_remote_code=True)

                # 尝试加载模型（仅检查是否能加载，不实际使用）
                model = AutoModel.from_pretrained(
                    model_id,
                    torch_dtype=torch.float16 if device == "cuda" else torch.float32,
                    trust_remote_code=True,
                    device_map="auto" if device == "cuda" else None
                )

                # 清理内存
                del model
                if device == "cuda":
                    torch.cuda.empty_cache()

                return True, f"✅ ModelScope CogVLM2模型连接成功 (设备: {device})"

            except Exception as e:
                return False, f"模型加载失败: {str(e)}"

        except Exception as e:
            return False, f"连接测试失败: {str(e)}"

    async def process_image_with_modelscope_cogvlm2(self, image_path: str, volume_code: str = "", chapter_codes: str = "") -> Optional[str]:
        """使用ModelScope CogVLM2模型处理图片"""
        try:
            print(f"🤖 使用ModelScope CogVLM2处理图片: {image_path}")

            # 检查模型可用性
            if not self._check_modelscope_cogvlm2_available():
                print("❌ ModelScope CogVLM2模型不可用")
                return None

            import torch
            from modelscope import AutoTokenizer, AutoModel
            from PIL import Image

            model_id = "ZhipuAI/cogvlm2-llama3-chinese-chat-19B-int4"
            device = "cuda" if torch.cuda.is_available() else "cpu"

            print(f"📱 使用设备: {device}")

            # 加载模型和tokenizer
            print("🔄 加载模型...")
            tokenizer = AutoTokenizer.from_pretrained(model_id, trust_remote_code=True)
            model = AutoModel.from_pretrained(
                model_id,
                torch_dtype=torch.float16 if device == "cuda" else torch.float32,
                trust_remote_code=True,
                device_map="auto" if device == "cuda" else None
            ).eval()

            # 加载图片
            print("🖼️ 加载图片...")
            image = Image.open(image_path).convert('RGB')

            # 构建提示词
            prompt = self._get_modelscope_cogvlm2_prompt(volume_code, chapter_codes)

            print("🔍 开始识别...")

            # 进行推理
            with torch.no_grad():
                response = model.chat(
                    tokenizer=tokenizer,
                    image=image,
                    query=prompt,
                    history=[],
                    do_sample=False,
                    temperature=0.1,
                    max_new_tokens=4096
                )

            # 清理内存
            del model
            if device == "cuda":
                torch.cuda.empty_cache()

            print(f"✅ ModelScope CogVLM2识别完成，结果长度: {len(response)} 字符")
            return response

        except Exception as e:
            print(f"❌ ModelScope CogVLM2处理失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

    def _get_modelscope_cogvlm2_prompt(self, volume_code: str = "", chapter_codes: str = "") -> str:
        """获取ModelScope CogVLM2的优化提示词"""

        # 基础提示词
        base_prompt = """请仔细分析这张北京市消耗定额表格图片，按照以下格式提取信息：

1. **识别表格结构**：
   - 确定表格上半部分的定额编号列（如：1-11、1-12、1-13）
   - 识别每个定额项对应的名称和工作内容
   - 确认表格下半部分的资源消耗明细

2. **提取每个定额项的信息**：
   - 定额编号（如：1-1、1-2、1-3）
   - 定额项名称：需要组合主项名称和差异描述
     * 主项名称（如：人工挖一般土方）
     * 差异描述（如：一、二类土、三类土、四类土）
     * 完整名称（如：人工挖一般土方 一、二类土）
   - 工作内容（如：挖土、余土清理、修整底边等）
   - 单位（如：m³）

3. **提取资源消耗信息**（每个定额项都有相同的资源消耗）：
   - 资源编号（如：00010701）
   - 类别（如：人工、机械、其他费用）
   - 子项名称（如：综合用工三类、电动打钉机、其他机具费占人工费）
   - 单位（如：工日、台班、%）
   - 消耗量（如：0.200、0.0039、1.50）

**重要说明**：
- 图片中可能有多个定额项（如1-1、1-2、1-3），每个定额项都要单独提取
- 定额项名称要完整：主项名称 + 差异描述（如：人工挖一般土方 一、二类土）
- 仔细观察表格中每列的差异描述文字，将其合并到定额项名称中
- 每个定额项通常有相同的资源消耗明细，但消耗量可能不同
- 请特别注意单位为"%"的资源项，这些是特殊的百分比费用项
- 确保提取所有资源消耗行，包括人工、材料、机械、其他费用等
- 消耗量请保留小数点后的精度

请以JSON格式返回结果（支持多个定额项）：
{
    "quotas": [
        {
            "parent_quota": {
                "code": "定额编号",
                "name": "定额项名称",
                "work_content": "工作内容",
                "unit": "单位"
            },
            "resource_consumption": [
                {
                    "resource_code": "资源编号",
                    "category": "类别",
                    "name": "子项名称",
                    "unit": "单位",
                    "consumption": "消耗量"
                }
            ]
        }
    ]
}

如果只有一个定额项，quotas数组中只包含一个元素。
如果有多个定额项（如1-11、1-12、1-13），quotas数组中包含多个元素。"""

        # 添加分册章节信息
        if volume_code and chapter_codes:
            chapters = [ch.strip() for ch in chapter_codes.split(',') if ch.strip()]
            if chapters:
                volume_chapter_instruction = f"""

**特别注意**：
- 当前处理的是第{volume_code}分册
- 重点关注章节：{', '.join(chapters)}
- 定额编号应该以 {volume_code}- 开头（如：{volume_code}-1、{volume_code}-2）
- 请确保提取的定额编号格式正确"""

                base_prompt += volume_chapter_instruction

        return base_prompt

    def get_model_id_from_key(self, model_key: str) -> str:
        """从模型键获取实际的模型ID"""
        if model_key.startswith("lm_studio_"):
            # 获取LM Studio模型列表
            models = self.get_lm_studio_models()
            for model in models:
                # 重建模型键进行匹配
                expected_key = f"lm_studio_{model['id'].replace('/', '_').replace('.', '_').replace('-', '_')}"
                if expected_key == model_key:
                    return model['id']

            # 如果没有找到匹配，尝试从键名反推
            model_id = model_key.replace("lm_studio_", "").replace("_", "-")
            return model_id

        return model_key

    def _ensure_utf8_encoding(self, data):
        """确保数据使用UTF-8编码"""
        if isinstance(data, dict):
            return {key: self._ensure_utf8_encoding(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self._ensure_utf8_encoding(item) for item in data]
        elif isinstance(data, str):
            # 确保字符串是UTF-8编码
            try:
                return data.encode('utf-8').decode('utf-8')
            except UnicodeError:
                # 如果编码失败，尝试其他编码
                try:
                    return data.encode('latin-1').decode('utf-8')
                except UnicodeError:
                    # 最后的备选方案，移除非ASCII字符
                    return data.encode('ascii', 'ignore').decode('ascii')
        else:
            return data

    def _get_lm_studio_optimized_prompt(self, model_name: str, volume_code: str = "", chapter_codes: str = "") -> str:
        """获取针对LM Studio模型优化的提示词"""

        # 根据模型类型选择不同的优化策略
        if "gemma" in model_name.lower():
            return self._get_gemma_optimized_prompt(volume_code, chapter_codes)
        elif "ocr" in model_name.lower() or "nanonets" in model_name.lower():
            return self._get_ocr_optimized_prompt(volume_code, chapter_codes)
        else:
            # 通用的LM Studio优化提示词
            return self._get_general_lm_studio_prompt(volume_code, chapter_codes)

    def _validate_and_fix_json_response(self, content: str, model_name: str) -> str:
        """验证和修复JSON响应格式"""
        try:
            # 首先尝试直接解析
            json.loads(content)
            self.logger.info(f"✅ 模型 {model_name} 返回有效JSON格式")
            return content
        except json.JSONDecodeError:
            self.logger.warning(f"⚠️ 模型 {model_name} 返回的JSON格式有问题，尝试修复...")

            # 特殊处理：检测是否包含推理过程（32B等大模型常见）
            if "### 分析与提取过程" in content or "### 输出结果" in content or "```json" in content:
                self.logger.info(f"🔍 检测到模型 {model_name} 包含推理过程，提取JSON部分")
                content = self._extract_json_from_reasoning_response(content)
                # 尝试解析提取后的内容
                try:
                    json.loads(content)
                    self.logger.info(f"✅ 成功从推理过程中提取有效JSON")
                    return content
                except json.JSONDecodeError:
                    self.logger.warning(f"⚠️ 从推理过程提取的JSON仍有问题，继续其他修复方法")

            # 尝试提取JSON部分
            try:
                # 查找第一个 { 和最后一个 }
                start = content.find('{')
                end = content.rfind('}')

                if start != -1 and end != -1 and end > start:
                    json_part = content[start:end+1]

                    # 尝试解析提取的部分
                    json.loads(json_part)
                    self.logger.info(f"✅ 成功从模型 {model_name} 响应中提取有效JSON")
                    return json_part
                else:
                    self.logger.error(f"❌ 无法从模型 {model_name} 响应中找到JSON结构")

            except json.JSONDecodeError:
                self.logger.error(f"❌ 提取的JSON仍然无效")

            # 如果JSON修复失败，尝试其他修复策略
            try:
                # 移除可能的markdown代码块标记
                cleaned = content.replace('```json', '').replace('```', '').strip()

                # 再次尝试提取JSON
                start = cleaned.find('{')
                end = cleaned.rfind('}')

                if start != -1 and end != -1 and end > start:
                    json_part = cleaned[start:end+1]
                    json.loads(json_part)
                    self.logger.info(f"✅ 清理markdown后成功提取JSON")
                    return json_part

            except json.JSONDecodeError:
                pass

            # 如果所有修复尝试都失败，返回原始内容并记录警告
            self.logger.warning(f"⚠️ 无法修复模型 {model_name} 的JSON格式，返回原始内容")
            self.logger.warning(f"   原始内容预览: {content[:200]}...")
            return content

    def _extract_json_from_reasoning_response(self, content: str) -> str:
        """从包含推理过程的响应中提取JSON部分"""
        try:
            # 方法1: 查找```json代码块
            if "```json" in content:
                start_marker = "```json"
                end_marker = "```"

                start_idx = content.find(start_marker)
                if start_idx != -1:
                    start_idx += len(start_marker)
                    # 从```json后面开始查找结束的```
                    remaining_content = content[start_idx:]
                    end_idx = remaining_content.find(end_marker)

                    if end_idx != -1:
                        json_content = remaining_content[:end_idx].strip()
                        self.logger.info("✅ 从```json代码块中提取JSON")
                        return json_content

            # 方法2: 查找"### 输出结果"或类似标记后的JSON
            markers = ["### 输出结果", "### **输出结果**", "以下是严格按照要求生成的 JSON 格式数据："]
            for marker in markers:
                if marker in content:
                    start_idx = content.find(marker)
                    if start_idx != -1:
                        # 从标记后开始查找JSON
                        remaining_content = content[start_idx + len(marker):]

                        # 查找第一个{和最后一个}
                        json_start = remaining_content.find('{')
                        if json_start != -1:
                            # 从{开始，查找匹配的}
                            json_content = self._extract_balanced_json(remaining_content[json_start:])
                            if json_content:
                                self.logger.info(f"✅ 从标记 '{marker}' 后提取JSON")
                                return json_content

            # 方法3: 直接查找最大的JSON结构
            json_start = content.find('{')
            if json_start != -1:
                json_content = self._extract_balanced_json(content[json_start:])
                if json_content:
                    self.logger.info("✅ 提取最大JSON结构")
                    return json_content

            self.logger.warning("⚠️ 无法从推理过程中提取JSON")
            return content

        except Exception as e:
            self.logger.error(f"❌ 提取推理过程JSON时发生错误: {str(e)}")
            return content

    def _extract_balanced_json(self, content: str) -> str:
        """提取平衡的JSON结构（正确匹配大括号）"""
        try:
            if not content.startswith('{'):
                return ""

            brace_count = 0
            for i, char in enumerate(content):
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        # 找到匹配的结束大括号
                        json_content = content[:i+1]
                        # 验证是否为有效JSON
                        try:
                            json.loads(json_content)
                            return json_content
                        except json.JSONDecodeError:
                            continue

            return ""

        except Exception as e:
            self.logger.error(f"❌ 提取平衡JSON时发生错误: {str(e)}")
            return ""

    def _get_gemma_optimized_prompt(self, volume_code: str = "", chapter_codes: str = "") -> str:
        """获取针对Google Gemma模型优化的提示词"""

        # Gemma模型优化的提示词，使用标准的quotas格式
        base_prompt = """你是一个专业的建筑工程定额数据提取专家。请仔细分析这张北京市消耗定额表格图片。

## 🚨 重要：Gemma模型专用表格理解指南

### 表格结构分析（关键）
这个表格有特殊的双层结构，Gemma需要特别注意：

#### 视觉布局理解
```
上半部分（横向）：定额项信息
┌─────┬─────┬─────┬─────┬─────┬─────┐
│ 编号 │ 项目 │ 1-22│ 1-23│ 1-24│ 1-25│
│     │     │人工挖│一般石│沟槽石│基坑石│
│     │     │石方  │方    │方    │方    │
└─────┴─────┴─────┴─────┴─────┴─────┘

下半部分（纵向）：资源消耗明细
┌─────────┬─────────┬─────┬─────┬─────┬─────┬─────┐
│资源编号   │资源名称   │单位  │1-22 │1-23 │1-24 │1-25 │
├─────────┼─────────┼─────┼─────┼─────┼─────┼─────┤
│00010701 │综合用工三类│工日  │0.525│0.067│0.073│0.080│
│99010002 │反铲挖掘机  │台班  │-    │0.0170│0.0182│0.0200│
└─────────┴─────────┴─────┴─────┴─────┴─────┴─────┘
```

## 🔍 Gemma识别步骤（严格遵守）

### 第一步：识别定额项（上半部分）
从图片上半部分识别：
- **定额编号**: 1-22, 1-23, 1-24, 1-25（这些是原始编号）
- **项目名称**: 人工挖石方, 一般石方, 沟槽石方, 基坑石方
- **工作内容**: 根据名称推断施工内容
- **单位**: 通常是m³

### 第二步：识别资源消耗（下半部分）
从图片下半部分识别：
- **资源编号**: 8位数字（如：00010701, 99010002）
- **资源名称**: 综合用工三类, 反铲挖掘机等
- **单位**: 工日, 台班, %等
- **消耗量**: 每个定额项对应的数值

### 第三步：建立精确关联关系
**重要规则**：
- **严格按表格文字**: 定额项名称不要自己理解，严格按表格显示
- **跨行合并识别**: 正确识别表格中跨行合并的单元格
- **完整扫描**: **必须识别表格下半部分的所有资源行，不要遗漏任何一行**
- **空值排除**: 消耗量为"-"的资源不要包含在该定额项中
- **动态数量**: 不同页面的资源数量可能不同，要全部识别
- **工作内容来源**: 必须来自表格左上方的"工作内容"描述

## 🎯 输出格式要求
请严格按照以下JSON格式输出，确保数据准确完整：

```json
{
  "quotas": [
    {
      "parent_quota": {
        "code": "定额编号",
        "name": "定额项名称",
        "work_content": "工作内容",
        "unit": "单位"
      },
      "resource_consumption": [
        {
          "resource_code": "资源编号",
          "category": "类别",
          "name": "子项名称",
          "unit": "单位",
          "consumption": "消耗量"
        }
      ]
    }
  ]
}
```

## ⚠️ 重要注意事项
1. **数据准确性**: 确保所有数字和文字都准确提取，不要遗漏或错误
2. **格式统一**: 严格按照quotas数组格式输出，便于程序解析
3. **完整性**: 提取表格中的所有定额项目和资源消耗，不要遗漏任何行
4. **单位规范**: 保持原始单位格式，如m³、m²、t、工日、台班、%等
5. **数值处理**: 消耗量保持原始格式，包括小数点精度
6. **结构对应**: 每个定额项都要包含完整的资源消耗明细

## 🔍 识别重点
- 定额编号通常在表格上半部分（如：1-1、1-2、1-3）
- 定额项名称要完整：主项名称 + 差异描述
- 工作内容描述具体的施工内容
- 资源消耗在表格下半部分，包含资源编号、类别、名称、单位、消耗量
- 特别注意单位为"%"的资源项，这些是特殊的百分比费用项
- 确保提取所有资源消耗行，包括人工、材料、机械、其他费用等"""

        # 如果提供了分册和章节信息，添加编号规则
        if volume_code and chapter_codes:
            chapters = [ch.strip() for ch in chapter_codes.split(',') if ch.strip()]
            if chapters:
                volume_chapter_instruction = f"""

## 🔥 特别重要：编号前缀规则
- **分册编号**: {volume_code}
- **章节编号**: {', '.join(chapters)}
- **编号格式**: 分册编号-章节编号-原定额编号

### 编号转换示例：
- 原编号 "1-1" → 输出 "{volume_code}-{chapters[0]}-1-1"
- 原编号 "1-2" → 输出 "{volume_code}-{chapters[0]}-1-2"
- 原编号 "2-1" → 输出 "{volume_code}-{chapters[0]}-2-1"

**请确保所有定额编号都按此规则添加前缀！**"""
                base_prompt += volume_chapter_instruction

        base_prompt += """

## 🚀 开始分析

⚠️ **输出格式要求**：
- **直接输出JSON**: 以{开始，以}结束的纯JSON格式
- **不要解释**: 不要添加分析过程或说明文字
- **不要代码块**: 不要使用```json包装

请现在开始分析图片中的定额表格，按照上述要求提取数据并**直接输出**标准的quotas格式JSON。"""

        return base_prompt

    def _get_ocr_optimized_prompt(self, volume_code: str = "", chapter_codes: str = "") -> str:
        """获取针对OCR模型优化的提示词"""

        base_prompt = """你是一个专业的OCR文字识别和建筑工程定额数据提取专家。请仔细识别这张北京市消耗定额表格图片。

## 🚨 重要：表格结构识别指南

### 表格布局识别
这个表格有特殊的双层结构，请按以下方式识别：

#### 上半部分（横向布局）- 定额项信息
```
┌─────┬─────┬─────┬─────┬─────┬─────┐
│ 编号 │ 项目 │ 1-22│ 1-23│ 1-24│ 1-25│
│     │     │人工挖│一般石│沟槽石│基坑石│
│     │     │石方  │方    │方    │方    │
└─────┴─────┴─────┴─────┴─────┴─────┘
```

#### 下半部分（纵向布局）- 资源消耗明细
```
┌─────────┬─────────┬─────┬─────┬─────┬─────┬─────┐
│资源编号   │资源名称   │单位  │1-22 │1-23 │1-24 │1-25 │
├─────────┼─────────┼─────┼─────┼─────┼─────┼─────┤
│00010701 │综合用工三类│工日  │0.525│0.067│0.073│0.080│
│99010002 │反铲挖掘机  │台班  │-    │0.0170│0.0182│0.0200│
│...      │...       │...  │...  │...  │...  │...  │
└─────────┴─────────┴─────┴─────┴─────┴─────┴─────┘
```

## 📋 OCR识别重点

### 第一步：精确文字识别
- **定额编号**: 识别1-22, 1-23, 1-24, 1-25等格式
- **项目名称**: 识别"人工挖石方"、"一般石方"等完整名称
- **资源编号**: 识别8位数字如00010701, 99010002等
- **资源名称**: 识别"综合用工三类"、"反铲挖掘机"等
- **数值精度**: 精确识别0.525, 0.067, 0.0170等小数

### 第二步：结构理解和精确提取
- **定额项**: 只有上半部分的1-22, 1-23, 1-24, 1-25是定额项
- **项目名称**: 严格按表格跨行合并规则，不要自己理解
  - 1-22: "人工挖石方"
  - 1-23: "机械破碎石方 一般石方"
  - 1-24: "机械破碎石方 沟槽石方"
  - 1-25: "机械破碎石方 基坑石方"
- **工作内容**: 必须来自表格左上方的工作内容描述
- **资源消耗**: **下半部分所有资源行，数量不固定，要全部识别**
- **完整扫描**: 从上到下识别表格下半部分的每一行资源
- **空值处理**: 消耗量为"-"的资源不包含在该定额项中

## 🎯 数据提取要求
请按照以下JSON格式输出，确保数据准确完整：

```json
{
  "quotas": [
    {
      "parent_quota": {
        "code": "定额编号",
        "name": "定额项名称",
        "work_content": "工作内容",
        "unit": "单位"
      },
      "resource_consumption": [
        {
          "resource_code": "资源编号",
          "category": "类别",
          "name": "子项名称",
          "unit": "单位",
          "consumption": "消耗量"
        }
      ]
    }
  ]
}
```

## ⚠️ OCR特别注意事项
1. **数字精度**: 消耗量数字要保持原始精度，如0.0039、1.50等
2. **文字完整**: 确保项目名称和工作内容的完整性
3. **符号识别**: 正确识别%、³、²等特殊符号
4. **编号格式**: 资源编号通常是8位数字，定额编号如1-1、1-2格式
5. **单位准确**: 工日、台班、m³、%等单位要准确识别"""

        # 添加分册章节信息
        if volume_code and chapter_codes:
            chapters = [ch.strip() for ch in chapter_codes.split(',') if ch.strip()]
            if chapters:
                volume_chapter_instruction = f"""

## 🔥 编号前缀规则（重要）
- **分册编号**: {volume_code}
- **章节编号**: {', '.join(chapters)}
- **编号格式**: 分册编号-章节编号-原定额编号

### 编号转换示例：
- 原编号 "1-1" → 输出 "{volume_code}-{chapters[0]}-1-1"
- 原编号 "1-2" → 输出 "{volume_code}-{chapters[0]}-1-2"

**请确保所有定额编号都按此规则添加前缀！**"""
                base_prompt += volume_chapter_instruction

        base_prompt += """

## 🚀 开始OCR识别和数据提取
请现在开始识别图片中的所有文字，理解表格结构，并按照上述要求提取定额数据。"""

        return base_prompt

    def _get_general_lm_studio_prompt(self, volume_code: str = "", chapter_codes: str = "") -> str:
        """获取通用的LM Studio模型优化提示词"""

        base_prompt = """你是一个专业的建筑工程定额数据提取专家。请仔细分析这张北京市消耗定额表格图片。

## 🚨 重要提醒：表格结构理解
这个表格有特殊的结构，请务必理解：

### 表格布局说明
```
上半部分（横向）：定额项信息
┌─────┬─────┬─────┬─────┬─────┐
│ 编号 │ 项目 │ 1-22│ 1-23│ 1-24│ 1-25│
│     │     │人工挖│一般石│沟槽石│基坑石│
│     │     │石方  │方    │方    │方    │
└─────┴─────┴─────┴─────┴─────┘

下半部分（纵向）：资源消耗明细
┌─────────┬─────────┬─────┬─────┬─────┬─────┐
│资源编号   │资源名称   │单位  │1-22 │1-23 │1-24 │1-25 │
├─────────┼─────────┼─────┼─────┼─────┼─────┤
│00010701 │综合用工三类│工日  │0.525│0.067│0.073│0.080│
│99010002 │反铲挖掘机  │台班  │-    │0.0170│0.0182│0.0200│
└─────────┴─────────┴─────┴─────┴─────┴─────┘
```

## 📋 识别规则（严格遵守）

### 第一步：识别定额项（上半部分）
从图片上半部分的横向表格中**严格按表格内容**识别：

#### 🚨 定额编号识别（严格）
- **位置**: 表格第一行的数字列
- **格式**: 1-22, 1-23, 1-24, 1-25
- **要求**: 完全按表格显示，不要修改

#### 🚨 项目名称识别（关键规则）
**必须严格按照表格中的跨行合并规则**：
- **1-22列**: "人工挖石方"（完整名称）
- **1-23列**: "机械破碎石方" + "一般石方" = "机械破碎石方 一般石方"
- **1-24列**: "机械破碎石方" + "沟槽石方" = "机械破碎石方 沟槽石方"
- **1-25列**: "机械破碎石方" + "基坑石方" = "机械破碎石方 基坑石方"

**重要**: 不要自己理解或修改名称，严格按表格中显示的文字组合！

#### 🚨 工作内容识别（严格来源）
**必须从表格左上方的工作内容文字中提取**：
- **位置**: 表格上方的"工作内容"部分
- **内容**: "1.人工挖石方：直石、清渣、摆排、清底修边等。2.机械破碎石方：装卸机头、破碎岩石等。"
- **要求**: 根据定额项类型选择对应的工作内容

### 第二步：识别资源消耗（下半部分）
从图片下半部分的纵向表格中**完整识别所有资源**：

#### 🚨 完整资源识别规则（重要）
**必须识别表格下半部分的所有资源行**：
- **完整扫描**: 从上到下识别表格下半部分的每一行资源
- **资源编号**: 通常是6-8位数字（如：00010701, 99010002, 9907000703等）
- **资源名称**: 人工、机械、材料、其他费用等各类资源
- **单位**: 工日、台班、m³、%等
- **数量不固定**: 不同页面的资源数量可能不同，要全部识别

#### 🚨 空值处理规则
- **"-"值**: 表示该定额项不使用此资源，**不要包含在输出中**
- **有效值**: 只提取有数值的资源消耗（包括0.000等小数值）
- **完整性**: 确保识别表格中的所有资源行，不要遗漏任何一行

### 第三步：建立精确对应关系
**🚨 完整性要求（关键）**：
- **整页扫描**: 必须识别表格下半部分的每一行资源
- **不遗漏**: 从第一行资源到最后一行资源，全部识别
- **动态适应**: 不同页面的资源数量可能不同（可能是3项、6项、10项等）
- **有效过滤**: 每个定额项只包含有数值的资源消耗（排除"-"值）

## 🎯 输出格式（严格按照此格式）

### 示例输出（严格按照表格内容）：
```json
{
  "quotas": [
    {
      "parent_quota": {
        "code": "1-22",
        "name": "人工挖石方",
        "work_content": "人工挖石方：直石、清渣、摆排、清底修边等",
        "unit": "m³"
      },
      "resource_consumption": [
        {
          "resource_code": "00010701",
          "category": "人工",
          "name": "综合用工三类",
          "unit": "工日",
          "consumption": "0.525"
        },
        {
          "resource_code": "9943000205",
          "category": "机械",
          "name": "空压机 6m³/min",
          "unit": "台班",
          "consumption": "0.2500"
        },
        {
          "resource_code": "99330001",
          "category": "机械",
          "name": "风镐",
          "unit": "台班",
          "consumption": "0.5000"
        },
        {
          "resource_code": "99460004",
          "category": "其他费用",
          "name": "其他机具费 占人工费",
          "unit": "%",
          "consumption": "1.50"
        }
      ]
    },
    {
      "parent_quota": {
        "code": "1-23",
        "name": "机械破碎石方 一般石方",
        "work_content": "机械破碎石方：装卸机头、破碎岩石等",
        "unit": "m³"
      },
      "resource_consumption": [
        {
          "resource_code": "00010701",
          "category": "人工",
          "name": "综合用工三类",
          "unit": "工日",
          "consumption": "0.067"
        },
        {
          "resource_code": "99010002",
          "category": "机械",
          "name": "反铲挖掘机（带液压锤）",
          "unit": "台班",
          "consumption": "0.0170"
        },
        {
          "resource_code": "9907000703",
          "category": "机械",
          "name": "轮胎式装载机 3m³",
          "unit": "台班",
          "consumption": "0.0057"
        },
        {
          "resource_code": "99460004",
          "category": "其他费用",
          "name": "其他机具费 占人工费",
          "unit": "%",
          "consumption": "1.50"
        }
      ]
    }
  ]
}
```

## ⚠️ 关键要求（必须严格遵守）

### 🚨 严格性要求
1. **定额项名称**: 严格按表格中的文字，不要自己理解或修改
2. **跨行合并**: 正确识别表格中跨行合并的单元格内容
3. **工作内容**: 必须来自表格左上方的"工作内容"文字
4. **资源完整性**: **识别表格下半部分的所有资源行**（数量不固定）
5. **空值排除**: 消耗量为"-"的资源不要包含在该定额项中
6. **数值精度**: 保持原始数值精度（如0.525, 0.067, 0.0170）

### 🎯 完整识别规则
- **整页扫描**: 必须识别表格下半部分的每一行资源，不要遗漏
- **动态数量**: 不同页面的资源数量可能不同，要全部识别
- **有效资源**: 每个定额项只包含有数值的资源（排除"-"值）
- **名称规则**: 严格按表格跨行合并规则组合名称

### 🔍 验证检查
- **完整性**: 确保识别了表格中的所有资源行
- **准确性**: 名称严格按表格文字，不要自己理解
- **有效性**: 只包含有数值的资源消耗，排除"-"值"""

        # 添加分册章节信息
        if volume_code and chapter_codes:
            chapters = [ch.strip() for ch in chapter_codes.split(',') if ch.strip()]
            if chapters:
                volume_chapter_instruction = f"""

## 🔥 编号前缀规则（重要）
- **分册编号**: {volume_code}
- **章节编号**: {', '.join(chapters)}
- **编号格式**: 分册编号-章节编号-原定额编号

### 编号转换示例：
- 原编号 "1-1" → 输出 "{volume_code}-{chapters[0]}-1-1"
- 原编号 "1-2" → 输出 "{volume_code}-{chapters[0]}-1-2"

**请确保所有定额编号都按此规则添加前缀！**"""
                base_prompt += volume_chapter_instruction

        base_prompt += """

## 🚀 开始分析

⚠️ **重要输出要求**：
- **只输出JSON**: 直接输出quotas格式的JSON，不要包含分析过程
- **不要解释**: 不要添加"### 分析与提取过程"等说明文字
- **不要markdown**: 不要使用```json代码块包装
- **纯JSON格式**: 直接以{开始，以}结束

请现在开始分析图片中的定额表格，按照上述要求提取数据并**直接输出**标准的quotas格式JSON。"""

        return base_prompt

    def _validate_and_fix_local_model_result(self, content: str, model_name: str) -> str:
        """验证和修复本地模型的识别结果"""
        try:
            # 首先进行基本的JSON修复
            cleaned_content = self._validate_and_fix_json_response(content, model_name)

            # 尝试解析JSON
            try:
                data = json.loads(cleaned_content)
            except json.JSONDecodeError:
                self.logger.warning(f"⚠️ 模型 {model_name} JSON解析失败，返回原始内容")
                return cleaned_content

            # 检查是否为quotas格式
            if "quotas" not in data:
                self.logger.warning(f"⚠️ 模型 {model_name} 未返回quotas格式")
                return cleaned_content

            # 验证和修复quotas数据
            fixed_quotas = []
            for i, quota in enumerate(data.get("quotas", [])):
                fixed_quota = self._fix_quota_item(quota, i, model_name)
                if fixed_quota:
                    fixed_quotas.append(fixed_quota)

            # 重新构建数据
            fixed_data = {"quotas": fixed_quotas}

            self.logger.info(f"✅ 模型 {model_name} 数据验证完成，修复了 {len(fixed_quotas)} 个定额项")
            return json.dumps(fixed_data, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.error(f"❌ 模型 {model_name} 数据验证失败: {str(e)}")
            return content

    def _fix_quota_item(self, quota: dict, index: int, model_name: str) -> dict:
        """修复单个定额项数据"""
        try:
            # 检查基本结构
            if "parent_quota" not in quota or "resource_consumption" not in quota:
                self.logger.warning(f"⚠️ 定额项 {index} 缺少基本结构")
                return None

            parent_quota = quota["parent_quota"]
            resource_consumption = quota["resource_consumption"]

            # 修复定额编号格式
            if "code" in parent_quota:
                code = str(parent_quota["code"])
                # 检查是否为资源编号（8位数字）误识别为定额编号
                if len(code) >= 8 and code.isdigit():
                    self.logger.warning(f"⚠️ 定额项 {index} 编号疑似为资源编号: {code}")
                    return None

                # 修复编号格式
                if not code.startswith(("1-", "2-", "3-", "4-")):
                    # 尝试修复编号格式
                    if code.isdigit() and len(code) <= 3:
                        parent_quota["code"] = f"1-{code}"
                        self.logger.info(f"✅ 修复定额编号: {code} -> 1-{code}")

            # 验证定额项名称的合理性
            if "name" in parent_quota:
                name = str(parent_quota["name"])
                # 检查是否包含资源名称特征（可能是误识别）
                resource_keywords = ["综合用工", "挖掘机", "装载机", "空压机", "风镐", "机具费"]
                if any(keyword in name for keyword in resource_keywords):
                    self.logger.warning(f"⚠️ 定额项 {index} 名称疑似为资源名称: {name}")
                    return None

                # 检查名称长度合理性
                if len(name) > 50:  # 定额项名称通常不会太长
                    self.logger.warning(f"⚠️ 定额项 {index} 名称过长: {name[:30]}...")
                    return None

            # 验证资源消耗数据
            valid_resources = []
            for res in resource_consumption:
                if self._is_valid_resource(res):
                    # 检查是否为空值资源（消耗量为"-"）
                    consumption = str(res.get("consumption", ""))
                    if consumption == "-" or consumption == "":
                        self.logger.info(f"🔄 跳过空值资源: {res.get('name', 'unknown')}")
                        continue
                    valid_resources.append(res)
                else:
                    self.logger.warning(f"⚠️ 无效资源消耗数据: {res}")

            # 验证资源数量的合理性（动态数量）
            if len(valid_resources) < 1:  # 每个定额项至少应该有1个有效资源
                self.logger.warning(f"⚠️ 定额项 {index} 没有有效资源")
                return None
            elif len(valid_resources) > 20:  # 防止异常情况，一般不会超过20个资源
                self.logger.warning(f"⚠️ 定额项 {index} 资源数量异常过多: {len(valid_resources)}")
                # 保留前20个资源
                valid_resources = valid_resources[:20]

            self.logger.info(f"✅ 定额项 {index} 包含 {len(valid_resources)} 个有效资源")

            # 返回修复后的定额项
            return {
                "parent_quota": parent_quota,
                "resource_consumption": valid_resources
            }

        except Exception as e:
            self.logger.error(f"❌ 修复定额项 {index} 失败: {str(e)}")
            return None

    def _is_valid_resource(self, resource: dict) -> bool:
        """验证资源消耗数据是否有效"""
        try:
            # 检查必要字段
            required_fields = ["resource_code", "name", "unit", "consumption"]
            for field in required_fields:
                if field not in resource:
                    return False

            # 检查资源编号格式
            resource_code = str(resource["resource_code"])
            if len(resource_code) < 6:  # 资源编号通常至少6位
                return False

            # 检查消耗量格式
            consumption = str(resource["consumption"])
            if consumption not in ["-", ""] and not self._is_valid_number(consumption):
                return False

            return True

        except Exception:
            return False

    def _is_valid_number(self, value: str) -> bool:
        """检查是否为有效数字"""
        try:
            float(value)
            return True
        except ValueError:
            return False
