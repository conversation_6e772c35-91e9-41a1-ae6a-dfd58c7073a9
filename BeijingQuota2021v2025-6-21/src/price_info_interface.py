#!/usr/bin/env python3
"""
信息价识别界面组件
Price Information Recognition Interface Components
为高级定额管理系统提供信息价识别功能的用户界面
"""

import gradio as gr
import pandas as pd
import os
import asyncio
from typing import List, Dict, Any, Optional, Tuple
import logging

from .price_info_processor import PriceInfoProcessor

class PriceInfoInterface:
    """信息价识别界面类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.processor = PriceInfoProcessor()
        
    def create_price_info_recognition_interface(self):
        """创建信息价识别界面"""
        with gr.Group(elem_classes="feature-card"):
            gr.HTML("""
                <h3 style="color: #667eea; margin-bottom: 15px;">
                    <span class="icon">💰</span>信息价识别处理
                </h3>
                <p style="color: #666; margin-bottom: 20px;">
                    上传北京市造价信息PDF文件，自动识别价格信息表格数据
                </p>
            """)

            with gr.Row():
                with gr.Column(scale=2):
                    # PDF文件上传
                    price_pdf_input = gr.File(
                        label="📄 选择造价信息PDF文件",
                        file_types=[".pdf"],
                        type="filepath"
                    )

                    # PDF预览区域
                    with gr.Group(elem_classes="feature-card"):
                        gr.HTML("""
                            <h4 style="color: #667eea; margin-bottom: 10px;">
                                <span class="icon">📖</span>PDF预览浏览器
                            </h4>
                        """)

                        # PDF控制按钮
                        with gr.Row():
                            with gr.Column(scale=1):
                                current_page_input = gr.Number(
                                    label="当前页码",
                                    value=1,
                                    minimum=1,
                                    precision=0,
                                    interactive=True
                                )
                            with gr.Column(scale=1):
                                total_pages_display = gr.Textbox(
                                    label="总页数",
                                    value="0",
                                    interactive=False
                                )
                            with gr.Column(scale=2):
                                with gr.Row():
                                    prev_page_btn = gr.Button("⬅️ 上一页", size="sm", elem_classes="btn-secondary")
                                    next_page_btn = gr.Button("➡️ 下一页", size="sm", elem_classes="btn-secondary")
                                    zoom_btn = gr.Button("🔍 放大查看", size="sm", elem_classes="btn-secondary")

                        # PDF页面显示
                        pdf_viewer = gr.HTML(
                            label="PDF预览",
                            value="""
                                <div style="text-align: center; padding: 60px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 15px; border: 2px dashed #667eea;">
                                    <div style="font-size: 3em; margin-bottom: 20px;">📄</div>
                                    <h3 style="color: #667eea; margin-bottom: 10px;">PDF智能浏览器</h3>
                                    <p style="color: #666;">上传PDF文件后将显示交互式浏览器</p>
                                    <p style="color: #999; font-size: 0.9em;">支持页面导航、缩放查看等功能</p>
                                </div>
                            """
                        )

                    # 页码设置
                    with gr.Row():
                        start_page_input = gr.Number(
                            label="开始页码",
                            value=1,
                            minimum=1,
                            precision=0
                        )
                        end_page_input = gr.Number(
                            label="结束页码",
                            value=1,
                            minimum=1,
                            precision=0
                        )

                    # AI模型选择
                    model_type_input = gr.Dropdown(
                        label="🤖 选择AI模型",
                        choices=[
                            ("阿里通义千问-QVQ-Max", "qwen_qvq_max"),
                            ("LM Studio: qwen2.5-vl-7b", "lm_studio_qwen2_5_vl_7b")
                        ],
                        value="qwen_qvq_max"
                    )

                    # 处理按钮
                    process_price_btn = gr.Button(
                        "🚀 开始识别信息价",
                        variant="primary",
                        size="lg"
                    )

                with gr.Column(scale=1):
                    # 处理状态显示
                    price_status_output = gr.HTML(
                        label="处理状态",
                        value="<p style='color: #666;'>等待开始处理...</p>"
                    )
        
        # 结果展示区域
        with gr.Group(elem_classes="feature-card"):
            gr.HTML("""
                <h3 style="color: #667eea; margin-bottom: 15px;">
                    <span class="icon">📊</span>识别结果
                </h3>
            """)
            
            # 结果统计
            price_stats_output = gr.HTML(
                label="统计信息",
                value="<p style='color: #666;'>暂无数据</p>"
            )
            
            # 结果预览
            price_preview_output = gr.Dataframe(
                label="信息价数据预览",
                headers=["页眉标识", "章节编号", "章节名称", "资源编号", "产品名称", 
                        "规格型号及特征", "计量单位", "市场参考价（含税）", "市场参考价（不含税）", "备注"],
                interactive=False,
                wrap=True
            )
            
            # 下载链接
            price_download_output = gr.HTML(
                label="下载文件",
                value=""
            )
        
        # 绑定事件
        process_price_btn.click(
            fn=self.process_price_info,
            inputs=[
                price_pdf_input,
                start_page_input,
                end_page_input,
                model_type_input
            ],
            outputs=[
                price_status_output,
                price_stats_output,
                price_preview_output,
                price_download_output
            ]
        )
        
        return {
            'pdf_input': price_pdf_input,
            'pdf_viewer': pdf_viewer,
            'current_page': current_page_input,
            'total_pages': total_pages_display,
            'prev_page_btn': prev_page_btn,
            'next_page_btn': next_page_btn,
            'zoom_btn': zoom_btn,
            'start_page': start_page_input,
            'end_page': end_page_input,
            'model_type': model_type_input,
            'process_btn': process_price_btn,
            'status_output': price_status_output,
            'stats_output': price_stats_output,
            'preview_output': price_preview_output,
            'download_output': price_download_output
        }
    
    def create_price_merge_interface(self):
        """创建信息价合并界面"""
        with gr.Group(elem_classes="feature-card"):
            gr.HTML("""
                <h3 style="color: #667eea; margin-bottom: 15px;">
                    <span class="icon">🔗</span>信息价与定额数据合并
                </h3>
                <p style="color: #666; margin-bottom: 20px;">
                    将识别的信息价数据与定额资源数据进行智能匹配合并
                </p>
            """)
            
            with gr.Row():
                with gr.Column():
                    # 信息价文件选择
                    price_file_input = gr.Dropdown(
                        label="📊 选择信息价文件",
                        choices=[],
                        interactive=True
                    )
                    
                    # 定额资源文件选择
                    quota_file_input = gr.Dropdown(
                        label="📋 选择定额资源文件",
                        choices=[],
                        interactive=True
                    )
                    
                    # 刷新文件列表按钮
                    refresh_files_btn = gr.Button(
                        "🔄 刷新文件列表",
                        size="sm"
                    )
                    
                    # 合并按钮
                    merge_btn = gr.Button(
                        "🔗 开始合并",
                        variant="primary",
                        size="lg"
                    )
                
                with gr.Column():
                    # 合并状态
                    merge_status_output = gr.HTML(
                        label="合并状态",
                        value="<p style='color: #666;'>等待开始合并...</p>"
                    )
        
        # 合并结果展示
        with gr.Group(elem_classes="feature-card"):
            gr.HTML("""
                <h3 style="color: #667eea; margin-bottom: 15px;">
                    <span class="icon">📈</span>合并结果
                </h3>
            """)
            
            merge_preview_output = gr.Dataframe(
                label="合并数据预览",
                interactive=False,
                wrap=True
            )
            
            merge_download_output = gr.HTML(
                label="下载合并文件",
                value=""
            )
        
        # 绑定事件
        refresh_files_btn.click(
            fn=self.refresh_file_lists,
            outputs=[price_file_input, quota_file_input]
        )
        
        merge_btn.click(
            fn=self.merge_price_with_quota,
            inputs=[price_file_input, quota_file_input],
            outputs=[merge_status_output, merge_preview_output, merge_download_output]
        )
        
        return {
            'price_file': price_file_input,
            'quota_file': quota_file_input,
            'refresh_btn': refresh_files_btn,
            'merge_btn': merge_btn,
            'merge_status': merge_status_output,
            'merge_preview': merge_preview_output,
            'merge_download': merge_download_output
        }
    
    def process_price_info(
        self,
        pdf_path: str,
        start_page: int,
        end_page: int,
        model_type: str,
        api_key: str = None
    ) -> Tuple[str, str, pd.DataFrame, str]:
        """处理信息价识别"""
        try:
            if not pdf_path:
                return (
                    "<p style='color: red;'>❌ 请先上传PDF文件</p>",
                    "<p style='color: #666;'>暂无数据</p>",
                    pd.DataFrame(),
                    ""
                )

            # 如果提供了API密钥，临时设置到环境变量
            if api_key and model_type == "qwen_qvq_max":
                import os
                os.environ["DASHSCOPE_API_KEY"] = api_key
                # 重新加载AI处理器的API密钥
                self.processor.ai_processor.reload_api_keys()

            # 显示处理状态
            status_html = "<p style='color: blue;'>🔄 正在处理信息价识别...</p>"
            
            # 异步处理
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                success, message, stats = loop.run_until_complete(
                    self.processor.process_price_info_pdf(
                        pdf_path, start_page, end_page, model_type
                    )
                )
            finally:
                loop.close()
            
            if success:
                # 生成统计信息HTML
                stats_html = f"""
                <div style="background: #f0f9ff; padding: 15px; border-radius: 8px; border-left: 4px solid #0ea5e9;">
                    <h4 style="color: #0ea5e9; margin: 0 0 10px 0;">📊 处理统计</h4>
                    <p><strong>总页数:</strong> {stats.get('total_pages', 0)}</p>
                    <p><strong>成功页数:</strong> {stats.get('processed_pages', 0)}</p>
                    <p><strong>识别章节:</strong> {stats.get('total_chapters', 0)}</p>
                    <p><strong>价格条目:</strong> {stats.get('total_price_items', 0)}</p>
                </div>
                """
                
                # 加载预览数据
                output_files = stats.get('output_files', [])
                preview_df = pd.DataFrame()
                download_html = ""
                
                if output_files:
                    csv_files = [f for f in output_files if f.endswith('.csv')]
                    if csv_files:
                        preview_df = pd.read_csv(csv_files[0], encoding='utf-8-sig')
                        # 只显示前10行
                        if len(preview_df) > 10:
                            preview_df = preview_df.head(10)
                    
                    # 生成下载链接
                    download_links = []
                    for file_path in output_files:
                        file_name = os.path.basename(file_path)
                        download_links.append(f"<a href='file/{file_path}' download='{file_name}'>{file_name}</a>")
                    
                    download_html = f"""
                    <div style="background: #f0fdf4; padding: 15px; border-radius: 8px; border-left: 4px solid #22c55e;">
                        <h4 style="color: #22c55e; margin: 0 0 10px 0;">📁 下载文件</h4>
                        {'<br>'.join(download_links)}
                    </div>
                    """
                
                return (
                    f"<p style='color: green;'>✅ {message}</p>",
                    stats_html,
                    preview_df,
                    download_html
                )
            else:
                return (
                    f"<p style='color: red;'>❌ {message}</p>",
                    "<p style='color: #666;'>处理失败</p>",
                    pd.DataFrame(),
                    ""
                )
                
        except Exception as e:
            error_msg = f"处理失败: {str(e)}"
            self.logger.error(error_msg)
            return (
                f"<p style='color: red;'>❌ {error_msg}</p>",
                "<p style='color: #666;'>处理失败</p>",
                pd.DataFrame(),
                ""
            )
    
    def refresh_file_lists(self) -> Tuple[gr.Dropdown, gr.Dropdown]:
        """刷新文件列表"""
        try:
            output_dir = "output"
            if not os.path.exists(output_dir):
                return gr.Dropdown(choices=[]), gr.Dropdown(choices=[])
            
            # 获取信息价文件（price_info_result开头的CSV文件）
            price_files = []
            quota_files = []
            
            for file in os.listdir(output_dir):
                if file.endswith('.csv'):
                    file_path = os.path.join(output_dir, file)
                    if file.startswith('price_info_result_'):
                        price_files.append((file, file_path))
                    elif any(keyword in file for keyword in ['child_resources', 'resource']):
                        quota_files.append((file, file_path))
            
            # 按时间排序（最新的在前面）
            price_files.sort(key=lambda x: os.path.getmtime(x[1]), reverse=True)
            quota_files.sort(key=lambda x: os.path.getmtime(x[1]), reverse=True)
            
            return (
                gr.Dropdown(choices=price_files),
                gr.Dropdown(choices=quota_files)
            )
            
        except Exception as e:
            self.logger.error(f"刷新文件列表失败: {str(e)}")
            return gr.Dropdown(choices=[]), gr.Dropdown(choices=[])
    
    def merge_price_with_quota(
        self,
        price_file: str,
        quota_file: str
    ) -> Tuple[str, pd.DataFrame, str]:
        """合并信息价与定额数据"""
        try:
            if not price_file or not quota_file:
                return (
                    "<p style='color: red;'>❌ 请选择信息价文件和定额资源文件</p>",
                    pd.DataFrame(),
                    ""
                )
            
            success, message, output_file = self.processor.merge_price_info_with_quotas(
                price_file, quota_file
            )
            
            if success:
                # 加载预览数据
                preview_df = pd.read_csv(output_file, encoding='utf-8-sig')
                if len(preview_df) > 10:
                    preview_df = preview_df.head(10)
                
                # 生成下载链接
                file_name = os.path.basename(output_file)
                download_html = f"""
                <div style="background: #f0fdf4; padding: 15px; border-radius: 8px; border-left: 4px solid #22c55e;">
                    <h4 style="color: #22c55e; margin: 0 0 10px 0;">📁 下载合并文件</h4>
                    <a href='file/{output_file}' download='{file_name}'>{file_name}</a>
                </div>
                """
                
                return (
                    f"<p style='color: green;'>✅ {message}</p>",
                    preview_df,
                    download_html
                )
            else:
                return (
                    f"<p style='color: red;'>❌ {message}</p>",
                    pd.DataFrame(),
                    ""
                )
                
        except Exception as e:
            error_msg = f"合并失败: {str(e)}"
            self.logger.error(error_msg)
            return (
                f"<p style='color: red;'>❌ {error_msg}</p>",
                pd.DataFrame(),
                ""
            )

    def handle_pdf_upload(self, pdf_file):
        """处理PDF文件上传"""
        if not pdf_file:
            return (
                """
                <div style="text-align: center; padding: 60px; background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)); border-radius: 15px; border: 2px dashed #667eea;">
                    <div style="font-size: 3em; margin-bottom: 20px;">📄</div>
                    <h3 style="color: #667eea; margin-bottom: 10px;">PDF智能浏览器</h3>
                    <p style="color: #666;">上传PDF文件后将显示交互式浏览器</p>
                    <p style="color: #999; font-size: 0.9em;">支持页面导航、缩放查看等功能</p>
                </div>
                """,
                gr.update(visible=False),
                gr.update(value=1),
                gr.update(value="0")
            )

        try:
            import fitz  # PyMuPDF
            doc = fitz.open(pdf_file)
            total_pages = len(doc)
            doc.close()

            # 显示第一页
            page_html = self.render_pdf_page(pdf_file, 1)

            return (
                page_html,
                gr.update(visible=True),
                gr.update(value=1, maximum=total_pages),
                gr.update(value=str(total_pages))
            )
        except Exception as e:
            error_html = f"""
            <div style="text-align: center; padding: 40px; background: #fff3cd; border-radius: 15px; border: 2px solid #ffc107;">
                <div style="font-size: 2.5em; margin-bottom: 15px;">⚠️</div>
                <h3 style="color: #856404; margin-bottom: 10px;">PDF加载失败</h3>
                <p style="color: #856404;">错误信息: {str(e)}</p>
                <p style="color: #6c757d; font-size: 0.9em;">请确保上传的是有效的PDF文件</p>
            </div>
            """
            return (
                error_html,
                gr.update(visible=False),
                gr.update(value=1),
                gr.update(value="0")
            )

    def render_pdf_page(self, pdf_file, page_num, zoom_level=2.0):
        """渲染指定页面"""
        try:
            import fitz  # PyMuPDF
            import base64

            doc = fitz.open(pdf_file)
            page = doc[page_num - 1]  # 页面索引从0开始

            # 设置缩放矩阵
            mat = fitz.Matrix(zoom_level, zoom_level)
            pix = page.get_pixmap(matrix=mat)

            # 转换为PNG字节
            img_data = pix.tobytes("png")

            # 转换为base64
            img_base64 = base64.b64encode(img_data).decode()

            doc.close()

            # 生成HTML
            html = f"""
            <div style="text-align: center; padding: 20px; background: white; border-radius: 15px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
                <div style="margin-bottom: 15px;">
                    <span style="background: #667eea; color: white; padding: 8px 16px; border-radius: 20px; font-size: 0.9em;">
                        📄 第 {page_num} 页
                    </span>
                </div>
                <div style="border: 2px solid #e9ecef; border-radius: 10px; overflow: hidden; display: inline-block; max-width: 100%;">
                    <img src="data:image/png;base64,{img_base64}"
                         style="max-width: 100%; height: auto; display: block;"
                         alt="PDF页面 {page_num}"/>
                </div>
                <div style="margin-top: 15px; color: #6c757d; font-size: 0.9em;">
                    💡 使用上方按钮进行页面导航和缩放操作
                </div>
            </div>
            """

            return html

        except Exception as e:
            return f"""
            <div style="text-align: center; padding: 40px; background: #f8d7da; border-radius: 15px; border: 2px solid #dc3545;">
                <div style="font-size: 2.5em; margin-bottom: 15px;">❌</div>
                <h3 style="color: #721c24; margin-bottom: 10px;">页面渲染失败</h3>
                <p style="color: #721c24;">错误信息: {str(e)}</p>
            </div>
            """

    def show_page(self, pdf_file, page_num):
        """显示指定页面"""
        if not pdf_file:
            return "<p style='text-align: center; color: #666;'>📄 请先上传PDF文件</p>"

        return self.render_pdf_page(pdf_file, int(page_num))

    def prev_page(self, pdf_file, current_page):
        """上一页"""
        if not pdf_file:
            return current_page, "<p style='text-align: center; color: #666;'>📄 请先上传PDF文件</p>"

        new_page = max(1, current_page - 1)
        page_html = self.render_pdf_page(pdf_file, new_page)
        return new_page, page_html

    def next_page(self, pdf_file, current_page, total_pages):
        """下一页"""
        if not pdf_file:
            return current_page, "<p style='text-align: center; color: #666;'>📄 请先上传PDF文件</p>"

        max_pages = int(total_pages) if total_pages.isdigit() else 1
        new_page = min(max_pages, current_page + 1)
        page_html = self.render_pdf_page(pdf_file, new_page)
        return new_page, page_html

    def zoom_page(self, pdf_file, current_page):
        """放大查看当前页面"""
        if not pdf_file:
            return "<p style='text-align: center; color: #666;'>📄 请先上传PDF文件</p>"

        # 使用更高的缩放级别
        return self.render_pdf_page(pdf_file, current_page, zoom_level=3.0)
