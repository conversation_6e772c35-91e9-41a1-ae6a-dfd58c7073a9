#!/usr/bin/env python3
"""
企业定额管理系统 v2.0
Enterprise Quota Management System v2.0
基于MCP数据库转换工具构建，避免重复造轮子
"""

import os
import sqlite3
import pandas as pd
import json
from typing import List, Dict, Optional, Tuple, Any
import logging
from datetime import datetime
from .mcp_database_converter import MCPDatabaseConverter

class EnterpriseQuotaManager:
    """企业定额管理系统类（基于MCP工具）"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.db_path = None
        self.connection = None
        
        # 集成MCP数据库转换工具
        self.mcp_converter = MCPDatabaseConverter()
        
        # 支持的数据库类型（基于MCP工具）
        self.supported_db_types = self.mcp_converter.supported_formats
    
    def create_quota_database(
        self, 
        db_type: str,
        db_config: Dict[str, str],
        parent_quotas_files: List[str],
        child_resources_files: List[str]
    ) -> Tuple[bool, str]:
        """创建企业定额数据库（基于MCP工具）"""
        try:
            # 合并所有CSV文件
            all_csv_files = parent_quotas_files + child_resources_files
            
            if not all_csv_files:
                return False, "请至少选择一个CSV文件"
            
            # 根据数据库类型确定输出路径
            if db_type == 'sqlite':
                output_path = db_config.get('database_path', 'output/enterprise_quota.db')
                # 使用MCP工具创建SQLite数据库
                success, message, stats = self.mcp_converter.convert_to_sqlite(all_csv_files, output_path)
                
            elif db_type == 'mongodb':
                output_path = db_config.get('database_path', 'output/enterprise_quota.json')
                # 使用MCP工具创建MongoDB JSON导出
                success, message, stats = self.mcp_converter.convert_to_mongodb(all_csv_files, output_path)
                
            elif db_type in ['mysql', 'postgresql', 'sql_server', 'oracle']:
                output_path = db_config.get('database_path', f'output/enterprise_quota_{db_type}.sql')
                # 使用MCP工具创建SQL脚本
                success, message, stats = self.mcp_converter.convert_to_sql_script(all_csv_files, output_path, db_type)
                
            else:
                return False, f"不支持的数据库类型: {db_type}"
            
            if success:
                # 保存数据库路径以便后续查询
                self.db_path = output_path
                
                # 增强成功消息，添加企业定额管理特色
                enhanced_message = f"""✅ 企业定额数据库创建成功！

{message}

🏢 企业定额管理功能:
- 定额项与资源关联关系已建立
- 支持定额项搜索和资源查看
- 支持价格计算和数据导出
- 可通过查询管理系统进行操作

💡 下一步: 使用"定额查询管理系统"连接并管理此数据库"""
                
                return True, enhanced_message
            else:
                return False, message
            
        except Exception as e:
            error_msg = f"创建定额数据库失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def connect_to_database(self, db_path: str) -> Tuple[bool, str]:
        """连接到定额数据库（使用MCP工具）"""
        try:
            if not os.path.exists(db_path):
                return False, "数据库文件不存在"
            
            # 判断数据库类型
            file_ext = os.path.splitext(db_path)[1].lower()
            
            if file_ext in ['.db', '.sqlite']:
                # SQLite数据库
                self.connection = sqlite3.connect(db_path)
                self.db_path = db_path
                
                # 验证数据库结构（检查是否有表）
                cursor = self.connection.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                if not tables:
                    return False, "数据库中没有找到任何表"
                
                return True, f"SQLite数据库连接成功，找到 {len(tables)} 个表"
                
            elif file_ext == '.json':
                # MongoDB JSON文件
                self.db_path = db_path
                return True, "MongoDB JSON文件连接成功"
                
            else:
                return False, f"不支持的数据库文件格式: {file_ext}"
            
        except Exception as e:
            return False, f"连接数据库失败: {str(e)}"
    
    def preview_database(self, db_path: str) -> str:
        """预览数据库内容（使用MCP工具）"""
        try:
            # 使用MCP工具的预览功能
            return self.mcp_converter.preview_database_file(db_path)
        except Exception as e:
            return f"<p style='color: red;'>❌ 预览失败: {str(e)}</p>"
    
    def get_database_statistics(self) -> Tuple[bool, str, Dict]:
        """获取数据库统计信息（使用MCP工具）"""
        try:
            if not self.db_path:
                return False, "未连接到数据库", {}
            
            file_ext = os.path.splitext(self.db_path)[1].lower()
            
            if file_ext in ['.db', '.sqlite'] and self.connection:
                # SQLite数据库统计
                cursor = self.connection.cursor()
                
                # 获取表列表
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                total_rows = 0
                table_stats = []
                
                for table in tables:
                    cursor.execute(f"SELECT COUNT(*) FROM `{table}`")
                    count = cursor.fetchone()[0]
                    total_rows += count
                    table_stats.append({'name': table, 'rows': count})
                
                # 获取数据库文件大小
                db_size = os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0
                
                stats = {
                    'database_type': 'SQLite',
                    'database_path': self.db_path,
                    'total_tables': len(tables),
                    'total_rows': total_rows,
                    'db_size_kb': db_size / 1024,
                    'tables': table_stats
                }
                
                return True, "统计信息获取成功", stats
                
            elif file_ext == '.json':
                # MongoDB JSON文件统计
                with open(self.db_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if 'collections' in data:
                    # MongoDB导出格式
                    collections = data['collections']
                    total_documents = sum(len(coll.get('documents', [])) for coll in collections.values())
                    
                    stats = {
                        'database_type': 'MongoDB JSON',
                        'database_path': self.db_path,
                        'total_collections': len(collections),
                        'total_documents': total_documents,
                        'db_size_kb': os.path.getsize(self.db_path) / 1024,
                        'collections': [
                            {'name': name, 'documents': len(coll.get('documents', []))}
                            for name, coll in collections.items()
                        ]
                    }
                else:
                    # 普通JSON文件
                    stats = {
                        'database_type': 'JSON',
                        'database_path': self.db_path,
                        'db_size_kb': os.path.getsize(self.db_path) / 1024
                    }
                
                return True, "统计信息获取成功", stats
            
            else:
                return False, "不支持的数据库类型", {}
            
        except Exception as e:
            return False, f"获取统计信息失败: {str(e)}", {}
    
    def search_quotas(
        self, 
        search_term: str = "", 
        limit: int = 50,
        offset: int = 0
    ) -> Tuple[bool, str, List[Dict]]:
        """搜索定额项（基于MCP工具创建的数据库）"""
        try:
            if not self.connection or not self.db_path:
                return False, "未连接到数据库", []
            
            file_ext = os.path.splitext(self.db_path)[1].lower()
            
            if file_ext in ['.db', '.sqlite']:
                # SQLite数据库查询
                cursor = self.connection.cursor()
                
                # 获取表名（动态查找包含定额数据的表）
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                # 查找可能的定额表
                quota_table = None
                for table in tables:
                    cursor.execute(f"PRAGMA table_info(`{table}`)")
                    columns = [col[1] for col in cursor.fetchall()]
                    # 如果表包含定额相关字段，认为是定额表
                    if any(col in ['quota_code', '定额编号', 'quota_name', '定额名称'] for col in columns):
                        quota_table = table
                        break
                
                if not quota_table:
                    # 如果没有找到专门的定额表，使用第一个表
                    quota_table = tables[0] if tables else None
                
                if not quota_table:
                    return False, "数据库中没有找到数据表", []
                
                # 构建查询
                if search_term:
                    # 获取表的所有列
                    cursor.execute(f"PRAGMA table_info(`{quota_table}`)")
                    columns = [col[1] for col in cursor.fetchall()]
                    
                    # 构建模糊搜索条件
                    search_conditions = []
                    for col in columns:
                        search_conditions.append(f"`{col}` LIKE ?")
                    
                    where_clause = " OR ".join(search_conditions)
                    query = f"SELECT * FROM `{quota_table}` WHERE {where_clause} LIMIT ? OFFSET ?"
                    
                    search_pattern = f"%{search_term}%"
                    params = [search_pattern] * len(columns) + [limit, offset]
                    cursor.execute(query, params)
                else:
                    # 获取所有数据
                    query = f"SELECT * FROM `{quota_table}` LIMIT ? OFFSET ?"
                    cursor.execute(query, (limit, offset))
                
                rows = cursor.fetchall()
                
                # 获取列名
                cursor.execute(f"PRAGMA table_info(`{quota_table}`)")
                columns = [col[1] for col in cursor.fetchall()]
                
                # 转换为字典格式
                quotas = []
                for row in rows:
                    quota = dict(zip(columns, row))
                    quotas.append(quota)
                
                return True, f"找到 {len(quotas)} 个记录", quotas
                
            else:
                return False, "当前只支持SQLite数据库查询", []
            
        except Exception as e:
            return False, f"搜索失败: {str(e)}", []
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
