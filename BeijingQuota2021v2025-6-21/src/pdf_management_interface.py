#!/usr/bin/env python3
"""
PDF管理界面
提供已存储PDF文件的查看、管理和操作功能
"""

import gradio as gr
import pandas as pd
import os
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
import logging

from .pdf_storage_manager import PDFStorageManager

class PDFManagementInterface:
    """PDF管理界面类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.pdf_storage = PDFStorageManager()
        
    def create_pdf_management_interface(self):
        """创建PDF管理界面"""
        with gr.Column():
            gr.HTML("""
                <h3 style="color: #667eea; margin-bottom: 15px;">
                    <span class="icon">📁</span>PDF文件管理
                </h3>
                <p style="color: #666; margin-bottom: 20px;">
                    管理已上传的定额和信息价PDF文件，支持查看、删除和统计功能
                </p>
            """)
            
            # 存储统计信息
            with gr.Group():
                gr.HTML("""
                    <h4 style="color: #667eea; margin-bottom: 10px;">
                        📊 存储统计
                    </h4>
                """)
                storage_stats = gr.HTML(
                    value=self._get_storage_stats_html(),
                    elem_classes="stats-display"
                )
            
            # 操作按钮
            with gr.Row():
                refresh_btn = gr.Button("🔄 刷新列表", elem_classes="btn-secondary", size="sm")
                cleanup_btn = gr.Button("🧹 清理孤立文件", elem_classes="btn-secondary", size="sm")
                export_list_btn = gr.Button("📤 导出列表", elem_classes="btn-secondary", size="sm")
            
            # PDF文件列表
            with gr.Group():
                gr.HTML("""
                    <h4 style="color: #667eea; margin-bottom: 10px;">
                        📋 PDF文件列表
                    </h4>
                """)
                
                # 过滤选项
                with gr.Row():
                    file_type_filter = gr.Dropdown(
                        choices=[("全部", "all"), ("定额PDF", "quota"), ("信息价PDF", "price")],
                        value="all",
                        label="文件类型",
                        scale=1
                    )
                    sort_by = gr.Dropdown(
                        choices=[("上传时间", "upload_time"), ("文件大小", "size"), ("文件名", "original_name")],
                        value="upload_time",
                        label="排序方式",
                        scale=1
                    )
                
                # 文件列表表格
                pdf_files_table = gr.Dataframe(
                    value=self._get_pdf_files_dataframe(),
                    headers=["ID", "文件名", "类型", "大小(MB)", "上传时间", "存储路径"],
                    datatype=["str", "str", "str", "number", "str", "str"],
                    interactive=False,
                    wrap=True,
                    max_height=400
                )
            
            # 文件操作
            with gr.Group():
                gr.HTML("""
                    <h4 style="color: #667eea; margin-bottom: 10px;">
                        🛠️ 文件操作
                    </h4>
                """)
                
                with gr.Row():
                    selected_file_id = gr.Textbox(
                        label="选中文件ID",
                        placeholder="点击表格行选择文件",
                        interactive=True,
                        scale=2
                    )
                    delete_file_btn = gr.Button("🗑️ 删除文件", elem_classes="btn-danger", size="sm", scale=1)
                
                # 文件详情
                file_details = gr.HTML(
                    value="<p style='color: #666; text-align: center;'>选择文件查看详情</p>",
                    elem_classes="file-details"
                )
            
            # 操作结果
            operation_status = gr.HTML(
                value="",
                elem_classes="status-display"
            )
            
            # 导出文件下载
            export_download = gr.File(
                label="导出文件下载",
                visible=False
            )
        
        # 绑定事件
        self._bind_events(
            refresh_btn, cleanup_btn, export_list_btn, delete_file_btn,
            file_type_filter, sort_by, selected_file_id,
            storage_stats, pdf_files_table, file_details, operation_status, export_download
        )
        
        return {
            'storage_stats': storage_stats,
            'pdf_files_table': pdf_files_table,
            'file_details': file_details,
            'operation_status': operation_status,
            'export_download': export_download
        }
    
    def _get_storage_stats_html(self) -> str:
        """获取存储统计信息HTML"""
        try:
            stats = self.pdf_storage.get_storage_stats()
            
            html = f"""
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0;">
                <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 2em; margin-bottom: 5px;">📊</div>
                    <div style="font-size: 1.5em; font-weight: bold;">{stats['total_count']}</div>
                    <div style="font-size: 0.9em; opacity: 0.9;">总文件数</div>
                </div>
                <div style="background: linear-gradient(135deg, #f093fb, #f5576c); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 2em; margin-bottom: 5px;">📋</div>
                    <div style="font-size: 1.5em; font-weight: bold;">{stats['quota_count']}</div>
                    <div style="font-size: 0.9em; opacity: 0.9;">定额PDF</div>
                </div>
                <div style="background: linear-gradient(135deg, #4facfe, #00f2fe); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 2em; margin-bottom: 5px;">💰</div>
                    <div style="font-size: 1.5em; font-weight: bold;">{stats['price_count']}</div>
                    <div style="font-size: 0.9em; opacity: 0.9;">信息价PDF</div>
                </div>
                <div style="background: linear-gradient(135deg, #43e97b, #38f9d7); color: white; padding: 20px; border-radius: 10px; text-align: center;">
                    <div style="font-size: 2em; margin-bottom: 5px;">💾</div>
                    <div style="font-size: 1.5em; font-weight: bold;">{stats['total_size_mb']}</div>
                    <div style="font-size: 0.9em; opacity: 0.9;">总大小(MB)</div>
                </div>
            </div>
            """
            
            return html
            
        except Exception as e:
            return f"""
            <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 15px; margin: 10px 0;">
                <p style="color: #721c24; margin: 0;">❌ 获取统计信息失败: {str(e)}</p>
            </div>
            """
    
    def _get_pdf_files_dataframe(self, file_type: str = "all", sort_by: str = "upload_time") -> pd.DataFrame:
        """获取PDF文件列表数据框"""
        try:
            pdf_files = self.pdf_storage.get_stored_pdfs(file_type)
            
            if not pdf_files:
                return pd.DataFrame(columns=["ID", "文件名", "类型", "大小(MB)", "上传时间", "存储路径"])
            
            # 转换为DataFrame
            data = []
            for file_info in pdf_files:
                data.append([
                    file_info.get("id", ""),
                    file_info.get("original_name", ""),
                    "定额PDF" if file_info.get("type") == "quota" else "信息价PDF",
                    file_info.get("size_mb", 0),
                    file_info.get("upload_time", "").replace("T", " ").split(".")[0] if file_info.get("upload_time") else "",
                    file_info.get("stored_path", "")
                ])
            
            df = pd.DataFrame(data, columns=["ID", "文件名", "类型", "大小(MB)", "上传时间", "存储路径"])
            
            # 排序
            if sort_by == "upload_time":
                df = df.sort_values("上传时间", ascending=False)
            elif sort_by == "size":
                df = df.sort_values("大小(MB)", ascending=False)
            elif sort_by == "original_name":
                df = df.sort_values("文件名")
            
            return df
            
        except Exception as e:
            self.logger.error(f"获取PDF文件列表失败: {e}")
            return pd.DataFrame(columns=["ID", "文件名", "类型", "大小(MB)", "上传时间", "存储路径"])
    
    def refresh_pdf_list(self, file_type: str, sort_by: str):
        """刷新PDF文件列表"""
        try:
            stats_html = self._get_storage_stats_html()
            files_df = self._get_pdf_files_dataframe(file_type, sort_by)
            
            status_html = """
            <div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 15px; margin: 10px 0;">
                <p style="color: #155724; margin: 0;">✅ 文件列表已刷新</p>
            </div>
            """
            
            return stats_html, files_df, status_html
            
        except Exception as e:
            error_html = f"""
            <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 15px; margin: 10px 0;">
                <p style="color: #721c24; margin: 0;">❌ 刷新失败: {str(e)}</p>
            </div>
            """
            return self._get_storage_stats_html(), self._get_pdf_files_dataframe(), error_html
    
    def cleanup_orphaned_files(self):
        """清理孤立文件"""
        try:
            count, orphaned_files = self.pdf_storage.cleanup_orphaned_files()
            
            if count > 0:
                status_html = f"""
                <div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 15px; margin: 10px 0;">
                    <p style="color: #155724; margin: 0;">✅ 清理完成，删除了 {count} 个孤立文件</p>
                    <details style="margin-top: 10px;">
                        <summary style="color: #155724; cursor: pointer;">查看删除的文件</summary>
                        <ul style="margin: 5px 0 0 20px; color: #155724;">
                            {''.join([f'<li>{file}</li>' for file in orphaned_files])}
                        </ul>
                    </details>
                </div>
                """
            else:
                status_html = """
                <div style="background: #d1ecf1; border: 1px solid #bee5eb; border-radius: 8px; padding: 15px; margin: 10px 0;">
                    <p style="color: #0c5460; margin: 0;">ℹ️ 没有发现孤立文件</p>
                </div>
                """
            
            # 刷新统计信息
            stats_html = self._get_storage_stats_html()
            files_df = self._get_pdf_files_dataframe()
            
            return stats_html, files_df, status_html
            
        except Exception as e:
            error_html = f"""
            <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 15px; margin: 10px 0;">
                <p style="color: #721c24; margin: 0;">❌ 清理失败: {str(e)}</p>
            </div>
            """
            return self._get_storage_stats_html(), self._get_pdf_files_dataframe(), error_html
    
    def delete_selected_file(self, file_id: str):
        """删除选中的文件"""
        try:
            if not file_id or not file_id.strip():
                return self._get_storage_stats_html(), self._get_pdf_files_dataframe(), """
                <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 10px 0;">
                    <p style="color: #856404; margin: 0;">⚠️ 请先选择要删除的文件</p>
                </div>
                """, "<p style='color: #666; text-align: center;'>选择文件查看详情</p>"
            
            # 获取文件信息
            file_info = self.pdf_storage.get_pdf_info(file_id.strip())
            if not file_info:
                return self._get_storage_stats_html(), self._get_pdf_files_dataframe(), """
                <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 15px; margin: 10px 0;">
                    <p style="color: #721c24; margin: 0;">❌ 文件不存在</p>
                </div>
                """, "<p style='color: #666; text-align: center;'>选择文件查看详情</p>"
            
            # 删除文件
            success, message = self.pdf_storage.delete_pdf(file_id.strip())
            
            if success:
                status_html = f"""
                <div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 15px; margin: 10px 0;">
                    <p style="color: #155724; margin: 0;">✅ 文件删除成功: {file_info['original_name']}</p>
                </div>
                """
            else:
                status_html = f"""
                <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 15px; margin: 10px 0;">
                    <p style="color: #721c24; margin: 0;">❌ 删除失败: {message}</p>
                </div>
                """
            
            # 刷新列表
            stats_html = self._get_storage_stats_html()
            files_df = self._get_pdf_files_dataframe()
            
            return stats_html, files_df, status_html, "<p style='color: #666; text-align: center;'>选择文件查看详情</p>"
            
        except Exception as e:
            error_html = f"""
            <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 15px; margin: 10px 0;">
                <p style="color: #721c24; margin: 0;">❌ 删除操作失败: {str(e)}</p>
            </div>
            """
            return self._get_storage_stats_html(), self._get_pdf_files_dataframe(), error_html, "<p style='color: #666; text-align: center;'>选择文件查看详情</p>"
    
    def show_file_details(self, file_id: str):
        """显示文件详情"""
        try:
            if not file_id or not file_id.strip():
                return "<p style='color: #666; text-align: center;'>选择文件查看详情</p>"
            
            file_info = self.pdf_storage.get_pdf_info(file_id.strip())
            if not file_info:
                return "<p style='color: #f56565; text-align: center;'>文件不存在</p>"
            
            # 格式化上传时间
            upload_time = file_info.get("upload_time", "")
            if upload_time:
                try:
                    dt = datetime.fromisoformat(upload_time.replace("Z", "+00:00"))
                    upload_time = dt.strftime("%Y-%m-%d %H:%M:%S")
                except:
                    pass
            
            html = f"""
            <div style="background: #f8f9fa; border-radius: 10px; padding: 20px; margin: 10px 0;">
                <h4 style="color: #667eea; margin-top: 0;">📄 {file_info.get('original_name', 'Unknown')}</h4>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0;">
                    <div>
                        <strong>文件ID:</strong><br>
                        <code style="background: #e9ecef; padding: 2px 6px; border-radius: 4px; font-size: 0.9em;">{file_info.get('id', 'N/A')}</code>
                    </div>
                    <div>
                        <strong>文件类型:</strong><br>
                        <span style="color: {'#28a745' if file_info.get('type') == 'quota' else '#007bff'};">
                            {'📋 定额PDF' if file_info.get('type') == 'quota' else '💰 信息价PDF'}
                        </span>
                    </div>
                    <div>
                        <strong>文件大小:</strong><br>
                        {file_info.get('size_mb', 0)} MB ({file_info.get('size', 0):,} bytes)
                    </div>
                    <div>
                        <strong>上传时间:</strong><br>
                        {upload_time}
                    </div>
                </div>
                
                <div style="margin: 15px 0;">
                    <strong>存储路径:</strong><br>
                    <code style="background: #e9ecef; padding: 4px 8px; border-radius: 4px; font-size: 0.8em; word-break: break-all;">
                        {file_info.get('stored_path', 'N/A')}
                    </code>
                </div>
                
                <div style="margin: 15px 0;">
                    <strong>文件哈希:</strong><br>
                    <code style="background: #e9ecef; padding: 2px 6px; border-radius: 4px; font-size: 0.8em;">
                        {file_info.get('hash', 'N/A')[:16]}...
                    </code>
                </div>
            </div>
            """
            
            return html
            
        except Exception as e:
            return f"<p style='color: #f56565; text-align: center;'>获取文件详情失败: {str(e)}</p>"
    
    def export_pdf_list(self):
        """导出PDF文件列表"""
        try:
            pdf_files = self.pdf_storage.get_stored_pdfs("all")
            
            if not pdf_files:
                return """
                <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin: 10px 0;">
                    <p style="color: #856404; margin: 0;">⚠️ 没有PDF文件可导出</p>
                </div>
                """, gr.update(visible=False)
            
            # 创建导出数据
            export_data = []
            for file_info in pdf_files:
                export_data.append({
                    "ID": file_info.get("id", ""),
                    "原始文件名": file_info.get("original_name", ""),
                    "文件类型": "定额PDF" if file_info.get("type") == "quota" else "信息价PDF",
                    "文件大小(MB)": file_info.get("size_mb", 0),
                    "文件大小(Bytes)": file_info.get("size", 0),
                    "上传时间": file_info.get("upload_time", ""),
                    "存储路径": file_info.get("stored_path", ""),
                    "文件哈希": file_info.get("hash", "")
                })
            
            # 保存为CSV
            df = pd.DataFrame(export_data)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            export_filename = f"output/pdf_files_list_{timestamp}.csv"
            
            # 确保输出目录存在
            os.makedirs("output", exist_ok=True)
            
            df.to_csv(export_filename, index=False, encoding='utf-8-sig')
            
            status_html = f"""
            <div style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 8px; padding: 15px; margin: 10px 0;">
                <p style="color: #155724; margin: 0;">✅ 导出成功，共 {len(export_data)} 个文件</p>
            </div>
            """
            
            return status_html, gr.update(value=export_filename, visible=True)
            
        except Exception as e:
            error_html = f"""
            <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; padding: 15px; margin: 10px 0;">
                <p style="color: #721c24; margin: 0;">❌ 导出失败: {str(e)}</p>
            </div>
            """
            return error_html, gr.update(visible=False)
    
    def _bind_events(self, refresh_btn, cleanup_btn, export_list_btn, delete_file_btn,
                     file_type_filter, sort_by, selected_file_id,
                     storage_stats, pdf_files_table, file_details, operation_status, export_download):
        """绑定事件"""
        
        # 刷新按钮
        refresh_btn.click(
            fn=self.refresh_pdf_list,
            inputs=[file_type_filter, sort_by],
            outputs=[storage_stats, pdf_files_table, operation_status]
        )
        
        # 过滤器变化
        file_type_filter.change(
            fn=self.refresh_pdf_list,
            inputs=[file_type_filter, sort_by],
            outputs=[storage_stats, pdf_files_table, operation_status]
        )
        
        sort_by.change(
            fn=self.refresh_pdf_list,
            inputs=[file_type_filter, sort_by],
            outputs=[storage_stats, pdf_files_table, operation_status]
        )
        
        # 清理孤立文件
        cleanup_btn.click(
            fn=self.cleanup_orphaned_files,
            outputs=[storage_stats, pdf_files_table, operation_status]
        )
        
        # 删除文件
        delete_file_btn.click(
            fn=self.delete_selected_file,
            inputs=[selected_file_id],
            outputs=[storage_stats, pdf_files_table, operation_status, file_details]
        )
        
        # 显示文件详情
        selected_file_id.change(
            fn=self.show_file_details,
            inputs=[selected_file_id],
            outputs=[file_details]
        )
        
        # 导出列表
        export_list_btn.click(
            fn=self.export_pdf_list,
            outputs=[operation_status, export_download]
        )
