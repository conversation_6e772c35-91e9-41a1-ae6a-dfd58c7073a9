#!/usr/bin/env python3
"""
测试CSV导入修复
"""

import os
import sys
import pandas as pd
from pathlib import Path

# 添加src目录到路径
sys.path.append('src')

def create_test_data():
    """创建测试数据文件"""
    # 确保output目录存在
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    # 创建parent_quotas测试数据
    parent_data = {
        '定额编号': ['001-001', '001-002'],
        '定额名称': ['混凝土浇筑C30', '钢筋绑扎HPB300'],
        '单位': ['m³', 'kg'],
        '人工费': [120.5, 85.2],
        '材料费': [450.8, 320.5],
        '机械费': [80.3, 45.8],
        '合计': [651.6, 451.5]
    }
    
    parent_df = pd.DataFrame(parent_data)
    parent_csv_path = output_dir / "fix_test_parent_quotas.csv"
    parent_df.to_csv(parent_csv_path, index=False, encoding='utf-8-sig')
    
    # 创建child_resources测试数据
    child_data = {
        '定额编号': ['001-001', '001-002'],
        '资源编号': ['R001', 'R002'],
        '资源名称': ['C30混凝土', 'HPB300钢筋'],
        '资源类型': ['材料', '材料'],
        '数量': [1.05, 1.02],
        '单位': ['m³', 'kg'],
        '单价': [420.0, 4.2],
        '合价': [441.0, 4.284]
    }
    
    child_df = pd.DataFrame(child_data)
    child_csv_path = output_dir / "fix_test_child_resources.csv"
    child_df.to_csv(child_csv_path, index=False, encoding='utf-8-sig')
    
    print(f"✅ 创建修复测试数据文件:")
    print(f"   - {parent_csv_path} ({len(parent_df)} 行)")
    print(f"   - {child_csv_path} ({len(child_df)} 行)")
    
    return [str(parent_csv_path)], [str(child_csv_path)]

def test_fixed_csv_import():
    """测试修复后的CSV导入"""
    try:
        from enterprise_quota_manager import EnterpriseQuotaManager
        
        # 创建测试数据
        parent_files, child_files = create_test_data()
        
        print(f"\n🔧 测试修复后的CSV导入...")
        
        # 创建管理器
        manager = EnterpriseQuotaManager()
        
        # 配置数据库创建参数
        db_config = {
            'host': 'localhost',
            'port': 5432,
            'user': 'postgres',
            'password': 'postgres123',
            'default_db': 'postgres',
            'database': 'fix_test_enterprise_quota'
        }
        
        print(f"📋 目标数据库: {db_config['database']}")
        
        # 创建数据库
        success, message = manager.create_quota_database(
            'postgresql', db_config, parent_files, child_files
        )
        
        if success:
            print("✅ PostgreSQL数据库创建成功!")
            print(f"📋 创建结果:\n{message}")
            
            # 验证数据库是否真的创建了并且有数据
            print("\n🔍 验证数据库创建和数据导入...")
            
            import psycopg2
            
            # 连接到目标数据库检查表和数据
            target_conn = psycopg2.connect(
                host=db_config['host'],
                port=db_config['port'],
                user=db_config['user'],
                password=db_config['password'],
                database=db_config['database']
            )
            target_cursor = target_conn.cursor()
            
            # 获取表列表
            target_cursor.execute("""
                SELECT table_name FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
            """)
            tables = [row[0] for row in target_cursor.fetchall()]
            
            print(f"✅ 找到 {len(tables)} 个表: {tables}")
            
            # 检查表中的数据
            total_rows = 0
            for table in tables:
                target_cursor.execute(f'SELECT COUNT(*) FROM "{table}"')
                count = target_cursor.fetchone()[0]
                total_rows += count
                print(f"   - {table}: {count} 行数据")
                
                # 显示表的前几行数据
                target_cursor.execute(f'SELECT * FROM "{table}" LIMIT 3')
                rows = target_cursor.fetchall()
                if rows:
                    print(f"     示例数据: {rows[0]}")
            
            target_cursor.close()
            target_conn.close()
            
            print(f"\n📊 最终统计:")
            print(f"   - 表数量: {len(tables)}")
            print(f"   - 总记录数: {total_rows}")
            
            if len(tables) > 0 and total_rows > 0:
                print("🎉 CSV数据导入修复成功!")
                return True
            else:
                print("❌ 数据库创建成功但数据导入失败")
                return False
        else:
            print(f"❌ PostgreSQL数据库创建失败: {message}")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_test_database():
    """清理测试数据库"""
    try:
        import psycopg2
        
        config = {
            'host': 'localhost',
            'port': 5432,
            'user': 'postgres',
            'password': 'postgres123',
            'database': 'postgres'
        }
        
        conn = psycopg2.connect(**config)
        conn.set_isolation_level(psycopg2.extensions.ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # 检查测试数据库是否存在
        cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", ('fix_test_enterprise_quota',))
        if cursor.fetchone():
            # 断开所有连接并删除数据库
            cursor.execute("""
                SELECT pg_terminate_backend(pid)
                FROM pg_stat_activity
                WHERE datname = 'fix_test_enterprise_quota' AND pid <> pg_backend_pid()
            """)
            cursor.execute('DROP DATABASE "fix_test_enterprise_quota"')
            print("🗑️ 清理测试数据库: fix_test_enterprise_quota")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"⚠️ 清理测试数据库时出错: {e}")

def main():
    """主函数"""
    print("🔧 CSV导入修复测试")
    print("=" * 50)
    
    # 清理之前的测试数据库
    cleanup_test_database()
    
    # 运行测试
    success = test_fixed_csv_import()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 CSV导入修复测试通过!")
        print("\n💡 修复效果:")
        print("1. ✅ SQL语句分割逻辑改进")
        print("2. ✅ CREATE TABLE语句正确执行")
        print("3. ✅ INSERT语句正确执行")
        print("4. ✅ 数据成功导入PostgreSQL")
        
        print("\n🚀 现在Web界面应该可以:")
        print("   - 正确创建PostgreSQL数据库")
        print("   - 成功导入CSV数据到表中")
        print("   - 显示正确的表数量和记录数")
        print("   - 在PostgreSQL客户端中查看数据")
    else:
        print("❌ CSV导入修复测试失败!")
        print("\n🔧 需要进一步检查:")
        print("1. SQL语句分割逻辑")
        print("2. PostgreSQL执行过程")
        print("3. 错误处理机制")
    
    # 询问是否清理测试数据库
    if success:
        try:
            response = input("\n🗑️ 是否清理测试数据库? (y/N): ").strip().lower()
            if response == 'y':
                cleanup_test_database()
        except KeyboardInterrupt:
            print("\n")
    
    return success

if __name__ == "__main__":
    main()
