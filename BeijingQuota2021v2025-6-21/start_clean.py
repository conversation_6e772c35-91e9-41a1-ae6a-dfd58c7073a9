#!/usr/bin/env python3
"""
清洁启动脚本 - 解决Gradio常见错误
Clean startup script - Fix common Gradio errors
"""

import os
import sys
import warnings

def setup_environment():
    """设置环境变量，禁用不必要的功能"""
    
    # 禁用警告
    warnings.filterwarnings("ignore")
    
    # 设置环境变量禁用外部服务
    os.environ['GRADIO_ANALYTICS_ENABLED'] = 'False'
    os.environ['GRADIO_SERVER_NAME'] = '0.0.0.0'
    os.environ['GRADIO_SERVER_PORT'] = '7864'
    os.environ['GRADIO_SHARE'] = 'False'
    os.environ['GRADIO_DEBUG'] = 'False'
    os.environ['GRADIO_QUIET'] = 'True'
    
    # 禁用Google翻译
    os.environ['GOOGLE_TRANSLATE_DISABLE'] = 'True'
    os.environ['DISABLE_TELEMETRY'] = 'True'
    
    # 禁用HuggingFace相关功能
    os.environ['HF_HUB_DISABLE_TELEMETRY'] = 'True'
    os.environ['HUGGINGFACE_HUB_DISABLE_TELEMETRY'] = 'True'
    
    # 设置字体相关
    os.environ['GRADIO_THEME_FONT'] = 'system'
    
    print("🔧 环境变量设置完成")
    print("📝 已禁用外部服务和遥测")
    print("🎨 已设置系统字体")

def create_static_files():
    """创建必要的静态文件"""
    
    # 创建目录
    directories = [
        'static',
        'static/fonts',
        'static/fonts/ui-sans-serif',
        'static/fonts/system-ui'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    # 创建空的字体文件
    font_files = [
        'static/fonts/ui-sans-serif/ui-sans-serif-Regular.woff2',
        'static/fonts/system-ui/system-ui-Regular.woff2'
    ]
    
    for font_file in font_files:
        if not os.path.exists(font_file):
            with open(font_file, 'wb') as f:
                f.write(b'')  # 创建空文件
    
    # 创建manifest.json
    manifest_content = '''{
  "name": "北京市2021消耗定额创建工具",
  "short_name": "定额创建工具",
  "description": "基于AI的智能定额数据提取和数据库创建工具",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#667eea",
  "icons": []
}'''
    
    if not os.path.exists('static/manifest.json'):
        with open('static/manifest.json', 'w', encoding='utf-8') as f:
            f.write(manifest_content)
    
    # 创建favicon.ico
    if not os.path.exists('static/favicon.ico'):
        with open('static/favicon.ico', 'wb') as f:
            f.write(b'')  # 创建空文件
    
    print("📁 静态文件创建完成")

def patch_gradio():
    """修补Gradio以减少错误"""
    try:
        import gradio as gr
        
        # 禁用分析
        if hasattr(gr, 'analytics'):
            gr.analytics.enabled = False
        
        # 设置默认主题
        if hasattr(gr, 'themes'):
            gr.themes.default = gr.themes.Soft()
        
        print("🔧 Gradio修补完成")
        
    except Exception as e:
        print(f"⚠️ Gradio修补失败: {e}")

def main():
    """主函数"""
    print("🚀 启动定额创建工具 - 清洁模式")
    print("=" * 50)
    
    # 设置环境
    setup_environment()
    
    # 创建静态文件
    create_static_files()
    
    # 修补Gradio
    patch_gradio()
    
    print("=" * 50)
    print("✅ 环境准备完成，启动主程序...")
    
    # 导入并启动主程序
    try:
        from main import QuotaExtractionApp
        
        app = QuotaExtractionApp()
        interface = app.create_interface()
        
        print("🌐 启动Web界面...")
        interface.launch(
            server_name="0.0.0.0",
            server_port=7864,
            share=False,
            debug=False,
            show_error=False,
            quiet=True,
            allowed_paths=["output", "output/price_info", "static"],
            favicon_path="static/favicon.ico"
        )
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
