#!/usr/bin/env python3
"""
测试价格类型选择逻辑
Test price type selection logic
"""

import sys
import os

# 添加src目录到路径
sys.path.insert(0, 'src')

def create_test_price_data_with_tax_info():
    """创建包含含税和不含税价格的测试数据"""
    price_data = [
        # 情况1: 同时包含含税和不含税价格（应选择不含税）
        {
            '资源编号': '0101010002-2',
            '产品名称': '热轧光圆钢筋',
            '市场参考价（含税）': '4200.00',
            '市场参考价（不含税）': '3716.81',  # 应该选择这个
            '计量单位': 't'
        },
        
        # 情况2: 只有含税价格（应选择含税价格）
        {
            '资源编号': '0101010003-2',
            '产品名称': '热轧带肋钢筋',
            '市场参考价（含税）': '4350.00',  # 应该选择这个
            '计量单位': 't'
        },
        
        # 情况3: 只有通用价格字段（应选择单价）
        {
            '资源编号': '0201010001-1',
            '产品名称': '普通硅酸盐水泥',
            '单价': '336.28',  # 应该选择这个
            '计量单位': 't'
        },
        
        # 情况4: 多种价格字段，优先级测试
        {
            '资源编号': '0301010001-1',
            '产品名称': '砂子',
            '含税价格': '90.00',
            '市场参考价': '85.00',
            '不含税价格': '79.65',  # 应该选择这个（最高优先级）
            '计量单位': 'm³'
        },
        
        # 情况5: 英文字段名测试
        {
            '资源编号': '0302010001-1',
            '产品名称': '石子',
            'price_with_tax': '95.00',
            'price_no_tax': '84.07',  # 应该选择这个
            'unit': 'm³'
        }
    ]
    return price_data

def test_price_field_selection():
    """测试价格字段选择逻辑"""
    print("🧪 测试价格字段选择逻辑")
    print("=" * 50)
    
    try:
        from advanced_quota_handlers import AdvancedQuotaHandlers
        
        # 创建处理器实例
        handlers = AdvancedQuotaHandlers()
        
        # 创建测试数据
        price_data = create_test_price_data_with_tax_info()
        
        print(f"📊 测试信息价数据: {len(price_data)} 条")
        
        # 模拟价格字段选择逻辑
        for i, price_item in enumerate(price_data, 1):
            print(f"\n📝 测试案例 {i}: {price_item['产品名称']}")
            print(f"   资源编号: {price_item['资源编号']}")
            
            # 显示所有可用的价格字段
            price_fields = []
            for key, value in price_item.items():
                if any(keyword in key.lower() for keyword in ['价格', '价', 'price', '单价']):
                    price_fields.append(f"{key}: {value}")
            
            print(f"   可用价格字段: {', '.join(price_fields)}")
            
            # 模拟选择逻辑
            resource_code = price_item['资源编号']
            price_value = None
            price_type = None
            selected_field = None
            
            # 定义价格字段优先级（不含税价格优先）
            price_fields_priority = [
                # 第一优先级：明确的不含税价格
                ('市场参考价（不含税）', 'no_tax'),
                ('不含税价格', 'no_tax'),
                ('不含税单价', 'no_tax'),
                ('市场价（不含税）', 'no_tax'),
                ('参考价（不含税）', 'no_tax'),
                ('price_no_tax', 'no_tax'),
                ('unit_price_no_tax', 'no_tax'),
                
                # 第二优先级：通用价格字段（可能是不含税）
                ('单价', 'general'),
                ('price', 'general'),
                ('unit_price', 'general'),
                ('市场价', 'general'),
                ('参考价', 'general'),
                
                # 第三优先级：市场参考价（通常是含税）
                ('市场参考价', 'market'),
                ('市场参考价格', 'market'),
                
                # 最后选择：含税价格（作为备选）
                ('市场参考价（含税）', 'with_tax'),
                ('含税价格', 'with_tax'),
                ('含税单价', 'with_tax'),
                ('市场价（含税）', 'with_tax'),
                ('参考价（含税）', 'with_tax'),
                ('price_with_tax', 'with_tax'),
                ('unit_price_with_tax', 'with_tax')
            ]
            
            # 按优先级查找价格字段
            for field_name, field_type in price_fields_priority:
                if field_name in price_item and price_item[field_name]:
                    try:
                        price_str = str(price_item[field_name]).replace(',', '').replace('¥', '').replace('￥', '').strip()
                        if price_str and price_str != '' and price_str.lower() != 'null':
                            price_value = float(price_str)
                            if price_value > 0:
                                price_type = field_type
                                selected_field = field_name
                                break
                    except (ValueError, TypeError):
                        continue
            
            # 显示选择结果
            if price_value:
                type_names = {
                    'no_tax': '不含税价格',
                    'general': '通用价格',
                    'market': '市场参考价',
                    'with_tax': '含税价格'
                }
                type_name = type_names.get(price_type, price_type)
                
                type_icons = {
                    'no_tax': '💰',
                    'general': '💵',
                    'market': '🏪',
                    'with_tax': '🧾'
                }
                type_icon = type_icons.get(price_type, '💵')
                
                print(f"   ✅ 选择结果: {selected_field} = ¥{price_value:.2f}")
                print(f"   {type_icon} 价格类型: {type_name}")
                
                if price_type == 'no_tax':
                    print(f"   🎯 推荐: 已选择不含税价格，符合定额计算标准")
                elif price_type == 'with_tax':
                    print(f"   ⚠️ 注意: 使用含税价格，建议提供不含税价格数据")
                else:
                    print(f"   ℹ️ 信息: 使用{type_name}，请确认是否为不含税价格")
            else:
                print(f"   ❌ 未找到有效的价格字段")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_price_type_statistics():
    """测试价格类型统计功能"""
    print("\n🧪 测试价格类型统计功能")
    print("=" * 50)
    
    try:
        # 模拟匹配结果
        match_results = {
            'updated': [
                {
                    'resource_code': '0101010002-2',
                    'resource_name': '热轧光圆钢筋',
                    'new_price': 3716.81,
                    'price_type': 'no_tax',
                    'price_field': '市场参考价（不含税）'
                },
                {
                    'resource_code': '0101010003-2',
                    'resource_name': '热轧带肋钢筋',
                    'new_price': 4350.00,
                    'price_type': 'with_tax',
                    'price_field': '市场参考价（含税）'
                },
                {
                    'resource_code': '0201010001-1',
                    'resource_name': '普通硅酸盐水泥',
                    'new_price': 336.28,
                    'price_type': 'general',
                    'price_field': '单价'
                }
            ]
        }
        
        # 统计价格类型
        price_type_stats = {}
        for item in match_results['updated']:
            price_type = item.get('price_type', 'unknown')
            price_type_stats[price_type] = price_type_stats.get(price_type, 0) + 1
        
        print("📊 价格类型统计:")
        price_type_names = {
            'no_tax': '不含税价格',
            'general': '通用价格',
            'market': '市场参考价',
            'with_tax': '含税价格',
            'unknown': '未知类型'
        }
        
        for price_type, count in price_type_stats.items():
            type_name = price_type_names.get(price_type, price_type)
            type_icon = {
                'no_tax': '💰',
                'general': '💵',
                'market': '🏪',
                'with_tax': '🧾',
                'unknown': '❓'
            }.get(price_type, '💵')
            
            print(f"   {type_icon} {type_name}: {count} 个资源")
        
        # 生成价格类型说明
        price_type_info = []
        for price_type, count in price_type_stats.items():
            type_name = price_type_names.get(price_type, price_type)
            price_type_info.append(f"{type_name}: {count}个")
        
        price_type_summary = "、".join(price_type_info)
        print(f"\n📋 汇总信息: {price_type_summary}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 价格类型选择逻辑测试")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("价格字段选择", test_price_field_selection),
        ("价格类型统计", test_price_type_statistics),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！价格类型选择逻辑正常。")
        print("💡 主要功能:")
        print("   - ✅ 优先选择不含税价格（💰 推荐用于定额计算）")
        print("   - ✅ 智能识别多种价格字段格式")
        print("   - ✅ 详细的价格类型统计和说明")
        print("   - ✅ 用户友好的价格类型标识")
        print("🌐 现在可以在Web界面中测试完整功能")
    elif passed >= total - 1:
        print("✅ 基本功能正常！可能有个别小问题。")
        print("💡 建议在Web界面中测试实际功能。")
    else:
        print("⚠️ 存在多个问题，需要进一步检查。")

if __name__ == "__main__":
    main()
