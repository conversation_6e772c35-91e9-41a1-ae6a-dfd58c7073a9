#!/usr/bin/env python3
"""
测试完整文件预览功能
"""

import os
import pandas as pd
from datetime import datetime

def create_comprehensive_test_file():
    """创建一个包含更多数据的测试文件"""
    
    print("🧪 创建综合测试文件")
    print("=" * 60)
    
    # 确保output目录存在
    output_dir = "output"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 创建包含更多行的测试数据
    data = {
        '类型': [],
        '编号': [],
        '名称': [],
        '资源编号': [],
        '类别': [],
        '消耗量': [],
        '单位': [],
        '备注': []
    }
    
    # 添加定额项数据
    for i in range(1, 21):  # 20个定额项
        data['类型'].append('定额项')
        data['编号'].append(f'1-{i}')
        data['名称'].append(f'人工挖{i}类土方 深度{i}m以内')
        data['资源编号'].append('')
        data['类别'].append('土石方工程')
        data['消耗量'].append(1.0 + i * 0.1)
        data['单位'].append('100m³')
        data['备注'].append(f'定额项{i}的详细说明，包含施工要求和技术标准')
    
    # 添加资源消耗数据
    resource_types = ['人工', '材料', '机械', '其他']
    for i in range(1, 31):  # 30个资源消耗项
        data['类型'].append('资源消耗')
        data['编号'].append(f'R{i:03d}')
        data['名称'].append(f'资源项目{i}')
        data['资源编号'].append(f'{i:02d}{(i%4)+1:02d}{i:02d}')
        data['类别'].append(resource_types[i % 4])
        data['消耗量'].append(round(50 + i * 2.5, 2))
        data['单位'].append(['工日', '元', '台班', 'kg'][i % 4])
        data['备注'].append(f'资源{i}的使用说明，包含规格型号和质量要求等详细信息')
    
    # 创建DataFrame
    df = pd.DataFrame(data)
    
    # 保存文件
    file_path = os.path.join(output_dir, "comprehensive_test_data.csv")
    df.to_csv(file_path, index=False, encoding='utf-8-sig')
    
    print(f"✅ 创建综合测试文件: {file_path}")
    print(f"- 总行数: {len(df)} 行")
    print(f"- 列数: {len(df.columns)} 列")
    print(f"- 文件大小: {os.path.getsize(file_path) / 1024:.2f} KB")
    
    return file_path

def test_full_preview_functionality():
    """测试完整预览功能"""
    
    print(f"\n" + "=" * 60)
    print("🧪 测试完整预览功能")
    print("=" * 60)
    
    # 创建测试文件
    test_file = create_comprehensive_test_file()
    
    try:
        # 读取文件
        df = pd.read_csv(test_file, encoding='utf-8-sig')
        
        print(f"📄 测试文件信息:")
        print(f"- 文件名: {os.path.basename(test_file)}")
        print(f"- 总行数: {len(df)} 行")
        print(f"- 列数: {len(df.columns)} 列")
        print(f"- 列名: {', '.join(df.columns.tolist())}")
        
        # 模拟完整预览HTML生成
        filename = os.path.basename(test_file)
        file_size = os.path.getsize(test_file)
        
        preview_html = f"""
        <div style="border: 1px solid #ddd; border-radius: 8px; padding: 16px; background: #f9f9f9;">
            <h3 style="margin-top: 0; color: #333;">📄 {filename}</h3>
            <div style="margin-bottom: 16px; padding: 12px; background: #e8f4fd; border-radius: 6px;">
                <p style="margin: 4px 0;"><strong>📊 文件统计:</strong></p>
                <p style="margin: 4px 0;">• 总行数: {len(df)} 行</p>
                <p style="margin: 4px 0;">• 列数: {len(df.columns)} 列</p>
                <p style="margin: 4px 0;">• 文件大小: {file_size / 1024:.2f} KB</p>
                <p style="margin: 4px 0;">• 列名: {', '.join(df.columns.tolist())}</p>
            </div>
            
            <h4 style="color: #333; margin-bottom: 12px;">📋 完整文件内容:</h4>
            <div style="overflow: auto; max-height: 600px; border: 1px solid #ccc; border-radius: 4px; background: white;">
                {df.to_html(index=True, classes='preview-table', table_id='full-preview-table', escape=False)}
            </div>
            
            <div style="margin-top: 12px; padding: 8px; background: #f0f8ff; border-radius: 4px; font-size: 12px; color: #666;">
                💡 提示: 表格支持滚动查看，显示完整的 {len(df)} 行数据
            </div>
        </div>
        """
        
        print(f"\n📋 预览HTML生成:")
        print(f"- HTML长度: {len(preview_html)} 字符")
        print(f"- 包含完整表格: {'✅' if 'to_html' in preview_html else '❌'}")
        print(f"- 包含统计信息: {'✅' if '文件统计' in preview_html else '❌'}")
        print(f"- 包含滚动提示: {'✅' if '滚动查看' in preview_html else '❌'}")
        
        # 验证数据完整性
        print(f"\n🔍 数据完整性验证:")
        print(f"- 定额项数量: {len(df[df['类型'] == '定额项'])} 个")
        print(f"- 资源消耗数量: {len(df[df['类型'] == '资源消耗'])} 个")
        print(f"- 数据类型分布:")
        for dtype in df.dtypes.unique():
            cols = df.select_dtypes(include=[dtype]).columns.tolist()
            print(f"  • {dtype}: {len(cols)} 列 ({', '.join(cols)})")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_large_file_handling():
    """测试大文件处理能力"""
    
    print(f"\n" + "=" * 60)
    print("🧪 测试大文件处理能力")
    print("=" * 60)
    
    output_dir = "output"
    
    # 创建更大的测试文件
    large_data = {
        '序号': list(range(1, 501)),  # 500行数据
        '定额编号': [f'D{i:04d}' for i in range(1, 501)],
        '定额名称': [f'定额项目{i} - 详细描述包含施工工艺和技术要求' for i in range(1, 501)],
        '工程类别': ['土石方工程', '砌筑工程', '混凝土工程', '钢筋工程', '模板工程'] * 100,
        '计量单位': ['m³', 'm²', 'm', 'kg', 't'] * 100,
        '基价': [round(100 + i * 1.5, 2) for i in range(1, 501)],
        '人工费': [round(30 + i * 0.5, 2) for i in range(1, 501)],
        '材料费': [round(50 + i * 0.8, 2) for i in range(1, 501)],
        '机械费': [round(20 + i * 0.3, 2) for i in range(1, 501)],
        '备注说明': [f'第{i}项定额的详细说明，包含适用范围、施工要求、质量标准等内容' for i in range(1, 501)]
    }
    
    large_df = pd.DataFrame(large_data)
    large_file_path = os.path.join(output_dir, "large_test_data.csv")
    large_df.to_csv(large_file_path, index=False, encoding='utf-8-sig')
    
    print(f"📄 大文件测试:")
    print(f"- 文件名: {os.path.basename(large_file_path)}")
    print(f"- 行数: {len(large_df)} 行")
    print(f"- 列数: {len(large_df.columns)} 列")
    print(f"- 文件大小: {os.path.getsize(large_file_path) / 1024:.2f} KB")
    
    # 测试读取性能
    import time
    start_time = time.time()
    df_loaded = pd.read_csv(large_file_path, encoding='utf-8-sig')
    load_time = time.time() - start_time
    
    print(f"- 读取时间: {load_time:.3f} 秒")
    print(f"- 读取性能: {'✅ 良好' if load_time < 1.0 else '⚠️ 较慢' if load_time < 3.0 else '❌ 慢'}")
    
    # 测试HTML生成性能
    start_time = time.time()
    html_content = df_loaded.to_html(index=True, escape=False)
    html_time = time.time() - start_time
    
    print(f"- HTML生成时间: {html_time:.3f} 秒")
    print(f"- HTML长度: {len(html_content)} 字符")
    print(f"- HTML性能: {'✅ 良好' if html_time < 2.0 else '⚠️ 较慢' if html_time < 5.0 else '❌ 慢'}")
    
    return load_time < 3.0 and html_time < 5.0

def test_different_file_types():
    """测试不同类型的CSV文件"""
    
    print(f"\n" + "=" * 60)
    print("🧪 测试不同类型的CSV文件")
    print("=" * 60)
    
    output_dir = "output"
    test_files = []
    
    # 测试文件类型1: 包含中文和特殊字符
    chinese_data = {
        '项目编号': ['ZH001', 'ZH002', 'ZH003'],
        '项目名称': ['人工挖土方（一、二类土）', '机械挖土方（三、四类土）', '土方回填（分层夯实）'],
        '计量单位': ['100m³', '100m³', '100m³'],
        '综合单价': [1250.50, 1580.75, 980.25],
        '备注': ['适用于一般土质，含水率≤25%', '适用于较硬土质，需要机械开挖', '回填土应分层夯实，每层厚度≤30cm']
    }
    chinese_df = pd.DataFrame(chinese_data)
    chinese_file = os.path.join(output_dir, "chinese_test_data.csv")
    chinese_df.to_csv(chinese_file, index=False, encoding='utf-8-sig')
    test_files.append(chinese_file)
    
    # 测试文件类型2: 包含数值和空值
    numeric_data = {
        'ID': [1, 2, 3, 4, 5],
        '数量': [100.5, 200.0, None, 150.75, 300.25],
        '单价': [25.50, 30.00, 28.75, None, 35.00],
        '金额': [2562.75, 6000.00, None, None, 10508.75],
        '状态': ['完成', '进行中', '暂停', '完成', None]
    }
    numeric_df = pd.DataFrame(numeric_data)
    numeric_file = os.path.join(output_dir, "numeric_test_data.csv")
    numeric_df.to_csv(numeric_file, index=False, encoding='utf-8-sig')
    test_files.append(numeric_file)
    
    # 测试文件类型3: 长文本内容
    long_text_data = {
        '编号': ['LT001', 'LT002'],
        '标题': ['长文本测试项目1', '长文本测试项目2'],
        '详细描述': [
            '这是一个包含很长文本内容的测试项目，用于验证系统对长文本的处理能力。内容包括：1）项目背景介绍；2）技术要求说明；3）质量标准规定；4）安全注意事项；5）验收标准等多个方面的详细信息。',
            '另一个长文本测试项目，主要用于测试表格显示时的文本换行和截断功能。这个描述故意写得很长，包含了大量的技术细节和操作说明，以便测试在有限的显示空间内如何合理地展示这些信息。'
        ]
    }
    long_text_df = pd.DataFrame(long_text_data)
    long_text_file = os.path.join(output_dir, "long_text_test_data.csv")
    long_text_df.to_csv(long_text_file, index=False, encoding='utf-8-sig')
    test_files.append(long_text_file)
    
    print(f"📋 创建了 {len(test_files)} 个不同类型的测试文件:")
    
    success_count = 0
    for file_path in test_files:
        try:
            df = pd.read_csv(file_path, encoding='utf-8-sig')
            filename = os.path.basename(file_path)
            
            print(f"\n✅ {filename}:")
            print(f"  - 行数: {len(df)} 行")
            print(f"  - 列数: {len(df.columns)} 列")
            print(f"  - 文件大小: {os.path.getsize(file_path) / 1024:.2f} KB")
            print(f"  - 数据类型: {', '.join([str(dtype) for dtype in df.dtypes.unique()])}")
            
            success_count += 1
            
        except Exception as e:
            print(f"❌ {os.path.basename(file_path)}: 处理失败 - {e}")
    
    return success_count == len(test_files)

if __name__ == "__main__":
    print("🚀 开始测试完整文件预览功能")
    print("=" * 80)
    
    # 测试完整预览功能
    preview_success = test_full_preview_functionality()
    
    # 测试大文件处理
    large_file_success = test_large_file_handling()
    
    # 测试不同文件类型
    different_types_success = test_different_file_types()
    
    print("\n" + "=" * 80)
    print("🎯 测试结果总结:")
    print(f"- 完整预览功能: {'✅ 正常' if preview_success else '❌ 异常'}")
    print(f"- 大文件处理: {'✅ 正常' if large_file_success else '❌ 异常'}")
    print(f"- 不同文件类型: {'✅ 正常' if different_types_success else '❌ 异常'}")
    
    all_success = all([preview_success, large_file_success, different_types_success])
    
    if all_success:
        print("🎉 完整文件预览功能测试全部通过！")
        print("\n📋 功能特性:")
        print("- ✅ 显示完整文件内容（不限制行数）")
        print("- ✅ 详细文件统计信息")
        print("- ✅ 优化的表格显示样式")
        print("- ✅ 支持大文件处理（500行+）")
        print("- ✅ 支持中文和特殊字符")
        print("- ✅ 支持数值和空值处理")
        print("- ✅ 支持长文本内容显示")
        print("- ✅ 响应式滚动查看")
    else:
        print("⚠️ 部分功能需要进一步检查。")
