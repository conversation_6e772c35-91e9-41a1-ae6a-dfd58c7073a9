#!/usr/bin/env python3
"""
测试智能定额数据处理器
Test Intelligent Quota Data Processor
"""

import os
import sys
from pathlib import Path

# 添加src目录到路径
sys.path.append('src')

def test_intelligent_processor():
    """测试智能定额处理器"""
    print("🧪 开始测试智能定额数据处理器")
    print("=" * 60)
    
    try:
        # 尝试不同的导入方式
        try:
            from src.intelligent_quota_processor import IntelligentQuotaProcessor
        except ImportError:
            from intelligent_quota_processor import IntelligentQuotaProcessor
        print("✅ 智能定额处理器导入成功")
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    
    # 检查测试文件
    print("\n📋 检查测试文件...")
    output_dir = Path("output")
    
    # 查找parent_quotas和child_resources文件
    parent_files = list(output_dir.glob("parent_quotas*.csv"))
    child_files = list(output_dir.glob("child_resources*.csv"))
    
    if not parent_files:
        print("❌ 未找到parent_quotas*.csv文件")
        return False
    if not child_files:
        print("❌ 未找到child_resources*.csv文件")
        return False
    
    # 选择测试文件（取前3个）
    test_files = [str(f) for f in parent_files[:3]] + [str(f) for f in child_files[:3]]
    
    print(f"✅ 找到测试文件:")
    print(f"   - 定额项文件: {len(parent_files[:3])} 个")
    print(f"   - 资源文件: {len(child_files[:3])} 个")
    print(f"   - 总计: {len(test_files)} 个文件")
    
    # 创建处理器并测试
    print("\n🧠 开始智能处理...")
    processor = IntelligentQuotaProcessor()
    
    success, message, stats = processor.process_quota_files(test_files)
    
    if success:
        print("✅ 智能处理成功!")
        print(f"📋 处理结果:")
        print(message)
        
        print(f"\n📊 详细统计:")
        classification = stats.get('classification', {})
        print(f"   - 分类结果:")
        print(f"     • 定额项文件: {len(classification.get('parent_files', []))} 个")
        print(f"     • 资源文件: {len(classification.get('child_files', []))} 个")
        print(f"     • 未知文件: {len(classification.get('unknown_files', []))} 个")
        
        merge_results = stats.get('merge_results', {})
        print(f"   - 合并结果:")
        print(f"     • 定额项记录: {merge_results.get('parent_rows', 0)} 条")
        print(f"     • 资源记录: {merge_results.get('child_rows', 0)} 条")
        
        association_results = stats.get('association_results', {})
        print(f"   - 关联结果:")
        print(f"     • 关联的定额项: {association_results.get('associated_quotas', 0)} 个")
        print(f"     • 计算价格的定额项: {association_results.get('calculated_quotas', 0)} 个")
        
        final_files = stats.get('final_files', [])
        print(f"   - 输出文件: {len(final_files)} 个")
        for file_path in final_files:
            if os.path.exists(file_path):
                size_kb = os.path.getsize(file_path) / 1024
                print(f"     • {os.path.basename(file_path)} ({size_kb:.1f} KB)")
        
        return True
        
    else:
        print(f"❌ 智能处理失败: {message}")
        return False

def test_with_enterprise_quota_manager():
    """测试与企业定额管理器的集成"""
    print("\n" + "=" * 60)
    print("🧪 测试与企业定额管理器的集成")
    print("=" * 60)
    
    try:
        # 尝试不同的导入方式
        try:
            from src.enterprise_quota_manager import EnterpriseQuotaManager
        except ImportError:
            from enterprise_quota_manager import EnterpriseQuotaManager
        print("✅ 企业定额管理器导入成功")
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    
    # 获取测试文件
    output_dir = Path("output")
    parent_files = list(output_dir.glob("parent_quotas*.csv"))[:2]
    child_files = list(output_dir.glob("child_resources*.csv"))[:2]
    parent_files = [str(f) for f in parent_files]
    child_files = [str(f) for f in child_files]
    
    if not parent_files or not child_files:
        print("❌ 测试文件不足")
        return False
    
    print(f"📋 使用测试文件:")
    print(f"   - 定额项文件: {len(parent_files)} 个")
    print(f"   - 资源文件: {len(child_files)} 个")
    
    # 测试SQLite数据库创建
    print("\n📱 测试智能SQLite数据库创建...")
    manager = EnterpriseQuotaManager()
    
    db_config = {
        'database_path': 'output/intelligent_test_quota.db'
    }
    
    success, message = manager.create_quota_database(
        'sqlite', db_config, parent_files, child_files
    )
    
    if success:
        print("✅ 智能SQLite数据库创建成功!")
        print(f"📋 结果消息:")
        print(message)
        
        # 检查生成的数据库文件
        db_path = db_config['database_path']
        if os.path.exists(db_path):
            size_kb = os.path.getsize(db_path) / 1024
            print(f"\n📄 数据库文件: {db_path} ({size_kb:.1f} KB)")
        
        return True
    else:
        print(f"❌ 智能SQLite数据库创建失败: {message}")
        return False

def main():
    """主函数"""
    print("🚀 智能定额数据处理器测试套件")
    print("=" * 60)
    
    # 测试1: 基础智能处理功能
    test1_success = test_intelligent_processor()
    
    # 测试2: 与企业定额管理器集成
    test2_success = test_with_enterprise_quota_manager()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    print(f"🧠 智能处理器测试: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"🔗 集成测试: {'✅ 通过' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print("\n🎉 所有测试通过！智能定额数据处理器工作正常")
        print("\n💡 功能特性:")
        print("   ✅ 智能文件分类和合并")
        print("   ✅ 定额项与资源关联")
        print("   ✅ 自动价格计算")
        print("   ✅ 与MCP工具无缝集成")
        print("   ✅ 支持跨区文件选择")
        return True
    else:
        print("\n❌ 部分测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    main()
