#!/usr/bin/env python3
"""
调试实际的定额和信息价识别流程
Debug actual quota and price info processing flow
"""

import sys
import os
import json
import asyncio
from PIL import Image, ImageDraw

# 添加src目录到路径
sys.path.insert(0, 'src')

def create_test_quota_image():
    """创建测试用的定额表格图片"""
    # 创建一个模拟的定额表格图片
    img = Image.new('RGB', (800, 600), color='white')
    draw = ImageDraw.Draw(img)
    
    # 绘制表格框架
    draw.rectangle([50, 50, 750, 550], outline='black', width=2)
    
    # 绘制表格内容
    try:
        # 表头
        draw.text((60, 60), "北京市建设工程消耗定额", fill='black')
        draw.text((60, 90), "定额编号    项目名称    单位    人工费    材料费    机械费", fill='black')
        
        # 表格数据
        draw.text((60, 120), "1-1        土方开挖    m³     25.50    15.30    8.20", fill='black')
        draw.text((60, 150), "1-2        土方回填    m³     18.20    12.40    6.50", fill='black')
        draw.text((60, 180), "1-3        基础开挖    m³     32.10    20.60    12.30", fill='black')
        
    except Exception as e:
        print(f"绘制文字失败: {e}")
        # 如果字体问题，使用简单的标记
        draw.text((60, 60), "Quota Table Test", fill='black')
        draw.text((60, 120), "1-1  Item1  m3  25.50  15.30  8.20", fill='black')
        draw.text((60, 150), "1-2  Item2  m3  18.20  12.40  6.50", fill='black')
    
    test_image_path = "test_quota_image.png"
    img.save(test_image_path)
    return test_image_path

def create_test_price_info_image():
    """创建测试用的信息价图片"""
    # 创建一个模拟的信息价表格图片
    img = Image.new('RGB', (800, 600), color='white')
    draw = ImageDraw.Draw(img)
    
    # 绘制表格框架
    draw.rectangle([50, 50, 750, 550], outline='black', width=2)
    
    # 绘制表格内容
    try:
        # 表头
        draw.text((60, 60), "北京市工程造价信息 (2025年6月)", fill='black')
        draw.text((60, 90), "资源编号        产品名称        单位    含税价格    不含税价格", fill='black')
        
        # 表格数据
        draw.text((60, 120), "0101010002-2   热轧光圆钢筋    t      4200.00    3716.81", fill='black')
        draw.text((60, 150), "0101010003-2   热轧带肋钢筋    t      4350.00    3849.56", fill='black')
        draw.text((60, 180), "0201010001-1   普通硅酸盐水泥  t       380.00     336.28", fill='black')
        
    except Exception as e:
        print(f"绘制文字失败: {e}")
        # 如果字体问题，使用简单的标记
        draw.text((60, 60), "Price Info Test", fill='black')
        draw.text((60, 120), "0101010002-2  Steel  t  4200.00  3716.81", fill='black')
        draw.text((60, 150), "0101010003-2  Rebar  t  4350.00  3849.56", fill='black')
    
    test_image_path = "test_price_info_image.png"
    img.save(test_image_path)
    return test_image_path

async def test_quota_processing():
    """测试定额识别处理"""
    print("🧪 测试定额识别处理")
    print("=" * 50)
    
    try:
        from ai_model_processor import AIModelProcessor
        
        # 创建处理器实例
        processor = AIModelProcessor()
        
        # 创建测试图片
        test_image = create_test_quota_image()
        print(f"📷 创建测试定额图片: {test_image}")
        
        # 测试不同的LM Studio模型
        test_models = [
            ("nanonets-ocr-s", "nanonets-ocr-s"),
            ("google/gemma-3-27b", "google/gemma-3-27b"),
            ("qwen/qwen2.5-vl-7b", "qwen/qwen2.5-vl-7b")
        ]
        
        results = {}
        
        for model_name, model_id in test_models:
            print(f"\n📝 测试模型: {model_name}")
            print("-" * 30)
            
            try:
                # 调用实际的处理方法
                result = await processor.process_image_with_lm_studio(
                    test_image, 
                    model_id, 
                    volume_code="01", 
                    chapter_codes="01,02"
                )
                
                if result:
                    print(f"✅ 模型 {model_name} 处理成功")
                    print(f"📄 结果长度: {len(result)} 字符")
                    print(f"📋 结果预览: {result[:200]}...")
                    
                    # 尝试解析JSON
                    try:
                        if '{' in result and '}' in result:
                            # 提取JSON部分
                            start = result.find('{')
                            end = result.rfind('}') + 1
                            json_part = result[start:end]
                            parsed = json.loads(json_part)
                            print(f"✅ JSON解析成功")
                            print(f"📊 JSON键: {list(parsed.keys())}")
                            
                            # 检查是否包含定额数据
                            if '定额数据' in parsed:
                                quota_data = parsed['定额数据']
                                print(f"📋 定额数据条数: {len(quota_data)}")
                                if quota_data:
                                    first_item = quota_data[0]
                                    print(f"📝 第一条数据键: {list(first_item.keys())}")
                            
                            results[model_name] = {
                                'success': True,
                                'raw_result': result,
                                'parsed_json': parsed,
                                'error': None
                            }
                        else:
                            print(f"⚠️ 结果不包含JSON格式")
                            results[model_name] = {
                                'success': False,
                                'raw_result': result,
                                'parsed_json': None,
                                'error': 'No JSON format found'
                            }
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON解析失败: {e}")
                        results[model_name] = {
                            'success': False,
                            'raw_result': result,
                            'parsed_json': None,
                            'error': f'JSON parse error: {e}'
                        }
                else:
                    print(f"❌ 模型 {model_name} 处理失败: 返回None")
                    results[model_name] = {
                        'success': False,
                        'raw_result': None,
                        'parsed_json': None,
                        'error': 'Returned None'
                    }
                    
            except Exception as e:
                print(f"❌ 模型 {model_name} 处理异常: {str(e)}")
                results[model_name] = {
                    'success': False,
                    'raw_result': None,
                    'parsed_json': None,
                    'error': str(e)
                }
        
        # 清理测试文件
        if os.path.exists(test_image):
            os.remove(test_image)
        
        return results
        
    except Exception as e:
        print(f"❌ 定额识别测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return {}

async def test_price_info_processing():
    """测试信息价识别处理"""
    print("\n🧪 测试信息价识别处理")
    print("=" * 50)
    
    try:
        from intelligent_price_info_processor import IntelligentPriceInfoProcessor
        
        # 创建处理器实例
        processor = IntelligentPriceInfoProcessor()
        
        # 创建测试图片
        test_image = create_test_price_info_image()
        print(f"📷 创建测试信息价图片: {test_image}")
        
        # 测试不同的LM Studio模型
        test_models = [
            ("nanonets-ocr-s", "nanonets-ocr-s"),
            ("google/gemma-3-27b", "google/gemma-3-27b"),
            ("qwen/qwen2.5-vl-7b", "qwen/qwen2.5-vl-7b")
        ]
        
        results = {}
        
        for model_name, model_id in test_models:
            print(f"\n📝 测试模型: {model_name}")
            print("-" * 30)
            
            try:
                # 调用实际的处理方法
                result = await processor._process_with_lm_studio(test_image, model_id)
                
                if result:
                    print(f"✅ 模型 {model_name} 处理成功")
                    print(f"📄 结果长度: {len(result)} 字符")
                    print(f"📋 结果预览: {result[:200]}...")
                    
                    # 尝试解析JSON
                    try:
                        if '{' in result and '}' in result:
                            # 提取JSON部分
                            start = result.find('{')
                            end = result.rfind('}') + 1
                            json_part = result[start:end]
                            parsed = json.loads(json_part)
                            print(f"✅ JSON解析成功")
                            print(f"📊 JSON键: {list(parsed.keys())}")
                            
                            # 检查是否包含信息价数据
                            if '信息价数据' in parsed:
                                price_data = parsed['信息价数据']
                                print(f"📋 信息价数据条数: {len(price_data)}")
                                if price_data:
                                    first_item = price_data[0]
                                    print(f"📝 第一条数据键: {list(first_item.keys())}")
                            
                            results[model_name] = {
                                'success': True,
                                'raw_result': result,
                                'parsed_json': parsed,
                                'error': None
                            }
                        else:
                            print(f"⚠️ 结果不包含JSON格式")
                            results[model_name] = {
                                'success': False,
                                'raw_result': result,
                                'parsed_json': None,
                                'error': 'No JSON format found'
                            }
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON解析失败: {e}")
                        results[model_name] = {
                            'success': False,
                            'raw_result': result,
                            'parsed_json': None,
                            'error': f'JSON parse error: {e}'
                        }
                else:
                    print(f"❌ 模型 {model_name} 处理失败: 返回None")
                    results[model_name] = {
                        'success': False,
                        'raw_result': None,
                        'parsed_json': None,
                        'error': 'Returned None'
                    }
                    
            except Exception as e:
                print(f"❌ 模型 {model_name} 处理异常: {str(e)}")
                results[model_name] = {
                    'success': False,
                    'raw_result': None,
                    'parsed_json': None,
                    'error': str(e)
                }
        
        # 清理测试文件
        if os.path.exists(test_image):
            os.remove(test_image)
        
        return results
        
    except Exception as e:
        print(f"❌ 信息价识别测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return {}

def analyze_results(quota_results, price_results):
    """分析测试结果"""
    print(f"\n{'='*60}")
    print("📊 测试结果分析")
    print("=" * 60)
    
    print("\n🔍 定额识别结果:")
    for model_name, result in quota_results.items():
        status = "✅ 成功" if result['success'] else "❌ 失败"
        print(f"   {model_name}: {status}")
        if not result['success']:
            print(f"     错误: {result['error']}")
    
    print("\n🔍 信息价识别结果:")
    for model_name, result in price_results.items():
        status = "✅ 成功" if result['success'] else "❌ 失败"
        print(f"   {model_name}: {status}")
        if not result['success']:
            print(f"     错误: {result['error']}")
    
    # 分析常见问题
    print(f"\n💡 问题分析:")
    
    all_results = {**quota_results, **price_results}
    
    # 检查是否有模型返回None
    none_results = [name for name, result in all_results.items() if result['raw_result'] is None]
    if none_results:
        print(f"   ⚠️ 返回None的模型: {', '.join(none_results)}")
        print(f"     可能原因: 模型调用失败、超时或API错误")
    
    # 检查是否有JSON解析问题
    json_errors = [name for name, result in all_results.items() if result['error'] and 'JSON' in result['error']]
    if json_errors:
        print(f"   ⚠️ JSON解析失败的模型: {', '.join(json_errors)}")
        print(f"     可能原因: 模型输出格式不符合预期")
    
    # 检查是否有格式问题
    format_errors = [name for name, result in all_results.items() if result['error'] == 'No JSON format found']
    if format_errors:
        print(f"   ⚠️ 输出格式异常的模型: {', '.join(format_errors)}")
        print(f"     可能原因: 模型没有按JSON格式输出")
    
    # 成功的模型
    successful_models = [name for name, result in all_results.items() if result['success']]
    if successful_models:
        print(f"   ✅ 工作正常的模型: {', '.join(successful_models)}")
    
    print(f"\n🔧 建议解决方案:")
    if none_results:
        print(f"   1. 检查LM Studio模型是否正确加载")
        print(f"   2. 增加API调用的超时时间")
        print(f"   3. 检查网络连接和端口设置")
    
    if json_errors or format_errors:
        print(f"   4. 优化提示词，确保模型输出JSON格式")
        print(f"   5. 添加输出格式验证和修复逻辑")
        print(f"   6. 考虑使用不同的提示词策略")

async def main():
    """主测试函数"""
    print("🚀 实际处理流程调试")
    print("=" * 60)
    
    try:
        # 测试定额识别
        quota_results = await test_quota_processing()
        
        # 测试信息价识别
        price_results = await test_price_info_processing()
        
        # 分析结果
        analyze_results(quota_results, price_results)
        
    except Exception as e:
        print(f"❌ 主测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
