#!/usr/bin/env python3
"""
错误抑制脚本 - 解决Gradio常见错误
Error suppression script - Fix common Gradio errors
"""

import os
import sys
import warnings
import logging

def setup_error_suppression():
    """设置错误抑制"""
    
    # 禁用所有警告
    warnings.filterwarnings("ignore")
    
    # 设置日志级别
    logging.getLogger().setLevel(logging.ERROR)
    
    # 禁用特定的日志记录器
    loggers_to_disable = [
        'gradio',
        'gradio.routes',
        'gradio.utils',
        'uvicorn',
        'uvicorn.access',
        'httpx',
        'httpcore',
        'asyncio'
    ]
    
    for logger_name in loggers_to_disable:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.ERROR)
        logger.disabled = True
    
    print("🔇 错误抑制设置完成")

def create_missing_files():
    """创建缺失的文件以避免404错误"""
    
    files_to_create = [
        'static/manifest.json',
        'static/favicon.ico',
        'static/fonts/ui-sans-serif/ui-sans-serif-Regular.woff2',
        'static/fonts/system-ui/system-ui-Regular.woff2'
    ]
    
    # 创建目录
    directories = [
        'static',
        'static/fonts',
        'static/fonts/ui-sans-serif',
        'static/fonts/system-ui'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    # 创建文件
    for file_path in files_to_create:
        if not os.path.exists(file_path):
            if file_path.endswith('.json'):
                # 创建manifest.json
                manifest_content = {
                    "name": "定额创建工具",
                    "short_name": "定额工具",
                    "start_url": "/",
                    "display": "standalone",
                    "background_color": "#ffffff",
                    "theme_color": "#667eea"
                }
                import json
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(manifest_content, f, ensure_ascii=False, indent=2)
            else:
                # 创建空文件
                with open(file_path, 'wb') as f:
                    f.write(b'')
    
    print("📁 缺失文件创建完成")

def patch_gradio_errors():
    """修补Gradio错误"""
    
    try:
        import gradio as gr
        
        # 禁用分析
        if hasattr(gr, 'analytics'):
            gr.analytics.enabled = False
        
        # 修补可能的错误源
        original_print = print
        def silent_print(*args, **kwargs):
            # 过滤掉特定的错误信息
            message = ' '.join(str(arg) for arg in args)
            if any(error_text in message.lower() for error_text in [
                'failed to load resource',
                'net::err_aborted',
                'manifest.json',
                'woff2',
                'postmessage',
                'huggingface.co'
            ]):
                return  # 不打印这些错误
            original_print(*args, **kwargs)
        
        # 不要替换print，因为会影响正常输出
        # print = silent_print
        
        print("🔧 Gradio错误修补完成")
        
    except Exception as e:
        print(f"⚠️ Gradio修补失败: {e}")

def main():
    """主函数"""
    print("🛠️ 启动错误抑制脚本")
    print("=" * 40)
    
    setup_error_suppression()
    create_missing_files()
    patch_gradio_errors()
    
    print("=" * 40)
    print("✅ 错误抑制设置完成")

if __name__ == "__main__":
    main()
