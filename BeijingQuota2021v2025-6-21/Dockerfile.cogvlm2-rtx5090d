# CogVLM2 Docker容器 - 专为RTX 5090 D优化
FROM nvidia/cuda:12.4-devel-ubuntu22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV CUDA_HOME=/usr/local/cuda
ENV PATH=${CUDA_HOME}/bin:${PATH}
ENV LD_LIBRARY_PATH=${CUDA_HOME}/lib64:${LD_LIBRARY_PATH}

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    git \
    wget \
    curl \
    build-essential \
    cmake \
    ninja-build \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*

# 升级pip
RUN python3 -m pip install --upgrade pip

# 安装支持RTX 5090 D的PyTorch nightly版本
RUN pip3 install --pre torch torchvision torchaudio \
    --index-url https://download.pytorch.org/whl/nightly/cu124

# 安装兼容的xformers版本
RUN pip3 install xformers==0.0.32.dev1060

# 安装其他必要依赖
RUN pip3 install \
    transformers>=4.37.0 \
    accelerate \
    bitsandbytes \
    sentencepiece \
    protobuf \
    pillow \
    requests \
    flask \
    psutil \
    modelscope

# 创建模型目录
RUN mkdir -p /app/models /app/cache

# 复制服务文件
COPY docker_cogvlm2_service.py /app/
COPY requirements_docker.txt /app/

# 安装额外依赖
RUN pip3 install -r /app/requirements_docker.txt

# 设置模型缓存目录
ENV HF_HOME=/app/cache
ENV TRANSFORMERS_CACHE=/app/cache
ENV MODELSCOPE_CACHE=/app/cache

# 暴露端口
EXPOSE 8765

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=300s --retries=3 \
    CMD curl -f http://localhost:8765/health || exit 1

# 启动命令
CMD ["python3", "/app/docker_cogvlm2_service.py"]
