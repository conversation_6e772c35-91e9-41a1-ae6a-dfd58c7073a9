#!/usr/bin/env python3
"""
测试信息价识别解析功能
验证改进后的解析逻辑是否能正确处理各种格式的AI返回结果
"""

import os
import sys
import json

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_json_parsing():
    """测试JSON解析功能"""
    print("🧪 测试JSON解析功能")
    print("=" * 50)
    
    try:
        from src.price_info_processor import PriceInfoProcessor
        processor = PriceInfoProcessor()
        
        # 测试用例1: 标准JSON格式
        test_case_1 = '''
        {
            "page_header": "工程造价信息",
            "chapters": [
                {
                    "chapter_code": "01",
                    "chapter_name": "黑色及有色金属",
                    "remarks": "",
                    "price_items": [
                        {
                            "resource_code": "3001010401",
                            "product_name": "网络球型摄像机",
                            "specifications": "1/3英寸CMOS，200万像素",
                            "unit": "台",
                            "price_with_tax": "1200.00",
                            "price_without_tax": "1061.95"
                        }
                    ]
                }
            ]
        }
        '''
        
        print("测试用例1: 标准JSON格式")
        result_1 = processor._parse_price_info_result(test_case_1, 1)
        if result_1:
            print(f"✅ 解析成功，章节数: {len(result_1)}")
            print(f"   价格条目数: {len(result_1[0]['price_items'])}")
        else:
            print("❌ 解析失败")
        
        # 测试用例2: 带额外文字的JSON
        test_case_2 = '''
        根据图片分析，我识别到以下信息：
        
        {
            "page_header": "市场参考价",
            "chapters": [
                {
                    "chapter_code": "02",
                    "chapter_name": "建筑材料",
                    "remarks": "价格仅供参考",
                    "price_items": [
                        {
                            "resource_code": "2001020101",
                            "product_name": "水泥",
                            "specifications": "P.O 42.5，袋装",
                            "unit": "t",
                            "price_with_tax": "450.00",
                            "price_without_tax": "398.23"
                        }
                    ]
                }
            ]
        }
        
        以上是我的分析结果。
        '''
        
        print("\n测试用例2: 带额外文字的JSON")
        result_2 = processor._parse_price_info_result(test_case_2, 2)
        if result_2:
            print(f"✅ 解析成功，章节数: {len(result_2)}")
            print(f"   价格条目数: {len(result_2[0]['price_items'])}")
        else:
            print("❌ 解析失败")
        
        # 测试用例3: 纯文本格式
        test_case_3 = '''
        工程造价信息
        
        1. 黑色及有色金属（编码：01）
        
        3001010401 网络球型摄像机 1/3英寸CMOS，200万像素 台 1200.00 1061.95
        3001010402 网络枪型摄像机 1/3英寸CMOS，400万像素 台 1800.00 1592.92
        '''
        
        print("\n测试用例3: 纯文本格式")
        result_3 = processor._parse_price_info_result(test_case_3, 3)
        if result_3:
            print(f"✅ 解析成功，章节数: {len(result_3)}")
            print(f"   价格条目数: {len(result_3[0]['price_items'])}")
        else:
            print("❌ 解析失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_text_parsing():
    """测试文本解析功能"""
    print("\n🧪 测试文本解析功能")
    print("=" * 50)
    
    try:
        from src.price_info_processor import PriceInfoProcessor
        processor = PriceInfoProcessor()
        
        # 测试文本行解析
        test_lines = [
            "3001010401 网络球型摄像机 1/3英寸CMOS，200万像素 台 1200.00 1061.95",
            "2001020101 水泥 P.O 42.5，袋装 t 450.00 398.23",
            "4001030201 挖掘机 斗容量1.0m³，功率120kW 台班 800.00 707.96"
        ]
        
        for i, line in enumerate(test_lines, 1):
            print(f"测试行 {i}: {line}")
            item = processor._extract_price_item_from_line(line)
            if item:
                print(f"✅ 解析成功:")
                print(f"   资源编号: {item['resource_code']}")
                print(f"   产品名称: {item['product_name']}")
                print(f"   单位: {item['unit']}")
                print(f"   含税价: {item['price_with_tax']}")
            else:
                print("❌ 解析失败")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_csv_generation():
    """测试CSV生成功能"""
    print("\n🧪 测试CSV生成功能")
    print("=" * 50)
    
    try:
        from src.price_info_processor import PriceInfoProcessor
        import tempfile
        import pandas as pd
        
        processor = PriceInfoProcessor()
        
        # 创建测试数据
        test_data = [
            {
                'page_number': 1,
                'page_header': '工程造价信息',
                'chapter_code': '01',
                'chapter_name': '黑色及有色金属',
                'remarks': '测试备注',
                'price_items': [
                    {
                        'resource_code': '3001010401',
                        'product_name': '网络球型摄像机',
                        'specifications': '1/3英寸CMOS，200万像素',
                        'unit': '台',
                        'price_with_tax': '1200.00',
                        'price_without_tax': '1061.95'
                    },
                    {
                        'resource_code': '3001010402',
                        'product_name': '网络枪型摄像机',
                        'specifications': '1/3英寸CMOS，400万像素',
                        'unit': '台',
                        'price_with_tax': '1800.00',
                        'price_without_tax': '1592.92'
                    }
                ]
            }
        ]
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 生成输出文件
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                output_files = loop.run_until_complete(
                    processor._generate_output_files(test_data, temp_dir)
                )
                
                if output_files:
                    print(f"✅ 生成文件成功，文件数: {len(output_files)}")
                    
                    for file_path in output_files:
                        if file_path.endswith('.csv'):
                            print(f"📄 CSV文件: {os.path.basename(file_path)}")
                            
                            # 验证CSV内容
                            df = pd.read_csv(file_path, encoding='utf-8-sig')
                            print(f"   行数: {len(df)}")
                            print(f"   列数: {len(df.columns)}")
                            print(f"   列名: {list(df.columns)}")
                            
                            # 显示前几行
                            if len(df) > 0:
                                print("   前2行数据:")
                                for _, row in df.head(2).iterrows():
                                    print(f"     资源编号: {row['资源编号']}, 产品名称: {row['产品名称']}")
                        
                        elif file_path.endswith('.json'):
                            print(f"📄 JSON文件: {os.path.basename(file_path)}")
                            
                            # 验证JSON内容
                            with open(file_path, 'r', encoding='utf-8') as f:
                                json_data = json.load(f)
                            
                            print(f"   元数据: {json_data.get('metadata', {})}")
                            print(f"   数据章节数: {len(json_data.get('data', []))}")
                    
                    return True
                else:
                    print("❌ 未生成任何文件")
                    return False
                    
            finally:
                loop.close()
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 信息价识别解析功能测试")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("JSON解析功能", test_json_parsing),
        ("文本解析功能", test_text_parsing),
        ("CSV生成功能", test_csv_generation)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！解析功能已完善。")
        print("💡 现在信息价识别可以处理各种格式的AI返回结果。")
        print("📊 支持JSON格式、文本格式，并能正确生成CSV输出。")
    elif passed >= total - 1:
        print("✅ 基本功能正常！可能有个别小问题。")
        print("💡 建议在Web界面中测试实际功能。")
    else:
        print("⚠️ 存在多个问题，需要进一步检查。")

if __name__ == "__main__":
    main()
