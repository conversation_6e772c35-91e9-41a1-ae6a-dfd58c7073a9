#!/usr/bin/env python3
"""
测试信息价识别界面
Test Price Information Recognition Interface
"""

import os
import sys

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_price_info_interface_import():
    """测试信息价识别界面模块导入"""
    print("🧪 测试信息价识别界面模块导入")
    print("=" * 50)
    
    try:
        from src.price_info_interface import PriceInfoInterface
        print("✅ PriceInfoInterface 导入成功")
        
        # 创建实例
        interface = PriceInfoInterface()
        print("✅ PriceInfoInterface 实例创建成功")
        
        return True, interface
        
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        return False, None
    except Exception as e:
        print(f"❌ 创建实例失败: {str(e)}")
        return False, None

def test_advanced_quota_interface():
    """测试高级定额管理界面"""
    print("\n🧪 测试高级定额管理界面")
    print("=" * 50)
    
    try:
        from src.advanced_quota_interface import AdvancedQuotaInterface
        print("✅ AdvancedQuotaInterface 导入成功")
        
        # 创建实例
        interface = AdvancedQuotaInterface()
        print("✅ AdvancedQuotaInterface 实例创建成功")
        
        # 测试信息价界面方法
        if hasattr(interface, 'create_price_info_interface'):
            print("✅ create_price_info_interface 方法存在")
        else:
            print("❌ create_price_info_interface 方法不存在")
            return False
        
        # 测试信息价界面组件
        if hasattr(interface, 'price_info_interface'):
            if interface.price_info_interface:
                print("✅ price_info_interface 组件已初始化")
            else:
                print("⚠️ price_info_interface 组件未初始化")
        else:
            print("❌ price_info_interface 属性不存在")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_main_app_integration():
    """测试主应用集成"""
    print("\n🧪 测试主应用集成")
    print("=" * 50)
    
    try:
        # 检查main.py中是否包含信息价识别相关代码
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if '📊 信息价识别' in content:
            print("✅ main.py 包含信息价识别标签页")
        else:
            print("❌ main.py 缺少信息价识别标签页")
            return False
        
        if 'create_price_info_interface' in content:
            print("✅ main.py 调用了 create_price_info_interface 方法")
        else:
            print("❌ main.py 未调用 create_price_info_interface 方法")
            return False
        
        if 'advanced_price_info_components' in content:
            print("✅ main.py 定义了 advanced_price_info_components 变量")
        else:
            print("❌ main.py 缺少 advanced_price_info_components 变量")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_config_integration():
    """测试配置集成"""
    print("\n🧪 测试配置集成")
    print("=" * 50)
    
    try:
        from src.config import Config
        config = Config()
        
        if hasattr(config, 'PRICE_INFO_EXTRACTION_PROMPT'):
            print("✅ 配置包含 PRICE_INFO_EXTRACTION_PROMPT")
            prompt_length = len(config.PRICE_INFO_EXTRACTION_PROMPT)
            print(f"   提示词长度: {prompt_length} 字符")
        else:
            print("❌ 配置缺少 PRICE_INFO_EXTRACTION_PROMPT")
            return False
        
        if hasattr(config, 'CSV_COLUMNS_PRICE_INFO'):
            print("✅ 配置包含 CSV_COLUMNS_PRICE_INFO")
            columns = config.CSV_COLUMNS_PRICE_INFO
            print(f"   CSV列数: {len(columns)}")
            print(f"   列名: {columns}")
        else:
            print("❌ 配置缺少 CSV_COLUMNS_PRICE_INFO")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_ai_processor_integration():
    """测试AI处理器集成"""
    print("\n🧪 测试AI处理器集成")
    print("=" * 50)
    
    try:
        from src.ai_model_processor import AIModelProcessor
        processor = AIModelProcessor()
        
        if hasattr(processor, 'process_image_with_price_info_prompt'):
            print("✅ AI处理器包含 process_image_with_price_info_prompt 方法")
        else:
            print("❌ AI处理器缺少 process_image_with_price_info_prompt 方法")
            return False
        
        if hasattr(processor, '_process_price_info_with_qwen_qvq'):
            print("✅ AI处理器包含 _process_price_info_with_qwen_qvq 方法")
        else:
            print("❌ AI处理器缺少 _process_price_info_with_qwen_qvq 方法")
            return False
        
        if hasattr(processor, '_process_price_info_with_lm_studio'):
            print("✅ AI处理器包含 _process_price_info_with_lm_studio 方法")
        else:
            print("❌ AI处理器缺少 _process_price_info_with_lm_studio 方法")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 信息价识别界面集成测试")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("信息价识别界面模块导入", test_price_info_interface_import),
        ("高级定额管理界面", test_advanced_quota_interface),
        ("主应用集成", test_main_app_integration),
        ("配置集成", test_config_integration),
        ("AI处理器集成", test_ai_processor_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_name == "信息价识别界面模块导入":
                result, _ = test_func()
            else:
                result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！信息价识别界面已正确集成。")
        print("💡 现在可以在Web界面中看到'📊 信息价识别'标签页。")
        print("🌐 访问 http://localhost:7864 查看界面")
    else:
        print("⚠️ 部分测试失败，请检查相关配置和依赖。")

if __name__ == "__main__":
    main()
