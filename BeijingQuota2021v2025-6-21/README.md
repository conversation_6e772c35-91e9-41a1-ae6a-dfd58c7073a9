# 北京市消耗定额创建工具

基于MCP（Model Context Protocol）的智能体系统，可以自动从PDF定额文件中提取表格数据，通过AI进行图像识别，并生成包含价格计算公式的CSV文件，实现完整的定额数据库创建功能。

## 功能特点

- 🔄 **自动化PDF处理**: 将PDF页面转换为高质量图片
- 🤖 **AI图像识别**: 通过多种AI模型自动识别表格内容
- 📊 **智能数据提取**: 提取定额编号、名称、工作内容、资源消耗等信息
- 💰 **价格计算**: 自动生成计算公式，处理特殊的百分比项
- 🗄️ **定额数据库创建**: 生成多种格式的定额数据库
- 🔧 **定额创建工具**: 完整的定额数据创建和管理功能
- 🌐 **Web界面**: 基于Gradio的友好用户界面

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Gradio Web    │    │  PDF Processor  │    │ Browser Auto    │
│   Interface     │◄──►│   (MCP Server)  │◄──►│  (MCP Server)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       ▼                       ▼
         │              ┌─────────────────┐    ┌─────────────────┐
         │              │  Image Extract  │    │  DeepSeek API   │
         │              │   & Process     │    │   Integration   │
         │              └─────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────┐
│ Data Processor  │
│  & CSV Export   │
└─────────────────┘
```

## 安装说明

### 1. 系统要求

- Python 3.8+
- Google Chrome浏览器
- Windows/Linux/macOS

### 2. 自动安装

```bash
# 克隆项目
git clone <repository-url>
cd BeijingQuota2021

# 运行安装脚本
python install.py
```

### 3. 手动安装

如果自动安装失败，可以手动安装：

```bash
# 安装Python依赖
pip install -r requirements.txt

# Windows系统需要安装Poppler
# 下载: https://github.com/oschwartz10612/poppler-windows/releases/
# 解压后将bin目录添加到PATH环境变量

# Linux系统
sudo apt-get install poppler-utils  # Ubuntu/Debian
# 或
sudo yum install poppler-utils       # CentOS/RHEL

# macOS系统
brew install poppler
```

### 4. 配置环境

复制并编辑配置文件：

```bash
cp .env.example .env
```

编辑`.env`文件，根据需要调整配置：

```env
# 浏览器配置
BROWSER_HEADLESS=false    # 是否无头模式运行
BROWSER_TIMEOUT=30        # 浏览器超时时间

# PDF处理配置
PDF_DPI=200              # PDF转图片的DPI
IMAGE_FORMAT=PNG         # 图片格式

# DeepSeek配置（如果需要自动登录）
DEEPSEEK_USERNAME=your_username
DEEPSEEK_PASSWORD=your_password
```

## 使用方法

### 1. 启动系统

```bash
python main.py
```

系统启动后会在控制台显示访问地址，通常是：`http://localhost:7860`

### 2. 使用Web界面

1. **上传PDF文件**: 点击"上传PDF定额文件"按钮，选择要处理的PDF文件
2. **设置页码范围**: 输入起始页码和结束页码
3. **开始提取**: 点击"开始提取"按钮
4. **等待处理**: 系统会显示处理进度
5. **下载结果**: 处理完成后下载生成的CSV文件

### 3. 处理流程

1. **PDF转图片**: 系统将指定页面转换为高质量图片
2. **图片上传**: 自动将图片上传到DeepSeek网站
3. **AI识别**: DeepSeek AI分析图片并提取定额信息
4. **数据处理**: 解析AI返回的结果，生成结构化数据
5. **价格计算**: 自动计算合价和综合单价
6. **CSV生成**: 输出包含计算公式的CSV文件

## 数据结构

### 父级定额表

| 字段名 | 说明 | 示例 |
|--------|------|------|
| 定额编号 | 定额项目编号 | 01-001 |
| 定额名称 | 定额项目名称 | 挖土方 |
| 工作内容 | 具体工作内容描述 | 人工挖土方 |
| 单位 | 计量单位 | m³ |
| 综合单价 | 自动计算的总价 | 45.67 |

### 子级资源消耗表

| 字段名 | 说明 | 示例 |
|--------|------|------|
| 定额编号 | 关联的定额编号 | 01-001 |
| 资源编号 | 资源材料编号 | A001 |
| 资源类别 | 人工/材料/机械 | 人工 |
| 子项名称 | 资源名称 | 普通工 |
| 单位 | 资源计量单位 | 工日 |
| 消耗量 | 消耗数量 | 0.5 |
| 单价 | 资源单价 | 80.00 |
| 合价 | 计算结果 | 40.00 |

## 价格计算规则

### 普通资源项
```
合价 = 单价 × 消耗量
```

### 百分比资源项
```
单价 = 其他项合价总和
合价 = 单价 × 消耗量 × 单位(%)
```

### 综合单价
```
综合单价 = 所有资源项合价之和
```

## 故障排除

### 常见问题

1. **ChromeDriver错误**
   - 确保Chrome浏览器已安装
   - 运行`python install.py`重新配置

2. **PDF转换失败**
   - 检查Poppler是否正确安装
   - 确认PDF文件没有密码保护

3. **DeepSeek连接失败**
   - 检查网络连接
   - 确认DeepSeek网站可以正常访问
   - 可能需要手动登录DeepSeek账户

4. **图片识别不准确**
   - 尝试提高PDF_DPI设置
   - 确保PDF图片质量清晰
   - 检查表格是否为标准格式

### 日志查看

系统运行日志保存在`logs/`目录下，可以查看详细的错误信息。

## 技术栈

- **前端**: Gradio Web UI
- **PDF处理**: pdf2image, OpenCV
- **浏览器自动化**: Selenium WebDriver
- **数据处理**: Pandas, NumPy
- **AI集成**: DeepSeek Chat API
- **架构**: MCP (Model Context Protocol)

## 开发说明

### 项目结构

```
BeijingQuota2021/
├── main.py                 # 主程序入口
├── install.py             # 安装脚本
├── requirements.txt       # Python依赖
├── .env.example          # 环境配置示例
├── README.md             # 说明文档
├── src/                  # 源代码目录
│   ├── __init__.py
│   ├── config.py         # 配置管理
│   ├── pdf_processor.py  # PDF处理模块
│   ├── browser_automation.py  # 浏览器自动化
│   └── data_processor.py # 数据处理模块
├── temp/                 # 临时文件目录
├── output/               # 输出文件目录
└── logs/                 # 日志文件目录
```

### 扩展开发

如需扩展功能，可以：

1. 修改`src/config.py`中的提取提示词
2. 在`src/data_processor.py`中添加新的数据处理逻辑
3. 在`src/browser_automation.py`中适配其他AI服务
4. 在`main.py`中添加新的界面功能

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交GitHub Issue
- 发送邮件至：[<EMAIL>]

---

**注意**: 本系统仅用于学习和研究目的，请遵守相关网站的使用条款和法律法规。
