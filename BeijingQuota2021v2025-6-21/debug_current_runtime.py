#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试当前运行时状态
检查程序运行时的实际连接状态和数据加载问题
"""

def debug_runtime_status():
    """调试运行时状态"""
    try:
        print("🔍 调试当前运行时状态...")
        
        # 1. 检查配置文件
        print("\n📊 当前配置文件状态:")
        from src.config_persistence_manager import ConfigPersistenceManager
        
        config_manager = ConfigPersistenceManager()
        config = config_manager.load_config()
        quota_db_config = config.get('database_configs', {}).get('quota_db', {})
        
        print(f"   数据库类型: {quota_db_config.get('db_type')}")
        print(f"   数据库名称: {quota_db_config.get('db_name')}")
        print(f"   主机: {quota_db_config.get('host')}")
        print(f"   端口: {quota_db_config.get('port')}")
        print(f"   用户: {quota_db_config.get('username')}")
        
        # 2. 直接测试数据库连接
        print("\n🔗 直接测试数据库连接:")
        import psycopg2
        
        try:
            conn = psycopg2.connect(
                host=quota_db_config.get('host', 'localhost'),
                port=int(quota_db_config.get('port', 5432)),
                user=quota_db_config.get('username', 'postgres'),
                password=quota_db_config.get('password', ''),
                database=quota_db_config.get('db_name', 'beijing2021_quota_test'),
                client_encoding='utf8'
            )
            
            cursor = conn.cursor()
            
            # 查询表
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
            """)
            tables = [row[0] for row in cursor.fetchall()]
            print(f"   ✅ 连接成功，找到表: {tables}")
            
            # 检查数据
            if tables:
                for table in tables:
                    cursor.execute(f'SELECT COUNT(*) FROM "{table}"')
                    count = cursor.fetchone()[0]
                    print(f"   📊 {table}: {count} 行")
            
            cursor.close()
            conn.close()
            
        except Exception as e:
            print(f"   ❌ 直接连接失败: {e}")
            return False
        
        # 3. 测试修订处理器
        print("\n🔍 测试修订处理器:")
        from src.quota_revision_processor import QuotaRevisionProcessor
        from src.advanced_quota_manager import AdvancedQuotaManager
        
        # 创建管理器
        quota_manager = AdvancedQuotaManager()
        print(f"   ✅ 创建高级定额管理器")
        
        # 手动设置连接
        quota_manager.connection = psycopg2.connect(
            host=quota_db_config.get('host', 'localhost'),
            port=int(quota_db_config.get('port', 5432)),
            user=quota_db_config.get('username', 'postgres'),
            password=quota_db_config.get('password', ''),
            database=quota_db_config.get('db_name', 'beijing2021_quota_test'),
            client_encoding='utf8'
        )
        quota_manager.db_type = 'postgresql'
        quota_manager.db_path = quota_db_config.get('db_name')
        
        print(f"   ✅ 手动设置连接对象")
        
        # 测试修订处理器
        if quota_manager.revision_processor:
            print(f"   ✅ 修订处理器存在")
            
            # 设置修订处理器的连接
            quota_manager.revision_processor.current_connection = quota_manager.connection
            print(f"   ✅ 设置修订处理器连接")
            
            # 测试数据加载
            try:
                success, message, quota_data, stats = quota_manager.revision_processor.load_from_connected_database()
                
                print(f"   📊 数据加载结果:")
                print(f"     成功: {success}")
                print(f"     消息: {message}")
                print(f"     统计: {stats}")
                print(f"     数据行数: {len(quota_data) if quota_data else 0}")
                
                if success and quota_data:
                    print(f"   📋 前3行数据:")
                    for i, row in enumerate(quota_data[:3]):
                        print(f"     行{i+1}: {row}")
                
                return success
                
            except Exception as e:
                print(f"   ❌ 数据加载异常: {e}")
                import traceback
                traceback.print_exc()
                return False
        else:
            print(f"   ❌ 修订处理器不存在")
            return False
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_interface_directly():
    """直接测试界面方法"""
    try:
        print("\n🔍 直接测试界面方法:")
        
        from src.advanced_quota_interface import AdvancedQuotaInterface
        from src.advanced_quota_manager import AdvancedQuotaManager
        from src.config_persistence_manager import ConfigPersistenceManager
        import psycopg2
        
        # 创建管理器和界面
        quota_manager = AdvancedQuotaManager()
        interface = AdvancedQuotaInterface(quota_manager)
        
        print(f"   ✅ 创建界面组件")
        
        # 获取配置
        config_manager = ConfigPersistenceManager()
        config = config_manager.load_config()
        quota_db_config = config.get('database_configs', {}).get('quota_db', {})
        
        # 手动设置连接
        quota_manager.connection = psycopg2.connect(
            host=quota_db_config.get('host', 'localhost'),
            port=int(quota_db_config.get('port', 5432)),
            user=quota_db_config.get('username', 'postgres'),
            password=quota_db_config.get('password', ''),
            database=quota_db_config.get('db_name', 'beijing2021_quota_test'),
            client_encoding='utf8'
        )
        quota_manager.db_type = 'postgresql'
        quota_manager.db_path = quota_db_config.get('db_name')
        
        # 设置修订处理器连接
        if quota_manager.revision_processor:
            quota_manager.revision_processor.current_connection = quota_manager.connection
        
        print(f"   ✅ 设置连接对象")
        
        # 直接调用加载数据方法
        try:
            result = interface._handle_load_revision_data()
            
            print(f"   📊 界面加载数据结果:")
            print(f"     返回值数量: {len(result)}")
            
            if len(result) >= 6:
                status_html, info_html, data_stats_html, quota_data, revision_status, revision_log = result[:6]
                
                print(f"     状态HTML: {status_html}")
                print(f"     修订状态: {revision_status}")
                print(f"     修订日志: {revision_log}")
                print(f"     数据行数: {len(quota_data) if quota_data else 0}")
                
                if quota_data:
                    print(f"   📋 前3行数据:")
                    for i, row in enumerate(quota_data[:3]):
                        print(f"     行{i+1}: {row}")
                
                return True
            else:
                print(f"     ❌ 返回值数量不正确")
                return False
                
        except Exception as e:
            print(f"   ❌ 界面方法调用异常: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 界面测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试流程"""
    print("🔧 运行时状态调试工具")
    print("=" * 60)
    
    # 1. 调试运行时状态
    runtime_success = debug_runtime_status()
    
    # 2. 直接测试界面
    interface_success = test_interface_directly()
    
    print("\n" + "=" * 60)
    
    if runtime_success and interface_success:
        print("🎉 所有测试通过！")
        print("✅ 数据库连接正常")
        print("✅ 修订处理器正常")
        print("✅ 界面方法正常")
        print("✅ 数据加载功能应该可以正常工作")
    else:
        print("❌ 部分测试失败")
        print("💡 请检查失败的项目")
        
        if not runtime_success:
            print("   - 运行时状态有问题")
        if not interface_success:
            print("   - 界面方法有问题")

if __name__ == "__main__":
    main()
