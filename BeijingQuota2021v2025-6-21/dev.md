我这有许多分册的2021年北京市消耗定额pdf文件，上千页的文档，里面定额数据在特定范围，而且是复杂布局的表格，可否设计一个本地部署多个MCP组合的智能体定额创建程序，实现pdf内容由mcp自动提取表格为图片，然后调用第二个MCP将提取的图片按顺序在deepseek的官网https://chat.deepseek.com/上传，在浏览器里面deepseek官网对定额图片进行识别，按照指令提取每页关键的定额编号的相关定额内容，待deepseek回应后，抓取提取的信息写入csv文件，然后重复这一过程，最终将pdf内的所有定额信息提取完毕？这个程序由Gradio驱动，在页面上上传pdf定额文件，由用户指定提取pdf页码范围数据；定额信息特征为1. **父级信息**：每个定额项的编号、名称、工作内容和单位需要准确无误地填入父级表格。2. **子级资源消耗**：每个资源消耗项需要列出资源编号、类别、子项名称、单位和消耗量，并且与父级定额编号相关联。3.父级表格增加综合单价列，子级表格加入单价列、合价列，北京市消耗定额无价格，因此需要统一填入初始值0，自动生成计算公式，计算每行资源单项合价，每行资源单项合价计算公式为：合价=单价*消耗量，但资源行单位为%比较特殊，该项单价为子项其他项合价总和，合价=单价*消耗量*单位；自动汇总每项定额下所有资源消耗合价的总额作为父级综合单价。