#!/usr/bin/env python3
"""
测试WSL ModelScope CogVLM2集成功能
"""

import sys
import os
import time
import asyncio
from pathlib import Path

# 添加src目录到路径
sys.path.append('src')

def test_wsl_service_health():
    """测试WSL服务健康状态"""
    print("🔍 测试WSL服务健康状态...")
    
    try:
        from wsl_cogvlm2_client import wsl_client
        
        # 检查服务健康状态
        health = wsl_client.check_service_health()
        if health:
            print("✅ WSL服务健康状态良好")
            return True
        else:
            print("⚠️ WSL服务不健康，尝试启动...")
            if wsl_client.start_wsl_service():
                print("✅ WSL服务启动成功")
                return True
            else:
                print("❌ WSL服务启动失败")
                return False
                
    except Exception as e:
        print(f"❌ WSL服务测试失败: {e}")
        return False

def test_wsl_service_status():
    """测试WSL服务状态接口"""
    print("🔍 测试WSL服务状态接口...")
    
    try:
        from wsl_cogvlm2_client import wsl_client
        
        status = wsl_client.get_service_status()
        if status:
            print("✅ WSL服务状态获取成功:")
            print(f"   - 服务: {status.get('service', 'Unknown')}")
            print(f"   - 模型已加载: {status.get('model_loaded', False)}")
            print(f"   - PyTorch版本: {status.get('pytorch_version', 'Unknown')}")
            print(f"   - CUDA可用: {status.get('cuda_available', False)}")
            
            gpu_info = status.get('gpu_info', {})
            if gpu_info:
                print(f"   - GPU: {gpu_info.get('name', 'Unknown')}")
                print(f"   - GPU内存: {gpu_info.get('memory_used_gb', 0):.1f} / {gpu_info.get('memory_total_gb', 0):.1f} GB")
            
            return True
        else:
            print("❌ WSL服务状态获取失败")
            return False
            
    except Exception as e:
        print(f"❌ WSL服务状态测试失败: {e}")
        return False

async def test_ai_model_processor():
    """测试AI模型处理器的WSL集成"""
    print("🔍 测试AI模型处理器的WSL集成...")
    
    try:
        from ai_model_processor import AIModelProcessor
        
        processor = AIModelProcessor()
        
        # 查找测试图片
        test_image = None
        for ext in ['.png', '.jpg', '.jpeg']:
            for pattern in ['*test*', '*sample*', '*demo*']:
                files = list(Path('.').glob(f"**/{pattern}{ext}"))
                if files:
                    test_image = str(files[0])
                    break
            if test_image:
                break
        
        if not test_image:
            print("⚠️ 未找到测试图片，跳过图片处理测试")
            return True
        
        print(f"📸 使用测试图片: {test_image}")
        
        # 测试ModelScope CogVLM2处理
        result = await processor.process_image_with_modelscope_cogvlm2(test_image)
        
        if result:
            print("✅ AI模型处理器WSL集成测试成功")
            print(f"   结果长度: {len(result)}")
            return True
        else:
            print("❌ AI模型处理器WSL集成测试失败")
            return False
            
    except Exception as e:
        print(f"❌ AI模型处理器测试失败: {e}")
        return False

def test_model_dropdown_integration():
    """测试模型下拉框集成"""
    print("🔍 测试模型下拉框集成...")
    
    try:
        # 检查main.py中的模型选项
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'ModelScope CogVLM2 (WSL本地)' in content and 'modelscope_cogvlm2' in content:
            print("✅ 模型下拉框集成成功")
            return True
        else:
            print("❌ 模型下拉框集成失败")
            return False
            
    except Exception as e:
        print(f"❌ 模型下拉框测试失败: {e}")
        return False

def test_performance_optimization():
    """测试性能优化功能"""
    print("🔍 测试性能优化功能...")
    
    try:
        from wsl_cogvlm2_client import wsl_client
        
        # 检查会话配置
        session = wsl_client.session
        if hasattr(session, 'adapters') and session.adapters:
            print("✅ HTTP连接池和重试机制已配置")
        
        # 检查超时设置
        if hasattr(session, 'timeout') and session.timeout == 900:
            print("✅ 超时设置已优化 (900秒)")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能优化测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始WSL ModelScope CogVLM2集成测试...")
    print("=" * 60)
    
    tests = [
        ("WSL服务健康状态", test_wsl_service_health),
        ("WSL服务状态接口", test_wsl_service_status),
        ("模型下拉框集成", test_model_dropdown_integration),
        ("性能优化功能", test_performance_optimization),
        ("AI模型处理器集成", test_ai_model_processor),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
        
        time.sleep(1)  # 测试间隔
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！WSL ModelScope CogVLM2集成成功！")
    else:
        print("⚠️ 部分测试失败，请检查相关配置")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试运行失败: {e}")
        sys.exit(1)
