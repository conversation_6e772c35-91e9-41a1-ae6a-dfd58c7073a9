#!/usr/bin/env python3
"""
验证信息价识别界面
简单验证界面是否正确集成
"""

import os
import re

def check_main_py_integration():
    """检查main.py中的信息价识别集成"""
    print("🔍 检查main.py中的信息价识别集成")
    print("=" * 50)
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ('📊 信息价识别标签页', '📊 信息价识别'),
            ('create_price_info_interface调用', 'create_price_info_interface'),
            ('advanced_price_info_components变量', 'advanced_price_info_components'),
            ('PriceInfoInterface导入', 'PriceInfoInterface'),
        ]
        
        results = []
        for check_name, pattern in checks:
            if pattern in content:
                print(f"✅ {check_name}: 已找到")
                results.append(True)
            else:
                print(f"❌ {check_name}: 未找到")
                results.append(False)
        
        return all(results)
        
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
        return False

def check_interface_files():
    """检查界面相关文件是否存在"""
    print("\n🔍 检查界面相关文件")
    print("=" * 50)
    
    files_to_check = [
        'src/price_info_interface.py',
        'src/price_info_processor.py',
        'src/advanced_quota_interface.py',
        'src/config.py'
    ]
    
    results = []
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size_kb = os.path.getsize(file_path) / 1024
            print(f"✅ {file_path}: 存在 ({size_kb:.1f} KB)")
            results.append(True)
        else:
            print(f"❌ {file_path}: 不存在")
            results.append(False)
    
    return all(results)

def check_config_integration():
    """检查配置文件集成"""
    print("\n🔍 检查配置文件集成")
    print("=" * 50)
    
    try:
        with open('src/config.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ('信息价识别提示词', 'PRICE_INFO_EXTRACTION_PROMPT'),
            ('信息价CSV列配置', 'CSV_COLUMNS_PRICE_INFO'),
            ('页眉标识', '页眉标识'),
            ('章节编号', '章节编号'),
            ('资源编号', '资源编号'),
        ]
        
        results = []
        for check_name, pattern in checks:
            if pattern in content:
                print(f"✅ {check_name}: 已配置")
                results.append(True)
            else:
                print(f"❌ {check_name}: 未配置")
                results.append(False)
        
        return all(results)
        
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
        return False

def check_advanced_quota_interface():
    """检查高级定额管理界面集成"""
    print("\n🔍 检查高级定额管理界面集成")
    print("=" * 50)
    
    try:
        with open('src/advanced_quota_interface.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ('PriceInfoInterface导入', 'from .price_info_interface import PriceInfoInterface'),
            ('price_info_interface属性', 'self.price_info_interface'),
            ('create_price_info_interface方法', 'def create_price_info_interface'),
            ('信息价识别标签页', '📊 信息价识别'),
        ]
        
        results = []
        for check_name, pattern in checks:
            if pattern in content:
                print(f"✅ {check_name}: 已集成")
                results.append(True)
            else:
                print(f"❌ {check_name}: 未集成")
                results.append(False)
        
        return all(results)
        
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
        return False

def check_ai_processor_integration():
    """检查AI处理器集成"""
    print("\n🔍 检查AI处理器集成")
    print("=" * 50)
    
    try:
        with open('src/ai_model_processor.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ('信息价识别方法', 'process_image_with_price_info_prompt'),
            ('QVQ信息价处理', '_process_price_info_with_qwen_qvq'),
            ('LM Studio信息价处理', '_process_price_info_with_lm_studio'),
            ('信息价提示词引用', 'PRICE_INFO_EXTRACTION_PROMPT'),
        ]
        
        results = []
        for check_name, pattern in checks:
            if pattern in content:
                print(f"✅ {check_name}: 已集成")
                results.append(True)
            else:
                print(f"❌ {check_name}: 未集成")
                results.append(False)
        
        return all(results)
        
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
        return False

def check_server_status():
    """检查服务器状态"""
    print("\n🔍 检查服务器状态")
    print("=" * 50)
    
    try:
        import requests
        response = requests.get('http://localhost:7864', timeout=5)
        if response.status_code == 200:
            print("✅ 服务器正在运行")
            print("🌐 访问地址: http://localhost:7864")
            return True
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        print("💡 请确保运行了 python main.py")
        return False
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
        return False

def main():
    """主验证函数"""
    print("🚀 信息价识别界面验证")
    print("=" * 60)
    
    # 运行检查
    checks = [
        ("main.py集成", check_main_py_integration),
        ("界面文件", check_interface_files),
        ("配置集成", check_config_integration),
        ("高级定额管理界面集成", check_advanced_quota_interface),
        ("AI处理器集成", check_ai_processor_integration),
        ("服务器状态", check_server_status)
    ]
    
    results = []
    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        try:
            result = check_func()
            results.append((check_name, result))
            if result:
                print(f"✅ {check_name} 检查通过")
            else:
                print(f"❌ {check_name} 检查失败")
        except Exception as e:
            print(f"❌ {check_name} 检查异常: {str(e)}")
            results.append((check_name, False))
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 验证总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for check_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {check_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 检查通过")
    
    if passed == total:
        print("🎉 所有检查都通过了！")
        print("💡 信息价识别界面已正确集成到系统中。")
        print("🌐 请访问 http://localhost:7864 查看界面")
        print("📋 在'高级定额管理系统'部分找到'📊 信息价识别'标签页")
    elif passed >= total - 1:
        print("✅ 基本集成完成！")
        print("💡 信息价识别功能已可用，可能有个别小问题。")
        print("🌐 请访问 http://localhost:7864 查看界面")
    else:
        print("⚠️ 存在多个问题，请检查相关配置。")

if __name__ == "__main__":
    main()
