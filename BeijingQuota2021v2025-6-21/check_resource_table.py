#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查资源表结构
"""

def check_resource_table():
    """检查资源表结构"""
    try:
        from src.config_persistence_manager import ConfigPersistenceManager
        import psycopg2
        
        config_manager = ConfigPersistenceManager()
        config = config_manager.load_config()
        quota_db_config = config.get('database_configs', {}).get('quota_db', {})
        
        conn = psycopg2.connect(
            host=quota_db_config.get('host', 'localhost'),
            port=int(quota_db_config.get('port', 5432)),
            user=quota_db_config.get('username', 'postgres'),
            password=quota_db_config.get('password', ''),
            database=quota_db_config.get('db_name', 'beijing2021_quota_database'),
            client_encoding='utf8'
        )
        
        cursor = conn.cursor()
        
        # 查询资源表
        resource_table = 'processed_child_resources_20250629_230857'
        
        print(f"🔍 检查资源表: {resource_table}")
        
        # 获取列信息
        cursor.execute(f"""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = '{resource_table}'
            ORDER BY ordinal_position
        """)
        columns = cursor.fetchall()
        
        print(f"📊 资源表列信息:")
        for col_name, col_type in columns:
            print(f"   {col_name} ({col_type})")
        
        # 查询前几行数据
        cursor.execute(f'SELECT * FROM "{resource_table}" LIMIT 5')
        rows = cursor.fetchall()
        
        print(f"\n📋 前5行数据:")
        for i, row in enumerate(rows):
            print(f"   行{i+1}: {row}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

if __name__ == "__main__":
    check_resource_table()
