#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
连接钩子测试脚本
测试数据库连接和识别定额修订页面的状态同步
"""

def test_connection_hook():
    """测试连接钩子功能"""
    try:
        print("🔍 测试连接钩子功能...")
        
        from src.advanced_quota_handlers import AdvancedQuotaHandlers
        from src.config_persistence_manager import ConfigPersistenceManager
        
        # 创建处理器
        handlers = AdvancedQuotaHandlers()
        
        # 加载配置
        config_manager = ConfigPersistenceManager()
        config = config_manager.load_config()
        quota_db_config = config.get('database_configs', {}).get('quota_db', {})
        
        print(f"📊 数据库配置:")
        print(f"   类型: {quota_db_config.get('db_type')}")
        print(f"   主机: {quota_db_config.get('host')}")
        print(f"   端口: {quota_db_config.get('port')}")
        print(f"   数据库: {quota_db_config.get('db_name')}")
        print(f"   用户: {quota_db_config.get('username')}")
        
        # 测试连接
        print(f"\n🔗 测试数据库连接...")
        
        connection_result = handlers.connect_to_database(
            quota_db_config.get('db_type', 'postgresql'),
            quota_db_config.get('db_path', ''),
            quota_db_config.get('host', 'localhost'),
            quota_db_config.get('port', '5432'),
            quota_db_config.get('db_name', 'beijing2021_quota_database'),
            quota_db_config.get('username', 'postgres'),
            quota_db_config.get('password', '')
        )
        
        connection_status, disconnect_btn_state, connect_btn_state = connection_result
        
        print(f"✅ 连接结果:")
        print(f"   状态: {connection_status}")
        print(f"   断开按钮: {disconnect_btn_state}")
        print(f"   连接按钮: {connect_btn_state}")
        
        # 检查连接对象
        if hasattr(handlers, 'connection') and handlers.connection:
            print(f"✅ 连接对象存在")
            print(f"   数据库路径: {getattr(handlers, 'db_path', '未知')}")
            print(f"   数据库类型: {getattr(handlers, 'db_type', '未知')}")
            
            # 测试查询
            cursor = handlers.connection.cursor()
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
            """)
            tables = [row[0] for row in cursor.fetchall()]
            print(f"   数据库表: {tables}")
            cursor.close()
            
            return True
        else:
            print(f"❌ 连接对象不存在")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_revision_processor_integration():
    """测试修订处理器集成"""
    try:
        print("\n🔍 测试修订处理器集成...")
        
        from src.advanced_quota_manager import AdvancedQuotaManager
        
        # 创建高级定额管理器
        quota_manager = AdvancedQuotaManager()
        
        # 检查修订处理器
        if quota_manager.revision_processor:
            print("✅ 修订处理器存在")
            
            # 检查quota_manager引用
            if quota_manager.revision_processor.quota_manager:
                print("✅ 修订处理器有quota_manager引用")
                
                # 测试连接检查
                if hasattr(quota_manager.revision_processor, 'load_from_connected_database'):
                    print("✅ load_from_connected_database方法存在")
                    
                    try:
                        success, message, quota_data, stats = quota_manager.revision_processor.load_from_connected_database()
                        
                        if success:
                            print(f"✅ 数据加载成功:")
                            print(f"   消息: {message}")
                            print(f"   统计: {stats}")
                            print(f"   数据行数: {len(quota_data)}")
                            return True
                        else:
                            print(f"❌ 数据加载失败: {message}")
                            return False
                    except Exception as e:
                        print(f"❌ 数据加载异常: {e}")
                        return False
                else:
                    print("❌ load_from_connected_database方法不存在")
                    return False
            else:
                print("❌ 修订处理器没有quota_manager引用")
                return False
        else:
            print("❌ 修订处理器不存在")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主测试流程"""
    print("🔧 连接钩子测试工具")
    print("=" * 50)
    
    # 1. 测试连接钩子
    connection_success = test_connection_hook()
    
    # 2. 测试修订处理器集成
    integration_success = test_revision_processor_integration()
    
    print("\n" + "=" * 50)
    
    if connection_success and integration_success:
        print("🎉 所有测试通过！")
        print("✅ 数据库连接正常")
        print("✅ 修订处理器集成正常")
        print("✅ 钩子功能应该可以正常工作")
        
        print("\n💡 现在可以:")
        print("1. 在数据库连接页面连接数据库")
        print("2. 连接成功后，识别定额修订页面会自动更新状态")
        print("3. 在识别定额修订页面加载数据")
    else:
        print("❌ 部分测试失败")
        print("💡 请检查失败的项目")

if __name__ == "__main__":
    main()
