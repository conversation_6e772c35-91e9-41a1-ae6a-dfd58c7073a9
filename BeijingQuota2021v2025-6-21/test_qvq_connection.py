#!/usr/bin/env python3
"""
快速测试阿里云QVQ模型连接
"""

import os
import requests
import json
import ssl
import urllib3
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_qvq_connection():
    """测试QVQ模型连接"""
    print("🧪 测试阿里云QVQ模型连接...")
    
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if not api_key:
        print("❌ 未找到DASHSCOPE_API_KEY")
        return False
    
    print(f"✅ API密钥: {api_key[:10]}...")
    
    # 配置session - 使用更强的重试策略
    session = requests.Session()
    
    # 设置重试策略
    retry_strategy = Retry(
        total=5,
        backoff_factor=2,
        status_forcelist=[429, 500, 502, 503, 504],
        allowed_methods=["HEAD", "GET", "OPTIONS", "POST"]
    )
    
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    
    # 设置请求头
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    }
    
    # 简单的文本测试请求
    test_data = {
        "model": "qvq-max",
        "messages": [
            {
                "role": "user", 
                "content": "你好，请简单回复。"
            }
        ],
        "stream": False,
        "max_tokens": 50
    }
    
    url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
    
    print(f"🌐 测试URL: {url}")
    
    # 尝试多种连接方式
    test_methods = [
        {"verify": True, "timeout": 30, "desc": "标准SSL连接"},
        {"verify": False, "timeout": 30, "desc": "禁用SSL验证"},
        {"verify": False, "timeout": 60, "desc": "禁用SSL + 长超时"},
    ]
    
    for i, method in enumerate(test_methods, 1):
        print(f"\n📡 方法 {i}: {method['desc']}")
        try:
            response = session.post(
                url,
                headers=headers,
                json=test_data,
                **{k: v for k, v in method.items() if k != 'desc'}
            )
            
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ 连接成功！")
                try:
                    result = response.json()
                    if 'choices' in result:
                        content = result['choices'][0]['message']['content']
                        print(f"   📝 回复: {content[:50]}...")
                        return True
                except:
                    print("   ⚠️ 响应格式异常")
                    return True
            
            elif response.status_code == 429:
                print("   ⚠️ 请求频率限制")
            elif response.status_code in [401, 403]:
                print("   ❌ API密钥错误或权限不足")
            else:
                print(f"   ⚠️ 其他错误: {response.text[:100]}")
                
        except requests.exceptions.SSLError as e:
            print(f"   🔒 SSL错误: {str(e)[:50]}...")
        except requests.exceptions.ProxyError as e:
            print(f"   🚫 代理错误: {str(e)[:50]}...")
        except requests.exceptions.ConnectionError as e:
            print(f"   ❌ 连接错误: {str(e)[:50]}...")
        except requests.exceptions.Timeout as e:
            print(f"   ⏱️ 超时错误: {str(e)[:50]}...")
        except Exception as e:
            print(f"   ❓ 未知错误: {str(e)[:50]}...")
    
    return False

def test_alternative_endpoints():
    """测试备用endpoint"""
    print("\n🔄 测试备用API端点...")
    
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if not api_key:
        return False
    
    # 阿里云的其他可能endpoint
    endpoints = [
        "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions",
        "https://dashscope-intl.aliyuncs.com/compatible-mode/v1/chat/completions", 
        "https://dashscope.cn-hangzhou.aliyuncs.com/compatible-mode/v1/chat/completions"
    ]
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    test_data = {
        "model": "qwen-max",  # 使用更通用的模型
        "messages": [{"role": "user", "content": "Hello"}],
        "max_tokens": 10
    }
    
    for endpoint in endpoints:
        print(f"\n📡 测试: {endpoint}")
        try:
            response = requests.post(
                endpoint,
                headers=headers,
                json=test_data,
                timeout=15,
                verify=False
            )
            
            print(f"   状态码: {response.status_code}")
            if response.status_code == 200:
                print("   ✅ 端点可用")
                return True
            elif response.status_code in [401, 403]:
                print("   ❌ 认证错误")
            else:
                print(f"   ⚠️ 错误: {response.text[:50]}")
                
        except Exception as e:
            print(f"   ❌ 连接失败: {str(e)[:30]}...")
    
    return False

def main():
    """主函数"""
    print("🔧 阿里云QVQ模型连接测试")
    print("=" * 40)
    
    # 检查基本配置
    print("📋 基本检查:")
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if api_key:
        print(f"   ✅ API密钥: {api_key[:10]}...")
    else:
        print("   ❌ 未设置API密钥")
        return
    
    # 测试主连接
    success = test_qvq_connection()
    
    if not success:
        print("\n🔄 主连接失败，尝试备用方案...")
        success = test_alternative_endpoints()
    
    print("\n" + "=" * 40)
    if success:
        print("✅ 阿里云API连接正常！")
        print("💡 现在可以启动项目并使用QVQ模型")
    else:
        print("❌ 所有连接尝试都失败了")
        print("\n💡 建议:")
        print("   1. 检查网络连接和防火墙设置")
        print("   2. 确认API密钥有效且有足够余额") 
        print("   3. 稍后重试或联系技术支持")
        print("   4. 考虑使用其他AI模型（DeepSeek、OpenAI等）")

if __name__ == "__main__":
    main() 