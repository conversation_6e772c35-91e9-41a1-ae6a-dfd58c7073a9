#!/usr/bin/env python3
"""
测试集成的价格计算功能
验证CSV文件是否包含正确的价格计算
"""

import os
import pandas as pd
from src.data_processor import DataProcessor

def test_integrated_price_calculation():
    """测试集成的价格计算功能"""
    
    print("🧪 测试集成的价格计算功能")
    print("=" * 60)
    
    # 创建数据处理器
    processor = DataProcessor()
    
    # 创建测试数据
    test_data = """
    定额编号: 1-1
    定额名称: 人工挖一般土方 一、二类土
    计量单位: 100m³
    
    资源消耗:
    - 00010701 综合用工三类 工日 0.8
    - 99030030 电动打钎机 台班 0.1
    - 99460004 其他机具费占人工费 % 5.0
    """
    
    print("📊 测试数据:")
    print(test_data)
    
    # 创建模拟的AI识别结果（JSON格式）
    mock_ai_response = """
    ```json
    {
        "parent_quota": {
            "code": "1-1",
            "name": "人工挖一般土方 一、二类土",
            "work_content": "挖掘一般土方",
            "unit": "100m³"
        },
        "resource_consumption": [
            {
                "resource_code": "00010701",
                "category": "人工",
                "name": "综合用工三类",
                "unit": "工日",
                "consumption": "0.8"
            },
            {
                "resource_code": "99030030",
                "category": "机械",
                "name": "电动打钎机",
                "unit": "台班",
                "consumption": "0.1"
            },
            {
                "resource_code": "99460004",
                "category": "其他费用",
                "name": "其他机具费占人工费",
                "unit": "%",
                "consumption": "5.0"
            }
        ]
    }
    ```
    """

    # 解析AI识别结果
    processed_data = processor.parse_recognition_result(mock_ai_response, 1)

    if processed_data:
        print("✅ 数据解析成功")
        print(f"📊 解析得到 {len(processed_data)} 条记录")

        # 生成CSV文件
        csv_path = processor.generate_csv(processed_data)
    
        if csv_path and os.path.exists(csv_path):
            print(f"✅ CSV文件生成成功: {os.path.basename(csv_path)}")

            file_path = csv_path

            print(f"📄 检查文件: {os.path.basename(file_path)}")

            # 读取CSV文件
            df = pd.read_csv(file_path, encoding='utf-8-sig')

            print(f"📋 文件内容:")
            print(f"- 行数: {len(df)}")
            print(f"- 列数: {len(df.columns)}")
            print(f"- 列名: {', '.join(df.columns.tolist())}")

            # 检查价格列
            price_columns = ['单价', '合价', 'unit_price', 'total_price', '综合单价']
            found_price_columns = [col for col in price_columns if col in df.columns]

            print(f"📊 价格相关列: {found_price_columns}")

            # 显示数据内容
            print(f"\n📋 数据内容:")
            print(df.to_string(index=False))

            # 检查是否有非零价格
            has_prices = False
            for col in found_price_columns:
                if col in df.columns:
                    non_zero_prices = df[col][pd.to_numeric(df[col], errors='coerce') > 0]
                    if len(non_zero_prices) > 0:
                        has_prices = True
                        print(f"✅ 发现非零价格在列 '{col}': {non_zero_prices.tolist()}")

            if not has_prices:
                print("❌ 所有价格都是0，价格计算可能没有生效")
                return False
            else:
                print("✅ 价格计算功能正常工作")
                return True
        else:
            print("❌ CSV文件生成失败")
            return False
    else:
        print("❌ 数据解析失败")
        return False

def test_price_database_integration():
    """测试价格数据库集成"""
    
    print(f"\n" + "=" * 60)
    print("🧪 测试价格数据库集成")
    print("=" * 60)
    
    # 检查价格数据库文件
    price_db_path = "data/price_database.json"
    
    if os.path.exists(price_db_path):
        print("✅ 价格数据库文件存在")
        
        # 检查文件大小
        file_size = os.path.getsize(price_db_path)
        print(f"📊 数据库文件大小: {file_size} 字节")
        
        if file_size > 1000:  # 至少1KB
            print("✅ 数据库文件大小正常")
            
            # 测试数据处理器是否能正确加载价格计算器
            try:
                processor = DataProcessor()
                
                if hasattr(processor, 'price_calculator'):
                    print("✅ 数据处理器成功集成价格计算器")
                    
                    # 测试获取单价
                    test_price = processor.price_calculator.get_unit_price("00010701", "人工")
                    print(f"📊 测试单价查询 (00010701): {test_price} 元")
                    
                    if test_price > 0:
                        print("✅ 单价查询功能正常")
                        return True
                    else:
                        print("❌ 单价查询返回0")
                        return False
                else:
                    print("❌ 数据处理器未集成价格计算器")
                    return False
                    
            except Exception as e:
                print(f"❌ 价格计算器集成测试失败: {e}")
                return False
        else:
            print("❌ 数据库文件太小，可能没有正确创建")
            return False
    else:
        print("❌ 价格数据库文件不存在")
        return False

def test_csv_output_format():
    """测试CSV输出格式"""
    
    print(f"\n" + "=" * 60)
    print("🧪 测试CSV输出格式")
    print("=" * 60)
    
    # 查找最新的CSV文件
    output_files = []
    for file in os.listdir("output"):
        if file.endswith(".csv") and not file.startswith("price_database"):
            output_files.append(file)
    
    if not output_files:
        print("❌ 没有找到CSV输出文件")
        return False
    
    # 检查最新文件
    latest_file = max(output_files, key=lambda x: os.path.getmtime(os.path.join("output", x)))
    file_path = os.path.join("output", latest_file)
    
    print(f"📄 检查文件: {latest_file}")
    
    try:
        df = pd.read_csv(file_path, encoding='utf-8-sig')
        
        # 检查必要的列
        required_columns = ['定额编号', '资源编号', '消耗量']
        price_columns = ['单价', '合价']
        
        print(f"📋 文件列名: {df.columns.tolist()}")
        
        # 检查必要列
        missing_required = [col for col in required_columns if col not in df.columns]
        if missing_required:
            print(f"❌ 缺少必要列: {missing_required}")
            return False
        else:
            print("✅ 包含所有必要列")
        
        # 检查价格列
        missing_price = [col for col in price_columns if col not in df.columns]
        if missing_price:
            print(f"⚠️ 缺少价格列: {missing_price}")
        else:
            print("✅ 包含所有价格列")
        
        # 检查数据内容
        print(f"\n📊 数据统计:")
        print(f"- 总行数: {len(df)}")
        print(f"- 非空行数: {len(df.dropna())}")
        
        # 检查价格数据
        if '单价' in df.columns:
            non_zero_unit_prices = len(df[df['单价'] > 0])
            print(f"- 有单价的行数: {non_zero_unit_prices}")
        
        if '合价' in df.columns:
            non_zero_total_prices = len(df[df['合价'] > 0])
            print(f"- 有合价的行数: {non_zero_total_prices}")
        
        # 显示前几行数据
        print(f"\n📋 前3行数据:")
        print(df.head(3).to_string(index=False))
        
        return True
        
    except Exception as e:
        print(f"❌ 读取CSV文件失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试集成的价格计算功能")
    print("=" * 80)
    
    # 测试价格数据库集成
    db_integration_success = test_price_database_integration()
    
    # 测试集成的价格计算
    price_calculation_success = test_integrated_price_calculation()
    
    # 测试CSV输出格式
    csv_format_success = test_csv_output_format()
    
    print("\n" + "=" * 80)
    print("🎯 测试结果总结:")
    print(f"- 价格数据库集成: {'✅ 正常' if db_integration_success else '❌ 异常'}")
    print(f"- 集成价格计算: {'✅ 正常' if price_calculation_success else '❌ 异常'}")
    print(f"- CSV输出格式: {'✅ 正常' if csv_format_success else '❌ 异常'}")
    
    all_success = all([db_integration_success, price_calculation_success, csv_format_success])
    
    if all_success:
        print("🎉 集成的价格计算功能测试全部通过！")
        print("\n📋 现在CSV文件包含:")
        print("- ✅ 正确的单价数据（从数据库自动匹配）")
        print("- ✅ 准确的合价计算（单价 × 消耗量）")
        print("- ✅ 百分比项的特殊处理")
        print("- ✅ 综合单价的自动计算")
        print("\n💡 您的定额识别系统现在具备完整的价格计算功能！")
    else:
        print("⚠️ 部分功能需要进一步检查。")
        print("💡 建议检查数据处理器的价格计算器集成是否正确。")
