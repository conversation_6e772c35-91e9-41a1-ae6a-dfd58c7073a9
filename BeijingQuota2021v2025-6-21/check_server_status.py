#!/usr/bin/env python3
"""
检查开发服务器状态
"""

import requests
import time
import sys

def check_server_status():
    """检查服务器状态"""
    print("🔍 检查开发服务器状态...")
    
    # 常见的Gradio默认端口
    ports = [7860, 7861, 7862, 7863, 7864]
    
    for port in ports:
        try:
            url = f"http://localhost:{port}"
            print(f"📡 尝试连接: {url}")
            
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ 服务器运行正常!")
                print(f"🌐 访问地址: {url}")
                print(f"📊 状态码: {response.status_code}")
                print(f"📄 页面标题: {response.text[:100]}...")
                return True
                
        except requests.exceptions.ConnectionError:
            print(f"❌ 端口 {port} 无法连接")
        except requests.exceptions.Timeout:
            print(f"⏰ 端口 {port} 连接超时")
        except Exception as e:
            print(f"⚠️ 端口 {port} 检查失败: {e}")
    
    print("❌ 未找到运行中的服务器")
    return False

def wait_for_server(max_wait=60):
    """等待服务器启动"""
    print(f"⏳ 等待服务器启动（最多等待 {max_wait} 秒）...")
    
    for i in range(max_wait):
        if check_server_status():
            return True
        
        if i < max_wait - 1:
            print(f"⏳ 等待中... ({i+1}/{max_wait})")
            time.sleep(1)
    
    return False

def main():
    """主函数"""
    print("🚀 开发服务器状态检查工具")
    print("=" * 40)
    
    # 首先直接检查
    if check_server_status():
        print("\n🎉 服务器已经在运行!")
        return True
    
    # 如果没有找到，等待启动
    print("\n⏳ 服务器可能正在启动中...")
    if wait_for_server(30):
        print("\n🎉 服务器启动成功!")
        return True
    else:
        print("\n❌ 服务器启动失败或超时")
        print("\n💡 建议:")
        print("1. 检查是否有错误信息")
        print("2. 确认所有依赖已安装")
        print("3. 检查端口是否被占用")
        print("4. 尝试重新启动服务器")
        return False

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️ 检查被用户中断")
    except Exception as e:
        print(f"\n❌ 检查过程中发生错误: {e}")
