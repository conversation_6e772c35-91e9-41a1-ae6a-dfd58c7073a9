#!/usr/bin/env python3
"""
测试MCP数据库转换功能
"""

import os
import pandas as pd
import sqlite3
from src.mcp_database_converter import MCPDatabaseConverter

def create_test_csv_files():
    """创建测试CSV文件"""
    
    print("🧪 创建数据库转换测试文件")
    print("=" * 60)
    
    # 确保output目录存在
    output_dir = "output"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    test_files = []
    
    # 文件1: 定额项目表
    quota_data = {
        '定额编号': ['D001', 'D002', 'D003', 'D004', 'D005'],
        '定额名称': [
            '人工挖一般土方 一、二类土',
            '人工挖一般土方 三、四类土', 
            '机械挖一般土方',
            '土方回填',
            '土方运输'
        ],
        '计量单位': ['100m³', '100m³', '100m³', '100m³', '100m³'],
        '基价': [1250.50, 1580.75, 980.25, 850.00, 320.50],
        '人工费': [800.00, 1000.00, 200.00, 600.00, 150.00],
        '材料费': [150.50, 180.75, 380.25, 150.00, 70.50],
        '机械费': [300.00, 400.00, 400.00, 100.00, 100.00],
        '工程类别': ['土石方工程'] * 5
    }
    quota_df = pd.DataFrame(quota_data)
    quota_file = os.path.join(output_dir, "quota_items.csv")
    quota_df.to_csv(quota_file, index=False, encoding='utf-8-sig')
    test_files.append(quota_file)
    print(f"✅ 创建定额项目表: {quota_file} ({len(quota_df)} 行)")
    
    # 文件2: 资源消耗表
    resource_data = {
        '资源编号': ['R001', 'R002', 'R003', 'R004', 'R005', 'R006'],
        '资源名称': ['综合工日', '普通工', '挖掘机', '自卸汽车', '水泥', '砂'],
        '资源类别': ['人工', '人工', '机械', '机械', '材料', '材料'],
        '计量单位': ['工日', '工日', '台班', '台班', 't', 'm³'],
        '单价': [120.00, 100.00, 800.00, 600.00, 450.00, 80.00],
        '规格型号': ['', '', 'PC200', '10t', '32.5级', '中砂'],
        '供应商': ['本地', '本地', '租赁公司A', '租赁公司B', '水泥厂A', '砂场B']
    }
    resource_df = pd.DataFrame(resource_data)
    resource_file = os.path.join(output_dir, "resource_items.csv")
    resource_df.to_csv(resource_file, index=False, encoding='utf-8-sig')
    test_files.append(resource_file)
    print(f"✅ 创建资源消耗表: {resource_file} ({len(resource_df)} 行)")
    
    # 文件3: 项目信息表
    project_data = {
        '项目ID': [1, 2, 3],
        '项目名称': ['办公楼基础工程', '住宅楼土方工程', '道路基础设施'],
        '项目地址': ['北京市朝阳区', '北京市海淀区', '北京市丰台区'],
        '开工日期': ['2024-01-15', '2024-02-01', '2024-03-01'],
        '计划完工日期': ['2024-06-15', '2024-08-01', '2024-12-01'],
        '项目经理': ['张三', '李四', '王五'],
        '预算金额': [5000000.00, 8000000.00, 12000000.00],
        '项目状态': ['进行中', '准备中', '规划中']
    }
    project_df = pd.DataFrame(project_data)
    project_file = os.path.join(output_dir, "project_info.csv")
    project_df.to_csv(project_file, index=False, encoding='utf-8-sig')
    test_files.append(project_file)
    print(f"✅ 创建项目信息表: {project_file} ({len(project_df)} 行)")
    
    return test_files

def test_csv_structure_analysis():
    """测试CSV结构分析功能"""
    
    print(f"\n" + "=" * 60)
    print("🧪 测试CSV结构分析功能")
    print("=" * 60)
    
    converter = MCPDatabaseConverter()
    
    # 创建测试文件
    test_files = create_test_csv_files()
    
    for csv_file in test_files:
        print(f"\n📄 分析文件: {os.path.basename(csv_file)}")
        
        structure = converter.analyze_csv_structure(csv_file)
        
        if structure:
            print(f"✅ 分析成功:")
            print(f"  - 行数: {structure['row_count']}")
            print(f"  - 列数: {structure['column_count']}")
            print(f"  - 列信息:")
            
            for col_name, col_info in structure['columns'].items():
                pk_indicator = " (主键候选)" if col_info['is_primary_key'] else ""
                print(f"    • {col_name}: {col_info['dtype']}, 最大长度: {col_info['max_length']}, 空值: {col_info['null_count']}{pk_indicator}")
        else:
            print(f"❌ 分析失败")
    
    return len(test_files) > 0

def test_sqlite_conversion():
    """测试SQLite转换功能"""
    
    print(f"\n" + "=" * 60)
    print("🧪 测试SQLite转换功能")
    print("=" * 60)
    
    converter = MCPDatabaseConverter()
    
    # 获取测试文件
    output_dir = "output"
    csv_files = [
        os.path.join(output_dir, "quota_items.csv"),
        os.path.join(output_dir, "resource_items.csv"),
        os.path.join(output_dir, "project_info.csv")
    ]
    
    # 过滤存在的文件
    existing_files = [f for f in csv_files if os.path.exists(f)]
    
    if not existing_files:
        print("❌ 没有找到测试CSV文件")
        return False
    
    # 转换为SQLite
    sqlite_path = os.path.join(output_dir, "test_database.db")
    
    print(f"🔄 转换 {len(existing_files)} 个CSV文件到SQLite...")
    success, message, stats = converter.convert_to_sqlite(existing_files, sqlite_path)
    
    if success:
        print(f"✅ {message}")
        print(f"📊 转换统计:")
        print(f"  - 成功文件: {stats['successful_files']}/{stats['total_files']}")
        print(f"  - 总行数: {stats['total_rows']}")
        print(f"  - 生成表:")
        
        for table_info in stats['tables']:
            print(f"    • {table_info['name']}: {table_info['rows']} 行")
        
        # 验证SQLite数据库
        print(f"\n🔍 验证SQLite数据库:")
        try:
            conn = sqlite3.connect(sqlite_path)
            cursor = conn.cursor()
            
            # 获取所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            print(f"  - 数据库文件大小: {os.path.getsize(sqlite_path) / 1024:.2f} KB")
            print(f"  - 表数量: {len(tables)}")
            
            for table_name, in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
                row_count = cursor.fetchone()[0]
                print(f"    • {table_name}: {row_count} 行")
            
            conn.close()
            return True
            
        except Exception as e:
            print(f"❌ SQLite验证失败: {e}")
            return False
    else:
        print(f"❌ {message}")
        return False

def test_sql_script_generation():
    """测试SQL脚本生成功能"""
    
    print(f"\n" + "=" * 60)
    print("🧪 测试SQL脚本生成功能")
    print("=" * 60)
    
    converter = MCPDatabaseConverter()
    
    # 获取测试文件
    output_dir = "output"
    csv_files = [
        os.path.join(output_dir, "quota_items.csv"),
        os.path.join(output_dir, "resource_items.csv")
    ]
    
    # 过滤存在的文件
    existing_files = [f for f in csv_files if os.path.exists(f)]
    
    if not existing_files:
        print("❌ 没有找到测试CSV文件")
        return False
    
    # 测试不同的SQL格式
    sql_formats = ['mysql', 'postgresql', 'sql_server']
    
    results = []
    
    for db_format in sql_formats:
        print(f"\n📋 生成 {db_format.upper()} SQL脚本:")
        
        sql_path = os.path.join(output_dir, f"test_{db_format}.sql")
        
        success, message, stats = converter.convert_to_sql_script(existing_files, sql_path, db_format)
        
        if success:
            print(f"✅ {message}")
            print(f"  - 文件: {os.path.basename(sql_path)}")
            print(f"  - 大小: {os.path.getsize(sql_path) / 1024:.2f} KB")
            print(f"  - 表数量: {len(stats['tables'])}")
            
            # 读取并分析SQL内容
            with open(sql_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            create_tables = len([line for line in lines if line.strip().upper().startswith('CREATE TABLE')])
            insert_statements = len([line for line in lines if line.strip().upper().startswith('INSERT INTO')])
            
            print(f"  - CREATE TABLE语句: {create_tables} 个")
            print(f"  - INSERT语句: {insert_statements} 个")
            
            results.append(True)
        else:
            print(f"❌ {message}")
            results.append(False)
    
    return all(results)

def test_database_preview():
    """测试数据库预览功能"""
    
    print(f"\n" + "=" * 60)
    print("🧪 测试数据库预览功能")
    print("=" * 60)
    
    converter = MCPDatabaseConverter()
    output_dir = "output"
    
    # 测试SQLite预览
    sqlite_path = os.path.join(output_dir, "test_database.db")
    if os.path.exists(sqlite_path):
        print("🔍 测试SQLite数据库预览:")
        preview_html = converter.preview_database_file(sqlite_path, 'sqlite')
        
        if "SQLite数据库预览" in preview_html and "数据库统计" in preview_html:
            print("✅ SQLite预览生成成功")
            print(f"  - HTML长度: {len(preview_html)} 字符")
            print("  - 包含数据库统计信息")
            print("  - 包含表结构和数据预览")
        else:
            print("❌ SQLite预览生成失败")
            return False
    
    # 测试SQL文件预览
    sql_path = os.path.join(output_dir, "test_mysql.sql")
    if os.path.exists(sql_path):
        print("\n🔍 测试SQL文件预览:")
        preview_html = converter.preview_database_file(sql_path, 'sql')
        
        if "SQL文件预览" in preview_html and "文件统计" in preview_html:
            print("✅ SQL文件预览生成成功")
            print(f"  - HTML长度: {len(preview_html)} 字符")
            print("  - 包含文件统计信息")
            print("  - 包含SQL内容预览")
        else:
            print("❌ SQL文件预览生成失败")
            return False
    
    return True

if __name__ == "__main__":
    print("🚀 开始测试MCP数据库转换功能")
    print("=" * 80)
    
    # 测试CSV结构分析
    analysis_success = test_csv_structure_analysis()
    
    # 测试SQLite转换
    sqlite_success = test_sqlite_conversion()
    
    # 测试SQL脚本生成
    sql_script_success = test_sql_script_generation()
    
    # 测试数据库预览
    preview_success = test_database_preview()
    
    print("\n" + "=" * 80)
    print("🎯 测试结果总结:")
    print(f"- CSV结构分析: {'✅ 正常' if analysis_success else '❌ 异常'}")
    print(f"- SQLite转换: {'✅ 正常' if sqlite_success else '❌ 异常'}")
    print(f"- SQL脚本生成: {'✅ 正常' if sql_script_success else '❌ 异常'}")
    print(f"- 数据库预览: {'✅ 正常' if preview_success else '❌ 异常'}")
    
    all_success = all([analysis_success, sqlite_success, sql_script_success, preview_success])
    
    if all_success:
        print("🎉 MCP数据库转换功能测试全部通过！")
        print("\n📋 功能特性:")
        print("- ✅ 智能CSV结构分析")
        print("- ✅ SQLite数据库转换")
        print("- ✅ 多种SQL脚本生成 (MySQL, PostgreSQL, SQL Server)")
        print("- ✅ 完整数据库预览功能")
        print("- ✅ 自动数据类型映射")
        print("- ✅ 主键检测和约束生成")
        print("- ✅ 批量文件处理")
    else:
        print("⚠️ 部分功能需要进一步检查。")
