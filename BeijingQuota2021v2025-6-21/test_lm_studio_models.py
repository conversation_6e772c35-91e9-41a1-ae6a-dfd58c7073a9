#!/usr/bin/env python3
"""
测试LM Studio模型管理功能
Test LM Studio model management functionality
"""

import sys
import os

# 添加src目录到路径
sys.path.insert(0, 'src')

def test_lm_studio_manager():
    """测试LM Studio管理器"""
    print("🧪 测试LM Studio管理器")
    print("=" * 50)
    
    try:
        from lm_studio_manager import LMStudioManager
        
        # 创建管理器实例
        manager = LMStudioManager()
        
        # 测试服务器状态
        print("📝 检查LM Studio服务器状态...")
        is_running = manager.is_server_running()
        print(f"   服务器运行状态: {'✅ 运行中' if is_running else '❌ 未运行'}")
        
        if not is_running:
            print("   💡 请启动LM Studio并加载模型后重新测试")
            return False
        
        # 获取可用模型
        print("\n📝 获取可用模型列表...")
        models = manager.get_available_models()
        print(f"   找到 {len(models)} 个模型:")
        for model in models:
            print(f"   • {model['name']} (ID: {model['id']})")
        
        # 获取视觉模型
        print("\n📝 获取视觉模型列表...")
        vision_models = manager.get_vision_models()
        print(f"   找到 {len(vision_models)} 个视觉模型:")
        for model in vision_models:
            print(f"   • {model['name']} (ID: {model['id']})")
        
        # 测试模型连接
        if vision_models:
            print("\n📝 测试第一个视觉模型连接...")
            test_model = vision_models[0]
            success, message = manager.test_model_connection(test_model['id'])
            print(f"   测试结果: {message}")
        
        # 获取服务器状态
        print("\n📝 获取详细服务器状态...")
        status = manager.get_server_status()
        print(f"   运行状态: {status['running']}")
        print(f"   状态信息: {status['message']}")
        print(f"   总模型数: {status['models_count']}")
        print(f"   视觉模型数: {status['vision_models_count']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_model_processor():
    """测试AI模型处理器的LM Studio集成"""
    print("\n🧪 测试AI模型处理器的LM Studio集成")
    print("=" * 50)
    
    try:
        from ai_model_processor import AIModelProcessor
        
        # 创建处理器实例
        processor = AIModelProcessor()
        
        # 测试刷新模型
        print("📝 刷新LM Studio模型...")
        success = processor.refresh_lm_studio_models()
        print(f"   刷新结果: {'✅ 成功' if success else '❌ 失败'}")
        
        # 获取支持的模型
        print("\n📝 获取支持的模型...")
        supported_models = processor.supported_models
        print(f"   支持的模型数: {len(supported_models)}")
        for key, name in supported_models.items():
            print(f"   • {name} (键: {key})")
        
        # 获取可用模型
        print("\n📝 获取可用模型...")
        available_models = processor.get_available_models()
        print(f"   可用模型数: {len(available_models)}")
        for key, name in available_models.items():
            print(f"   • {name} (键: {key})")
        
        # 测试模型ID转换
        print("\n📝 测试模型ID转换...")
        for key in supported_models.keys():
            if key.startswith("lm_studio_"):
                actual_id = processor.get_model_id_from_key(key)
                print(f"   {key} → {actual_id}")
        
        # 获取LM Studio状态
        print("\n📝 获取LM Studio状态...")
        lm_status = processor.get_lm_studio_status()
        print(f"   运行状态: {lm_status['running']}")
        print(f"   状态信息: {lm_status['message']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_price_info_processor():
    """测试信息价处理器的LM Studio集成"""
    print("\n🧪 测试信息价处理器的LM Studio集成")
    print("=" * 50)
    
    try:
        from intelligent_price_info_processor import IntelligentPriceInfoProcessor
        
        # 创建处理器实例
        processor = IntelligentPriceInfoProcessor()
        
        # 测试刷新模型
        print("📝 刷新LM Studio模型...")
        success = processor.refresh_lm_studio_models()
        print(f"   刷新结果: {'✅ 成功' if success else '❌ 失败'}")
        
        # 获取支持的模型
        print("\n📝 获取支持的模型...")
        supported_models = processor.supported_models
        print(f"   支持的模型数: {len(supported_models)}")
        for key, name in supported_models.items():
            print(f"   • {name} (键: {key})")
        
        # 测试模型ID转换
        print("\n📝 测试模型ID转换...")
        for key in supported_models.keys():
            if key.startswith("lm_studio_"):
                actual_id = processor.get_model_id_from_key(key)
                print(f"   {key} → {actual_id}")
        
        # 获取LM Studio状态
        print("\n📝 获取LM Studio状态...")
        lm_status = processor.get_lm_studio_status()
        print(f"   运行状态: {lm_status['running']}")
        print(f"   状态信息: {lm_status['message']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 LM Studio模型管理功能测试")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("LM Studio管理器", test_lm_studio_manager),
        ("AI模型处理器集成", test_ai_model_processor),
        ("信息价处理器集成", test_price_info_processor),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！LM Studio模型管理功能正常。")
        print("💡 主要功能:")
        print("   - ✅ 动态获取LM Studio模型列表")
        print("   - ✅ 智能识别视觉语言模型")
        print("   - ✅ 模型连接测试和状态检查")
        print("   - ✅ 与定额和信息价模块集成")
        print("🌐 现在可以在Web界面中使用更多LM Studio模型")
    elif passed >= total - 1:
        print("✅ 基本功能正常！可能有个别小问题。")
        print("💡 建议在Web界面中测试实际功能。")
    else:
        print("⚠️ 存在多个问题，需要进一步检查。")
        print("💡 请确保:")
        print("   1. LM Studio已启动并运行在 http://127.0.0.1:1234")
        print("   2. 已加载至少一个视觉语言模型")
        print("   3. 模型支持OpenAI兼容的API接口")

if __name__ == "__main__":
    main()
