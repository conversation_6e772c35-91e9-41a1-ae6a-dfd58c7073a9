#!/usr/bin/env python3
"""
测试信息价载入功能
Test price info loading functionality
"""

import sys
import os
import pandas as pd
import tempfile
import json
import sqlite3

# 添加src目录到路径
sys.path.insert(0, 'src')

def create_test_price_info_data():
    """创建测试用的信息价数据"""
    price_data = [
        {
            '资源编号': '0101010002-2',
            '产品名称': '热轧光圆钢筋',
            '规格型号及特征': 'HPB300 φ6-10',
            '计量单位': 't',
            '市场参考价（含税）': '4200.00',
            '市场参考价（不含税）': '3716.81',
            '备注': ''
        },
        {
            '资源编号': '0101010003-2',
            '产品名称': '热轧带肋钢筋',
            '规格型号及特征': 'HRB400 φ12-25',
            '计量单位': 't',
            '市场参考价（含税）': '4350.00',
            '市场参考价（不含税）': '3849.56',
            '备注': ''
        },
        {
            '资源编号': '0201010001-1',
            '产品名称': '普通硅酸盐水泥',
            '规格型号及特征': 'P.O 42.5',
            '计量单位': 't',
            '市场参考价（含税）': '380.00',
            '市场参考价（不含税）': '336.28',
            '备注': ''
        }
    ]
    return price_data

def create_test_resource_data():
    """创建测试用的定额资源数据"""
    resource_data = [
        {
            '资源编号': '0101010002-2',
            '资源名称': '热轧光圆钢筋',
            '类别': '钢材',
            '单位': 't',
            '平均单价': 4000.00,
            '最低单价': 3900.00,
            '最高单价': 4100.00,
            '使用次数': 15
        },
        {
            '资源编号': '0101010003-2',
            '资源名称': '热轧带肋钢筋',
            '类别': '钢材',
            '单位': 't',
            '平均单价': 4200.00,
            '最低单价': 4100.00,
            '最高单价': 4300.00,
            '使用次数': 25
        },
        {
            '资源编号': '0301010001-1',
            '资源名称': '砂子',
            '类别': '砂石',
            '单位': 'm³',
            '平均单价': 80.00,
            '最低单价': 75.00,
            '最高单价': 85.00,
            '使用次数': 30
        }
    ]
    return resource_data

def test_price_data_loading():
    """测试信息价数据载入功能"""
    print("🧪 测试信息价数据载入功能")
    print("=" * 50)
    
    try:
        from advanced_quota_handlers import AdvancedQuotaHandlers
        
        # 创建处理器实例
        handlers = AdvancedQuotaHandlers()
        
        # 创建临时测试文件
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建CSV格式的信息价文件
            price_data = create_test_price_info_data()
            csv_file = os.path.join(temp_dir, 'price_info_test.csv')
            
            df = pd.DataFrame(price_data)
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            print(f"📝 创建测试CSV文件: {csv_file}")
            
            # 测试从CSV载入数据
            loaded_data = handlers._load_price_data_from_file(csv_file)
            print(f"✅ 从CSV载入数据: {len(loaded_data)} 条记录")
            
            # 创建SQLite格式的信息价文件
            sqlite_file = os.path.join(temp_dir, 'price_info_test.db')
            conn = sqlite3.connect(sqlite_file)
            df.to_sql('price_info', conn, if_exists='replace', index=False)
            conn.close()
            print(f"📝 创建测试SQLite文件: {sqlite_file}")
            
            # 测试从SQLite载入数据
            loaded_data = handlers._load_price_data_from_file(sqlite_file)
            print(f"✅ 从SQLite载入数据: {len(loaded_data)} 条记录")
            
            # 创建MongoDB JSON格式的信息价文件
            json_file = os.path.join(temp_dir, 'price_info_test.json')
            mongodb_data = {
                'metadata': {
                    'export_date': '2025-06-28',
                    'source_files': 1,
                    'total_documents': len(price_data)
                },
                'collections': {
                    'price_info': {
                        'metadata': {
                            'document_count': len(price_data),
                            'source_files': ['test_price_info.csv'],
                            'last_updated': '2025-06-28'
                        },
                        'documents': price_data
                    }
                }
            }
            
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(mongodb_data, f, ensure_ascii=False, indent=2)
            print(f"📝 创建测试MongoDB JSON文件: {json_file}")
            
            # 测试从MongoDB JSON载入数据
            loaded_data = handlers._load_price_data_from_file(json_file)
            print(f"✅ 从MongoDB JSON载入数据: {len(loaded_data)} 条记录")
            
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_price_matching():
    """测试价格匹配功能"""
    print("\n🧪 测试价格匹配功能")
    print("=" * 50)
    
    try:
        from advanced_quota_handlers import AdvancedQuotaHandlers
        
        # 创建处理器实例
        handlers = AdvancedQuotaHandlers()
        
        # 创建测试数据
        resource_data = create_test_resource_data()
        price_data = create_test_price_info_data()
        
        print(f"📊 测试资源数据: {len(resource_data)} 条")
        print(f"📊 测试信息价数据: {len(price_data)} 条")
        
        # 测试匹配逻辑
        match_results = handlers._match_and_update_prices(resource_data, price_data)
        
        print(f"✅ 匹配结果:")
        print(f"   • 成功匹配: {len(match_results['matched'])} 个")
        print(f"   • 成功更新: {len(match_results['updated'])} 个")
        print(f"   • 未匹配: {len(match_results['not_matched'])} 个")
        print(f"   • 更新失败: {len(match_results['errors'])} 个")
        
        # 显示匹配详情
        if match_results['matched']:
            print("\n📋 匹配详情:")
            for item in match_results['matched']:
                print(f"   • {item['resource_code']}: {item['resource_name']}")
                print(f"     旧价格: ¥{item['old_price']:.2f} → 新价格: ¥{item['new_price']:.2f}")
        
        if match_results['not_matched']:
            print("\n⚠️ 未匹配的资源:")
            for item in match_results['not_matched']:
                print(f"   • {item['resource_code']}: {item['resource_name']}")
        
        # 测试报告生成
        status_html, stats_html = handlers._generate_price_update_report(match_results)
        print(f"\n📄 生成报告成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_database_scanning():
    """测试数据库扫描功能"""
    print("\n🧪 测试数据库扫描功能")
    print("=" * 50)
    
    try:
        from advanced_quota_handlers import AdvancedQuotaHandlers
        
        # 创建处理器实例
        handlers = AdvancedQuotaHandlers()
        
        # 确保output目录存在
        os.makedirs('output', exist_ok=True)
        
        # 创建一些测试文件
        test_files = [
            'output/price_info_test.db',
            'output/price_info_test.json',
            'output/price_info_test.csv',
            'output/信息价_20250628.db'
        ]
        
        for file_path in test_files:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('test content')
            print(f"📝 创建测试文件: {file_path}")
        
        # 测试扫描功能
        scan_result = handlers.scan_price_info_databases()
        print(f"✅ 扫描完成")
        
        # 清理测试文件
        for file_path in test_files:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"🗑️ 清理测试文件: {file_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 信息价载入功能测试")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("信息价数据载入", test_price_data_loading),
        ("价格匹配", test_price_matching),
        ("数据库扫描", test_database_scanning),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！信息价载入功能正常。")
        print("💡 主要功能:")
        print("   - ✅ 多格式信息价数据载入 (CSV、SQLite、MongoDB JSON)")
        print("   - ✅ 智能资源编号匹配")
        print("   - ✅ 自动价格更新")
        print("   - ✅ 详细的匹配报告")
        print("🌐 现在可以在Web界面中测试完整功能")
    elif passed >= total - 1:
        print("✅ 基本功能正常！可能有个别小问题。")
        print("💡 建议在Web界面中测试实际功能。")
    else:
        print("⚠️ 存在多个问题，需要进一步检查。")

if __name__ == "__main__":
    main()
