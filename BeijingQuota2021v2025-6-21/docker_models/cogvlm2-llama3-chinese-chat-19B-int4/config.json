{"_name_or_path": "/share/official_pretrains/hf_home/cogvlm2-series/cogvlm2-llama3-chinese-chat-19B", "architectures": ["CogVLMForCausalLM"], "auto_map": {"AutoConfig": "configuration_cogvlm.CogVLMConfig", "AutoModelForCausalLM": "modeling_cogvlm.CogVLMForCausalLM"}, "bos_token_id": 128000, "eos_token_id": [128001, 128009], "hidden_act": "silu", "hidden_size": 4096, "initializer_range": 0.02, "intermediate_size": 14336, "max_position_embeddings": 8192, "num_attention_heads": 32, "num_hidden_layers": 32, "num_multi_query_heads": 8, "pad_token_id": 128002, "quantization_config": {"_load_in_4bit": true, "_load_in_8bit": false, "bnb_4bit_compute_dtype": "float32", "bnb_4bit_quant_storage": "uint8", "bnb_4bit_quant_type": "fp4", "bnb_4bit_use_double_quant": false, "llm_int8_enable_fp32_cpu_offload": false, "llm_int8_has_fp16_weight": false, "llm_int8_skip_modules": null, "llm_int8_threshold": 6.0, "load_in_4bit": true, "load_in_8bit": false, "quant_method": "bitsandbytes"}, "rms_norm_eps": 1e-05, "template_version": "chat", "tie_word_embeddings": false, "torch_dtype": "float16", "transformers_version": "4.42.0.dev0", "use_cache": true, "vision_config": {"dropout_prob": 0.0, "hidden_act": "gelu", "hidden_size": 1792, "image_size": 1344, "in_channels": 3, "intermediate_size": 15360, "layer_norm_eps": 1e-06, "num_heads": 16, "num_hidden_layers": 63, "num_positions": 9217, "patch_size": 14}, "vocab_size": 128256}