#!/usr/bin/env python3
"""
GLM-4V集成测试脚本

测试智谱AI GLM-4V模型在北京定额识别系统中的集成情况

使用方法:
1. 设置环境变量 ZHIPU_API_KEY
2. 运行: python test_glm4v_integration.py
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

# 直接导入，避免相对导入问题
import importlib.util
spec = importlib.util.spec_from_file_location("ai_model_processor", "src/ai_model_processor.py")
ai_module = importlib.util.module_from_spec(spec)
sys.modules["ai_model_processor"] = ai_module
spec.loader.exec_module(ai_module)

AIModelProcessor = ai_module.AIModelProcessor

async def test_glm4v_connection():
    """测试GLM-4V连接"""
    print("🔍 测试GLM-4V连接...")
    
    # 检查API密钥
    api_key = os.getenv("ZHIPU_API_KEY")
    if not api_key:
        print("❌ 错误: 未设置ZHIPU_API_KEY环境变量")
        print("💡 请先设置智谱AI API密钥:")
        print("   export ZHIPU_API_KEY='your_api_key_here'")
        print("   或在Windows中: set ZHIPU_API_KEY=your_api_key_here")
        return False
    
    print(f"✅ API密钥已设置: ***{api_key[-4:]}")
    
    # 初始化AI处理器
    processor = AIModelProcessor()
    
    # 测试连接
    try:
        success, message = await processor._test_glm_4v_connection()
        if success:
            print(f"✅ {message}")
            return True
        else:
            print(f"❌ {message}")
            return False
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

async def test_glm4v_image_recognition():
    """测试GLM-4V图像识别"""
    print("\n🖼️ 测试GLM-4V图像识别...")
    
    # 查找测试图片
    test_image_paths = [
        "test_images/sample.jpg",
        "test_images/sample.png",
        "stored_pdfs/test.jpg",
        "stored_pdfs/test.png"
    ]
    
    test_image = None
    for path in test_image_paths:
        if os.path.exists(path):
            test_image = path
            break
    
    if not test_image:
        print("⚠️ 未找到测试图片，跳过图像识别测试")
        print("💡 可以将测试图片放在以下位置:")
        for path in test_image_paths:
            print(f"   - {path}")
        return False
    
    print(f"📁 使用测试图片: {test_image}")
    
    # 初始化AI处理器
    processor = AIModelProcessor()
    
    # 测试图像识别
    try:
        result = await processor.process_image_with_glm_4v(
            image_path=test_image,
            volume_code="测试分册",
            chapter_codes="测试章节"
        )
        
        if result:
            print("✅ 图像识别成功!")
            print("📄 识别结果:")
            print("-" * 50)
            print(result)
            print("-" * 50)
            return True
        else:
            print("❌ 图像识别失败")
            return False
            
    except Exception as e:
        print(f"❌ 图像识别测试失败: {e}")
        return False

def test_model_availability():
    """测试模型可用性"""
    print("\n📋 测试模型可用性...")
    
    processor = AIModelProcessor()
    
    # 检查基础模型列表
    base_models = processor.base_models
    print("🎯 基础模型列表:")
    for key, name in base_models.items():
        print(f"   - {key}: {name}")
    
    # 检查可用模型
    available_models = processor.get_available_models()
    print("\n✅ 可用模型列表:")
    for key, name in available_models.items():
        print(f"   - {key}: {name}")
    
    # 检查GLM-4V是否在列表中
    if "glm_4v" in base_models:
        print("\n✅ GLM-4V已添加到基础模型列表")
    else:
        print("\n❌ GLM-4V未在基础模型列表中")
    
    if "glm_4v" in available_models:
        print("✅ GLM-4V在可用模型列表中")
    else:
        print("⚠️ GLM-4V不在可用模型列表中 (可能是API密钥未配置)")

async def main():
    """主测试函数"""
    print("🎯 GLM-4V集成测试")
    print("=" * 50)
    
    # 测试1: 模型可用性
    test_model_availability()
    
    # 测试2: 连接测试
    connection_ok = await test_glm4v_connection()
    
    # 测试3: 图像识别测试 (仅在连接成功时进行)
    if connection_ok:
        await test_glm4v_image_recognition()
    
    print("\n" + "=" * 50)
    print("🎉 GLM-4V集成测试完成!")
    
    if connection_ok:
        print("✅ GLM-4V已成功集成到北京定额识别系统")
        print("💡 现在可以在主界面中选择'智谱AI GLM-4V'模型进行识别")
    else:
        print("⚠️ GLM-4V集成存在问题，请检查API密钥配置")

if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
