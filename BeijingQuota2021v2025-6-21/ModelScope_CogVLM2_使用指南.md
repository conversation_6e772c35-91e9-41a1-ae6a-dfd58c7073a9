# ModelScope CogVLM2 集成使用指南

## 📋 概述

本项目已集成 ModelScope 的 CogVLM2-Llama3-Chinese-Chat-19B-Int4 模型，用于定额识别和信息价识别。该模型是智谱AI开发的多模态大语言模型，专门优化了中文理解能力。

## 🔧 环境准备

### 1. 安装依赖

```bash
# 安装ModelScope
pip install modelscope

# 安装PyTorch (根据您的CUDA版本选择)
# CPU版本
pip install torch torchvision torchaudio

# GPU版本 (CUDA 11.8)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# GPU版本 (CUDA 12.1)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

# 其他必要依赖
pip install transformers pillow
```

### 2. 下载模型

```bash
# 使用ModelScope CLI下载模型
modelscope download --model ZhipuAI/cogvlm2-llama3-chinese-chat-19B-int4

# 或者使用Python代码下载
python -c "
from modelscope import snapshot_download
model_dir = snapshot_download('ZhipuAI/cogvlm2-llama3-chinese-chat-19B-int4')
print(f'模型下载到: {model_dir}')
"
```

## 🎯 模型特性

### 技术规格
- **基座模型**: Meta-Llama-3-8B-Instruct
- **参数量**: 19B (量化后约10GB显存)
- **量化方式**: Int4量化
- **支持语言**: 中文优化
- **输入格式**: 图片 + 文本
- **最大输出**: 4096 tokens

### 硬件要求
- **最低显存**: 10GB (推荐12GB+)
- **推荐GPU**: RTX 3080/4070以上
- **CPU模式**: 支持但速度较慢
- **内存**: 16GB+ (CPU模式需要32GB+)

## 🚀 使用方法

### 1. 在定额识别中使用

1. 启动应用后，进入"AI定额识别"页面
2. 在"AI模型选择"下拉框中选择"ModelScope-CogVLM2-Llama3-Chinese-19B-Int4"
3. 点击"🔧 测试模型连接"验证模型可用性
4. 上传PDF文件并开始识别

### 2. 在信息价识别中使用

1. 进入"信息价识别"页面
2. 在"AI模型类型"中选择"ModelScope-CogVLM2-Llama3-Chinese-19B-Int4"
3. 点击"测试模型连接"确认可用
4. 上传PDF文件并开始识别

## 🔍 模型性能

### 优势
- ✅ **中文优化**: 专门针对中文场景优化
- ✅ **表格理解**: 对复杂表格结构有良好理解
- ✅ **本地部署**: 无需API密钥，数据安全
- ✅ **成本控制**: 一次下载，无限使用

### 注意事项
- ⚠️ **首次加载**: 模型首次加载需要1-2分钟
- ⚠️ **显存占用**: 需要足够的GPU显存
- ⚠️ **处理速度**: 比在线API稍慢，但更稳定

## 🛠️ 故障排除

### 常见问题

#### 1. 模型未找到
```
错误: ModelScope CogVLM2模型不可用
解决: 确保已下载模型到本地
命令: modelscope download --model ZhipuAI/cogvlm2-llama3-chinese-chat-19B-int4
```

#### 2. 显存不足
```
错误: CUDA out of memory
解决方案:
1. 关闭其他GPU程序
2. 使用CPU模式 (较慢)
3. 升级GPU硬件
```

#### 3. 依赖缺失
```
错误: No module named 'modelscope'
解决: pip install modelscope torch transformers
```

#### 4. 模型加载失败
```
错误: 模型加载失败
解决方案:
1. 检查网络连接
2. 重新下载模型
3. 检查磁盘空间 (需要20GB+)
```

## 📊 性能对比

| 模型 | 准确率 | 速度 | 成本 | 数据安全 |
|------|--------|------|------|----------|
| QVQ-Max (在线) | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 💰💰💰 | ⚠️ |
| CogVLM2 (本地) | ⭐⭐⭐⭐ | ⭐⭐⭐ | 💰 | ✅ |
| LM Studio | ⭐⭐⭐ | ⭐⭐ | 💰 | ✅ |

## 🔄 更新日志

### v1.0.0 (2025-06-30)
- ✅ 集成ModelScope CogVLM2模型
- ✅ 支持定额识别和信息价识别
- ✅ 自动检测模型可用性
- ✅ GPU/CPU自适应运行
- ✅ 内存优化和清理

## 📞 技术支持

如果遇到问题，请：
1. 检查本指南的故障排除部分
2. 查看控制台日志输出
3. 确认硬件配置满足要求
4. 验证模型下载完整性

## 🔗 相关链接

- [ModelScope官网](https://modelscope.cn/)
- [CogVLM2模型页面](https://modelscope.cn/models/ZhipuAI/cogvlm2-llama3-chinese-chat-19B-int4)
- [智谱AI官网](https://www.zhipuai.cn/)
- [PyTorch安装指南](https://pytorch.org/get-started/locally/)
