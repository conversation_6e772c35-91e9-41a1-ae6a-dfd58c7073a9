#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库创建流程诊断脚本
检查从CSV到数据库创建的完整流程
"""

import os
import sys
import tempfile
import psycopg2
from pathlib import Path

def check_csv_files():
    """检查CSV文件"""
    print("🔍 检查CSV文件...")
    
    output_dir = Path("output")
    csv_files = []
    
    if output_dir.exists():
        for csv_file in output_dir.glob("*.csv"):
            csv_files.append(str(csv_file))
            print(f"   找到CSV: {csv_file}")
    
    if not csv_files:
        print("❌ 未找到CSV文件")
        return []
    
    print(f"✅ 找到 {len(csv_files)} 个CSV文件")
    return csv_files

def test_mcp_converter(csv_files):
    """测试MCP转换器"""
    print("\n🔍 测试MCP转换器...")
    
    try:
        from src.mcp_database_converter import MCPDatabaseConverter
        
        converter = MCPDatabaseConverter()
        
        # 创建临时SQL文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.sql', delete=False) as temp_file:
            temp_sql_path = temp_file.name
        
        print(f"   临时SQL文件: {temp_sql_path}")
        
        # 生成SQL脚本
        success, message, stats = converter.convert_to_sql_script(
            csv_files, temp_sql_path, 'postgresql'
        )
        
        if success:
            print(f"✅ SQL脚本生成成功: {message}")
            print(f"📊 统计信息: {stats}")
            
            # 检查SQL文件内容
            with open(temp_sql_path, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            print(f"📄 SQL文件大小: {len(sql_content)} 字符")
            
            # 显示SQL文件前几行
            lines = sql_content.split('\n')[:20]
            print("📋 SQL文件前20行:")
            for i, line in enumerate(lines, 1):
                print(f"   {i:2d}: {line}")
            
            # 统计SQL语句数量
            statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            print(f"📊 SQL语句数量: {len(statements)}")
            
            return True, temp_sql_path, sql_content, stats
        else:
            print(f"❌ SQL脚本生成失败: {message}")
            return False, None, None, None
            
    except Exception as e:
        print(f"❌ MCP转换器测试失败: {e}")
        return False, None, None, None

def test_postgresql_execution(sql_content, stats):
    """测试PostgreSQL执行"""
    print("\n🔍 测试PostgreSQL执行...")
    
    try:
        from src.config_persistence_manager import ConfigPersistenceManager
        
        config_manager = ConfigPersistenceManager()
        config = config_manager.load_config()
        
        quota_db_config = config.get('database_configs', {}).get('quota_db', {})
        
        # 连接到数据库
        conn = psycopg2.connect(
            host=quota_db_config.get('host', 'localhost'),
            port=int(quota_db_config.get('port', 5432)),
            user=quota_db_config.get('username', 'postgres'),
            password=quota_db_config.get('password', ''),
            database=quota_db_config.get('db_name', 'beijing2021_quota_database'),
            client_encoding='utf8'
        )
        
        cursor = conn.cursor()
        
        # 清空现有表
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
        """)
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        for table in existing_tables:
            cursor.execute(f'DROP TABLE IF EXISTS "{table}" CASCADE')
            print(f"🗑️ 删除现有表: {table}")
        
        conn.commit()
        
        # 分割并执行SQL语句
        lines = sql_content.split('\n')
        clean_lines = []
        for line in lines:
            line = line.strip()
            if line and not line.startswith('--'):
                clean_lines.append(line)
        
        # 分割SQL语句
        sql_statements = []
        current_statement = []
        
        for line in clean_lines:
            current_statement.append(line)
            if line.endswith(';'):
                stmt = '\n'.join(current_statement).strip()
                if stmt:
                    sql_statements.append(stmt)
                current_statement = []
        
        print(f"📊 准备执行 {len(sql_statements)} 个SQL语句")
        
        executed_count = 0
        create_table_count = 0
        insert_count = 0
        
        for i, stmt in enumerate(sql_statements):
            if stmt.strip():
                try:
                    print(f"   执行语句 {i+1}: {stmt[:50]}...")
                    cursor.execute(stmt)
                    executed_count += 1
                    
                    if stmt.upper().startswith('CREATE TABLE'):
                        create_table_count += 1
                    elif stmt.upper().startswith('INSERT INTO'):
                        insert_count += 1
                        
                except Exception as e:
                    print(f"❌ SQL执行错误 {i+1}: {e}")
                    print(f"   语句: {stmt[:100]}...")
        
        conn.commit()
        
        print(f"✅ 成功执行 {executed_count} 个SQL语句")
        print(f"   CREATE TABLE: {create_table_count} 个")
        print(f"   INSERT INTO: {insert_count} 个")
        
        # 获取最终统计
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
        """)
        final_tables = [row[0] for row in cursor.fetchall()]
        
        total_rows = 0
        for table in final_tables:
            cursor.execute(f'SELECT COUNT(*) FROM "{table}"')
            row_count = cursor.fetchone()[0]
            total_rows += row_count
            print(f"   表 {table}: {row_count} 行")
        
        print(f"📊 最终统计:")
        print(f"   表数量: {len(final_tables)}")
        print(f"   总行数: {total_rows}")
        
        cursor.close()
        conn.close()
        
        return True, len(final_tables), total_rows
        
    except Exception as e:
        print(f"❌ PostgreSQL执行测试失败: {e}")
        return False, 0, 0

def test_csv_analysis():
    """测试CSV文件分析"""
    print("\n🔍 测试CSV文件分析...")
    
    try:
        from src.mcp_database_converter import MCPDatabaseConverter
        import pandas as pd
        
        converter = MCPDatabaseConverter()
        output_dir = Path("output")
        
        for csv_file in output_dir.glob("*.csv"):
            print(f"\n📄 分析文件: {csv_file}")
            
            # 直接读取CSV
            try:
                df = pd.read_csv(csv_file, encoding='utf-8-sig')
                print(f"   行数: {len(df)}")
                print(f"   列数: {len(df.columns)}")
                print(f"   列名: {list(df.columns)}")
                
                # 显示前几行数据
                print("   前3行数据:")
                for i, row in df.head(3).iterrows():
                    print(f"     行{i+1}: {dict(row)}")
                
            except Exception as e:
                print(f"   ❌ 直接读取失败: {e}")
            
            # 使用MCP分析
            try:
                structure = converter.analyze_csv_structure(str(csv_file))
                if structure:
                    print(f"   MCP分析成功:")
                    print(f"     行数: {structure['row_count']}")
                    print(f"     列数: {structure['column_count']}")
                    print(f"     列信息: {list(structure['columns'].keys())}")
                else:
                    print(f"   ❌ MCP分析失败")
            except Exception as e:
                print(f"   ❌ MCP分析异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ CSV分析测试失败: {e}")
        return False

def main():
    """主诊断流程"""
    print("🔧 数据库创建流程诊断工具")
    print("=" * 60)
    
    # 1. 检查CSV文件
    csv_files = check_csv_files()
    if not csv_files:
        return
    
    # 2. 测试CSV分析
    if not test_csv_analysis():
        return
    
    # 3. 测试MCP转换器
    success, temp_sql_path, sql_content, stats = test_mcp_converter(csv_files)
    if not success:
        return
    
    # 4. 测试PostgreSQL执行
    success, table_count, row_count = test_postgresql_execution(sql_content, stats)
    
    # 5. 清理临时文件
    if temp_sql_path and os.path.exists(temp_sql_path):
        os.unlink(temp_sql_path)
        print(f"🗑️ 清理临时文件: {temp_sql_path}")
    
    print("\n" + "=" * 60)
    
    if success and table_count > 0 and row_count > 0:
        print("🎉 数据库创建流程诊断完成！")
        print("✅ 所有步骤都正常工作")
        print(f"✅ 成功创建 {table_count} 个表")
        print(f"✅ 成功插入 {row_count} 行数据")
    else:
        print("❌ 数据库创建流程存在问题")
        print("💡 请检查上述错误信息")

if __name__ == "__main__":
    main()
