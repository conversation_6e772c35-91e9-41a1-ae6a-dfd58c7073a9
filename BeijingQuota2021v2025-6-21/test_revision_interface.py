#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
定额修订界面测试脚本
测试定额修订功能的各个组件
"""

def test_revision_processor():
    """测试定额修订处理器"""
    try:
        print("🔍 测试定额修订处理器...")
        
        from src.quota_revision_processor import QuotaRevisionProcessor
        from src.advanced_quota_manager import AdvancedQuotaManager
        
        # 创建高级定额管理器
        quota_manager = AdvancedQuotaManager()
        
        # 检查修订处理器是否正确初始化
        if quota_manager.revision_processor:
            print("✅ 定额修订处理器初始化成功")
            
            # 测试PDF文件获取
            pdf_files = quota_manager.revision_processor.get_available_pdfs()
            print(f"📁 发现PDF文件: {len(pdf_files)} 个")
            for pdf in pdf_files:
                print(f"   - {pdf}")
            
            return True
        else:
            print("❌ 定额修订处理器未初始化")
            return False
            
    except Exception as e:
        print(f"❌ 测试定额修订处理器失败: {e}")
        return False

def test_database_connection():
    """测试数据库连接"""
    try:
        print("\n🔍 测试数据库连接...")
        
        from src.config_persistence_manager import ConfigPersistenceManager
        import psycopg2
        
        config_manager = ConfigPersistenceManager()
        config = config_manager.load_config()
        
        quota_db_config = config.get('database_configs', {}).get('quota_db', {})
        
        if quota_db_config.get('db_type') == 'postgresql':
            print("📊 PostgreSQL配置:")
            print(f"   主机: {quota_db_config.get('host')}")
            print(f"   端口: {quota_db_config.get('port')}")
            print(f"   数据库: {quota_db_config.get('db_name')}")
            print(f"   用户: {quota_db_config.get('username')}")
            
            # 测试连接
            conn = psycopg2.connect(
                host=quota_db_config.get('host', 'localhost'),
                port=int(quota_db_config.get('port', 5432)),
                user=quota_db_config.get('username', 'postgres'),
                password=quota_db_config.get('password', ''),
                database=quota_db_config.get('db_name', 'beijing2021_quota_database'),
                client_encoding='utf8'
            )
            
            cursor = conn.cursor()
            
            # 检查表
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
            """)
            tables = [row[0] for row in cursor.fetchall()]
            
            print(f"✅ 数据库连接成功")
            print(f"📊 现有表: {tables}")
            
            # 检查数据
            if 'parent_quotas' in tables:
                cursor.execute("SELECT COUNT(*) FROM parent_quotas")
                quota_count = cursor.fetchone()[0]
                print(f"📋 定额项数量: {quota_count}")
            
            if 'child_resources' in tables:
                cursor.execute("SELECT COUNT(*) FROM child_resources")
                resource_count = cursor.fetchone()[0]
                print(f"🔧 资源项数量: {resource_count}")
            
            cursor.close()
            conn.close()
            
            return True
            
        else:
            print("⚠️ 未配置PostgreSQL数据库")
            return False
            
    except Exception as e:
        print(f"❌ 测试数据库连接失败: {e}")
        return False

def test_interface_components():
    """测试界面组件"""
    try:
        print("\n🔍 测试界面组件...")
        
        from src.advanced_quota_interface import AdvancedQuotaInterface
        from src.advanced_quota_manager import AdvancedQuotaManager
        
        # 创建管理器和界面
        quota_manager = AdvancedQuotaManager()
        interface = AdvancedQuotaInterface(quota_manager)
        
        print("✅ 界面组件创建成功")
        
        # 测试连接检查方法
        if hasattr(interface, '_handle_check_connection'):
            print("✅ 连接检查方法存在")
            
            # 模拟调用
            try:
                result = interface._handle_check_connection()
                print(f"📊 连接检查结果: {len(result)} 个返回值")
            except Exception as e:
                print(f"⚠️ 连接检查调用异常: {e}")
        
        # 测试数据加载方法
        if hasattr(interface, '_handle_load_revision_data'):
            print("✅ 数据加载方法存在")
            
            try:
                result = interface._handle_load_revision_data()
                print(f"📊 数据加载结果: {len(result)} 个返回值")
            except Exception as e:
                print(f"⚠️ 数据加载调用异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试界面组件失败: {e}")
        return False

def main():
    """主测试流程"""
    print("🔧 定额修订界面测试工具")
    print("=" * 50)
    
    all_tests_passed = True
    
    # 1. 测试修订处理器
    if not test_revision_processor():
        all_tests_passed = False
    
    # 2. 测试数据库连接
    if not test_database_connection():
        all_tests_passed = False
    
    # 3. 测试界面组件
    if not test_interface_components():
        all_tests_passed = False
    
    print("\n" + "=" * 50)
    
    if all_tests_passed:
        print("🎉 所有测试通过！")
        print("✅ 定额修订功能组件正常")
        print("✅ 数据库连接正常")
        print("✅ 界面组件正常")
        
        print("\n💡 现在可以:")
        print("1. 在程序中使用定额修订功能")
        print("2. 检查数据库连接状态")
        print("3. 加载和编辑定额数据")
        print("4. 预览PDF文件")
    else:
        print("❌ 部分测试失败")
        print("💡 请检查失败的项目")

if __name__ == "__main__":
    main()
