#!/usr/bin/env python3
"""
PostgreSQL价格计算验证工具
验证定额项价格与资源的对应关系和计算公式
"""

import psycopg2
import pandas as pd
from typing import Dict, List, Tuple
import sys

def connect_postgresql():
    """连接PostgreSQL数据库"""
    try:
        # 尝试不同的数据库名称（按优先级排序）
        database_names = ['enterprise_quota.db', 'Beijing2021_quota.db', 'enterprise_quota', 'beijing2021_quota.db']

        for db_name in database_names:
            try:
                print(f"🔍 尝试连接数据库: {db_name}")
                conn = psycopg2.connect(
                    host='localhost',
                    port=5432,
                    user='postgres',
                    password='postgres123',  # 根据实际情况修改
                    database=db_name,
                    client_encoding='utf8'  # 明确指定编码
                )
                print(f"✅ 成功连接到数据库: {db_name}")
                return conn
            except psycopg2.OperationalError as e:
                if "does not exist" in str(e):
                    print(f"⚠️ 数据库 {db_name} 不存在")
                    continue
                else:
                    print(f"❌ 连接数据库 {db_name} 失败: {e}")
                    continue
            except Exception as e:
                print(f"❌ 连接数据库 {db_name} 时发生错误: {e}")
                continue

        print("❌ 所有数据库连接尝试都失败了")
        return None

    except Exception as e:
        print(f"❌ 连接PostgreSQL失败: {e}")
        return None

def get_table_names(conn) -> List[str]:
    """获取数据库中的表名"""
    try:
        cursor = conn.cursor()
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_type = 'BASE TABLE'
        """)
        tables = [row[0] for row in cursor.fetchall()]
        cursor.close()
        return tables
    except Exception as e:
        print(f"❌ 获取表名失败: {e}")
        return []

def get_parent_quotas_data(conn, table_name: str) -> pd.DataFrame:
    """获取定额项数据"""
    try:
        query = f"SELECT * FROM {table_name} ORDER BY 编号"
        df = pd.read_sql_query(query, conn)
        return df
    except Exception as e:
        print(f"❌ 获取定额项数据失败: {e}")
        return pd.DataFrame()

def get_child_resources_data(conn, table_name: str) -> pd.DataFrame:
    """获取资源数据"""
    try:
        query = f"SELECT * FROM {table_name} ORDER BY 定额编号, 资源编号"
        df = pd.read_sql_query(query, conn)
        return df
    except Exception as e:
        print(f"❌ 获取资源数据失败: {e}")
        return pd.DataFrame()

def verify_price_calculations(parent_df: pd.DataFrame, child_df: pd.DataFrame) -> Dict:
    """验证价格计算关系"""
    verification_results = {
        'total_quotas': len(parent_df),
        'verified_quotas': 0,
        'calculation_errors': [],
        'missing_resources': [],
        'correct_calculations': [],
        'price_breakdown': {}
    }
    
    print("🔍 开始验证价格计算关系...")
    print("=" * 60)
    
    for _, parent_row in parent_df.iterrows():
        quota_code = parent_row['编号']
        comprehensive_price = float(parent_row.get('综合单价（元/单位）', 0))
        labor_cost = float(parent_row.get('人工费', 0))
        material_cost = float(parent_row.get('材料费', 0))
        machinery_cost = float(parent_row.get('机械费', 0))
        other_cost = float(parent_row.get('其他费用', 0))
        
        # 获取该定额项的所有资源
        quota_resources = child_df[child_df['定额编号'] == quota_code]
        
        if quota_resources.empty:
            verification_results['missing_resources'].append(quota_code)
            continue
        
        # 计算资源总价
        calculated_total = 0.0
        category_totals = {'人工': 0.0, '材料': 0.0, '机械': 0.0, '其他': 0.0}
        
        for _, resource_row in quota_resources.iterrows():
            consumption = float(resource_row.get('消耗量', 0))
            unit_price = float(resource_row.get('单价', 0))
            total_price = float(resource_row.get('合价', 0))
            category = resource_row.get('类别', '其他')
            
            # 验证合价计算: 合价 = 消耗量 × 单价
            expected_total = consumption * unit_price
            if abs(total_price - expected_total) > 0.01:  # 允许0.01的误差
                verification_results['calculation_errors'].append({
                    'quota_code': quota_code,
                    'resource_code': resource_row.get('资源编号', ''),
                    'error_type': '资源合价计算错误',
                    'expected': expected_total,
                    'actual': total_price,
                    'consumption': consumption,
                    'unit_price': unit_price
                })
            
            calculated_total += total_price
            
            # 按类别统计
            if category in category_totals:
                category_totals[category] += total_price
            else:
                category_totals['其他'] += total_price
        
        # 验证综合单价计算
        if abs(comprehensive_price - calculated_total) > 0.01:
            verification_results['calculation_errors'].append({
                'quota_code': quota_code,
                'error_type': '综合单价计算错误',
                'expected': calculated_total,
                'actual': comprehensive_price,
                'difference': abs(comprehensive_price - calculated_total)
            })
        else:
            verification_results['correct_calculations'].append(quota_code)
            verification_results['verified_quotas'] += 1
        
        # 验证分类费用
        category_errors = []
        if abs(labor_cost - category_totals['人工']) > 0.01:
            category_errors.append(f"人工费: 期望{category_totals['人工']}, 实际{labor_cost}")
        if abs(material_cost - category_totals['材料']) > 0.01:
            category_errors.append(f"材料费: 期望{category_totals['材料']}, 实际{material_cost}")
        if abs(machinery_cost - category_totals['机械']) > 0.01:
            category_errors.append(f"机械费: 期望{category_totals['机械']}, 实际{machinery_cost}")
        if abs(other_cost - category_totals['其他']) > 0.01:
            category_errors.append(f"其他费用: 期望{category_totals['其他']}, 实际{other_cost}")
        
        if category_errors:
            verification_results['calculation_errors'].append({
                'quota_code': quota_code,
                'error_type': '分类费用计算错误',
                'errors': category_errors
            })
        
        # 保存价格分解信息
        verification_results['price_breakdown'][quota_code] = {
            'comprehensive_price': comprehensive_price,
            'calculated_total': calculated_total,
            'labor_cost': labor_cost,
            'material_cost': material_cost,
            'machinery_cost': machinery_cost,
            'other_cost': other_cost,
            'category_totals': category_totals,
            'resource_count': len(quota_resources)
        }
    
    return verification_results

def print_verification_results(results: Dict):
    """打印验证结果"""
    print("\n📊 价格计算验证结果")
    print("=" * 60)
    
    print(f"📋 总定额项数: {results['total_quotas']}")
    print(f"✅ 验证通过: {results['verified_quotas']}")
    print(f"❌ 计算错误: {len(results['calculation_errors'])}")
    print(f"⚠️ 缺少资源: {len(results['missing_resources'])}")
    
    accuracy = (results['verified_quotas'] / results['total_quotas'] * 100) if results['total_quotas'] > 0 else 0
    print(f"🎯 计算准确率: {accuracy:.1f}%")
    
    if results['missing_resources']:
        print(f"\n⚠️ 缺少资源的定额项:")
        for quota_code in results['missing_resources'][:10]:  # 只显示前10个
            print(f"   - {quota_code}")
        if len(results['missing_resources']) > 10:
            print(f"   ... 还有 {len(results['missing_resources']) - 10} 个")
    
    if results['calculation_errors']:
        print(f"\n❌ 计算错误详情:")
        for error in results['calculation_errors'][:5]:  # 只显示前5个错误
            print(f"   - 定额项: {error['quota_code']}")
            print(f"     错误类型: {error['error_type']}")
            if 'expected' in error and 'actual' in error:
                print(f"     期望值: {error['expected']:.4f}, 实际值: {error['actual']:.4f}")
            if 'errors' in error:
                for err in error['errors']:
                    print(f"     {err}")
            print()
        if len(results['calculation_errors']) > 5:
            print(f"   ... 还有 {len(results['calculation_errors']) - 5} 个错误")
    
    if results['correct_calculations']:
        print(f"\n✅ 计算正确的定额项示例:")
        for quota_code in results['correct_calculations'][:5]:
            breakdown = results['price_breakdown'][quota_code]
            print(f"   - {quota_code}: 综合单价 {breakdown['comprehensive_price']:.4f} 元")
            print(f"     人工费: {breakdown['labor_cost']:.4f}, 材料费: {breakdown['material_cost']:.4f}")
            print(f"     机械费: {breakdown['machinery_cost']:.4f}, 其他费用: {breakdown['other_cost']:.4f}")
            print(f"     关联资源数: {breakdown['resource_count']} 个")

def list_databases():
    """列出所有可用的数据库"""
    try:
        print("🔍 查找可用的数据库...")
        conn = psycopg2.connect(
            host='localhost',
            port=5432,
            user='postgres',
            password='postgres123',
            database='postgres',  # 连接到默认数据库
            client_encoding='utf8'
        )

        cursor = conn.cursor()
        cursor.execute("SELECT datname FROM pg_database WHERE datistemplate = false")
        databases = [row[0] for row in cursor.fetchall()]
        cursor.close()
        conn.close()

        print("📋 可用的数据库:")
        for db in databases:
            print(f"   - {db}")

        return databases

    except Exception as e:
        print(f"❌ 获取数据库列表失败: {e}")
        return []

def main():
    """主函数"""
    print("🔍 PostgreSQL价格计算验证工具")
    print("=" * 60)

    # 首先列出所有数据库
    databases = list_databases()

    # 连接数据库
    conn = connect_postgresql()
    if not conn:
        print("\n💡 提示:")
        print("1. 请确保PostgreSQL服务正在运行")
        print("2. 请确保用户名和密码正确")
        print("3. 请确保已创建企业定额数据库")
        return False
    
    try:
        # 获取表名
        tables = get_table_names(conn)
        print(f"📋 找到 {len(tables)} 个表:")
        for table in tables:
            print(f"   - {table}")
        
        # 查找最新的处理表
        parent_table = None
        child_table = None
        
        for table in tables:
            if table.startswith('processed_parent_quotas_'):
                parent_table = table
            elif table.startswith('processed_child_resources_'):
                child_table = table
        
        if not parent_table or not child_table:
            print("❌ 未找到处理后的定额项和资源表")
            return False
        
        print(f"\n🔍 验证表格:")
        print(f"   - 定额项表: {parent_table}")
        print(f"   - 资源表: {child_table}")
        
        # 获取数据
        parent_df = get_parent_quotas_data(conn, parent_table)
        child_df = get_child_resources_data(conn, child_table)
        
        if parent_df.empty or child_df.empty:
            print("❌ 无法获取数据")
            return False
        
        print(f"\n📊 数据统计:")
        print(f"   - 定额项记录: {len(parent_df)} 条")
        print(f"   - 资源记录: {len(child_df)} 条")
        
        # 验证价格计算
        results = verify_price_calculations(parent_df, child_df)
        
        # 打印结果
        print_verification_results(results)
        
        return True
        
    finally:
        conn.close()

if __name__ == "__main__":
    main()
