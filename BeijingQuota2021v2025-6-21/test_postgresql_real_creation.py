#!/usr/bin/env python3
"""
测试PostgreSQL数据库的实际创建
"""

import os
import sys
import pandas as pd
from pathlib import Path

# 添加src目录到路径
sys.path.append('src')

def create_test_data():
    """创建测试数据文件"""
    # 确保output目录存在
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    # 创建parent_quotas测试数据
    parent_data = {
        '定额编号': ['001-001', '001-002', '001-003'],
        '定额名称': ['混凝土浇筑C30', '钢筋绑扎HPB300', '模板安装拆除'],
        '单位': ['m³', 'kg', 'm²'],
        '人工费': [120.5, 85.2, 95.8],
        '材料费': [450.8, 320.5, 180.2],
        '机械费': [80.3, 45.8, 25.6],
        '合计': [651.6, 451.5, 301.6]
    }
    
    parent_df = pd.DataFrame(parent_data)
    parent_csv_path = output_dir / "parent_quotas_pg_test.csv"
    parent_df.to_csv(parent_csv_path, index=False, encoding='utf-8-sig')
    
    # 创建child_resources测试数据
    child_data = {
        '定额编号': ['001-001', '001-001', '001-002'],
        '资源编号': ['R001', 'R002', 'R003'],
        '资源名称': ['C30混凝土', '人工', 'HPB300钢筋'],
        '资源类型': ['材料', '人工', '材料'],
        '数量': [1.05, 8.5, 1.02],
        '单位': ['m³', '工日', 'kg'],
        '单价': [420.0, 150.0, 4.2],
        '合价': [441.0, 1275.0, 4.284]
    }
    
    child_df = pd.DataFrame(child_data)
    child_csv_path = output_dir / "child_resources_pg_test.csv"
    child_df.to_csv(child_csv_path, index=False, encoding='utf-8-sig')
    
    print(f"✅ 创建PostgreSQL测试数据文件:")
    print(f"   - {parent_csv_path}")
    print(f"   - {child_csv_path}")
    
    return [str(parent_csv_path)], [str(child_csv_path)]

def test_postgresql_connection():
    """测试PostgreSQL连接"""
    try:
        import psycopg2
        
        # 测试连接配置
        config = {
            'host': 'localhost',
            'port': 5432,
            'user': 'postgres',
            'password': 'postgres123',  # 用户提供的密码
            'database': 'postgres'
        }
        
        print("🔍 测试PostgreSQL连接...")
        print(f"   服务器: {config['host']}:{config['port']}")
        print(f"   用户: {config['user']}")
        
        conn = psycopg2.connect(**config)
        cursor = conn.cursor()
        
        # 获取PostgreSQL版本
        cursor.execute("SELECT version()")
        version = cursor.fetchone()[0]
        print(f"✅ PostgreSQL连接成功!")
        print(f"   版本: {version.split(',')[0]}")
        
        # 获取现有数据库列表
        cursor.execute("SELECT datname FROM pg_database WHERE datistemplate = false")
        databases = [row[0] for row in cursor.fetchall()]
        print(f"   现有数据库: {databases}")
        
        cursor.close()
        conn.close()
        
        return True, config
        
    except ImportError:
        print("❌ 缺少psycopg2模块，请安装: pip install psycopg2-binary")
        return False, None
    except Exception as e:
        print(f"❌ PostgreSQL连接失败: {e}")
        print("💡 请确认:")
        print("1. PostgreSQL服务已启动")
        print("2. 用户名密码正确")
        print("3. 防火墙设置正确")
        return False, None

def test_postgresql_database_creation():
    """测试PostgreSQL数据库实际创建"""
    try:
        from enterprise_quota_manager import EnterpriseQuotaManager
        
        # 测试连接
        success, pg_config = test_postgresql_connection()
        if not success:
            return False
        
        # 创建测试数据
        parent_files, child_files = create_test_data()
        
        # 创建管理器
        manager = EnterpriseQuotaManager()
        
        print("\n🗄️ 测试PostgreSQL数据库实际创建...")
        
        # 配置数据库创建参数
        db_config = {
            'host': pg_config['host'],
            'port': pg_config['port'],
            'user': pg_config['user'],
            'password': pg_config['password'],
            'default_db': 'postgres',
            'database': 'test_enterprise_quota'
        }
        
        print(f"📋 目标数据库: {db_config['database']}")
        
        # 创建数据库
        success, message = manager.create_quota_database(
            'postgresql', db_config, parent_files, child_files
        )
        
        if success:
            print("✅ PostgreSQL数据库创建成功!")
            print(f"📋 创建结果:\n{message}")
            
            # 验证数据库是否真的创建了
            print("\n🔍 验证数据库创建...")
            
            import psycopg2
            
            # 连接到默认数据库检查目标数据库是否存在
            conn = psycopg2.connect(
                host=db_config['host'],
                port=db_config['port'],
                user=db_config['user'],
                password=db_config['password'],
                database=db_config['default_db']
            )
            cursor = conn.cursor()
            
            cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (db_config['database'],))
            db_exists = cursor.fetchone() is not None
            
            if db_exists:
                print(f"✅ 数据库 '{db_config['database']}' 确实已创建!")
                
                # 连接到目标数据库检查表
                cursor.close()
                conn.close()
                
                target_conn = psycopg2.connect(
                    host=db_config['host'],
                    port=db_config['port'],
                    user=db_config['user'],
                    password=db_config['password'],
                    database=db_config['database']
                )
                target_cursor = target_conn.cursor()
                
                # 获取表列表
                target_cursor.execute("""
                    SELECT table_name FROM information_schema.tables 
                    WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
                """)
                tables = [row[0] for row in target_cursor.fetchall()]
                
                print(f"✅ 找到 {len(tables)} 个表: {tables}")
                
                # 检查表中的数据
                for table in tables:
                    target_cursor.execute(f'SELECT COUNT(*) FROM "{table}"')
                    count = target_cursor.fetchone()[0]
                    print(f"   - {table}: {count} 行数据")
                
                target_cursor.close()
                target_conn.close()
                
                return True
            else:
                print(f"❌ 数据库 '{db_config['database']}' 未找到!")
                cursor.close()
                conn.close()
                return False
        else:
            print(f"❌ PostgreSQL数据库创建失败: {message}")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_test_database():
    """清理测试数据库"""
    try:
        import psycopg2
        
        config = {
            'host': 'localhost',
            'port': 5432,
            'user': 'postgres',
            'password': 'postgres123',
            'database': 'postgres'
        }
        
        conn = psycopg2.connect(**config)
        conn.set_isolation_level(psycopg2.extensions.ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # 检查测试数据库是否存在
        cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", ('test_enterprise_quota',))
        if cursor.fetchone():
            # 断开所有连接并删除数据库
            cursor.execute("""
                SELECT pg_terminate_backend(pid)
                FROM pg_stat_activity
                WHERE datname = 'test_enterprise_quota' AND pid <> pg_backend_pid()
            """)
            cursor.execute('DROP DATABASE "test_enterprise_quota"')
            print("🗑️ 清理测试数据库: test_enterprise_quota")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"⚠️ 清理测试数据库时出错: {e}")

def main():
    """主函数"""
    print("🗄️ PostgreSQL数据库实际创建测试")
    print("=" * 50)
    
    # 清理之前的测试数据库
    cleanup_test_database()
    
    # 检查基础依赖
    try:
        import pandas as pd
        print("✅ pandas 已安装")
    except ImportError:
        print("❌ pandas 未安装")
        return False
    
    # 运行测试
    success = test_postgresql_database_creation()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 PostgreSQL数据库实际创建测试通过!")
        print("\n💡 测试结果:")
        print("1. ✅ PostgreSQL连接正常")
        print("2. ✅ 数据库实际创建成功")
        print("3. ✅ 表结构正确生成")
        print("4. ✅ 数据成功导入")
        
        print("\n🚀 现在Web界面可以:")
        print("   - 真正创建PostgreSQL数据库")
        print("   - 在PostgreSQL服务器中查看数据库")
        print("   - 使用标准SQL工具连接和查询")
        
        print("\n📋 验证方法:")
        print("1. 打开pgAdmin或其他PostgreSQL客户端")
        print("2. 连接到localhost:5432")
        print("3. 查找'test_enterprise_quota'数据库")
        print("4. 检查表和数据")
    else:
        print("❌ PostgreSQL数据库实际创建测试失败!")
        print("\n🔧 可能的原因:")
        print("1. PostgreSQL服务未启动")
        print("2. 连接配置不正确")
        print("3. 权限不足")
        print("4. 缺少必要的Python模块")
    
    # 询问是否清理测试数据库
    if success:
        try:
            response = input("\n🗑️ 是否清理测试数据库? (y/N): ").strip().lower()
            if response == 'y':
                cleanup_test_database()
        except KeyboardInterrupt:
            print("\n")
    
    return success

if __name__ == "__main__":
    main()
