#!/usr/bin/env python3
"""
服务器启动脚本 - 带完整启动信息显示
Server startup script with complete startup info display
"""

import os
import sys
import socket
import platform
from datetime import datetime

def get_local_ip():
    """获取本机IP地址"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

def print_startup_info():
    """打印启动信息"""
    local_ip = get_local_ip()
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    print("\n" + "="*80)
    print("🔧 北京市2021消耗定额创建工具 | Beijing Quota Creation Tool")
    print("="*80)
    print(f"🕒 启动时间: {current_time}")
    print(f"🖥️  系统信息: {platform.system()} {platform.release()}")
    print(f"🐍 Python版本: {sys.version.split()[0]}")
    print(f"📂 工作目录: {os.getcwd()}")
    print("-"*80)
    print("🌐 服务器信息:")
    print(f"   • 本地访问: http://localhost:7864")
    print(f"   • 局域网访问: http://{local_ip}:7864")
    print(f"   • 服务器端口: 7864")
    print(f"   • 绑定地址: 0.0.0.0 (所有网络接口)")
    print("-"*80)
    print("📋 功能模块:")
    print("   • 🤖 AI定额识别 - 智能识别PDF中的定额数据")
    print("   • 🗄️ 数据库创建 - 支持SQLite、MongoDB、MySQL、PostgreSQL")
    print("   • 📊 信息价识别 - 识别信息价数据并写入数据库")
    print("   • ⚡ 高级定额管理 - 完整的数据库管理和查询功能")
    print("   • 📋 程序运行日志 - 实时显示系统运行状态")
    print("-"*80)
    print("💡 使用提示:")
    print("   • 首次使用请先配置AI模型API密钥")
    print("   • 支持多种数据库类型，推荐使用SQLite开始")
    print("   • 所有输出文件保存在 output/ 目录下")
    print("   • 程序日志保存在 logs/ 目录下")
    print("="*80)
    print("🚀 正在启动Web服务器...")
    print()

def print_startup_success():
    """打印启动成功信息"""
    local_ip = get_local_ip()
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    print("\n" + "="*80)
    print("✅ 服务器启动成功！")
    print("="*80)
    print(f"🕒 启动完成时间: {current_time}")
    print(f"🌐 访问地址:")
    print(f"   • 本地访问: http://localhost:7864")
    print(f"   • 局域网访问: http://{local_ip}:7864")
    print("-"*80)
    print("📱 快速访问:")
    print("   • 定额识别: 主页面 → AI定额识别")
    print("   • 数据库创建: 主页面 → 定额创建工具")
    print("   • 信息价识别: 高级定额管理系统 → 信息价识别")
    print("   • 数据库管理: 高级定额管理系统 → 数据库浏览器")
    print("-"*80)
    print("💡 提示: 按 Ctrl+C 停止服务器")
    print("="*80)
    print()

def main():
    """主函数"""
    # 显示启动信息
    print_startup_info()
    
    try:
        # 导入主程序
        print("📦 正在加载应用模块...")
        from main import QuotaExtractionApp
        print("✅ 应用模块加载成功")
        
        # 创建应用实例
        print("🔧 正在创建应用实例...")
        app = QuotaExtractionApp()
        print("✅ 应用实例创建成功")
        
        # 创建界面
        print("🎨 正在创建用户界面...")
        interface = app.create_interface()
        print("✅ 用户界面创建成功")
        
        # 显示启动成功信息
        print_startup_success()
        
        # 启动服务器
        interface.launch(
            server_name="0.0.0.0",
            server_port=7864,
            share=False,
            debug=False,
            show_error=False,
            quiet=False,
            allowed_paths=["output", "output/price_info", "static"],
            favicon_path="static/favicon.ico"
        )
        
    except KeyboardInterrupt:
        print("\n" + "="*80)
        print("⏹️  服务器已停止")
        print("👋 感谢使用北京市2021消耗定额创建工具！")
        print("="*80)
        
    except ImportError as e:
        print(f"\n❌ 模块导入失败: {str(e)}")
        print("💡 请检查依赖是否正确安装")
        print("   可以尝试运行: pip install -r requirements.txt")
        
    except Exception as e:
        print(f"\n❌ 服务器启动失败: {str(e)}")
        print("💡 请检查端口7864是否被占用，或尝试重新启动")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
