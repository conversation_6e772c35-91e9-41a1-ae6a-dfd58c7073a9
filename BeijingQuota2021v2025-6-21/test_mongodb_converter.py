#!/usr/bin/env python3
"""
测试MongoDB数据库转换功能
"""

import os
import sys
import pandas as pd
import json
from pathlib import Path

# 添加src目录到路径
sys.path.append('src')

def create_test_csv():
    """创建测试CSV文件"""
    # 确保output目录存在
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    # 创建测试数据
    test_data = {
        '定额编号': ['001', '002', '003', '004', '005'],
        '定额名称': ['混凝土浇筑', '钢筋绑扎', '模板安装', '砌砖工程', '抹灰工程'],
        '单位': ['m³', 'kg', 'm²', 'm³', 'm²'],
        '人工费': [120.5, 85.2, 95.8, 110.3, 75.6],
        '材料费': [450.8, 320.5, 180.2, 280.9, 120.4],
        '机械费': [80.3, 45.8, 25.6, 35.2, 15.8],
        '合计': [651.6, 451.5, 301.6, 426.4, 211.8]
    }
    
    df = pd.DataFrame(test_data)
    test_csv_path = output_dir / "test_quota_data.csv"
    df.to_csv(test_csv_path, index=False, encoding='utf-8-sig')
    
    print(f"✅ 创建测试CSV文件: {test_csv_path}")
    return str(test_csv_path)

def test_mongodb_conversion():
    """测试MongoDB转换功能"""
    try:
        from mcp_database_converter import MCPDatabaseConverter
        
        # 创建测试CSV文件
        csv_path = create_test_csv()
        
        # 创建转换器
        converter = MCPDatabaseConverter()
        
        # 测试MongoDB转换
        output_path = "output/test_mongodb_export.json"
        success, message, stats = converter.convert_to_mongodb([csv_path], output_path)
        
        if success:
            print(f"✅ MongoDB转换成功!")
            print(f"📊 转换统计:")
            print(f"   - 源文件数: {stats['total_files']}")
            print(f"   - 成功转换: {stats['successful_files']}")
            print(f"   - 总文档数: {stats['total_rows']}")
            print(f"   - 集合数: {len(stats['collections'])}")
            
            print(f"\n📋 生成的集合:")
            for collection in stats['collections']:
                print(f"   - {collection['name']}: {collection['documents']} 个文档")
            
            # 验证生成的JSON文件
            if os.path.exists(output_path):
                with open(output_path, 'r', encoding='utf-8') as f:
                    mongodb_data = json.load(f)
                
                print(f"\n🔍 JSON文件验证:")
                print(f"   - 文件大小: {os.path.getsize(output_path) / 1024:.2f} KB")
                print(f"   - 导出时间: {mongodb_data['metadata']['export_date']}")
                print(f"   - 集合数量: {len(mongodb_data['collections'])}")
                
                # 显示第一个集合的示例文档
                for collection_name, collection_data in mongodb_data['collections'].items():
                    documents = collection_data['documents']
                    if documents:
                        print(f"\n📄 集合 '{collection_name}' 示例文档:")
                        first_doc = {k: v for k, v in documents[0].items() if not k.startswith('_')}
                        print(json.dumps(first_doc, ensure_ascii=False, indent=2))
                    break
            
            # 测试预览功能
            print(f"\n🔍 测试预览功能...")
            preview_html = converter.preview_database_file(output_path)
            if "MongoDB JSON导出预览" in preview_html:
                print("✅ 预览功能正常")
            else:
                print("❌ 预览功能异常")
            
        else:
            print(f"❌ MongoDB转换失败: {message}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def main():
    """主函数"""
    print("🧪 开始测试MongoDB数据库转换功能")
    print("=" * 50)
    
    # 检查依赖
    try:
        import pandas as pd
        print("✅ pandas 已安装")
    except ImportError:
        print("❌ pandas 未安装")
        return False
    
    # 运行测试
    success = test_mongodb_conversion()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 MongoDB转换功能测试通过!")
        print("\n💡 使用说明:")
        print("1. 在Web界面中选择 '🍃 MongoDB JSON导出' 格式")
        print("2. 选择要转换的CSV文件")
        print("3. 设置输出文件名（自动添加.json扩展名）")
        print("4. 点击转换按钮")
        print("5. 生成的JSON文件可以直接导入到MongoDB数据库")
    else:
        print("❌ MongoDB转换功能测试失败!")
    
    return success

if __name__ == "__main__":
    main()
