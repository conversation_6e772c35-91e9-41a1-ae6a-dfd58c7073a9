#!/usr/bin/env python3
"""
测试阿里云通义千问-QVQ模型
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.ai_model_processor import AIModelProcessor

def check_qvq_configuration():
    """检查QVQ模型配置"""
    print("🔍 检查QVQ模型配置")
    print("=" * 40)
    
    # 检查API密钥
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if not api_key:
        print("❌ 未找到DASHSCOPE_API_KEY环境变量")
        print("\n📝 配置步骤:")
        print("1. 访问阿里云百炼控制台: https://bailian.console.aliyun.com/")
        print("2. 创建API密钥")
        print("3. 设置环境变量: DASHSCOPE_API_KEY=your_api_key")
        print("4. 或运行: py setup_api_keys.py")
        return False
    
    print(f"✅ 找到API密钥: {api_key[:10]}...")
    
    # 检查模型可用性
    processor = AIModelProcessor()
    available_models = processor.get_available_models()
    
    qvq_models = [k for k in available_models.keys() if k.startswith("qwen_qvq")]
    
    if qvq_models:
        print(f"✅ 可用的QVQ模型: {len(qvq_models)} 个")
        for model_id in qvq_models:
            print(f"   • {available_models[model_id]} ({model_id})")
        return True
    else:
        print("❌ 未检测到QVQ模型")
        return False

async def test_qvq_api():
    """测试QVQ API连接"""
    print("\n🧪 测试QVQ API连接")
    print("=" * 40)
    
    try:
        processor = AIModelProcessor()
        
        # 创建一个简单的测试图片（纯色图片）
        from PIL import Image
        import tempfile
        
        # 创建一个简单的测试图片
        test_image = Image.new('RGB', (100, 100), color='white')
        
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
            test_image.save(tmp_file.name)
            test_image_path = tmp_file.name
        
        print("📸 创建测试图片...")
        
        # 测试QVQ-Max模型
        print("🤖 测试QVQ-Max模型...")
        result = await processor.process_image_with_qwen_qvq(test_image_path, "qvq-max")
        
        if result:
            print("✅ QVQ-Max API连接成功")
            print(f"📝 响应长度: {len(result)} 字符")
            print(f"📄 响应预览: {result[:200]}...")
        else:
            print("❌ QVQ-Max API连接失败")
        
        # 清理测试文件
        os.unlink(test_image_path)
        
        return bool(result)
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def show_qvq_features():
    """显示QVQ模型特性"""
    print("\n🌟 通义千问-QVQ模型特性")
    print("=" * 40)
    
    features = [
        "🧠 **强大的视觉推理能力**：具备思维链推理，能够逐步分析复杂图像",
        "📊 **表格理解专长**：特别适合处理结构化数据和表格内容",
        "🔍 **细节识别精准**：能够准确识别图像中的文字、数字和结构",
        "💭 **思考过程可见**：提供详细的推理过程，便于理解AI的分析思路",
        "🎯 **中文优化**：针对中文内容进行了特别优化",
        "⚡ **流式输出**：支持实时查看思考和回答过程"
    ]
    
    for feature in features:
        print(feature)
    
    print(f"\n📋 模型规格:")
    print(f"   • QVQ-Max: 最强版本，推理能力最佳")
    print(f"   • QVQ-Plus: 平衡版本，速度与效果兼顾")
    print(f"   • 输入限制: 支持多种图片格式")
    print(f"   • 输出格式: 流式JSON响应")

def show_usage_guide():
    """显示使用指南"""
    print("\n📖 QVQ模型使用指南")
    print("=" * 40)
    
    print("1. **配置API密钥**:")
    print("   py setup_api_keys.py")
    print("   # 选择配置阿里云百炼/通义千问-QVQ")
    
    print("\n2. **启动系统**:")
    print("   py main.py")
    print("   # 访问 http://localhost:7862")
    
    print("\n3. **选择模型**:")
    print("   • 阿里通义千问-QVQ-Max (推荐)")
    print("   • 阿里通义千问-QVQ-Plus")
    
    print("\n4. **上传定额表格**:")
    print("   • 支持PDF文件自动转换")
    print("   • 支持直接上传图片")
    
    print("\n5. **查看结果**:")
    print("   • 实时显示思考过程")
    print("   • 自动生成CSV文件")
    print("   • 包含价格计算公式")

async def main():
    """主函数"""
    print("🚀 阿里云通义千问-QVQ模型测试")
    print("=" * 60)
    
    # 显示模型特性
    show_qvq_features()
    
    # 检查配置
    config_ok = check_qvq_configuration()
    
    if config_ok:
        # 测试API连接
        api_ok = await test_qvq_api()
        
        if api_ok:
            print("\n🎉 QVQ模型配置完成，可以正常使用！")
        else:
            print("\n⚠️ QVQ模型配置有问题，请检查API密钥")
    else:
        print("\n❌ QVQ模型未正确配置")
    
    # 显示使用指南
    show_usage_guide()
    
    print("\n" + "=" * 60)
    print("📊 测试完成")

if __name__ == "__main__":
    asyncio.run(main())
