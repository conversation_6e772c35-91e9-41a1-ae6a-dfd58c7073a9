#!/usr/bin/env python3
"""
测试数据格式处理 - 验证是否与期望格式一致
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.data_processor import DataProcessor

def test_data_processing():
    """测试数据处理逻辑"""
    print("🧪 测试数据处理逻辑")
    print("=" * 50)
    
    # 模拟AI识别结果（基于您的HTML示例）
    test_recognition_result = '''
    {
        "parent_quota": {
            "code": "04-01-1-6",
            "name": "人工挖沟槽土方 一、二类土",
            "work_content": "挖土、余土清理、修整底边、打钉拍底等",
            "unit": "m³"
        },
        "resource_consumption": [
            {
                "resource_code": "00010701",
                "category": "人工",
                "name": "综合用工三类",
                "unit": "工日/m³",
                "consumption": "0.182"
            },
            {
                "resource_code": "99030030",
                "category": "机械",
                "name": "电动打钉机",
                "unit": "台班/m³",
                "consumption": "0.0039"
            },
            {
                "resource_code": "99460004",
                "category": "其他费用",
                "name": "其他机具费占人工费",
                "unit": "%",
                "consumption": "1.50"
            }
        ]
    }
    '''
    
    # 创建数据处理器
    processor = DataProcessor()
    
    # 解析识别结果
    print("📊 解析AI识别结果...")
    parsed_data = processor.parse_recognition_result(test_recognition_result, 1)
    
    if not parsed_data:
        print("❌ 解析失败")
        return False
    
    print(f"✅ 解析成功，提取 {len(parsed_data)} 条数据")
    
    # 显示解析结果
    print("\n📋 解析结果详情:")
    for i, item in enumerate(parsed_data):
        print(f"\n{i+1}. {item['type']} 项:")
        if item['type'] == 'parent':
            print(f"   编号: {item['quota_code']}")
            print(f"   名称: {item['quota_name']}")
            print(f"   工作内容: {item['work_content']}")
            print(f"   单位: {item['unit']}")
        else:
            print(f"   定额编号: {item['quota_code']}")
            print(f"   资源编号: {item['resource_code']}")
            print(f"   类别: {item['category']}")
            print(f"   子项名称: {item['resource_name']}")
            print(f"   资源单位: {item['resource_unit']}")
            print(f"   消耗量: {item['consumption']}")
    
    # 模拟设置单价（实际使用中由用户输入）
    print("\n💰 设置测试单价...")
    for item in parsed_data:
        if item['type'] == 'child':
            if item['resource_code'] == '00010701':  # 人工
                item['unit_price'] = 120.0  # 120元/工日
            elif item['resource_code'] == '99030030':  # 机械
                item['unit_price'] = 50.0   # 50元/台班
            # 百分比项的单价会自动计算
    
    # 生成CSV文件
    print("\n📄 生成CSV文件...")
    try:
        csv_path = processor.generate_csv(parsed_data)
        print(f"✅ CSV文件生成成功: {csv_path}")
        
        # 读取并显示CSV内容
        import pandas as pd
        df = pd.read_csv(csv_path, encoding='utf-8-sig')
        print(f"\n📊 CSV文件内容预览 (共{len(df)}行):")
        print(df.to_string(index=False))
        
        return True
        
    except Exception as e:
        print(f"❌ CSV生成失败: {e}")
        return False

def test_price_calculation():
    """测试价格计算逻辑"""
    print("\n\n🧮 测试价格计算逻辑")
    print("=" * 50)
    
    # 创建测试数据
    parent_data = [{
        "type": "parent",
        "quota_code": "04-01-1-6",
        "quota_name": "人工挖沟槽土方 一、二类土",
        "work_content": "挖土、余土清理、修整底边、打钉拍底等",
        "unit": "m³",
        "comprehensive_price": 0
    }]
    
    child_data = [
        {
            "type": "child",
            "quota_code": "04-01-1-6",
            "resource_code": "00010701",
            "category": "人工",
            "resource_name": "综合用工三类",
            "resource_unit": "工日/m³",
            "consumption": 0.182,
            "unit_price": 120.0,
            "total_price": 0
        },
        {
            "type": "child",
            "quota_code": "04-01-1-6",
            "resource_code": "99030030",
            "category": "机械",
            "resource_name": "电动打钉机",
            "resource_unit": "台班/m³",
            "consumption": 0.0039,
            "unit_price": 50.0,
            "total_price": 0
        },
        {
            "type": "child",
            "quota_code": "04-01-1-6",
            "resource_code": "99460004",
            "category": "其他费用",
            "resource_name": "其他机具费占人工费",
            "resource_unit": "%",
            "consumption": 1.50,
            "unit_price": 0,  # 百分比项，自动计算
            "total_price": 0
        }
    ]
    
    # 执行价格计算
    processor = DataProcessor()
    processor._calculate_prices(parent_data, child_data)
    
    # 显示计算结果
    print("💰 价格计算结果:")
    print("\n父级定额:")
    parent = parent_data[0]
    print(f"  综合单价: {parent['comprehensive_price']:.2f} 元/m³")
    
    print("\n子级资源:")
    for child in child_data:
        print(f"  {child['resource_name']}:")
        print(f"    消耗量: {child['consumption']} {child['resource_unit']}")
        print(f"    单价: {child['unit_price']:.2f}")
        print(f"    合价: {child['total_price']:.2f}")
    
    # 验证计算逻辑
    expected_human_cost = 0.182 * 120.0  # 21.84
    expected_machine_cost = 0.0039 * 50.0  # 0.195
    expected_other_base = expected_human_cost + expected_machine_cost  # 22.035
    expected_other_cost = expected_other_base * 1.50 / 100  # 0.33
    expected_total = expected_human_cost + expected_machine_cost + expected_other_cost  # 22.365
    
    print(f"\n🔍 验证计算:")
    print(f"  人工费: 0.182 × 120.0 = {expected_human_cost:.3f}")
    print(f"  机械费: 0.0039 × 50.0 = {expected_machine_cost:.3f}")
    print(f"  其他费基数: {expected_other_base:.3f}")
    print(f"  其他费: {expected_other_base:.3f} × 1.50% = {expected_other_cost:.3f}")
    print(f"  综合单价: {expected_total:.3f}")
    
    # 检查计算是否正确
    actual_total = parent['comprehensive_price']
    if abs(actual_total - expected_total) < 0.01:
        print("✅ 价格计算正确")
        return True
    else:
        print(f"❌ 价格计算错误，期望: {expected_total:.3f}，实际: {actual_total:.3f}")
        return False

def main():
    """主测试函数"""
    print("🧪 数据格式处理测试")
    print("=" * 60)
    
    tests = [
        ("数据处理", test_data_processing),
        ("价格计算", test_price_calculation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✅ {test_name} 测试通过")
            else:
                print(f"\n❌ {test_name} 测试失败")
        except Exception as e:
            print(f"\n❌ {test_name} 测试异常: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！数据格式处理符合期望。")
    else:
        print("⚠️ 部分测试失败，请检查数据处理逻辑。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
