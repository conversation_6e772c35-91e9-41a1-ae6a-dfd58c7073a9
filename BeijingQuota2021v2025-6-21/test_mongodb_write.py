#!/usr/bin/env python3
"""
MongoDB数据写入测试脚本
测试企业定额管理系统的MongoDB数据库创建和写入功能
"""

import os
import sys
import json
from pathlib import Path

def test_mongodb_write():
    """测试MongoDB数据写入功能"""
    print("🧪 开始测试MongoDB数据写入功能")
    print("=" * 60)
    
    # 检查MongoDB连接
    print("\n🔍 检查MongoDB连接...")
    try:
        from pymongo import MongoClient
        client = MongoClient('mongodb://localhost:27017/', serverSelectionTimeoutMS=2000)
        client.server_info()  # 触发连接
        print("✅ MongoDB服务器连接正常")
        client.close()
    except Exception as e:
        print(f"❌ MongoDB连接失败: {e}")
        print("💡 请确保MongoDB服务器正在运行")
        return False
    
    # 导入企业定额管理器
    print("\n📦 导入企业定额管理器...")
    try:
        # 添加src目录到路径
        sys.path.append('src')
        from enterprise_quota_manager import EnterpriseQuotaManager
        print("✅ 企业定额管理器导入成功")
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    
    # 检查CSV文件
    print("\n📋 检查CSV文件...")
    output_dir = Path("output")
    parent_files = list(output_dir.glob("parent_quotas*.csv"))
    child_files = list(output_dir.glob("child_resources*.csv"))
    
    if not parent_files:
        print("❌ 未找到parent_quotas*.csv文件")
        return False
    if not child_files:
        print("❌ 未找到child_resources*.csv文件")
        return False
    
    # 选择测试文件（取前2个）
    test_parent_files = [str(f) for f in parent_files[:2]]
    test_child_files = [str(f) for f in child_files[:2]]
    
    print(f"✅ 找到CSV文件:")
    print(f"   - 定额项文件: {len(test_parent_files)} 个")
    print(f"   - 资源文件: {len(test_child_files)} 个")
    
    # 创建MongoDB数据库
    print("\n🍃 创建MongoDB数据库...")
    manager = EnterpriseQuotaManager()
    
    db_config = {
        'database_path': 'output/test_mongodb_enterprise.json',
        'host': 'localhost',
        'port': 27017,
        'database': 'test_enterprise_quota'
    }
    
    success, message = manager.create_quota_database(
        'mongodb', db_config, test_parent_files, test_child_files
    )
    
    if success:
        print("✅ MongoDB数据库创建成功!")
        print(f"📋 {message}")
        
        # 检查生成的JSON文件
        json_file = db_config['database_path']
        if os.path.exists(json_file):
            print(f"\n📄 检查生成的JSON文件: {json_file}")
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                print(f"✅ JSON文件解析成功")
                print(f"   - 元数据: {data.get('metadata', {})}")
                print(f"   - 集合数量: {len(data.get('collections', {}))}")
                
                # 显示集合信息
                collections = data.get('collections', {})
                for collection_name, collection_data in collections.items():
                    doc_count = len(collection_data.get('documents', []))
                    print(f"   - {collection_name}: {doc_count} 个文档")
                
            except Exception as e:
                print(f"❌ JSON文件解析失败: {e}")
                return False
        else:
            print(f"❌ JSON文件未生成: {json_file}")
            return False
        
        # 测试实际写入MongoDB
        print("\n💾 测试实际写入MongoDB...")
        try:
            client = MongoClient('mongodb://localhost:27017/')
            db = client[db_config['database']]
            
            # 读取JSON文件并写入MongoDB
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            collections = data.get('collections', {})
            total_docs = 0
            
            for collection_name, collection_data in collections.items():
                documents = collection_data.get('documents', [])
                if documents:
                    # 清空现有集合
                    db[collection_name].drop()
                    # 插入文档
                    result = db[collection_name].insert_many(documents)
                    inserted_count = len(result.inserted_ids)
                    total_docs += inserted_count
                    print(f"   ✅ {collection_name}: 插入 {inserted_count} 个文档")
            
            print(f"✅ MongoDB写入成功! 总共插入 {total_docs} 个文档")
            
            # 验证数据
            print("\n🔍 验证MongoDB中的数据...")
            for collection_name in collections.keys():
                count = db[collection_name].count_documents({})
                print(f"   - {collection_name}: {count} 个文档")
                
                # 显示第一个文档示例
                sample_doc = db[collection_name].find_one()
                if sample_doc:
                    # 移除_id字段以便显示
                    sample_doc.pop('_id', None)
                    print(f"     示例文档: {str(sample_doc)[:100]}...")
            
            client.close()
            return True
            
        except Exception as e:
            print(f"❌ MongoDB写入失败: {e}")
            return False
        
    else:
        print(f"❌ MongoDB数据库创建失败: {message}")
        return False

def main():
    """主函数"""
    success = test_mongodb_write()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 MongoDB数据写入测试完成！所有功能正常")
    else:
        print("❌ MongoDB数据写入测试失败")
    
    return success

if __name__ == "__main__":
    main()
