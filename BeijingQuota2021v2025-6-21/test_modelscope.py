#!/usr/bin/env python3
"""
测试ModelScope CogVLM2模型可用性
"""

import os
import sys

def test_modelscope_cogvlm2():
    """测试ModelScope CogVLM2模型"""
    print("🔍 测试ModelScope CogVLM2模型可用性")
    print("=" * 50)
    
    # 1. 检查模型文件是否存在
    model_cache_path = os.path.expanduser("~/.cache/modelscope/hub/models/ZhipuAI/cogvlm2-llama3-chinese-chat-19B-int4")
    print(f"📁 检查模型路径: {model_cache_path}")
    
    if os.path.exists(model_cache_path):
        print("✅ 模型文件存在")
        files = os.listdir(model_cache_path)
        print(f"📋 模型文件数量: {len(files)}")
        print(f"📋 主要文件: {[f for f in files if f.endswith(('.json', '.bin', '.safetensors'))]}")
    else:
        print("❌ 模型文件不存在")
        print("💡 请运行: modelscope download --model ZhipuAI/cogvlm2-llama3-chinese-chat-19B-int4")
        return False
    
    # 2. 检查PyTorch
    print("\n🔍 检查PyTorch...")
    try:
        import torch
        print(f"✅ PyTorch已安装: {torch.__version__}")
        print(f"🖥️ CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"🎮 GPU数量: {torch.cuda.device_count()}")
            print(f"🎮 当前GPU: {torch.cuda.get_device_name()}")
    except ImportError:
        print("❌ PyTorch未安装")
        print("💡 请运行: pip install torch torchvision torchaudio")
        return False
    
    # 3. 检查ModelScope
    print("\n🔍 检查ModelScope...")
    try:
        import modelscope
        print(f"✅ ModelScope已安装: {modelscope.__version__}")
    except ImportError as e:
        print(f"❌ ModelScope未安装: {e}")
        print("💡 请运行: pip install modelscope")
        return False
    
    # 4. 检查transformers
    print("\n🔍 检查transformers...")
    try:
        import transformers
        print(f"✅ transformers已安装: {transformers.__version__}")
    except ImportError:
        print("❌ transformers未安装")
        print("💡 请运行: pip install transformers")
        return False
    
    # 5. 尝试加载模型
    print("\n🔍 尝试加载模型...")
    try:
        from modelscope import AutoTokenizer, AutoModel
        model_id = "ZhipuAI/cogvlm2-llama3-chinese-chat-19B-int4"
        
        print("🔄 加载tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_id, trust_remote_code=True)
        print("✅ tokenizer加载成功")
        
        print("🔄 检查模型配置...")
        # 不实际加载模型，只检查配置
        from transformers import AutoConfig
        config = AutoConfig.from_pretrained(model_id, trust_remote_code=True)
        print(f"✅ 模型配置加载成功: {config.model_type}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return False

def main():
    """主函数"""
    success = test_modelscope_cogvlm2()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 ModelScope CogVLM2模型完全可用！")
        print("💡 您现在可以在应用中使用这个模型了")
    else:
        print("❌ ModelScope CogVLM2模型不可用")
        print("💡 请按照上述提示安装缺失的依赖")
    
    print("\n📖 安装指南:")
    print("1. 安装PyTorch: pip install torch torchvision torchaudio")
    print("2. 安装ModelScope: pip install modelscope")
    print("3. 安装transformers: pip install transformers")
    print("4. 下载模型: modelscope download --model ZhipuAI/cogvlm2-llama3-chinese-chat-19B-int4")

if __name__ == "__main__":
    main()
