
# Instances generator

  The instances generator is a simple python tool used to generate several hundred of instances (.cpp files) and their references (.h files). 
  Without this tool, manually writing those instances and references will be very laborious and easy to get wrong. 
  
  The instances generated by this scripts are divided into three categories visible from the scripts: 
   * Infer -- which refers to instances for calling inference-only kernels
   * Forward -- which refers to instances for calling training forward kernels
   * Backward -- which refers to instances for calling training backward kernels
     
  The instance generator is for being used by the HIP fmha developers themselves. It is not supposed to be used by the xformers users for 
  building xformers, since for xformers users, the instances are already well prepared as part of the xformers codes. 

## how to use instance generator

   * To generate complete instances supported by current implementation

     ```
      #> python xformers/csrc/attention/hip_fmha/generate_instances.py
     ```
   * To generate reduced instances (when headdim256 is not required)

     ``` 
      #> python xformers/csrc/attention/hip_fmha/generate_instances.py --ignore-hd256
     ```
   * More options except for `--ignore-hd256` could be added to suppport further customization in generating instances as required

## where the instances files are located
   The instances files and references files are always located under a folder `instances/` that is located under the same directory
   as the file `generate_instances.py` itself

     
