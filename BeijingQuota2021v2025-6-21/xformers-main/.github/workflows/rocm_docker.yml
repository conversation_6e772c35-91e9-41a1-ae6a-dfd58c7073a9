name: Build and Publish ROCm Docker Image

on:
  push:
    branches:
      - develop

jobs:
  build-and-push:
    runs-on: rocm
    if: github.repository == 'rocm/xformers'
    steps:
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ vars.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      
      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          push: true
          tags: rocm/xformers:latest
          file: Dockerfile.rocm
