name: wheels_build

on:
  workflow_call:
    inputs:
      os:
        required: true
        type: string
      python:
        required: true
        type: string
      torch_version:
        required: true
        type: string
        description: "Example: 1.13.1"
      toolkit_type:
        required: true
        type: string
        description: "Example: cuda for cuda, rocm for rocm"
      toolkit_short_version:
        required: true
        type: string
        description: "Example: 117 for 11.7"
      artifact_tag:
        default: "facebookresearch"
        type: string

# this yaml file can be cleaned up using yaml anchors, but they're not supported in github actions yet
# https://github.com/actions/runner/issues/1182

env:
  # you need at least cuda 5.0 for some of the stuff compiled here.
  TORCH_CUDA_ARCH_LIST: ${{ contains(inputs.toolkit_type, 'cuda') && '7.5 8.0+PTX' || '' }}
  HIP_ARCHITECTURES: ${{ contains(inputs.toolkit_type, 'rocm') && 'gfx90a gfx942' || '' }}
  MAX_JOBS: 2 # (FA3 is memory hungry!)
  DISTUTILS_USE_SDK: 1 # otherwise distutils will complain on windows about multiple versions of msvc
  XFORMERS_BUILD_TYPE: "Release"
  TWINE_USERNAME: __token__
  XFORMERS_PACKAGE_FROM: "wheel-${{ github.ref_name }}"

jobs:
  build:
    name: ${{ contains(inputs.os, 'ubuntu') && 'ubuntu' || 'win' }}-py${{ inputs.python }}-pt${{ inputs.torch_version }}+${{ contains(inputs.toolkit_type, 'cuda') && 'cu' || 'rocm' }}${{ inputs.toolkit_short_version }}
    runs-on: ${{ inputs.os }}
    env:
      # alias for the current python version
      # windows does not have per version binary, it is just 'python3'
      PY: python${{ contains(inputs.os, 'ubuntu') && inputs.python || '3' }}

    container: ${{ contains(inputs.os, 'ubuntu') && 'quay.io/pypa/manylinux_2_28_x86_64' || null }}
    timeout-minutes: 360
    defaults:
      run:
        shell: bash
    steps:
      - if: contains(inputs.toolkit_type, 'cuda') && fromJSON(inputs.toolkit_short_version) >= 120
        run: |
          echo "TORCH_CUDA_ARCH_LIST=$TORCH_CUDA_ARCH_LIST 9.0a" >> ${GITHUB_ENV}

      - if: runner.os == 'Windows'
        run: git config --system core.longpaths true
      - name: Recursive checkout
        uses: actions/checkout@v4
        with:
          submodules: recursive
          path: "."
          fetch-depth: 0 # for tags

      - name: HACKFIX for cutlass compiler bug
        if: runner.os == 'Windows'
        run: |
          # See https://github.com/NVIDIA/cutlass/issues/1732
          rm -f third_party/cutlass/include/cutlass/gemm/kernel/sm90_gemm_tma_warpspecialized_pingpong.hpp
          touch third_party/cutlass/include/cutlass/gemm/kernel/sm90_gemm_tma_warpspecialized_pingpong.hpp
      - name: Setup Runner
        uses: ./.github/actions/setup-build-cuda
        with:
          toolkit_type: ${{ inputs.toolkit_type }}
          toolkit_short_version: ${{ inputs.toolkit_short_version }}
          python: ${{ inputs.python }}
      - if: runner.os == 'Linux'
        run: printenv

      - if: runner.os != 'Windows'
        name: (Linux) Setup venv for linux
        shell: bash -l {0}
        run: |
          $PY -m venv venv
          . ./venv/bin/activate
          which pip
          echo "PY=$(which python)" >> ${GITHUB_ENV}
          echo "PATH=$PATH" >> ${GITHUB_ENV}
          git config --global --add safe.directory "*"
          pip install packaging ninja wheel setuptools twine

      - name: Define version
        id: xformers_version
        env:
          VERSION_SOURCE: ${{ github.ref_type == 'tag' && 'tag' || 'dev'  }}
        run: |
          set -Eeuo pipefail
          git config --global --add safe.directory "*"
          version=`python .github/compute_wheel_version.py --source $VERSION_SOURCE`
          echo $version > version.txt
          echo "BUILD_VERSION=$version${{ steps.cuda_info.outputs.CUDA_VERSION_SUFFIX }}" >> ${GITHUB_ENV}
          echo "BUILD_VERSION=$version${{ steps.cuda_info.outputs.CUDA_VERSION_SUFFIX }}" >> ${GITHUB_OUTPUT}
          which ninja
          ninja --version
          cat ${GITHUB_ENV}
      - run: echo "xformers-${BUILD_VERSION}"
      - run: echo "release version (will upload to PyTorch)"
        if: ${{ !contains(steps.xformers_version.outputs.BUILD_VERSION, '.dev') }}

      - name: Setup proper pytorch dependency in "requirements.txt"
        run: |
          sed -i '/torch/d' ./requirements.txt
          echo "torch == ${{ inputs.torch_version }}" >> ./requirements.txt
          cat ./requirements.txt

      - name: Install corresponding PyTorch
        run: |
          PYTORCH_INDEX_URL="https://download.pytorch.org/whl/${{ contains(inputs.toolkit_type, 'cuda') && 'cu' || 'rocm' }}${{ inputs.toolkit_short_version }}"
          $PY -m pip install wheel -r requirements.txt --extra-index-url $PYTORCH_INDEX_URL

      - name: Build wheel
        shell: bash -l {0}
        run: |
          $PY setup.py bdist_wheel -d dist/ -k $PLAT_ARG
        env:
          PLAT_ARG: ${{ contains(inputs.os, 'ubuntu') && '--plat-name manylinux_2_28_x86_64' || '' }}

      - run: du -h dist/*
      - uses: actions/upload-artifact@v4
        with:
          name: ${{ inputs.os }}-py${{ inputs.python }}-torch${{ inputs.torch_version }}+${{ contains(inputs.toolkit_type, 'cuda') && 'cu' || 'rocm' }}${{ inputs.toolkit_short_version }}_${{ inputs.artifact_tag }}
          path: dist/*.whl
# Note: it might be helpful to have additional steps that test if the built wheels actually work
