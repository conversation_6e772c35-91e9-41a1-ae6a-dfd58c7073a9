#!/usr/bin/env python3
"""
测试Google Gemma模型支持
Test Google Gemma model support
"""

import sys
import os

# 添加src目录到路径
sys.path.insert(0, 'src')

def test_gemma_model_recognition():
    """测试Gemma模型识别"""
    print("🧪 测试Gemma模型识别")
    print("=" * 50)
    
    try:
        from lm_studio_manager import LMStudioManager
        
        # 创建管理器实例
        manager = LMStudioManager()
        
        # 测试友好名称提取
        test_models = [
            "google/gemma-3-27b",
            "gemma-3-27b",
            "gemma-2-27b", 
            "gemma-2-9b",
            "gemma-2-2b",
            "gemma-7b",
            "gemma-2b",
            "other-model"
        ]
        
        print("📝 测试模型名称映射:")
        for model_id in test_models:
            friendly_name = manager._extract_friendly_name(model_id)
            print(f"   {model_id} → {friendly_name}")
        
        # 测试视觉模型识别
        print("\n📝 测试视觉模型识别:")
        for model_id in test_models:
            model_lower = model_id.lower()
            vision_keywords = [
                "vl", "vision", "llava", "cogvlm", "internvl", 
                "minicpm-v", "yi-vl", "deepseek-vl", "monkey", 
                "phi-3-vision", "moondream", "bakllava", "obsidian",
                "pixtral", "qwen2.5-vl", "qwen2-vl", "ocr",
                "gemma"
            ]
            
            is_vision = any(keyword in model_lower for keyword in vision_keywords)
            status = "✅ 视觉模型" if is_vision else "❌ 非视觉模型"
            print(f"   {model_id}: {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_gemma_prompt_optimization():
    """测试Gemma提示词优化"""
    print("\n🧪 测试Gemma提示词优化")
    print("=" * 50)
    
    try:
        from ai_model_processor import AIModelProcessor
        from intelligent_price_info_processor import IntelligentPriceInfoProcessor
        
        # 测试定额识别的Gemma提示词
        print("📝 测试定额识别Gemma提示词...")
        ai_processor = AIModelProcessor()
        
        # 测试基础提示词
        basic_prompt = ai_processor._get_gemma_optimized_prompt()
        print(f"   基础提示词长度: {len(basic_prompt)} 字符")
        print(f"   包含关键词: {'JSON' in basic_prompt and '定额数据' in basic_prompt}")
        
        # 测试带分册章节的提示词
        volume_prompt = ai_processor._get_gemma_optimized_prompt("01", "01,02")
        print(f"   分册提示词长度: {len(volume_prompt)} 字符")
        print(f"   包含编号规则: {'01-01-' in volume_prompt}")
        
        # 测试信息价识别的Gemma提示词
        print("\n📝 测试信息价识别Gemma提示词...")
        price_processor = IntelligentPriceInfoProcessor()
        
        price_prompt = price_processor._get_gemma_price_info_prompt()
        print(f"   信息价提示词长度: {len(price_prompt)} 字符")
        print(f"   包含关键词: {'信息价数据' in price_prompt and '不含税' in price_prompt}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_gemma_model_integration():
    """测试Gemma模型集成"""
    print("\n🧪 测试Gemma模型集成")
    print("=" * 50)
    
    try:
        from ai_model_processor import AIModelProcessor
        from intelligent_price_info_processor import IntelligentPriceInfoProcessor
        
        # 测试AI模型处理器
        print("📝 测试AI模型处理器Gemma集成...")
        ai_processor = AIModelProcessor()
        
        # 模拟Gemma模型
        test_models = ["gemma-3-27b", "google/gemma-3-27b", "qwen2.5-vl-7b"]
        
        for model_id in test_models:
            model_key = f"lm_studio_{model_id.replace('/', '_').replace('.', '_').replace('-', '_')}"
            actual_id = ai_processor.get_model_id_from_key(model_key)
            print(f"   {model_key} → {actual_id}")
        
        # 测试信息价处理器
        print("\n📝 测试信息价处理器Gemma集成...")
        price_processor = IntelligentPriceInfoProcessor()
        
        for model_id in test_models:
            model_key = f"lm_studio_{model_id.replace('/', '_').replace('.', '_').replace('-', '_')}"
            actual_id = price_processor.get_model_id_from_key(model_key)
            print(f"   {model_key} → {actual_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_lm_studio_gemma_detection():
    """测试LM Studio中的Gemma模型检测"""
    print("\n🧪 测试LM Studio中的Gemma模型检测")
    print("=" * 50)
    
    try:
        from lm_studio_manager import LMStudioManager
        
        # 创建管理器实例
        manager = LMStudioManager()
        
        # 检查LM Studio状态
        print("📝 检查LM Studio状态...")
        is_running = manager.is_server_running()
        print(f"   LM Studio运行状态: {'✅ 运行中' if is_running else '❌ 未运行'}")
        
        if not is_running:
            print("   💡 LM Studio未运行，跳过实际模型检测")
            return True
        
        # 获取所有模型
        print("\n📝 获取所有模型...")
        all_models = manager.get_available_models()
        print(f"   总模型数: {len(all_models)}")
        
        # 查找Gemma模型
        gemma_models = []
        for model in all_models:
            if "gemma" in model['id'].lower():
                gemma_models.append(model)
                print(f"   找到Gemma模型: {model['name']} (ID: {model['id']})")
        
        if not gemma_models:
            print("   ⚠️ 未找到Gemma模型，但这是正常的")
            print("   💡 如果您有Gemma模型，请在LM Studio中加载后重新测试")
        
        # 获取视觉模型
        print("\n📝 获取视觉模型...")
        vision_models = manager.get_vision_models()
        print(f"   视觉模型数: {len(vision_models)}")
        
        # 查找Gemma视觉模型
        gemma_vision_models = []
        for model in vision_models:
            if "gemma" in model['id'].lower():
                gemma_vision_models.append(model)
                print(f"   找到Gemma视觉模型: {model['name']} (ID: {model['id']})")
        
        if gemma_vision_models:
            print(f"   ✅ 成功识别 {len(gemma_vision_models)} 个Gemma视觉模型")
        else:
            print("   ℹ️ 当前未加载Gemma视觉模型")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 Google Gemma模型支持测试")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("Gemma模型识别", test_gemma_model_recognition),
        ("Gemma提示词优化", test_gemma_prompt_optimization),
        ("Gemma模型集成", test_gemma_model_integration),
        ("LM Studio Gemma检测", test_lm_studio_gemma_detection),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！Google Gemma模型支持已成功添加。")
        print("💡 主要功能:")
        print("   - ✅ 自动识别Gemma系列模型为视觉模型")
        print("   - ✅ 为Gemma模型优化了专门的提示词")
        print("   - ✅ 支持定额识别和信息价识别")
        print("   - ✅ 完整的模型管理和转换功能")
        print("🌐 现在可以在Web界面中使用Google Gemma模型")
        print("\n📋 支持的Gemma模型:")
        print("   • Gemma-3-27B (Google视觉语言)")
        print("   • Gemma-2-27B (Google视觉语言)")
        print("   • Gemma-2-9B (Google视觉语言)")
        print("   • Gemma-2-2B (Google轻量视觉)")
        print("   • Gemma-7B (Google视觉语言)")
        print("   • Gemma-2B (Google轻量视觉)")
    elif passed >= total - 1:
        print("✅ 基本功能正常！可能有个别小问题。")
        print("💡 建议在Web界面中测试实际功能。")
    else:
        print("⚠️ 存在多个问题，需要进一步检查。")
        print("💡 请确保:")
        print("   1. LM Studio已启动并运行")
        print("   2. 已加载Gemma系列模型")
        print("   3. 模型支持视觉输入功能")

if __name__ == "__main__":
    main()
