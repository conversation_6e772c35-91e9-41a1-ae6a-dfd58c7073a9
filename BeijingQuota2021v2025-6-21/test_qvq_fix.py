#!/usr/bin/env python3
"""
测试QVQ修复
验证信息价识别的QVQ API调用是否正常
"""

import os
import sys
import asyncio
import json

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_api_key():
    """测试API密钥设置"""
    print("🔑 测试API密钥设置")
    print("=" * 50)
    
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if api_key:
        print(f"✅ 找到API密钥: {api_key[:10]}...{api_key[-4:]}")
        return True
    else:
        print("❌ 未设置DASHSCOPE_API_KEY环境变量")
        print("💡 请设置环境变量：set DASHSCOPE_API_KEY=your_api_key")
        return False

def test_ai_processor_import():
    """测试AI处理器导入"""
    print("\n🧪 测试AI处理器导入")
    print("=" * 50)
    
    try:
        from src.ai_model_processor import AIModelProcessor
        processor = AIModelProcessor()
        print("✅ AIModelProcessor 导入成功")
        
        # 检查方法是否存在
        if hasattr(processor, 'process_image_with_price_info_prompt'):
            print("✅ process_image_with_price_info_prompt 方法存在")
        else:
            print("❌ process_image_with_price_info_prompt 方法不存在")
            return False
        
        if hasattr(processor, '_process_price_info_with_qwen_qvq'):
            print("✅ _process_price_info_with_qwen_qvq 方法存在")
        else:
            print("❌ _process_price_info_with_qwen_qvq 方法不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {str(e)}")
        return False

def test_config_prompt():
    """测试配置提示词"""
    print("\n📝 测试配置提示词")
    print("=" * 50)
    
    try:
        from src.config import Config
        config = Config()
        
        if hasattr(config, 'PRICE_INFO_EXTRACTION_PROMPT'):
            prompt = config.PRICE_INFO_EXTRACTION_PROMPT
            print(f"✅ 信息价识别提示词存在，长度: {len(prompt)} 字符")
            
            # 检查关键词
            keywords = ['页眉标识', '章节分类', '资源编号', 'JSON格式']
            missing_keywords = []
            
            for keyword in keywords:
                if keyword in prompt:
                    print(f"   ✅ 包含关键词: {keyword}")
                else:
                    print(f"   ❌ 缺少关键词: {keyword}")
                    missing_keywords.append(keyword)
            
            if not missing_keywords:
                print("✅ 提示词包含所有必要关键词")
                return True
            else:
                print(f"❌ 提示词缺少关键词: {missing_keywords}")
                return False
        else:
            print("❌ 未找到 PRICE_INFO_EXTRACTION_PROMPT")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def create_test_image():
    """创建测试图片"""
    print("\n🖼️ 创建测试图片")
    print("=" * 50)
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        import io
        import base64
        
        # 创建一个简单的测试图片
        width, height = 800, 600
        image = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(image)
        
        # 绘制表格框架
        draw.rectangle([50, 50, width-50, height-50], outline='black', width=2)
        
        # 添加标题
        try:
            # 尝试使用系统字体
            font = ImageFont.truetype("arial.ttf", 24)
        except:
            # 如果没有找到字体，使用默认字体
            font = ImageFont.load_default()
        
        draw.text((100, 80), "北京市造价信息测试表格", fill='black', font=font)
        draw.text((100, 120), "1. 黑色及有色金属（编码：01）", fill='black', font=font)
        draw.text((100, 160), "资源编号: 3001010401", fill='black', font=font)
        draw.text((100, 200), "产品名称: 网络球型摄像机", fill='black', font=font)
        draw.text((100, 240), "市场参考价（含税）: 1200.00", fill='black', font=font)
        
        # 保存图片
        test_image_path = "test_price_info_image.png"
        image.save(test_image_path)
        print(f"✅ 测试图片已创建: {test_image_path}")
        
        return test_image_path
        
    except Exception as e:
        print(f"❌ 创建测试图片失败: {str(e)}")
        return None

async def test_qvq_api_call():
    """测试QVQ API调用"""
    print("\n🚀 测试QVQ API调用")
    print("=" * 50)
    
    # 检查API密钥
    if not os.getenv("DASHSCOPE_API_KEY"):
        print("❌ 跳过API测试：未设置API密钥")
        return False
    
    try:
        from src.ai_model_processor import AIModelProcessor
        
        # 创建测试图片
        test_image = create_test_image()
        if not test_image:
            print("❌ 无法创建测试图片，跳过API测试")
            return False
        
        # 创建AI处理器
        processor = AIModelProcessor()
        
        print("🔄 开始调用QVQ API...")
        result = await processor.process_image_with_price_info_prompt(
            test_image, "qwen_qvq_max"
        )
        
        if result:
            print("✅ QVQ API调用成功!")
            print(f"📋 返回结果长度: {len(result)} 字符")
            
            # 尝试解析JSON
            try:
                if result.strip().startswith('{'):
                    json_data = json.loads(result)
                    print("✅ 返回结果是有效的JSON格式")
                    
                    if 'chapters' in json_data:
                        print(f"✅ 包含章节信息: {len(json_data['chapters'])} 个章节")
                    
                    if 'page_header' in json_data:
                        print(f"✅ 包含页眉信息: {json_data['page_header']}")
                        
                else:
                    print("⚠️ 返回结果不是JSON格式，但API调用成功")
                    print(f"📄 结果预览: {result[:200]}...")
                    
            except json.JSONDecodeError:
                print("⚠️ 返回结果不是有效的JSON格式")
                print(f"📄 结果预览: {result[:200]}...")
            
            return True
        else:
            print("❌ QVQ API调用失败，返回空结果")
            return False
            
    except Exception as e:
        print(f"❌ QVQ API测试失败: {str(e)}")
        return False
    finally:
        # 清理测试图片
        if 'test_image' in locals() and test_image and os.path.exists(test_image):
            try:
                os.remove(test_image)
                print(f"🧹 已清理测试图片: {test_image}")
            except:
                pass

def main():
    """主测试函数"""
    print("🚀 QVQ修复验证测试")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("API密钥设置", test_api_key),
        ("AI处理器导入", test_ai_processor_import),
        ("配置提示词", test_config_prompt)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 如果基础测试通过，进行API测试
    basic_tests_passed = all(result for _, result in results)
    
    if basic_tests_passed and os.getenv("DASHSCOPE_API_KEY"):
        print(f"\n{'='*20} QVQ API调用测试 {'='*20}")
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            api_result = loop.run_until_complete(test_qvq_api_call())
            results.append(("QVQ API调用", api_result))
        except Exception as e:
            print(f"❌ QVQ API测试异常: {str(e)}")
            results.append(("QVQ API调用", False))
        finally:
            loop.close()
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！QVQ修复成功。")
        print("💡 现在可以在Web界面中正常使用信息价识别功能。")
    elif passed >= total - 1:
        print("✅ 基本功能正常！可能有个别小问题。")
        print("💡 建议在Web界面中测试实际功能。")
    else:
        print("⚠️ 存在多个问题，需要进一步检查。")

if __name__ == "__main__":
    main()
