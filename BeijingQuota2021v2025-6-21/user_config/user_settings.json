{"api_keys": {"dashscope_key": "Z0FBQUFBQm9ZaTIwTkFzQV80alN6Qjh3RW8wazYtMHJvemswXzBIWmpwUm5hNzFYWWRlWjF5Vi1Wd2VVVDVLdXpZNFEwR0FHbnlBSE5BVHVOcm5hd3RaMWY0dmpqQlNITW02ZFlTdl9EVXFwMEdMX3MyUmZXOF9lNlQyR29fZmhnWlJDOEJiS2dtZUc=", "openai_key": "", "last_updated": "2025-06-30T14:06:12.390264"}, "database_configs": {"quota_db": {"db_type": "postgresql", "db_name": "beijing2021_quota_test", "host": "localhost", "port": "5432", "username": "postgres", "password": "Z0FBQUFBQm9ZaTIwdFc3c2ZkLTB0dmRQZExVMXNULVAtbkZaWXpTRVVUYkttUlZqRkJIMXdjYW15YkE3SzBHczFCeTh0cmpqSXJqdVY1ZE5Pbkt0bmM5QkxTTnFQdDhLWlE9PQ==", "default_db": "postgres"}, "price_db": {"db_type": "postgresql", "db_name": "price_database.db", "host": "localhost", "port": "5432", "username": "postgres", "password": "Z0FBQUFBQm9ZaTIwUklVRk9ZSVJyZW9tQXY4VktKS0N6c0ZjNXYyYlgzVVcwdnFmTzkxY0lYRk1wX1lUTGxGZnBWRXRLSXhPZ0o1dUQ0Y3hueUYwM3RQMk1ybHRIczRUeEE9PQ==", "default_db": "postgres", "merge_strategy": "smart_merge"}}, "ui_preferences": {"last_model_type": "dashscope", "default_start_page": "1", "default_end_page": "10", "default_volume_code": "04", "default_chapter_codes": "01"}, "system_info": {"created_time": "2025-06-29T22:28:43.281585", "last_access_time": "2025-06-30T14:24:52.626329", "access_count": 20}}