{"api_keys": {"dashscope_key": "Z0FBQUFBQm9ZVzdldWpjUmdzQWdZbW9GTnh2MUN4RWt0V2xBMTdMM3pQZ1YzQ05fUXByc0Y3SXN4NjBrOUF5SWZNWmJ3NVNfbGF4TmhfN3lTOUVseEI3ZWg1eWVzU0Y4RWljMFYxZ1F4bUtoZ0Q4cE9rV2FiM0xrZUtwLTVpU2lUUWJuY19RVmlfclk=", "openai_key": "", "last_updated": "2025-06-29T20:49:27.341237"}, "database_configs": {"quota_db": {"db_type": "postgresql", "db_name": "beijing2021_quota_test", "host": "localhost", "port": "5432", "username": "postgres", "password": "Z0FBQUFBQm9ZVzdlR3hhaTh4TnJkbGdtSVJyNUE1UV9LQ19VeVNabWxiRDJwNzBQb0QxU3o1MnlVdXBJUEpjT2J0bVljdU0tWFlLMXBidldTMU54bm5pSmZwNlIwVnFpOUE9PQ==", "default_db": "postgres"}, "price_db": {"db_type": "sqlite", "db_name": "price_database.db", "host": "localhost", "port": "3306", "username": "", "password": "", "default_db": "price_db", "merge_strategy": "replace"}}, "ui_preferences": {"last_model_type": "dashscope", "default_start_page": "1", "default_end_page": "10", "default_volume_code": "04", "default_chapter_codes": "01"}, "system_info": {"created_time": "2025-06-29T22:28:43.281585", "last_access_time": "2025-06-30T00:50:38.358530", "access_count": 7}}