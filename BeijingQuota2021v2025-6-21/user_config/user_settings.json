{"api_keys": {"dashscope_key": "Z0FBQUFBQm9ZaU9XSlVrdG5ucUVLVkluSlZnWkJZTnQ5U3dla0tvcWpDX1Z6cFJjTENVRlExS09EclZ4ajN6OFI2ZjBfS1JYdFF1QVA5d05FU3FYOTVpOHVUeUl2aDJqQjZmYWx3WFl4V0FvQ2I3LU9SNy0wN2M5OXFkbkU1NUNaanVEOTNRcGlsMXo=", "openai_key": "", "last_updated": "2025-06-29T20:49:27.341237"}, "database_configs": {"quota_db": {"db_type": "postgresql", "db_name": "beijing2021_quota_test", "host": "localhost", "port": "5432", "username": "postgres", "password": "Z0FBQUFBQm9ZaU9XMk9WeF9zZnhSSnc2UHFiamFPZ3pJZVJaemhycEhsMS1RR3gtTWlwNC1LWVB4Y0xwUFZDSTVZVkdnYTVrb3daYVdEQTNQbWNJUzQ5TmVueUhKeWdyTGc9PQ==", "default_db": "postgres"}, "price_db": {"db_type": "postgresql", "db_name": "price_database.db", "host": "localhost", "port": "3306", "username": "postgres", "password": "Z0FBQUFBQm9ZaU9XYktOWEx1aUd6T1N5WnhVa2hKdXR1MlVlbXRvX29DbmpVSG04TnZ4ZW9ocHNsQnlXc2kzNWQwWjdoWlJpMWIwMzhqSzN4UHJZSUZFclB2NFg2UUJfV3c9PQ==", "default_db": "postgres", "merge_strategy": "replace"}}, "ui_preferences": {"last_model_type": "dashscope", "default_start_page": "1", "default_end_page": "10", "default_volume_code": "04", "default_chapter_codes": "01"}, "system_info": {"created_time": "2025-06-29T22:28:43.281585", "last_access_time": "2025-06-30T13:41:42.833223", "access_count": 12}}