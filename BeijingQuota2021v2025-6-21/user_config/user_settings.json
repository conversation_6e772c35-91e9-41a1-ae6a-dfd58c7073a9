{"api_keys": {"dashscope_key": "Z0FBQUFBQm9Za0xXaUR0MDMzYkJ4anpWTDF2N21ZVUQ1OF82SHoweFpTUGZSbTdITVQtVXU3bmx4VXp4cVhrSndpVi0xNHA2c2tuYjh1aEFGYnlkMUJyeTRTQ0xHdjBiZFNnbWdkbXdtUThUVXJZdEx4RER4LVpaVnU5SnVtSV95T1hoNFN2QUVxaWc=", "openai_key": "", "last_updated": "2025-06-30T14:06:12.390264"}, "database_configs": {"quota_db": {"db_type": "postgresql", "db_name": "beijing2021_quota_test", "host": "localhost", "port": "5432", "username": "postgres", "password": "Z0FBQUFBQm9Za0xXTGROYVh3RVhNb05tQmNaeG5kVDlCeHZ4cC03YWVxWXBqUS13UWJfTEhvWFlIZkdsYl9Tb1Y4eERVYkR6Tm55djVBWnVsZEtqdGxqVlpNN2YtX1Vsb2c9PQ==", "default_db": "postgres"}, "price_db": {"db_type": "postgresql", "db_name": "price_database.db", "host": "localhost", "port": "5432", "username": "postgres", "password": "Z0FBQUFBQm9Za0xXdkt3SUVJWmR5b1lidmtWdElnZzRNNnRKTEs5aVRhNjJsU2ZMM2owOTFyQ3lFemtHRVM0SHV5ZHdIcUJSTWV2VHYzcUdESXh5VFNxVncyc2swV3hlbFE9PQ==", "default_db": "postgres", "merge_strategy": "smart_merge"}}, "ui_preferences": {"last_model_type": "dashscope", "default_start_page": "1", "default_end_page": "10", "default_volume_code": "04", "default_chapter_codes": "01"}, "system_info": {"created_time": "2025-06-29T22:28:43.281585", "last_access_time": "2025-06-30T15:55:02.493429", "access_count": 23}}