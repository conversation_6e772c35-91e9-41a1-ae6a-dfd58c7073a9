{"api_keys": {"dashscope_key": "Z0FBQUFBQm9ZaW11LUVaNnJYSUEzSTkyVmdvM2o1bThvUENSRjgtaWx1SURrczRqTTdwR1lLMzJPaFJxM1hLQnRBVXdDZVcwNEttRzAydDdwZ1FEanM0LWlCX3JZcTBJNEtpMmdPalhWcTl4ME1URTR3V21ndXhCSlJSVmtWY0pJRUxiQ0hzSHkwWEQ=", "openai_key": "", "last_updated": "2025-06-30T14:06:12.390264"}, "database_configs": {"quota_db": {"db_type": "postgresql", "db_name": "beijing2021_quota_test", "host": "localhost", "port": "5432", "username": "postgres", "password": "Z0FBQUFBQm9ZaW11RDRVR0Y4VVZjZUNWV3UzY1NaaktPdU90NWRNNDlSeXVFQnUtRWtCeW9wUkI0T2x0SE8wNmtxaHYzWEc2bU5WeGJLQzlPcUt4a3R5TlFiaU4xTHpBM0E9PQ==", "default_db": "postgres"}, "price_db": {"db_type": "postgresql", "db_name": "price_database.db", "host": "localhost", "port": "5432", "username": "postgres", "password": "Z0FBQUFBQm9ZaW11YXpseUdrLWk5bmtTYXg1b0h0MG94eE1sM2h6WUhKQTNFcHhjRlhxeGoxb08tSE1iWl9GWk5kcHBCR2xnT1pnUW15bWFGUkhNZTdXVWN4RDZ6Uzc3VGc9PQ==", "default_db": "postgres", "merge_strategy": "smart_merge"}}, "ui_preferences": {"last_model_type": "dashscope", "default_start_page": "1", "default_end_page": "10", "default_volume_code": "04", "default_chapter_codes": "01"}, "system_info": {"created_time": "2025-06-29T22:28:43.281585", "last_access_time": "2025-06-30T14:07:42.160910", "access_count": 18}}