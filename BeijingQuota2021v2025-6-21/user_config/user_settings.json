{"api_keys": {"dashscope_key": "Z0FBQUFBQm9ZZ1VWMDR6NEk5c3VDUGZyc1U3cGdfaUdUTG04YzEwTm1HeHVLMDVoNTlIVkd6N0k2WEtTbjVvTHh3UzZJc2N4UEZlU3prN092UURGZjZHNzgxVTNJLXo1bk52N1B3Z1psLVZiOFBVSkkwblBtY1VKQnpkT2FDb2tPNXBqRlk4LWRvTnU=", "openai_key": "", "last_updated": "2025-06-29T20:49:27.341237"}, "database_configs": {"quota_db": {"db_type": "postgresql", "db_name": "beijing2021_quota_test", "host": "localhost", "port": "5432", "username": "postgres", "password": "Z0FBQUFBQm9ZZ1VWMDdYcVg1WFM3d3J0UTFTcDF6aDBEMXcyaGxBZllvVFhfejIwZFV4bkU3d2xVaTItSGV0UDJlSWxkT0VTblc4VXY2TDRaNTJBaXd5N0dKT0twQTJVbmc9PQ==", "default_db": "postgres"}, "price_db": {"db_type": "sqlite", "db_name": "price_database.db", "host": "localhost", "port": "3306", "username": "", "password": "", "default_db": "price_db", "merge_strategy": "replace"}}, "ui_preferences": {"last_model_type": "dashscope", "default_start_page": "1", "default_end_page": "10", "default_volume_code": "04", "default_chapter_codes": "01"}, "system_info": {"created_time": "2025-06-29T22:28:43.281585", "last_access_time": "2025-06-30T11:31:33.966806", "access_count": 9}}