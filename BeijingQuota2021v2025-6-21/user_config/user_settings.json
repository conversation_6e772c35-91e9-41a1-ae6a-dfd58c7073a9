{"api_keys": {"dashscope_key": "Z0FBQUFBQm9ZZVNTTFhZSnhPN1kxc01OZlRYUHhSSkt5cndIUm1UVzhmeWctSTRKV3gwZzFZNHJxZDhCUG9wc1hVaExWNG9vRHNWdm1IZFJxMUlRZ3BOXzZIOVVLc3FJSDQ1a05pb1RIZmNScWtocEdnWWJOejJkTFBhQnZ4S1ZUVXlQa2RVTkFmOVI=", "openai_key": "", "last_updated": "2025-06-29T20:49:27.341237"}, "database_configs": {"quota_db": {"db_type": "postgresql", "db_name": "beijing2021_quota_test", "host": "localhost", "port": "5432", "username": "postgres", "password": "Z0FBQUFBQm9ZZVNTTUQ2Q3VScEhCeWpPaEczcXFQNkhNX3hhVnhiblpkc1I0aFdmSjVUbWdMUUNraVVzZVlkQ2JzNDFEZ2RsWmY3UTg5VWh6amxlV0JJN0EzaDR3SG5NSmc9PQ==", "default_db": "postgres"}, "price_db": {"db_type": "sqlite", "db_name": "price_database.db", "host": "localhost", "port": "3306", "username": "", "password": "", "default_db": "price_db", "merge_strategy": "replace"}}, "ui_preferences": {"last_model_type": "dashscope", "default_start_page": "1", "default_end_page": "10", "default_volume_code": "04", "default_chapter_codes": "01"}, "system_info": {"created_time": "2025-06-29T22:28:43.281585", "last_access_time": "2025-06-30T09:12:50.626812", "access_count": 8}}