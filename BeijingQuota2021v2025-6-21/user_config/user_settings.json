{"api_keys": {"dashscope_key": "Z0FBQUFBQm9ZakJzMjJOWDUxTmhRWTBnLWY0dEtpT0VodDJhZ1BLZVJMdVo2OW5fSWRfR0ctZy1wSDdGUEkxZUJqZXJPVXZ0UkhVTnhuQ3RKNmpuZjJOeW1LajBpSkZJRVZPT0d3Q2tZa0RpX1FwNURVNmNhS3loeUV4NjhPOE9wdjRFXzJBUTAtX1Q=", "openai_key": "", "last_updated": "2025-06-30T14:06:12.390264"}, "database_configs": {"quota_db": {"db_type": "postgresql", "db_name": "beijing2021_quota_test", "host": "localhost", "port": "5432", "username": "postgres", "password": "Z0FBQUFBQm9ZakJzeWYxbWZ5QkVheWlDczlzZzNuYW1pM1doNmRDbDlqUjAtczkyUE5wNXFQc05BZXlaNDVtOHV6R2JMM3E4YUlYM1Z2TDNmQmJQNGpUcE1HcEZjS2pXUlE9PQ==", "default_db": "postgres"}, "price_db": {"db_type": "postgresql", "db_name": "price_database.db", "host": "localhost", "port": "5432", "username": "postgres", "password": "Z0FBQUFBQm9ZakJzT2pUcmFfVEx5aWdBRXJwcV9vN2VKUHhEekFkNFJ4QURGNTFrS1RkQlhla0Rpb2JyR1pZTF9pSm5jVWR1c21jZmF4ZWd3eVlmQkNjR3d1R0QxelprUnc9PQ==", "default_db": "postgres", "merge_strategy": "smart_merge"}}, "ui_preferences": {"last_model_type": "dashscope", "default_start_page": "1", "default_end_page": "10", "default_volume_code": "04", "default_chapter_codes": "01"}, "system_info": {"created_time": "2025-06-29T22:28:43.281585", "last_access_time": "2025-06-30T14:36:28.519074", "access_count": 21}}