{"api_keys": {"dashscope_key": "Z0FBQUFBQm9Za0VvYlVmbEFNSmxCTTctaWw2NmRnZ1I0Q1A3dkhDVGxPSzR0blpTbVpqem5pbDlFR2ladHRvZ243WW9PbmFBMHRTcVZtcF9ZV0UzWFFyZUU1SnVSbl9HUGxXUGRDRzZxV2FYYmx1UVRPYzZvSmFVS2JjeE1XcWYzT21TYVNfUnl2d2M=", "openai_key": "", "last_updated": "2025-06-30T14:06:12.390264"}, "database_configs": {"quota_db": {"db_type": "postgresql", "db_name": "beijing2021_quota_test", "host": "localhost", "port": "5432", "username": "postgres", "password": "Z0FBQUFBQm9Za0VvSDdva0JsNjAzMFRzT2pGYVR1TFdLNTdDV1lZQ3dZTjdIZHh6NnlwbzdIeGNEaWcxUnZwa3Z2aU9fX09ERXFoT1EyYU5XTzRUSUZzVXR4em4xZWlhN2c9PQ==", "default_db": "postgres"}, "price_db": {"db_type": "postgresql", "db_name": "price_database.db", "host": "localhost", "port": "5432", "username": "postgres", "password": "Z0FBQUFBQm9Za0VvUnhHVzFsUTBTNXhzSG9KZUljRFZRSWpaRUVHMko2Zy0xS0w0U2Z4TzhUeHY5V3BDNGRPMUQzU28zRGZsMm9vTVM5Z2FISkd0cEtEby1fY1F2Rkp6bEE9PQ==", "default_db": "postgres", "merge_strategy": "smart_merge"}}, "ui_preferences": {"last_model_type": "dashscope", "default_start_page": "1", "default_end_page": "10", "default_volume_code": "04", "default_chapter_codes": "01"}, "system_info": {"created_time": "2025-06-29T22:28:43.281585", "last_access_time": "2025-06-30T15:47:52.058457", "access_count": 22}}