#!/usr/bin/env python3
"""
测试修复后的AI模型配置系统
"""

import sys
import os
from pathlib import Path

# 添加src目录到路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_ai_processor_improvements():
    """测试AI处理器的改进"""
    print("🧪 测试AI处理器改进")
    print("=" * 50)
    
    try:
        from src.ai_model_processor import AIModelProcessor
        
        # 创建AI处理器
        processor = AIModelProcessor()
        
        print("✅ AI处理器创建成功")
        
        # 测试新的方法
        print(f"\n📋 测试新增方法:")
        
        # 测试get_models_by_provider方法
        providers = ["dashscope", "deepseek", "openai", "anthropic", "custom_openai", "ollama", "lm_studio"]
        
        for provider in providers:
            models = processor.get_models_by_provider(provider)
            print(f"  • {provider}: {len(models)} 个模型")
            for model_key, model_name in models.items():
                print(f"    - {model_key}: {model_name}")
        
        # 测试reload_api_keys方法
        print(f"\n🔄 测试API密钥重新加载:")
        processor.reload_api_keys()
        print("✅ API密钥重新加载成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_config_system_integration():
    """测试配置系统集成"""
    print(f"\n🔗 测试配置系统集成")
    print("=" * 40)
    
    try:
        # 检查主文件中的关键改进
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查关键改进
        improvements = [
            ("provider_models", "Provider模型选择器"),
            ("get_models_by_provider", "按Provider获取模型方法"),
            ("reload_api_keys", "重新加载API密钥"),
            ("on_provider_change.*provider_models", "Provider变更时更新模型列表"),
            ("重新加载AI处理器以应用新配置", "保存配置后重新加载"),
            ("配置已生效", "配置生效提示")
        ]
        
        import re
        
        for pattern, description in improvements:
            if re.search(pattern, content):
                print(f"✅ {description}: 已实现")
            else:
                print(f"❌ {description}: 未实现")
        
        # 检查重复函数是否已清理
        refresh_functions = re.findall(r'def refresh_models\(\):', content)
        print(f"\n🔍 检查重复函数:")
        print(f"  • refresh_models函数数量: {len(refresh_functions)}")
        if len(refresh_functions) == 1:
            print("✅ 重复函数已清理")
        else:
            print("❌ 仍有重复函数")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_provider_model_mapping():
    """测试Provider模型映射"""
    print(f"\n🎯 测试Provider模型映射")
    print("=" * 35)
    
    try:
        from src.ai_model_processor import AIModelProcessor
        
        processor = AIModelProcessor()
        
        # 测试各个provider的模型映射
        test_cases = [
            ("dashscope", ["qwen_qvq_max", "qwen_qvq_plus"]),
            ("deepseek", ["deepseek_api"]),
            ("openai", ["openai_gpt4v"]),
            ("anthropic", ["claude_vision"]),
            ("custom_openai", ["custom_openai"]),
            ("ollama", []),  # 动态获取
            ("lm_studio", [])  # 动态获取
        ]
        
        for provider, expected_models in test_cases:
            models = processor.get_models_by_provider(provider)
            print(f"\n📋 {provider}:")
            print(f"  • 期望模型: {expected_models}")
            print(f"  • 实际模型: {list(models.keys())}")
            
            if provider in ["ollama", "lm_studio"]:
                print(f"  • 状态: 动态检测 ({'有模型' if models else '无模型'})")
            else:
                has_expected = any(model in models for model in expected_models)
                print(f"  • 状态: {'✅ 正常' if has_expected or not expected_models else '❌ 缺少模型'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_configuration_flow():
    """测试配置流程"""
    print(f"\n⚙️ 测试配置流程")
    print("=" * 30)
    
    try:
        # 检查配置流程的完整性
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        flow_steps = [
            ("provider_select", "1. 选择Provider"),
            ("on_provider_change", "2. Provider变更处理"),
            ("provider_models", "3. 显示Provider模型"),
            ("test_api_connection", "4. 测试连接"),
            ("save_api_configuration", "5. 保存配置"),
            ("reload_api_keys", "6. 重新加载配置"),
            ("refresh_models", "7. 刷新模型列表")
        ]
        
        print("📋 配置流程检查:")
        for component, step in flow_steps:
            if component in content:
                print(f"✅ {step}: 已实现")
            else:
                print(f"❌ {step}: 未实现")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 修复后的AI模型配置系统测试")
    print("=" * 60)
    
    # 运行各项测试
    tests = [
        ("AI处理器改进", test_ai_processor_improvements),
        ("配置系统集成", test_config_system_integration),
        ("Provider模型映射", test_provider_model_mapping),
        ("配置流程", test_configuration_flow)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        results[test_name] = test_func()
    
    # 汇总结果
    print(f"\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print(f"\n🎉 所有测试通过！AI模型配置系统已完全修复！")
        print(f"💡 主要修复:")
        print(f"   • Provider-based模型管理")
        print(f"   • 动态模型列表获取")
        print(f"   • 配置与AI处理器正确连接")
        print(f"   • 实时配置重新加载")
        print(f"   • 清理重复函数")
    else:
        print(f"\n⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
