#!/usr/bin/env python3
"""
简单的GLM-4V API测试脚本

直接测试智谱AI GLM-4V API调用，不依赖项目代码

使用方法:
1. 设置环境变量 ZHIPU_API_KEY
2. 运行: py simple_glm4v_test.py
"""

import os
import requests
import json

def test_glm4v_api():
    """测试GLM-4V API调用"""
    print("🔍 测试GLM-4V API调用...")
    
    # 检查API密钥
    api_key = os.getenv("ZHIPU_API_KEY")
    if not api_key:
        print("❌ 错误: 未设置ZHIPU_API_KEY环境变量")
        print("💡 请先设置智谱AI API密钥:")
        print("   在Windows中: set ZHIPU_API_KEY=your_api_key_here")
        print("   或在PowerShell中: $env:ZHIPU_API_KEY='your_api_key_here'")
        print("   获取API密钥: https://open.bigmodel.cn/")
        return False
    
    print(f"✅ API密钥已设置: ***{api_key[-4:]}")
    
    # API调用配置
    url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 简单的文本测试请求
    data = {
        "model": "glm-4v",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "你好，请回复'GLM-4V连接成功'"
                    }
                ]
            }
        ],
        "temperature": 0.1,
        "max_tokens": 50
    }
    
    try:
        print("📡 正在调用GLM-4V API...")
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API调用成功!")
            print("📄 响应内容:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            if 'choices' in result and len(result['choices']) > 0:
                content = result['choices'][0]['message']['content']
                print(f"\n🎯 模型回复: {content}")
                return True
            else:
                print("⚠️ 响应格式异常")
                return False
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"📄 错误信息: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 网络连接错误")
        return False
    except Exception as e:
        print(f"❌ 调用失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 GLM-4V API简单测试")
    print("=" * 50)
    
    # 测试基础API调用
    basic_test_ok = test_glm4v_api()
    
    print("\n" + "=" * 50)
    print("🎉 GLM-4V API测试完成!")
    
    if basic_test_ok:
        print("✅ GLM-4V API完全正常，可以集成到北京定额识别系统")
        print("💡 现在可以在主界面中配置API密钥并使用GLM-4V模型")
    else:
        print("⚠️ GLM-4V API存在问题，请检查API密钥和网络连接")
        print("💡 获取API密钥: https://open.bigmodel.cn/")

if __name__ == "__main__":
    main()
