#!/usr/bin/env python3
"""
测试新的AI模型配置系统
"""

import sys
import os
from pathlib import Path

# 添加src目录到路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_config_system():
    """测试配置系统"""
    print("🧪 测试新的AI模型配置系统")
    print("=" * 60)
    
    try:
        # 检查主文件中的关键组件
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查新的配置组件
        new_components = [
            ("provider_select", "Provider选择器"),
            ("dashscope_config", "阿里云百炼配置"),
            ("deepseek_config", "DeepSeek配置"),
            ("openai_config", "OpenAI配置"),
            ("anthropic_config", "Anthropic配置"),
            ("custom_openai_config", "自定义OpenAI配置"),
            ("ollama_config", "Ollama配置"),
            ("lm_studio_config", "LM Studio配置"),
            ("on_provider_change", "Provider变更处理函数")
        ]
        
        print("📋 检查新配置组件:")
        for component, description in new_components:
            if component in content:
                print(f"✅ {description}: 已找到")
            else:
                print(f"❌ {description}: 未找到")
        
        # 检查是否移除了旧的问题组件
        old_components = [
            ("api_service", "旧的API服务选择"),
            ("api_key_input", "旧的API密钥输入"),
            ("api_url_input", "旧的API URL输入")
        ]
        
        print(f"\n📋 检查旧组件移除:")
        for component, description in old_components:
            if component not in content:
                print(f"✅ {description}: 已移除")
            else:
                print(f"⚠️ {description}: 仍存在")
        
        # 检查Provider选择的choices
        provider_choices = [
            "阿里云百炼",
            "DeepSeek", 
            "OpenAI",
            "Anthropic",
            "自定义OpenAI兼容",
            "本地Ollama",
            "LM Studio"
        ]
        
        print(f"\n📋 检查Provider选择项:")
        for choice in provider_choices:
            if choice in content:
                print(f"✅ {choice}: 已包含")
            else:
                print(f"❌ {choice}: 未包含")
        
        # 检查事件绑定
        event_bindings = [
            ("provider_select.change", "Provider选择事件"),
            ("test_connection_btn.click", "测试连接事件"),
            ("save_config_btn.click", "保存配置事件")
        ]
        
        print(f"\n📋 检查事件绑定:")
        for binding, description in event_bindings:
            if binding in content:
                print(f"✅ {description}: 已绑定")
            else:
                print(f"❌ {description}: 未绑定")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_dropdown_warnings():
    """检查是否还有下拉菜单警告"""
    print(f"\n🔍 检查下拉菜单警告问题")
    print("=" * 40)
    
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查可能导致警告的模式
        warning_patterns = [
            ("value=.*qwen_qvq_max", "硬编码的qwen_qvq_max值"),
            ("allow_custom_value=False", "禁用自定义值的下拉菜单"),
            ("choices=.*qwen_qvq_max", "choices中的qwen_qvq_max")
        ]
        
        import re
        
        for pattern, description in warning_patterns:
            matches = re.findall(pattern, content)
            if matches:
                print(f"⚠️ {description}: 找到 {len(matches)} 处")
                for match in matches[:3]:  # 只显示前3个
                    print(f"   - {match}")
            else:
                print(f"✅ {description}: 未发现")
        
        # 检查新的Provider-based结构
        provider_structure = [
            ("provider_select.*value=None", "Provider选择器默认值为None"),
            ("visible=False.*dashscope_config", "配置区域默认隐藏"),
            ("on_provider_change", "Provider变更处理函数")
        ]
        
        print(f"\n📋 检查新结构:")
        for pattern, description in provider_structure:
            if re.search(pattern, content):
                print(f"✅ {description}: 已实现")
            else:
                print(f"❌ {description}: 未实现")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def test_ui_improvements():
    """测试UI改进"""
    print(f"\n🎨 测试UI改进")
    print("=" * 30)
    
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查UI改进
        ui_improvements = [
            ("modal-overlay", "模态框覆盖层"),
            ("modal-content", "模态框内容"),
            ("Provider选择器", "Provider选择器"),
            ("条件显示配置", "根据Provider显示配置"),
            ("统一的测试连接", "统一的测试连接功能"),
            ("统一的保存配置", "统一的保存配置功能")
        ]
        
        for improvement, description in ui_improvements:
            if improvement in content:
                print(f"✅ {description}: 已实现")
            else:
                print(f"❌ {description}: 未实现")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 新AI模型配置系统测试")
    print("=" * 60)
    
    # 测试配置系统
    config_test = test_config_system()
    
    # 检查下拉菜单警告
    dropdown_test = test_dropdown_warnings()
    
    # 测试UI改进
    ui_test = test_ui_improvements()
    
    print(f"\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print(f"   🔧 配置系统: {'✅ 通过' if config_test else '❌ 失败'}")
    print(f"   🔍 下拉菜单: {'✅ 通过' if dropdown_test else '❌ 失败'}")
    print(f"   🎨 UI改进: {'✅ 通过' if ui_test else '❌ 失败'}")
    
    if config_test and dropdown_test and ui_test:
        print(f"\n🎉 新的AI模型配置系统已完全实现！")
        print(f"💡 主要改进:")
        print(f"   • Provider-based配置结构")
        print(f"   • 条件显示配置区域")
        print(f"   • 解决下拉菜单警告问题")
        print(f"   • 统一的测试和保存功能")
        print(f"   • 更好的用户体验")
    else:
        print(f"\n⚠️ 部分功能需要进一步检查")

if __name__ == "__main__":
    main()
