#!/usr/bin/env python3
"""
调试CSV数据导入问题
"""

import os
import sys
import pandas as pd
from pathlib import Path

# 添加src目录到路径
sys.path.append('src')

def create_test_data():
    """创建测试数据文件"""
    # 确保output目录存在
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    # 创建parent_quotas测试数据
    parent_data = {
        '定额编号': ['001-001', '001-002', '001-003'],
        '定额名称': ['混凝土浇筑C30', '钢筋绑扎HPB300', '模板安装拆除'],
        '单位': ['m³', 'kg', 'm²'],
        '人工费': [120.5, 85.2, 95.8],
        '材料费': [450.8, 320.5, 180.2],
        '机械费': [80.3, 45.8, 25.6],
        '合计': [651.6, 451.5, 301.6]
    }
    
    parent_df = pd.DataFrame(parent_data)
    parent_csv_path = output_dir / "debug_parent_quotas.csv"
    parent_df.to_csv(parent_csv_path, index=False, encoding='utf-8-sig')
    
    # 创建child_resources测试数据
    child_data = {
        '定额编号': ['001-001', '001-001', '001-002'],
        '资源编号': ['R001', 'R002', 'R003'],
        '资源名称': ['C30混凝土', '人工', 'HPB300钢筋'],
        '资源类型': ['材料', '人工', '材料'],
        '数量': [1.05, 8.5, 1.02],
        '单位': ['m³', '工日', 'kg'],
        '单价': [420.0, 150.0, 4.2],
        '合价': [441.0, 1275.0, 4.284]
    }
    
    child_df = pd.DataFrame(child_data)
    child_csv_path = output_dir / "debug_child_resources.csv"
    child_df.to_csv(child_csv_path, index=False, encoding='utf-8-sig')
    
    print(f"✅ 创建调试测试数据文件:")
    print(f"   - {parent_csv_path} ({len(parent_df)} 行)")
    print(f"   - {child_csv_path} ({len(child_df)} 行)")
    
    # 验证文件内容
    print(f"\n📋 parent_quotas内容预览:")
    print(parent_df.head())
    print(f"\n📋 child_resources内容预览:")
    print(child_df.head())
    
    return [str(parent_csv_path)], [str(child_csv_path)]

def test_mcp_sql_generation():
    """测试MCP工具的SQL生成"""
    try:
        from mcp_database_converter import MCPDatabaseConverter
        
        # 创建测试数据
        parent_files, child_files = create_test_data()
        all_csv_files = parent_files + child_files
        
        print(f"\n🔧 测试MCP工具SQL生成...")
        print(f"📋 输入文件: {all_csv_files}")
        
        # 创建MCP转换器
        converter = MCPDatabaseConverter()
        
        # 生成SQL脚本
        output_path = "output/debug_test.sql"
        success, message, stats = converter.convert_to_sql_script(
            all_csv_files, output_path, 'postgresql'
        )
        
        print(f"📊 SQL生成结果:")
        print(f"   - success: {success}")
        print(f"   - message: {message}")
        print(f"   - stats: {stats}")
        
        if success and os.path.exists(output_path):
            # 读取生成的SQL内容
            with open(output_path, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            print(f"\n📄 生成的SQL文件:")
            print(f"   - 文件大小: {len(sql_content)} 字符")
            print(f"   - 文件路径: {output_path}")
            
            # 显示SQL内容的前1000字符
            print(f"\n📋 SQL内容预览 (前1000字符):")
            print("=" * 50)
            print(sql_content[:1000])
            print("=" * 50)
            
            # 分析SQL语句
            sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            print(f"\n📊 SQL语句分析:")
            print(f"   - 总语句数: {len(sql_statements)}")
            
            create_table_count = 0
            insert_count = 0
            
            for i, stmt in enumerate(sql_statements[:10]):  # 只显示前10个
                stmt_type = "未知"
                if stmt.upper().startswith('CREATE TABLE'):
                    stmt_type = "CREATE TABLE"
                    create_table_count += 1
                elif stmt.upper().startswith('INSERT INTO'):
                    stmt_type = "INSERT INTO"
                    insert_count += 1
                elif stmt.upper().startswith('DROP TABLE'):
                    stmt_type = "DROP TABLE"
                
                print(f"   {i+1}. {stmt_type}: {stmt[:80]}...")
            
            print(f"\n📈 语句统计:")
            print(f"   - CREATE TABLE语句: {create_table_count}")
            print(f"   - INSERT INTO语句: {insert_count}")
            
            return True, sql_content
        else:
            print(f"❌ SQL文件生成失败或文件不存在")
            return False, None
        
    except Exception as e:
        print(f"❌ MCP SQL生成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, None

def test_postgresql_execution():
    """测试PostgreSQL执行"""
    try:
        # 首先测试SQL生成
        success, sql_content = test_mcp_sql_generation()
        if not success:
            return False
        
        print(f"\n🗄️ 测试PostgreSQL执行...")
        
        import psycopg2
        from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
        
        # 连接配置
        config = {
            'host': 'localhost',
            'port': 5432,
            'user': 'postgres',
            'password': 'postgres123',
            'default_db': 'postgres',
            'target_db': 'debug_csv_import_test'
        }
        
        # 连接到默认数据库
        conn = psycopg2.connect(
            host=config['host'],
            port=config['port'],
            user=config['user'],
            password=config['password'],
            database=config['default_db']
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # 删除测试数据库（如果存在）
        cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (config['target_db'],))
        if cursor.fetchone():
            cursor.execute(f'DROP DATABASE "{config["target_db"]}"')
            print(f"🗑️ 删除已存在的测试数据库")
        
        # 创建测试数据库
        cursor.execute(f'CREATE DATABASE "{config["target_db"]}"')
        print(f"✅ 创建测试数据库: {config['target_db']}")
        
        cursor.close()
        conn.close()
        
        # 连接到测试数据库
        target_conn = psycopg2.connect(
            host=config['host'],
            port=config['port'],
            user=config['user'],
            password=config['password'],
            database=config['target_db']
        )
        target_cursor = target_conn.cursor()
        
        # 执行SQL脚本
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        print(f"📊 执行SQL脚本:")
        print(f"   - 语句总数: {len(sql_statements)}")
        
        executed_count = 0
        error_count = 0
        
        for i, stmt in enumerate(sql_statements):
            if stmt.strip():
                try:
                    target_cursor.execute(stmt)
                    executed_count += 1
                    if i < 5:  # 显示前5个语句的执行情况
                        print(f"   ✅ 语句 {i+1}: {stmt[:50]}...")
                except Exception as e:
                    error_count += 1
                    print(f"   ❌ 语句 {i+1} 错误: {e}")
                    print(f"      语句内容: {stmt[:100]}...")
        
        target_conn.commit()
        
        print(f"\n📈 执行结果:")
        print(f"   - 成功执行: {executed_count} 个语句")
        print(f"   - 执行错误: {error_count} 个语句")
        
        # 检查表和数据
        target_cursor.execute("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
        """)
        tables = [row[0] for row in target_cursor.fetchall()]
        
        print(f"\n📋 创建的表:")
        total_rows = 0
        for table in tables:
            target_cursor.execute(f'SELECT COUNT(*) FROM "{table}"')
            count = target_cursor.fetchone()[0]
            total_rows += count
            print(f"   - {table}: {count} 行")
        
        print(f"\n📊 最终统计:")
        print(f"   - 表数量: {len(tables)}")
        print(f"   - 总记录数: {total_rows}")
        
        target_cursor.close()
        target_conn.close()
        
        return len(tables) > 0 and total_rows > 0
        
    except Exception as e:
        print(f"❌ PostgreSQL执行测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 CSV数据导入调试")
    print("=" * 50)
    
    # 检查基础依赖
    try:
        import pandas as pd
        print("✅ pandas 已安装")
    except ImportError:
        print("❌ pandas 未安装")
        return False
    
    try:
        import psycopg2
        print("✅ psycopg2 已安装")
    except ImportError:
        print("❌ psycopg2 未安装")
        return False
    
    # 运行测试
    success = test_postgresql_execution()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 CSV数据导入调试成功!")
        print("\n💡 发现的问题:")
        print("1. ✅ CSV文件创建正常")
        print("2. ✅ MCP工具SQL生成正常")
        print("3. ✅ PostgreSQL执行正常")
        print("4. ✅ 数据成功导入")
        
        print("\n🔧 如果Web界面仍然显示0个表，可能的原因:")
        print("1. CSV文件路径问题")
        print("2. 文件编码问题")
        print("3. SQL执行过程中的错误被忽略")
        print("4. 事务提交问题")
    else:
        print("❌ CSV数据导入调试失败!")
        print("\n🔧 需要检查:")
        print("1. CSV文件格式和内容")
        print("2. MCP工具的SQL生成")
        print("3. PostgreSQL的SQL执行")
        print("4. 错误处理和日志")
    
    return success

if __name__ == "__main__":
    main()
