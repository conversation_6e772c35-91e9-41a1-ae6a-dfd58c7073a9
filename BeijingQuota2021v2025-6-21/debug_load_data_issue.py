#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试加载数据问题
检查连接状态同步是否正确
"""

def debug_load_data_issue():
    """调试加载数据问题"""
    try:
        print("🔍 调试加载数据问题...")
        
        from main import QuotaExtractionApp
        
        # 创建应用实例
        app = QuotaExtractionApp()
        
        print("✅ 应用实例创建成功")
        
        # 模拟连接数据库
        db_type = "postgresql"
        db_path = ""
        db_host = "localhost"
        db_port = "5432"
        db_name = "beijing2021_quota_test"
        db_user = "postgres"
        db_password = "postgres123"  # 请根据实际情况修改
        
        print(f"\n🔗 模拟连接数据库...")
        
        # 调用钩子方法连接数据库
        result = app._handle_database_connection_with_revision_update(
            db_type, db_path, db_host, db_port, db_name, db_user, db_password
        )
        
        connection_status = result[0]
        print(f"   连接结果: {connection_status}")
        
        # 检查各个组件的连接状态
        print(f"\n🔍 检查各组件连接状态...")
        
        # 1. 检查 advanced_quota_handlers
        if app.advanced_quota_handlers:
            print(f"✅ advanced_quota_handlers 存在")
            if hasattr(app.advanced_quota_handlers, 'manager') and app.advanced_quota_handlers.manager:
                print(f"✅ advanced_quota_handlers.manager 存在")
                if hasattr(app.advanced_quota_handlers.manager, 'connection') and app.advanced_quota_handlers.manager.connection:
                    print(f"✅ advanced_quota_handlers.manager.connection 存在")
                else:
                    print(f"❌ advanced_quota_handlers.manager.connection 不存在")
            else:
                print(f"❌ advanced_quota_handlers.manager 不存在")
        else:
            print(f"❌ advanced_quota_handlers 不存在")
        
        # 2. 检查 advanced_quota_interface
        if hasattr(app, 'advanced_quota_interface') and app.advanced_quota_interface:
            print(f"✅ advanced_quota_interface 存在")
            if hasattr(app.advanced_quota_interface, 'quota_manager') and app.advanced_quota_interface.quota_manager:
                print(f"✅ advanced_quota_interface.quota_manager 存在")
                if hasattr(app.advanced_quota_interface.quota_manager, 'connection') and app.advanced_quota_interface.quota_manager.connection:
                    print(f"✅ advanced_quota_interface.quota_manager.connection 存在")
                    
                    # 测试连接
                    try:
                        cursor = app.advanced_quota_interface.quota_manager.connection.cursor()
                        cursor.execute("SELECT 1")
                        cursor.fetchone()
                        cursor.close()
                        print(f"✅ 连接测试成功")
                    except Exception as e:
                        print(f"❌ 连接测试失败: {e}")
                else:
                    print(f"❌ advanced_quota_interface.quota_manager.connection 不存在")
                
                # 检查修订处理器
                if app.advanced_quota_interface.quota_manager.revision_processor:
                    print(f"✅ revision_processor 存在")
                    if hasattr(app.advanced_quota_interface.quota_manager.revision_processor, 'current_connection') and app.advanced_quota_interface.quota_manager.revision_processor.current_connection:
                        print(f"✅ revision_processor.current_connection 存在")
                    else:
                        print(f"❌ revision_processor.current_connection 不存在")
                else:
                    print(f"❌ revision_processor 不存在")
            else:
                print(f"❌ advanced_quota_interface.quota_manager 不存在")
        else:
            print(f"❌ advanced_quota_interface 不存在")
        
        # 3. 测试加载数据方法
        print(f"\n🔍 测试加载数据方法...")
        if hasattr(app, 'advanced_quota_interface') and app.advanced_quota_interface:
            try:
                result = app.advanced_quota_interface._handle_load_revision_data()
                print(f"✅ 加载数据方法调用成功")
                print(f"   返回值数量: {len(result)}")
                
                if len(result) >= 6:
                    status_html, info_html, data_stats_html, quota_data, revision_status, revision_log = result[:6]
                    print(f"   状态: {status_html}")
                    print(f"   修订状态: {revision_status}")
                    print(f"   修订日志: {revision_log}")
                    print(f"   数据行数: {len(quota_data) if quota_data else 0}")
                
            except Exception as e:
                print(f"❌ 加载数据方法调用失败: {e}")
                import traceback
                traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_load_data_issue()
