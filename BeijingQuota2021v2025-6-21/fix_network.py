#!/usr/bin/env python3
import os
import ssl
import requests
from urllib3.util import ssl_

# 修复SSL证书问题
try:
    import certifi
    os.environ['SSL_CERT_FILE'] = certifi.where()
    os.environ['REQUESTS_CA_BUNDLE'] = certifi.where()
    print("SSL证书路径已设置")
except ImportError:
    print("请安装certifi: pip install certifi")

# 禁用SSL警告
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

print("网络修复脚本执行完成")
