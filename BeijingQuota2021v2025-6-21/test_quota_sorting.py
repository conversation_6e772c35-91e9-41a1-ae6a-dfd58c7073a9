#!/usr/bin/env python3
"""
测试定额编号排序算法
"""

import sys
import re
sys.path.append('src')

def sort_quota_code(quota_code: str) -> tuple:
    """
    智能排序定额编号
    将 "1-1", "1-2", "1-11" 等转换为可正确排序的元组
    确保 1-1, 1-2, 1-3, ..., 1-10, 1-11 的正确顺序
    """
    try:
        # 分割定额编号，如 "1-1" -> ["1", "1"]
        parts = quota_code.split('-')
        # 转换为整数元组用于排序，确保数字比较而不是字符串比较
        numeric_parts = []
        for part in parts:
            if part.isdigit():
                numeric_parts.append(int(part))
            else:
                # 如果不是纯数字，尝试提取数字部分
                numbers = re.findall(r'\d+', part)
                if numbers:
                    numeric_parts.append(int(numbers[0]))
                else:
                    # 如果没有数字，使用ASCII值
                    numeric_parts.append(ord(part[0]) if part else 0)
        
        return tuple(numeric_parts)
    except (ValueError, AttributeError, IndexError):
        # 如果转换失败，返回一个大的数值，让它排在最后
        return (999999, 999999)

def test_sorting():
    """测试排序算法"""
    print("🧪 测试定额编号排序算法")
    print("=" * 50)
    
    # 测试用例
    test_codes = [
        "1-1", "1-2", "1-3", "1-10", "1-11", "1-12", "1-13", "1-14", "1-15",
        "1-16", "1-17", "1-18", "1-19", "1-20", "1-21", "1-22", "1-23", "1-24",
        "1-25", "1-26", "1-27", "1-28", "1-29", "2-1", "2-2", "2-10", "2-11",
        "04-01-1-6", "04-01-1-7", "04-01-1-10", "04-01-1-11"
    ]
    
    print("📋 原始顺序:")
    for i, code in enumerate(test_codes, 1):
        print(f"   {i:2d}. {code}")
    
    # 使用排序算法排序
    sorted_codes = sorted(test_codes, key=sort_quota_code)
    
    print(f"\n✅ 排序后顺序:")
    for i, code in enumerate(sorted_codes, 1):
        sort_key = sort_quota_code(code)
        print(f"   {i:2d}. {code} (排序键: {sort_key})")
    
    # 验证关键的排序关系
    print(f"\n🔍 关键排序验证:")
    
    # 验证1-1到1-29的顺序
    chapter1_codes = [code for code in sorted_codes if code.startswith("1-")]
    print(f"   第1章定额项顺序: {chapter1_codes}")
    
    # 检查1-2是否在1-10之前
    pos_1_2 = chapter1_codes.index("1-2") if "1-2" in chapter1_codes else -1
    pos_1_10 = chapter1_codes.index("1-10") if "1-10" in chapter1_codes else -1
    
    if pos_1_2 != -1 and pos_1_10 != -1:
        if pos_1_2 < pos_1_10:
            print(f"   ✅ 1-2 (位置{pos_1_2+1}) 正确排在 1-10 (位置{pos_1_10+1}) 之前")
        else:
            print(f"   ❌ 1-2 (位置{pos_1_2+1}) 错误排在 1-10 (位置{pos_1_10+1}) 之后")
    
    # 检查1-3是否在1-11之前
    pos_1_3 = chapter1_codes.index("1-3") if "1-3" in chapter1_codes else -1
    pos_1_11 = chapter1_codes.index("1-11") if "1-11" in chapter1_codes else -1
    
    if pos_1_3 != -1 and pos_1_11 != -1:
        if pos_1_3 < pos_1_11:
            print(f"   ✅ 1-3 (位置{pos_1_3+1}) 正确排在 1-11 (位置{pos_1_11+1}) 之前")
        else:
            print(f"   ❌ 1-3 (位置{pos_1_3+1}) 错误排在 1-11 (位置{pos_1_11+1}) 之后")
    
    # 检查多级编号排序
    multi_level_codes = [code for code in sorted_codes if code.startswith("04-01-1-")]
    if multi_level_codes:
        print(f"   多级编号排序: {multi_level_codes}")
    
    return sorted_codes

def test_with_real_data():
    """使用真实数据测试"""
    print(f"\n" + "=" * 50)
    print("🔍 使用真实数据测试排序")
    print("=" * 50)
    
    try:
        from intelligent_quota_processor import IntelligentQuotaProcessor
        
        # 创建处理器
        processor = IntelligentQuotaProcessor()
        
        # 测试排序方法
        test_codes = [
            "1-1", "1-2", "1-3", "1-10", "1-11", "1-12", "1-20", "1-21",
            "2-1", "2-2", "2-10", "04-01-1-6", "04-01-1-10"
        ]
        
        print("📋 使用智能处理器的排序方法:")
        sorted_codes = sorted(test_codes, key=processor._sort_quota_code)
        
        for i, code in enumerate(sorted_codes, 1):
            sort_key = processor._sort_quota_code(code)
            print(f"   {i:2d}. {code} (排序键: {sort_key})")
        
        return True
        
    except ImportError as e:
        print(f"❌ 无法导入智能处理器: {e}")
        return False

def main():
    """主函数"""
    print("🚀 定额编号排序测试套件")
    print("=" * 50)
    
    # 测试1: 独立排序算法
    sorted_codes = test_sorting()
    
    # 测试2: 智能处理器排序方法
    test_with_real_data()
    
    print(f"\n" + "=" * 50)
    print("📊 测试总结")
    print("=" * 50)
    
    # 检查关键的排序关系
    chapter1_codes = [code for code in sorted_codes if code.startswith("1-") and len(code.split('-')) == 2]
    
    print(f"第1章定额项排序结果:")
    for i, code in enumerate(chapter1_codes, 1):
        print(f"   {i:2d}. {code}")
    
    # 验证是否符合预期
    expected_order = ["1-1", "1-2", "1-3", "1-10", "1-11", "1-12", "1-13", "1-14", "1-15", 
                     "1-16", "1-17", "1-18", "1-19", "1-20", "1-21", "1-22", "1-23", "1-24",
                     "1-25", "1-26", "1-27", "1-28", "1-29"]
    
    actual_order = [code for code in chapter1_codes if code in expected_order]
    
    if actual_order == sorted([code for code in expected_order if code in actual_order], key=sort_quota_code):
        print(f"\n✅ 排序算法正确！")
    else:
        print(f"\n❌ 排序算法需要调整")
        print(f"期望顺序: {expected_order}")
        print(f"实际顺序: {actual_order}")

if __name__ == "__main__":
    main()
