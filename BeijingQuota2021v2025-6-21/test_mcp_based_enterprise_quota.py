#!/usr/bin/env python3
"""
测试基于MCP工具的企业定额管理系统
"""

import os
import sys
import pandas as pd
from pathlib import Path

# 添加src目录到路径
sys.path.append('src')

def create_test_data():
    """创建测试数据文件"""
    # 确保output目录存在
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    # 创建parent_quotas测试数据
    parent_data = {
        '定额编号': ['001-001', '001-002', '001-003', '002-001', '002-002'],
        '定额名称': ['混凝土浇筑C30', '钢筋绑扎HPB300', '模板安装拆除', '砌砖工程MU10', '抹灰工程'],
        '单位': ['m³', 'kg', 'm²', 'm³', 'm²'],
        '人工费': [120.5, 85.2, 95.8, 110.3, 75.6],
        '材料费': [450.8, 320.5, 180.2, 280.9, 120.4],
        '机械费': [80.3, 45.8, 25.6, 35.2, 15.8],
        '合计': [651.6, 451.5, 301.6, 426.4, 211.8]
    }
    
    parent_df = pd.DataFrame(parent_data)
    parent_csv_path = output_dir / "parent_quotas_mcp_test.csv"
    parent_df.to_csv(parent_csv_path, index=False, encoding='utf-8-sig')
    
    # 创建child_resources测试数据
    child_data = {
        '定额编号': ['001-001', '001-001', '001-001', '001-002', '001-002', '002-001', '002-001'],
        '资源编号': ['R001', 'R002', 'R003', 'R004', 'R005', 'R006', 'R007'],
        '资源名称': ['C30混凝土', '人工', '机械台班', 'HPB300钢筋', '人工', 'MU10砖', '砂浆'],
        '资源类型': ['材料', '人工', '机械', '材料', '人工', '材料', '材料'],
        '数量': [1.05, 8.5, 2.3, 1.02, 12.8, 0.98, 0.25],
        '单位': ['m³', '工日', '台班', 'kg', '工日', 'm³', 'm³'],
        '单价': [420.0, 150.0, 350.0, 4.2, 150.0, 280.0, 180.0],
        '合价': [441.0, 1275.0, 805.0, 4.284, 1920.0, 274.4, 45.0]
    }
    
    child_df = pd.DataFrame(child_data)
    child_csv_path = output_dir / "child_resources_mcp_test.csv"
    child_df.to_csv(child_csv_path, index=False, encoding='utf-8-sig')
    
    print(f"✅ 创建测试数据文件:")
    print(f"   - {parent_csv_path}")
    print(f"   - {child_csv_path}")
    
    return str(parent_csv_path), str(child_csv_path)

def test_mcp_based_enterprise_quota():
    """测试基于MCP工具的企业定额管理系统"""
    try:
        from enterprise_quota_manager import EnterpriseQuotaManager
        
        # 创建测试数据
        parent_file, child_file = create_test_data()
        
        # 创建管理器
        manager = EnterpriseQuotaManager()
        
        print("\n🔧 测试基于MCP工具的数据库创建...")
        
        # 测试SQLite数据库创建
        print("\n📱 测试SQLite数据库创建...")
        db_config = {
            'database_path': 'output/mcp_enterprise_quota.db'
        }
        
        success, message = manager.create_quota_database(
            'sqlite', db_config, [parent_file], [child_file]
        )
        
        if success:
            print("✅ SQLite数据库创建成功!")
            print(f"📋 {message}")
            
            # 测试数据库连接
            print("\n🔗 测试数据库连接...")
            success, conn_message = manager.connect_to_database('output/mcp_enterprise_quota.db')
            
            if success:
                print(f"✅ {conn_message}")
                
                # 测试统计信息
                print("\n📊 测试统计信息...")
                success, stats_msg, stats = manager.get_database_statistics()
                
                if success:
                    print(f"✅ {stats_msg}")
                    print(f"   - 数据库类型: {stats.get('database_type')}")
                    print(f"   - 表数量: {stats.get('total_tables')}")
                    print(f"   - 总记录数: {stats.get('total_rows')}")
                    print(f"   - 数据库大小: {stats.get('db_size_kb', 0):.2f} KB")
                    
                    if 'tables' in stats:
                        print("   - 表详情:")
                        for table in stats['tables']:
                            print(f"     • {table['name']}: {table['rows']} 行")
                
                # 测试搜索功能
                print("\n🔍 测试搜索功能...")
                success, search_msg, results = manager.search_quotas("混凝土")
                
                if success:
                    print(f"✅ {search_msg}")
                    for result in results:
                        # 显示前几个字段
                        display_fields = list(result.keys())[:4]
                        display_info = ", ".join([f"{k}: {result[k]}" for k in display_fields])
                        print(f"   - {display_info}")
                
                # 测试预览功能
                print("\n👀 测试预览功能...")
                preview_html = manager.preview_database('output/mcp_enterprise_quota.db')
                if "SQLite数据库预览" in preview_html:
                    print("✅ 预览功能正常")
                else:
                    print("⚠️ 预览功能可能有问题")
                
                manager.close_connection()
            else:
                print(f"❌ 数据库连接失败: {conn_message}")
        else:
            print(f"❌ SQLite数据库创建失败: {message}")
            return False
        
        # 测试MongoDB JSON导出
        print("\n🍃 测试MongoDB JSON导出...")
        db_config = {
            'database_path': 'output/mcp_enterprise_quota.json'
        }
        
        success, message = manager.create_quota_database(
            'mongodb', db_config, [parent_file], [child_file]
        )
        
        if success:
            print("✅ MongoDB JSON导出成功!")
            
            # 测试JSON文件连接
            success, conn_message = manager.connect_to_database('output/mcp_enterprise_quota.json')
            if success:
                print(f"✅ {conn_message}")
                
                # 测试JSON统计信息
                success, stats_msg, stats = manager.get_database_statistics()
                if success:
                    print(f"✅ JSON统计: {stats.get('database_type')}, 大小: {stats.get('db_size_kb', 0):.2f} KB")
        else:
            print(f"❌ MongoDB JSON导出失败: {message}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 测试基于MCP工具的企业定额管理系统")
    print("=" * 60)
    
    # 检查基础依赖
    try:
        import pandas as pd
        print("✅ pandas 已安装")
    except ImportError:
        print("❌ pandas 未安装")
        return False
    
    try:
        import sqlite3
        print("✅ sqlite3 已安装")
    except ImportError:
        print("❌ sqlite3 未安装")
        return False
    
    # 运行测试
    success = test_mcp_based_enterprise_quota()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 基于MCP工具的企业定额管理系统测试通过!")
        print("\n💡 MCP工具集成优势:")
        print("1. ✅ 复用成熟的数据库转换功能")
        print("2. ✅ 避免重复造轮子，减少维护成本")
        print("3. ✅ 保持系统架构的一致性")
        print("4. ✅ 利用MCP工具的预览和统计功能")
        print("5. ✅ 支持多种数据库格式的统一接口")
        
        print("\n🎯 系统架构改进:")
        print("- 🔧 企业定额管理系统专注于业务逻辑")
        print("- 🗄️ MCP工具负责数据库操作和转换")
        print("- 🔗 通过组合模式实现功能复用")
        print("- 📊 统一的数据库预览和统计接口")
        
        print("\n📋 使用建议:")
        print("1. 使用MCP数据库转换工具创建数据库")
        print("2. 使用企业定额管理系统进行业务查询")
        print("3. 利用MCP工具的预览功能查看数据")
        print("4. 通过统一接口管理不同类型的数据库")
    else:
        print("❌ 基于MCP工具的企业定额管理系统测试失败!")
    
    return success

if __name__ == "__main__":
    main()
