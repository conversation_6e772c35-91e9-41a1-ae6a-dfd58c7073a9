<!DOCTYPE html>
<html>
<head>
    <title>北京市2021消耗定额表04册 01-1-6</title>
    <style>
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        input { width: 100px; padding: 4px; }
        .total-row { font-weight: bold; }
    </style>
</head>
<body>
<!-- 父级表格 -->
<table>
    <tr>
        <th>编号</th>
        <th>定额项名称</th>
        <th>工作内容</th>
        <th>单位</th>
        <th>综合单价（元/m³）</th>
    </tr>
    <tr>
        <td>04-01-1-6</td>
        <td>人工挖沟槽土方 一、二类土</td>
        <td>挖土、余土清理、修整底边、打钉拍底等</td>
        <td>m³</td>
        <td id="total-price">0.00</td>
    </tr>
</table>

<!-- 子级表格 -->
<table>
    <tr>
        <th>资源编号</th>
        <th>类别</th>
        <th>子项名称</th>
        <th>单位</th>
        <th>消耗量</th>
        <th>单价（元）</th>
        <th>合价（元/m³）</th>
    </tr>
    <!-- 人工 -->
    <tr class="resource-row">
        <td>00010701</td>
        <td>人工</td>
        <td>综合用工三类</td>
        <td>工日/m³</td>
        <td>0.182</td>
        <td><input type="number" class="price-input" data-unit="工日"></td>
        <td class="subtotal">0.00</td>
    </tr>
    <!-- 机械 -->
    <tr class="resource-row">
        <td>99030030</td>
        <td>机械</td>
        <td>电动打钉机</td>
        <td>台班/m³</td>
        <td>0.0039</td>
        <td><input type="number" class="price-input" data-unit="台班"></td>
        <td class="subtotal">0.00</td>
    </tr>
    <!-- 其他费用（特殊处理%） -->
    <tr class="percentage-row">
        <td>99460004</td>
        <td>其他费用</td>
        <td>其他机具费占人工费</td>
        <td>%</td>
        <td>1.50</td>
        <td><input type="number" class="price-input" data-unit="%" disabled></td>
        <td class="subtotal">0.00</td>
    </tr>
</table>

<script>
    // 监听所有单价输入框的变动
    document.querySelectorAll('.price-input').forEach(input => {
        input.addEventListener('input', updateCalculations);
    });

    // 计算函数
    function updateCalculations() {
        let total = 0;
        const percentageRow = document.querySelector('.percentage-row');

        // 第一步：计算非%行的合价
        document.querySelectorAll('.resource-row').forEach(row => {
            const price = parseFloat(row.querySelector('.price-input').value) || 0;
            const consumption = parseFloat(row.querySelector('td:nth-child(5)').textContent);
            const subtotal = price * consumption;
            row.querySelector('.subtotal').textContent = subtotal.toFixed(2);
            total += subtotal;
        });

        // 第二步：计算%行的合价（基于其他行总和）
        const percentage = parseFloat(percentageRow.querySelector('td:nth-child(5)').textContent) / 100;
        const percentageSubtotal = total * percentage;
        percentageRow.querySelector('.subtotal').textContent = percentageSubtotal.toFixed(2);
        total += percentageSubtotal;

        // 更新父级综合单价
        document.getElementById('total-price').textContent = total.toFixed(2);
    }
</script>
</body>
</html>