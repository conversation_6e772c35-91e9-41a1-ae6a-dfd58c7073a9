#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PostgreSQL连接诊断脚本
用于诊断和修复PostgreSQL连接问题
"""

import os
import sys
import json
from pathlib import Path

def load_user_config():
    """加载用户配置"""
    try:
        config_path = Path("user_config/user_settings.json")
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return None
    except Exception as e:
        print(f"❌ 加载用户配置失败: {e}")
        return None

def decrypt_password(encrypted_password):
    """解密密码"""
    try:
        from src.config_persistence_manager import ConfigPersistenceManager

        # 使用配置持久化管理器解密
        config_manager = ConfigPersistenceManager()
        decrypted = config_manager._decrypt_sensitive_data(encrypted_password)
        return decrypted
    except Exception as e:
        print(f"❌ 密码解密失败: {e}")
        return None

def test_postgresql_basic():
    """测试基本PostgreSQL连接"""
    try:
        import psycopg2
        print("✅ psycopg2模块已安装")
        
        # 基本连接测试
        print("\n🔍 测试基本PostgreSQL连接...")
        
        # 尝试连接到默认数据库
        conn = psycopg2.connect(
            host='localhost',
            port=5432,
            user='postgres',
            password='postgres123',  # 请根据实际情况修改
            database='postgres',
            client_encoding='utf8'
        )
        
        cursor = conn.cursor()
        cursor.execute("SELECT version()")
        version = cursor.fetchone()[0]
        print(f"✅ PostgreSQL连接成功!")
        print(f"   版本: {version.split(',')[0]}")
        
        # 列出现有数据库
        cursor.execute("SELECT datname FROM pg_database WHERE datistemplate = false")
        databases = [row[0] for row in cursor.fetchall()]
        print(f"   现有数据库: {databases}")
        
        cursor.close()
        conn.close()
        
        return True, databases
        
    except ImportError:
        print("❌ 缺少psycopg2模块")
        print("💡 请安装: pip install psycopg2-binary")
        return False, []
    except Exception as e:
        print(f"❌ PostgreSQL连接失败: {e}")
        print("💡 请确认:")
        print("1. PostgreSQL服务已启动")
        print("2. 用户名密码正确 (默认: postgres/postgres123)")
        print("3. 端口5432可访问")
        return False, []

def test_user_config_connection():
    """测试用户配置的连接"""
    print("\n🔍 测试用户配置的PostgreSQL连接...")
    
    config = load_user_config()
    if not config:
        print("❌ 无法加载用户配置")
        return False
    
    quota_db_config = config.get('database_configs', {}).get('quota_db', {})
    if quota_db_config.get('db_type') != 'postgresql':
        print("❌ 用户配置中没有PostgreSQL数据库配置")
        return False
    
    print(f"📋 配置信息:")
    print(f"   主机: {quota_db_config.get('host')}")
    print(f"   端口: {quota_db_config.get('port')}")
    print(f"   用户: {quota_db_config.get('username')}")
    print(f"   数据库名: {quota_db_config.get('db_name')}")
    print(f"   默认数据库: {quota_db_config.get('default_db')}")
    
    # 解密密码
    encrypted_password = quota_db_config.get('password', '')
    if encrypted_password:
        decrypted_password = decrypt_password(encrypted_password)
        if decrypted_password:
            print(f"   密码: {'*' * len(decrypted_password)} (已解密)")
        else:
            print("   密码: 解密失败")
            return False
    else:
        print("   密码: 未设置")
        decrypted_password = ''
    
    try:
        import psycopg2
        
        # 测试连接到默认数据库
        conn = psycopg2.connect(
            host=quota_db_config.get('host', 'localhost'),
            port=int(quota_db_config.get('port', 5432)),
            user=quota_db_config.get('username', 'postgres'),
            password=decrypted_password,
            database=quota_db_config.get('default_db', 'postgres'),
            client_encoding='utf8'
        )
        
        cursor = conn.cursor()
        
        # 检查目标数据库是否存在
        target_db = quota_db_config.get('db_name', '').replace('.db', '')  # 移除.db扩展名
        cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (target_db,))
        db_exists = cursor.fetchone() is not None
        
        print(f"✅ 连接成功!")
        print(f"   目标数据库 '{target_db}' {'存在' if db_exists else '不存在'}")
        
        if not db_exists:
            print(f"💡 可以创建数据库: {target_db}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def fix_database_name():
    """修复数据库名称（移除.db扩展名）"""
    print("\n🔧 修复数据库名称...")
    
    config_path = Path("user_config/user_settings.json")
    if not config_path.exists():
        print("❌ 用户配置文件不存在")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        quota_db_config = config.get('database_configs', {}).get('quota_db', {})
        old_name = quota_db_config.get('db_name', '')
        
        if old_name.endswith('.db'):
            new_name = old_name.replace('.db', '')
            quota_db_config['db_name'] = new_name
            
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 数据库名称已修复: {old_name} -> {new_name}")
            return True
        else:
            print(f"✅ 数据库名称正确: {old_name}")
            return True
            
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 PostgreSQL连接诊断工具")
    print("=" * 50)
    
    # 1. 测试基本连接
    basic_success, databases = test_postgresql_basic()
    
    if not basic_success:
        print("\n❌ 基本连接失败，请先解决PostgreSQL服务问题")
        return
    
    # 2. 修复数据库名称
    fix_database_name()
    
    # 3. 测试用户配置连接
    config_success = test_user_config_connection()
    
    if config_success:
        print("\n✅ 所有测试通过！PostgreSQL连接正常")
    else:
        print("\n❌ 用户配置连接失败，请检查配置")
    
    print("\n💡 建议:")
    print("1. 确保PostgreSQL服务正在运行")
    print("2. 确认用户名和密码正确")
    print("3. 数据库名称不应包含.db扩展名")
    print("4. 使用UTF-8编码")

if __name__ == "__main__":
    main()
