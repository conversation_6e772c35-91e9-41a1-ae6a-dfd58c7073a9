//===----------------------------------------------------------------------===//
//
// Part of libcu++, the C++ Standard Library for your entire system,
// under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
// SPDX-FileCopyrightText: Copyright (c) 2024 NVIDIA CORPORATION & AFFILIATES.
//
//===----------------------------------------------------------------------===//

#ifndef _CUDA_STD_VERSION
#define _CUDA_STD_VERSION

#include <cuda/std/detail/__config> // IWYU pragma: export

#if defined(_CCCL_IMPLICIT_SYSTEM_HEADER_GCC)
#  pragma GCC system_header
#elif defined(_CCCL_IMPLICIT_SYSTEM_HEADER_CLANG)
#  pragma clang system_header
#elif defined(_CCCL_IMPLICIT_SYSTEM_HEADER_MSVC)
#  pragma system_header
#endif // no system header

// We need to define our own macros to not conflict with the host stl.
// At the same time we want bring in all feature test macros from host
#if __has_include(<version>) // <version> should be the smallest include possible
#  include <version>
#elif !defined(_CCCL_COMPILER_NVRTC)
#  include <ciso646> // otherwise go for the smallest possible header
#endif

#if _CCCL_STD_VER >= 2014
#  define __cccl_lib_bit_cast     201806L
#  define __cccl_lib_chrono_udls  201304L
#  define __cccl_lib_complex_udls 201309L
#  ifdef _LIBCUDACXX_IS_CONSTANT_EVALUATED
#    define __cccl_lib_constexpr_complex 201711L
#  endif // _LIBCUDACXX_IS_CONSTANT_EVALUATED
#  define __cccl_lib_concepts          202002L
#  define __cccl_lib_exchange_function 201304L
#  define __cccl_lib_expected          202211L
// # define __cccl_lib_generic_associative_lookup           201304L
#  define __cccl_lib_integer_sequence           201304L
#  define __cccl_lib_integral_constant_callable 201304L
#  define __cccl_lib_is_final                   201402L
#  define __cccl_lib_is_null_pointer            201309L
#  define __cccl_lib_make_reverse_iterator      201402L
// # define __cccl_lib_make_unique                          201304L
#  define __cccl_lib_null_iterators 201304L
#  define __cccl_lib_optional       202110L
// # define __cccl_lib_quoted_string_io                     201304L
#  define __cccl_lib_result_of_sfinae            201210L
#  define __cccl_lib_robust_nonmodifying_seq_ops 201304L
#  ifndef _LIBCUDACXX_HAS_NO_THREADS
// #   define __cccl_lib_shared_timed_mutex                 201402L
#  endif // !_LIBCUDACXX_HAS_NO_THREADS
#  define __cccl_lib_span 202002L
// # define __cccl_lib_string_udls                          201304L
#  define __cccl_lib_transformation_trait_aliases 201304L
#  define __cccl_lib_transparent_operators        201210L
#  define __cccl_lib_tuple_element_t              201402L
#  define __cccl_lib_tuples_by_type               201304L
#endif // _CCCL_STD_VER >= 2014

#if _CCCL_STD_VER >= 2017
#  ifdef _LIBCUDACXX_ADDRESSOF
#    define __cccl_lib_addressof_constexpr 201603L
#  endif // _LIBCUDACXX_ADDRESSOF
// # define __cccl_lib_allocator_traits_is_always_equal     201411L
// # define __cccl_lib_any                                  201606L
#  define __cccl_lib_apply           201603L
#  define __cccl_lib_array_constexpr 201603L
#  define __cccl_lib_as_const        201510L
#  ifndef _LIBCUDACXX_HAS_NO_THREADS
#    define __cccl_lib_atomic_is_always_lock_free 201603L
#  endif // _LIBCUDACXX_HAS_NO_THREADS
#  define __cccl_lib_bind_front    201907L
#  define __cccl_lib_bool_constant 201505L
// # define __cccl_lib_boyer_moore_searcher                 201603L
#  define __cccl_lib_byte   201603L
#  define __cccl_lib_chrono 201611L
// # define __cccl_lib_clamp                                201603L
// # define __cccl_lib_enable_shared_from_this              201603L
// # define __cccl_lib_execution                            201603L
// # define __cccl_lib_filesystem                           201703L
#  define __cccl_lib_gcd_lcm                    201606L
#  define __cccl_lib_hardware_interference_size 201703L
#  ifdef _LIBCUDACXX_HAS_UNIQUE_OBJECT_REPRESENTATIONS
#    define __cccl_lib_has_unique_object_representations 201606L
#  endif // _LIBCUDACXX_HAS_UNIQUE_OBJECT_REPRESENTATIONS
#  define __cccl_lib_hypot 201603L
// # define __cccl_lib_incomplete_container_elements        201505L
#  define __cccl_lib_invoke 201411L
#  ifndef _LIBCUDACXX_HAS_NO_IS_AGGREGATE
#    define __cccl_lib_is_aggregate 201703L
#  endif // _LIBCUDACXX_HAS_NO_IS_AGGREGATE
#  define __cccl_lib_is_invocable    201703L
#  define __cccl_lib_is_swappable    201603L
#  define __cccl_lib_launder         201606L
#  define __cccl_lib_logical_traits  201510L
#  define __cccl_lib_make_from_tuple 201606L
// # define __cccl_lib_map_try_emplace                      201411L
// # define __cccl_lib_math_special_functions               201603L
// # define __cccl_lib_memory_resource                      201603L
// # define __cccl_lib_node_extract                         201606L
// # define __cccl_lib_nonmember_container_access           201411L
#  define __cccl_lib_not_fn 201603L
// # define __cccl_lib_parallel_algorithm                   201603L
// # define __cccl_lib_raw_memory_algorithms                201606L
// # define __cccl_lib_sample                               201603L
// # define __cccl_lib_scoped_lock                          201703L
#  if !defined(_LIBCUDACXX_HAS_NO_THREADS)
// #   define __cccl_lib_shared_mutex                       201505L
#  endif
// # define __cccl_lib_shared_ptr_arrays                    201611L
// # define __cccl_lib_shared_ptr_weak_type                 201606L
// # define __cccl_lib_string_view                          201606L
// # define __cccl_lib_to_chars                             201611L
#  define __cccl_lib_type_trait_variable_templates 201510L
#  define __cccl_lib_uncaught_exceptions           201411L
#  define __cccl_lib_unordered_map_try_emplace     201411L
#  define __cccl_lib_variant                       201606L
#  define __cccl_lib_void_t                        201411L
#endif // _CCCL_STD_VER >= 2017

#if _CCCL_STD_VER >= 2020
#  undef __cccl_lib_array_constexpr
#  define __cccl_lib_array_constexpr 201811L
// # define __cccl_lib_assume_aligned                       201811L
#  define __cccl_lib_atomic_flag_test              201907L
#  define __cccl_lib_atomic_float                  201711L
#  define __cccl_lib_atomic_lock_free_type_aliases 201907L
#  ifndef _LIBCUDACXX_HAS_NO_THREADS
#    define __cccl_lib_atomic_ref 201806L
#  endif // _LIBCUDACXX_HAS_NO_THREADS
// # define __cccl_lib_atomic_shared_ptr                    201711L
#  define __cccl_lib_atomic_value_initialization 201911L
#  ifndef _LIBCUDACXX_AVAILABILITY_DISABLE_FTM___cpp_lib_atomic_wait
#    define __cccl_lib_atomic_wait 201907L
#  endif // _LIBCUDACXX_AVAILABILITY_DISABLE_FTM___cpp_lib_atomic_wait
#  if !defined(_LIBCUDACXX_HAS_NO_THREADS) && !defined(_LIBCUDACXX_AVAILABILITY_DISABLE_FTM___cpp_lib_barrier)
#    define __cccl_lib_barrier 201907L
#  endif
#  define __cccl_lib_bit_cast             201806L
#  define __cccl_lib_bitops               201907L
#  define __cccl_lib_bounded_array_traits 201902L
#  ifndef _LIBCUDACXX_NO_HAS_CHAR8_T
#    define __cccl_lib_char8_t 201811L
#  endif // _LIBCUDACXX_NO_HAS_CHAR8_T
// # define __cccl_lib_constexpr_algorithms                 201806L
// # define __cccl_lib_constexpr_dynamic_alloc              201907L
#  define __cccl_lib_constexpr_functional 201907L
// # define __cccl_lib_constexpr_iterator                   201811L
// # define __cccl_lib_constexpr_memory                     201811L
// # define __cccl_lib_constexpr_misc                       201811L
// # define __cccl_lib_constexpr_numeric                    201911L
// # define __cccl_lib_constexpr_string                     201907L
// # define __cccl_lib_constexpr_string_view                201811L
// # define __cccl_lib_constexpr_swap_algorithms            201806L
// # define __cccl_lib_constexpr_tuple                      201811L
// # define __cccl_lib_constexpr_utility                    201811L
// # define __cccl_lib_constexpr_vector                     201907L
// # define __cccl_lib_coroutine                            201902L
#  if defined(__cpp_impl_destroying_delete) && __cpp_impl_destroying_delete >= 201806L \
    && defined(__cpp_lib_destroying_delete)
#    define __cccl_lib_destroying_delete 201806L
#  endif
// # define __cccl_lib_endian                               201907L
// # define __cccl_lib_erase_if                             201811L
// # undef  __cccl_lib_execution
// # define __cccl_lib_execution                            201902L
#  if !defined(_LIBCUDACXX_AVAILABILITY_DISABLE_FTM___cpp_lib_format) && !defined(_LIBCUDACXX_HAS_NO_INCOMPLETE_FORMAT)
// #   define __cccl_lib_format                             202106L
#  endif
// # define __cccl_lib_generic_unordered_lookup             201811L
// # define __cccl_lib_int_pow2                             202002L
// # define __cccl_lib_integer_comparison_functions         202002L
// # define __cccl_lib_interpolate                          201902L
#  ifdef _LIBCUDACXX_IS_CONSTANT_EVALUATED
#    define __cccl_lib_is_constant_evaluated 201811L
#  endif // _LIBCUDACXX_IS_CONSTANT_EVALUATED
// # define __cccl_lib_is_layout_compatible                 201907L
#  define __cccl_lib_is_nothrow_convertible 201806L
// # define __cccl_lib_is_pointer_interconvertible          201907L
#  if !defined(_LIBCUDACXX_HAS_NO_THREADS)
// #   define __cccl_lib_jthread                            201911L
#  endif
#  if !defined(_LIBCUDACXX_HAS_NO_THREADS) && !defined(_LIBCUDACXX_AVAILABILITY_DISABLE_FTM___cpp_lib_latch)
// #   define __cccl_lib_latch                              201907L
#  endif
// # define __cccl_lib_list_remove_return_type              201806L
// # define __cccl_lib_math_constants                       201907L
// # define __cccl_lib_polymorphic_allocator                201902L
// # define __cccl_lib_ranges                               201811L
// # define __cccl_lib_remove_cvref                         201711L
#  if !defined(_LIBCUDACXX_HAS_NO_THREADS) && !defined(_LIBCUDACXX_AVAILABILITY_DISABLE_FTM___cpp_lib_semaphore)
// #   define __cccl_lib_semaphore                          201907L
#  endif
// # undef  __cccl_lib_shared_ptr_arrays
// # define __cccl_lib_shared_ptr_arrays                    201707L
// # define __cccl_lib_shift                                201806L
// # define __cccl_lib_smart_ptr_for_overwrite              202002L
// # define __cccl_lib_source_location                      201907L
// # define __cccl_lib_ssize                                201902L
// # define __cccl_lib_starts_ends_with                     201711L
// # undef  __cccl_lib_string_view
// # define __cccl_lib_string_view                          201803L
// # define __cccl_lib_syncbuf                              201803L
// # define __cccl_lib_three_way_comparison                 201907L
// # define __cccl_lib_to_address                           201711L
// # define __cccl_lib_to_array                             201907L
// # define __cccl_lib_type_identity                        201806L
#  define __cccl_lib_unwrap_ref 201811L
#endif // _CCCL_STD_VER >= 2020

#if _CCCL_STD_VER >= 2023
// # define __cccl_lib_adaptor_iterator_pair_constructor    202106L
// # define __cccl_lib_allocate_at_least                    202106L
// # define __cccl_lib_associative_heterogeneous_erasure    202110L
// # define __cccl_lib_bind_back                            202202L
// # define __cccl_lib_byteswap                             202110L
// # define __cccl_lib_constexpr_bitset                     202207L
// # define __cccl_lib_constexpr_charconv                   202207L
// # define __cccl_lib_constexpr_cmath                      202202L
// # undef  __cccl_lib_constexpr_memory
// # define __cccl_lib_constexpr_memory                     202202L
// # define __cccl_lib_constexpr_typeinfo                   202106L
#  define __cccl_lib_forward_like 202207L
// # define __cccl_lib_invoke_r                             202106L
#  define __cccl_lib_is_scoped_enum 202011L
// # define __cccl_lib_move_only_function                   202110L
// # define __cccl_lib_out_ptr                              202106L
// # define __cccl_lib_ranges_chunk                         202202L
// # define __cccl_lib_ranges_chunk_by                      202202L
// # define __cccl_lib_ranges_iota                          202202L
// # define __cccl_lib_ranges_join_with                     202202L
// # define __cccl_lib_ranges_slide                         202202L
// # define __cccl_lib_ranges_starts_ends_with              202106L
// # define __cccl_lib_ranges_to_container                  202202L
// # define __cccl_lib_ranges_zip                           202110L
// # define __cccl_lib_reference_from_temporary             202202L
// # define __cccl_lib_spanstream                           202106L
// # define __cccl_lib_stacktrace                           202011L
// # define __cccl_lib_stdatomic_h                          202011L
// # define __cccl_lib_string_contains                      202011L
// # define __cccl_lib_string_resize_and_overwrite          202110L
#  define __cccl_lib_to_underlying 202102L
#  define __cccl_lib_unreachable   202202L

#endif // _CCCL_STD_VER >= 2023

#endif // _CUDA_STD_VERSION
