// -*- C++ -*-
//===----------------------------------------------------------------------===//
//
// Part of libcu++, the C++ Standard Library for your entire system,
// under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
// SPDX-FileCopyrightText: Copyright (c) 2024 NVIDIA CORPORATION & AFFILIATES.
//
//===----------------------------------------------------------------------===//

#ifndef _LIBCUDACXX_STDEXCEPT
#define _LIBCUDACXX_STDEXCEPT

#include <cuda/std/detail/__config>

#if defined(_CCCL_IMPLICIT_SYSTEM_HEADER_GCC)
#  pragma GCC system_header
#elif defined(_CCCL_IMPLICIT_SYSTEM_HEADER_CLANG)
#  pragma clang system_header
#elif defined(_CCCL_IMPLICIT_SYSTEM_HEADER_MSVC)
#  pragma system_header
#endif // no system header

#include <cuda/std/__exception/terminate.h>
#include <cuda/std/detail/libcxx/include/__assert> // all public C++ headers provide the assertion handler

#ifndef _CCCL_NO_EXCEPTIONS
#  include <stdexcept>
#endif // _LIBCUDACXX_HAS_STRING

_LIBCUDACXX_BEGIN_NAMESPACE_STD

_CCCL_NORETURN _LIBCUDACXX_HIDE_FROM_ABI void __throw_runtime_error(const char* __msg)
{
#ifndef _CCCL_NO_EXCEPTIONS
  NV_IF_ELSE_TARGET(NV_IS_HOST, (throw ::std::runtime_error(__msg);), ((void) __msg; _CUDA_VSTD_NOVERSION::terminate();))
#else // ^^^ !_CCCL_NO_EXCEPTIONS ^^^ / vvv _CCCL_NO_EXCEPTIONS vvv
  (void) __msg;
  _CUDA_VSTD_NOVERSION::terminate();
#endif // _CCCL_NO_EXCEPTIONS
}

_CCCL_NORETURN _LIBCUDACXX_HIDE_FROM_ABI void __throw_logic_error(const char* __msg)
{
#ifndef _CCCL_NO_EXCEPTIONS
  NV_IF_ELSE_TARGET(NV_IS_HOST, (throw ::std::logic_error(__msg);), ((void) __msg; _CUDA_VSTD_NOVERSION::terminate();))
#else // ^^^ !_CCCL_NO_EXCEPTIONS ^^^ / vvv _CCCL_NO_EXCEPTIONS vvv
  (void) __msg;
  _CUDA_VSTD_NOVERSION::terminate();
#endif // _CCCL_NO_EXCEPTIONS
}

_CCCL_NORETURN _LIBCUDACXX_HIDE_FROM_ABI void __throw_domain_error(const char* __msg)
{
#ifndef _CCCL_NO_EXCEPTIONS
  NV_IF_ELSE_TARGET(NV_IS_HOST, (throw ::std::domain_error(__msg);), ((void) __msg; _CUDA_VSTD_NOVERSION::terminate();))
#else // ^^^ !_CCCL_NO_EXCEPTIONS ^^^ / vvv _CCCL_NO_EXCEPTIONS vvv
  (void) __msg;
  _CUDA_VSTD_NOVERSION::terminate();
#endif // _CCCL_NO_EXCEPTIONS
}

_CCCL_NORETURN _LIBCUDACXX_HIDE_FROM_ABI void __throw_invalid_argument(const char* __msg)
{
#ifndef _CCCL_NO_EXCEPTIONS
  NV_IF_ELSE_TARGET(
    NV_IS_HOST, (throw ::std::invalid_argument(__msg);), ((void) __msg; _CUDA_VSTD_NOVERSION::terminate();))
#else // ^^^ !_CCCL_NO_EXCEPTIONS ^^^ / vvv _CCCL_NO_EXCEPTIONS vvv
  (void) __msg;
  _CUDA_VSTD_NOVERSION::terminate();
#endif // _CCCL_NO_EXCEPTIONS
}

_CCCL_NORETURN _LIBCUDACXX_HIDE_FROM_ABI void __throw_length_error(const char* __msg)
{
#ifndef _CCCL_NO_EXCEPTIONS
  NV_IF_ELSE_TARGET(NV_IS_HOST, (throw ::std::length_error(__msg);), ((void) __msg; _CUDA_VSTD_NOVERSION::terminate();))
#else // ^^^ !_CCCL_NO_EXCEPTIONS ^^^ / vvv _CCCL_NO_EXCEPTIONS vvv
  (void) __msg;
  _CUDA_VSTD_NOVERSION::terminate();
#endif // _CCCL_NO_EXCEPTIONS
}

_CCCL_NORETURN _LIBCUDACXX_HIDE_FROM_ABI void __throw_out_of_range(const char* __msg)
{
#ifndef _CCCL_NO_EXCEPTIONS
  NV_IF_ELSE_TARGET(NV_IS_HOST, (throw ::std::out_of_range(__msg);), ((void) __msg; _CUDA_VSTD_NOVERSION::terminate();))
#else // ^^^ !_CCCL_NO_EXCEPTIONS ^^^ / vvv _CCCL_NO_EXCEPTIONS vvv
  (void) __msg;
  _CUDA_VSTD_NOVERSION::terminate();
#endif // _CCCL_NO_EXCEPTIONS
}

_CCCL_NORETURN _LIBCUDACXX_HIDE_FROM_ABI void __throw_range_error(const char* __msg)
{
#ifndef _CCCL_NO_EXCEPTIONS
  NV_IF_ELSE_TARGET(NV_IS_HOST, (throw ::std::range_error(__msg);), ((void) __msg; _CUDA_VSTD_NOVERSION::terminate();))
#else // ^^^ !_CCCL_NO_EXCEPTIONS ^^^ / vvv _CCCL_NO_EXCEPTIONS vvv
  (void) __msg;
  _CUDA_VSTD_NOVERSION::terminate();
#endif // _CCCL_NO_EXCEPTIONS
}

_CCCL_NORETURN _LIBCUDACXX_HIDE_FROM_ABI void __throw_overflow_error(const char* __msg)
{
#ifndef _CCCL_NO_EXCEPTIONS
  NV_IF_ELSE_TARGET(
    NV_IS_HOST, (throw ::std::overflow_error(__msg);), ((void) __msg; _CUDA_VSTD_NOVERSION::terminate();))
#else // ^^^ !_CCCL_NO_EXCEPTIONS ^^^ / vvv _CCCL_NO_EXCEPTIONS vvv
  (void) __msg;
  _CUDA_VSTD_NOVERSION::terminate();
#endif // _CCCL_NO_EXCEPTIONS
}

_CCCL_NORETURN _LIBCUDACXX_HIDE_FROM_ABI void __throw_underflow_error(const char* __msg)
{
#ifndef _CCCL_NO_EXCEPTIONS
  NV_IF_ELSE_TARGET(
    NV_IS_HOST, (throw ::std::underflow_error(__msg);), ((void) __msg; _CUDA_VSTD_NOVERSION::terminate();))
#else // ^^^ !_CCCL_NO_EXCEPTIONS ^^^ / vvv _CCCL_NO_EXCEPTIONS vvv
  (void) __msg;
  _CUDA_VSTD_NOVERSION::terminate();
#endif // _CCCL_NO_EXCEPTIONS
}

_LIBCUDACXX_END_NAMESPACE_STD

#endif // _LIBCUDACXX_STDEXCEPT
