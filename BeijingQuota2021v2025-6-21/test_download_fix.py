#!/usr/bin/env python3
"""
测试下载功能修复
Test download functionality fix
"""

import os
import sys
import tempfile
import pandas as pd
import json

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_output_directory_creation():
    """测试输出目录创建"""
    print("🧪 测试输出目录创建")
    print("=" * 50)
    
    try:
        # 检查输出目录是否存在
        output_dir = "output"
        price_info_dir = "output/price_info"
        
        if os.path.exists(output_dir):
            print(f"✅ 主输出目录存在: {output_dir}")
        else:
            print(f"❌ 主输出目录不存在: {output_dir}")
            return False
        
        if os.path.exists(price_info_dir):
            print(f"✅ 信息价输出目录存在: {price_info_dir}")
        else:
            print(f"⚠️ 信息价输出目录不存在: {price_info_dir}")
            # 创建目录
            os.makedirs(price_info_dir, exist_ok=True)
            print(f"✅ 已创建信息价输出目录: {price_info_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_file_generation():
    """测试文件生成功能"""
    print("\n🧪 测试文件生成功能")
    print("=" * 50)
    
    try:
        from src.intelligent_price_info_processor import IntelligentPriceInfoProcessor
        processor = IntelligentPriceInfoProcessor()
        
        # 创建测试数据
        test_data = [
            {
                'page_number': 1,
                'page_header': '工程造价信息价',
                'chapter_code': '01',
                'chapter_name': '黑色及有色金属',
                'remarks': '测试数据',
                'price_items': [
                    {
                        'resource_code': '0101010002-2',
                        'product_name': '热轧光圆钢筋',
                        'specifications': 'HPB300 Φ8',
                        'unit': 't',
                        'price_with_tax': '4172.00',
                        'price_without_tax': '3692.00'
                    },
                    {
                        'resource_code': '0101010003-2',
                        'product_name': '热轧光圆钢筋',
                        'specifications': 'HPB300 Φ10',
                        'unit': 't',
                        'price_with_tax': '4096.00',
                        'price_without_tax': '3625.00'
                    }
                ]
            }
        ]
        
        # 使用专门的输出目录
        output_dir = "output/price_info"
        
        # 异步生成文件
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            output_files = loop.run_until_complete(
                processor._generate_output_files(test_data, output_dir)
            )
            
            if output_files:
                print(f"✅ 文件生成成功，文件数: {len(output_files)}")
                
                csv_files = [f for f in output_files if f.endswith('.csv')]
                json_files = [f for f in output_files if f.endswith('.json')]
                
                print(f"   📄 CSV文件: {len(csv_files)}")
                print(f"   📄 JSON文件: {len(json_files)}")
                
                # 验证文件内容
                for file_path in output_files:
                    if os.path.exists(file_path):
                        size_kb = os.path.getsize(file_path) / 1024
                        print(f"   ✅ {os.path.basename(file_path)}: {size_kb:.1f} KB")
                        
                        # 验证CSV内容
                        if file_path.endswith('.csv'):
                            df = pd.read_csv(file_path, encoding='utf-8-sig')
                            print(f"      - 数据行数: {len(df)}")
                            print(f"      - 列数: {len(df.columns)}")
                            
                        # 验证JSON内容
                        elif file_path.endswith('.json'):
                            with open(file_path, 'r', encoding='utf-8') as f:
                                json_data = json.load(f)
                            print(f"      - 数据章节: {len(json_data.get('data', []))}")
                    else:
                        print(f"   ❌ 文件不存在: {file_path}")
                        return False
                
                return True, output_files
            else:
                print("❌ 未生成任何文件")
                return False, []
                
        finally:
            loop.close()
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, []

def test_interface_components():
    """测试界面组件"""
    print("\n🧪 测试界面组件")
    print("=" * 50)
    
    try:
        from src.intelligent_price_info_interface import IntelligentPriceInfoInterface
        interface = IntelligentPriceInfoInterface()
        
        # 创建界面组件
        components = interface.create_price_info_recognition_interface()
        
        # 检查下载相关组件
        download_components = [
            'download_csv',
            'download_json', 
            'download_output'
        ]
        
        missing_components = []
        for comp in download_components:
            if comp in components:
                print(f"✅ {comp}: 存在")
            else:
                print(f"❌ {comp}: 缺失")
                missing_components.append(comp)
        
        if not missing_components:
            print("✅ 所有下载组件都存在")
            return True
        else:
            print(f"❌ 缺失组件: {missing_components}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_gradio_file_serving():
    """测试Gradio文件服务配置"""
    print("\n🧪 测试Gradio文件服务配置")
    print("=" * 50)
    
    try:
        # 检查main.py中的配置
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ('allowed_paths配置', 'allowed_paths'),
            ('输出目录创建', 'makedirs.*output'),
            ('信息价目录创建', 'output/price_info'),
        ]
        
        results = []
        for check_name, pattern in checks:
            if pattern in content:
                print(f"✅ {check_name}: 已配置")
                results.append(True)
            else:
                print(f"❌ {check_name}: 未配置")
                results.append(False)
        
        return all(results)
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_file_path_handling():
    """测试文件路径处理"""
    print("\n🧪 测试文件路径处理")
    print("=" * 50)
    
    try:
        # 创建测试文件
        test_dir = "output/price_info"
        os.makedirs(test_dir, exist_ok=True)
        
        test_file = os.path.join(test_dir, "test_file.csv")
        
        # 创建测试CSV文件
        test_df = pd.DataFrame({
            '资源编号': ['0101010002-2', '0101010003-2'],
            '产品名称': ['热轧光圆钢筋', '热轧光圆钢筋'],
            '规格型号及特征': ['HPB300 Φ8', 'HPB300 Φ10'],
            '计量单位': ['t', 't'],
            '市场参考价（含税）': ['4172.00', '4096.00'],
            '市场参考价（不含税）': ['3692.00', '3625.00']
        })
        
        test_df.to_csv(test_file, index=False, encoding='utf-8-sig')
        
        if os.path.exists(test_file):
            print(f"✅ 测试文件创建成功: {test_file}")
            
            # 检查文件大小
            size = os.path.getsize(test_file)
            print(f"   文件大小: {size} 字节")
            
            # 检查文件内容
            df = pd.read_csv(test_file, encoding='utf-8-sig')
            print(f"   数据行数: {len(df)}")
            print(f"   列数: {len(df.columns)}")
            
            # 清理测试文件
            os.remove(test_file)
            print("✅ 测试文件已清理")
            
            return True
        else:
            print("❌ 测试文件创建失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 下载功能修复测试")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("输出目录创建", test_output_directory_creation),
        ("界面组件", test_interface_components),
        ("Gradio文件服务配置", test_gradio_file_serving),
        ("文件路径处理", test_file_path_handling),
        ("文件生成功能", lambda: test_file_generation()[0]),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！下载功能修复成功。")
        print("💡 现在信息价识别的文件下载功能应该正常工作：")
        print("   - 专门的输出目录: output/price_info")
        print("   - Gradio文件组件: 支持直接下载")
        print("   - 文件服务配置: 正确的路径访问")
        print("🌐 访问 http://localhost:7864 测试下载功能")
    elif passed >= total - 1:
        print("✅ 基本功能正常！可能有个别小问题。")
        print("💡 建议在Web界面中测试实际下载功能。")
    else:
        print("⚠️ 存在多个问题，需要进一步检查。")

if __name__ == "__main__":
    main()
