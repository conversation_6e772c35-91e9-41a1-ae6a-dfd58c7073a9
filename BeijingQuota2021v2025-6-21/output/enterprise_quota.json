{"metadata": {"export_date": "2025-06-25T18:13:50.535278", "source_files": 46, "format": "MongoDB JSON Export", "description": "CSV to MongoDB collection export"}, "collections": {"parent_quotas_01bdaa91": {"metadata": {"source_file": "parent_quotas_01bdaa91.csv", "document_count": 3, "fields": ["编号", "定额项名称", "工作内容", "单位", "综合单价（元/单位）"], "collection_name": "parent_quotas_01bdaa91"}, "documents": [{"编号": "1-1", "定额项名称": "人工挖一般土方 一、二类土", "工作内容": "挖土、余土清理、修整底边、打钉拍底等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_01bdaa91.csv", "_import_date": "2025-06-25T18:13:50.544465", "_row_index": 0}, {"编号": "1-2", "定额项名称": "人工挖一般土方 三类土", "工作内容": "挖土、余土清理、修整底边、打钉拍底等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_01bdaa91.csv", "_import_date": "2025-06-25T18:13:50.544564", "_row_index": 1}, {"编号": "1-3", "定额项名称": "人工挖一般土方 四类土", "工作内容": "挖土、余土清理、修整底边、打钉拍底等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_01bdaa91.csv", "_import_date": "2025-06-25T18:13:50.544635", "_row_index": 2}]}, "parent_quotas_2475f0db": {"metadata": {"source_file": "parent_quotas_2475f0db.csv", "document_count": 12, "fields": ["编号", "定额项名称", "工作内容", "单位", "综合单价（元/单位）"], "collection_name": "parent_quotas_2475f0db"}, "documents": [{"编号": "1-43", "定额项名称": "土方运输 装车", "工作内容": "装卸土方、淤泥、流砂、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 1.218, "_source_file": "parent_quotas_2475f0db.csv", "_import_date": "2025-06-25T18:13:50.556070", "_row_index": 0}, {"编号": "1-44", "定额项名称": "土方运输 运距1km以内", "工作内容": "装卸土方、淤泥、流砂、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 1.218, "_source_file": "parent_quotas_2475f0db.csv", "_import_date": "2025-06-25T18:13:50.556276", "_row_index": 1}, {"编号": "1-45", "定额项名称": "土方运输 每增1km", "工作内容": "装卸土方、淤泥、流砂、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 1.218, "_source_file": "parent_quotas_2475f0db.csv", "_import_date": "2025-06-25T18:13:50.556467", "_row_index": 2}, {"编号": "1-46", "定额项名称": "淤泥、流砂运输 装车", "工作内容": "装卸土方、淤泥、流砂、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 1.9488, "_source_file": "parent_quotas_2475f0db.csv", "_import_date": "2025-06-25T18:13:50.556638", "_row_index": 3}, {"编号": "1-47", "定额项名称": "淤泥、流砂运输 运距1km以内", "工作内容": "装卸土方、淤泥、流砂、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 1.9488, "_source_file": "parent_quotas_2475f0db.csv", "_import_date": "2025-06-25T18:13:50.556806", "_row_index": 4}, {"编号": "1-48", "定额项名称": "淤泥、流砂运输 每增1km", "工作内容": "装卸土方、淤泥、流砂、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 1.9488, "_source_file": "parent_quotas_2475f0db.csv", "_import_date": "2025-06-25T18:13:50.556964", "_row_index": 5}, {"编号": "1-49", "定额项名称": "泥浆运输 装车", "工作内容": "装卸泥浆、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 2.6796, "_source_file": "parent_quotas_2475f0db.csv", "_import_date": "2025-06-25T18:13:50.557212", "_row_index": 6}, {"编号": "1-50", "定额项名称": "泥浆运输 运距1km以内", "工作内容": "装卸泥浆、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_2475f0db.csv", "_import_date": "2025-06-25T18:13:50.557468", "_row_index": 7}, {"编号": "1-51", "定额项名称": "泥浆运输 每增1km", "工作内容": "装卸泥浆、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_2475f0db.csv", "_import_date": "2025-06-25T18:13:50.557651", "_row_index": 8}, {"编号": "1-52", "定额项名称": "旧路材料等运输 装车", "工作内容": "装卸拆挖后旧路材料、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 1.3398, "_source_file": "parent_quotas_2475f0db.csv", "_import_date": "2025-06-25T18:13:50.557803", "_row_index": 9}, {"编号": "1-53", "定额项名称": "旧路材料等运输 运距1km以内", "工作内容": "装卸拆挖后旧路材料、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_2475f0db.csv", "_import_date": "2025-06-25T18:13:50.557932", "_row_index": 10}, {"编号": "1-54", "定额项名称": "旧路材料等运输 每增1km", "工作内容": "装卸拆挖后旧路材料、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_2475f0db.csv", "_import_date": "2025-06-25T18:13:50.558064", "_row_index": 11}]}, "parent_quotas_251c88f2": {"metadata": {"source_file": "parent_quotas_251c88f2.csv", "document_count": 5, "fields": ["编号", "定额项名称", "工作内容", "单位", "综合单价（元/单位）"], "collection_name": "parent_quotas_251c88f2"}, "documents": [{"编号": "1-11", "定额项名称": "人工挖基坑土方 一、二类土", "工作内容": "挖土、余土清理、修整底边、打钎拍底等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_251c88f2.csv", "_import_date": "2025-06-25T18:13:50.566978", "_row_index": 0}, {"编号": "1-16", "定额项名称": "人工挖淤泥、流砂", "工作内容": "人工挖淤泥、流砂、清理边坡、运输至地面堆放等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_251c88f2.csv", "_import_date": "2025-06-25T18:13:50.567087", "_row_index": 1}, {"编号": "1-18", "定额项名称": "箱涵顶进挖土 人工挖土 人挖人装", "工作内容": "1.人工挖土:安拆挖土支架;铺钢轨、挖土、箱涵内运土、出坑、堆放、清理等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_251c88f2.csv", "_import_date": "2025-06-25T18:13:50.567182", "_row_index": 2}, {"编号": "1-21", "定额项名称": "挖桩间土", "工作内容": "挖土、甩土或装土、清理机下余土等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_251c88f2.csv", "_import_date": "2025-06-25T18:13:50.567297", "_row_index": 3}, {"编号": "1-22", "定额项名称": "人工凿石方", "工作内容": "凿石、清渣、装堆、清底修边等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_251c88f2.csv", "_import_date": "2025-06-25T18:13:50.567424", "_row_index": 4}]}, "parent_quotas_370b78af": {"metadata": {"source_file": "parent_quotas_370b78af.csv", "document_count": 1, "fields": ["编号", "定额项名称", "工作内容", "单位", "综合单价（元/单位）"], "collection_name": "parent_quotas_370b78af"}, "documents": [{"编号": "1-1", "定额项名称": "人工挖一般土方 一、二类土", "工作内容": "挖掘一般土方", "单位": "100m³", "综合单价（元/单位）": 106.05, "_source_file": "parent_quotas_370b78af.csv", "_import_date": "2025-06-25T18:13:50.578132", "_row_index": 0}]}, "parent_quotas_2de3313e": {"metadata": {"source_file": "parent_quotas_2de3313e.csv", "document_count": 15, "fields": ["编号", "定额项名称", "工作内容", "单位", "综合单价（元/单位）"], "collection_name": "parent_quotas_2de3313e"}, "documents": [{"编号": "1-43", "定额项名称": "土方运输 装车", "工作内容": "装卸土方、淤泥、流砂、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 1.218, "_source_file": "parent_quotas_2de3313e.csv", "_import_date": "2025-06-25T18:13:50.588811", "_row_index": 0}, {"编号": "1-44", "定额项名称": "土方运输 运距1km以内", "工作内容": "装卸土方、淤泥、流砂、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_2de3313e.csv", "_import_date": "2025-06-25T18:13:50.589129", "_row_index": 1}, {"编号": "1-45", "定额项名称": "土方运输 每增1km", "工作内容": "装卸土方、淤泥、流砂、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_2de3313e.csv", "_import_date": "2025-06-25T18:13:50.590589", "_row_index": 2}, {"编号": "1-46", "定额项名称": "淤泥、流砂运输 装车", "工作内容": "装卸土方、淤泥、流砂、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 1.9488, "_source_file": "parent_quotas_2de3313e.csv", "_import_date": "2025-06-25T18:13:50.590830", "_row_index": 3}, {"编号": "1-47", "定额项名称": "淤泥、流砂运输 运距1km以内", "工作内容": "装卸土方、淤泥、流砂、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_2de3313e.csv", "_import_date": "2025-06-25T18:13:50.591015", "_row_index": 4}, {"编号": "1-48", "定额项名称": "淤泥、流砂运输 每增1km", "工作内容": "装卸土方、淤泥、流砂、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_2de3313e.csv", "_import_date": "2025-06-25T18:13:50.591187", "_row_index": 5}, {"编号": "1-49", "定额项名称": "泥浆运输 装车", "工作内容": "装卸泥浆、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 2.6796, "_source_file": "parent_quotas_2de3313e.csv", "_import_date": "2025-06-25T18:13:50.591393", "_row_index": 6}, {"编号": "1-50", "定额项名称": "泥浆运输 运距1km以内", "工作内容": "装卸泥浆、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_2de3313e.csv", "_import_date": "2025-06-25T18:13:50.591663", "_row_index": 7}, {"编号": "1-51", "定额项名称": "泥浆运输 每增1km", "工作内容": "装卸泥浆、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_2de3313e.csv", "_import_date": "2025-06-25T18:13:50.591966", "_row_index": 8}, {"编号": "1-52", "定额项名称": "旧路材料等运输 装车", "工作内容": "装卸拆挖后旧路材料、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 1.3398, "_source_file": "parent_quotas_2de3313e.csv", "_import_date": "2025-06-25T18:13:50.592163", "_row_index": 9}, {"编号": "1-53", "定额项名称": "旧路材料等运输 运距1km以内", "工作内容": "装卸拆挖后旧路材料、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_2de3313e.csv", "_import_date": "2025-06-25T18:13:50.592307", "_row_index": 10}, {"编号": "1-54", "定额项名称": "旧路材料等运输 每增1km", "工作内容": "装卸拆挖后旧路材料、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_2de3313e.csv", "_import_date": "2025-06-25T18:13:50.592558", "_row_index": 11}, {"编号": "1-55", "定额项名称": "石方运输 装车", "工作内容": "装卸石方、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 1.3398, "_source_file": "parent_quotas_2de3313e.csv", "_import_date": "2025-06-25T18:13:50.592716", "_row_index": 12}, {"编号": "1-56", "定额项名称": "石方运输 运距1km以内", "工作内容": "装卸石方、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_2de3313e.csv", "_import_date": "2025-06-25T18:13:50.592855", "_row_index": 13}, {"编号": "1-57", "定额项名称": "石方运输 每增1km", "工作内容": "装卸石方、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_2de3313e.csv", "_import_date": "2025-06-25T18:13:50.592969", "_row_index": 14}]}, "parent_quotas_2660c31e": {"metadata": {"source_file": "parent_quotas_2660c31e.csv", "document_count": 3, "fields": ["编号", "定额项名称", "工作内容", "单位", "综合单价（元/单位）"], "collection_name": "parent_quotas_2660c31e"}, "documents": [{"编号": "1-26", "定额项名称": "路基回填 土", "工作内容": "回填、找平、碾压等。", "单位": "m³", "综合单价（元/单位）": 1.218, "_source_file": "parent_quotas_2660c31e.csv", "_import_date": "2025-06-25T18:13:50.601812", "_row_index": 0}, {"编号": "1-27", "定额项名称": "路基回填 级配砂石", "工作内容": "回填、找平、碾压等。", "单位": "m³", "综合单价（元/单位）": 1.353, "_source_file": "parent_quotas_2660c31e.csv", "_import_date": "2025-06-25T18:13:50.601992", "_row_index": 1}, {"编号": "1-28", "定额项名称": "路基回填 石灰、粉煤灰碎石", "工作内容": "回填、找平、碾压等。", "单位": "m³", "综合单价（元/单位）": 1.599, "_source_file": "parent_quotas_2660c31e.csv", "_import_date": "2025-06-25T18:13:50.602141", "_row_index": 2}]}, "parent_quotas_3a5e7949": {"metadata": {"source_file": "parent_quotas_3a5e7949.csv", "document_count": 3, "fields": ["编号", "定额项名称", "工作内容", "单位", "综合单价（元/单位）"], "collection_name": "parent_quotas_3a5e7949"}, "documents": [{"编号": "1-1", "定额项名称": "人工挖一般土方", "工作内容": "挖土、余土清理、修整底边、打钎拍底等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_3a5e7949.csv", "_import_date": "2025-06-25T18:13:50.614100", "_row_index": 0}, {"编号": "1-2", "定额项名称": "人工挖一般土方", "工作内容": "挖土、余土清理、修整底边、打钎拍底等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_3a5e7949.csv", "_import_date": "2025-06-25T18:13:50.614189", "_row_index": 1}, {"编号": "1-3", "定额项名称": "人工挖一般土方", "工作内容": "挖土、余土清理、修整底边、打钎拍底等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_3a5e7949.csv", "_import_date": "2025-06-25T18:13:50.614518", "_row_index": 2}]}, "parent_quotas_69534dc4": {"metadata": {"source_file": "parent_quotas_69534dc4.csv", "document_count": 4, "fields": ["编号", "定额项名称", "工作内容", "单位", "综合单价（元/单位）"], "collection_name": "parent_quotas_69534dc4"}, "documents": [{"编号": "1-22", "定额项名称": "人工凿石方", "工作内容": "凿石、清渣、攒堆、清底修边等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_69534dc4.csv", "_import_date": "2025-06-25T18:13:50.626617", "_row_index": 0}, {"编号": "1-23", "定额项名称": "机械破碎一般石方", "工作内容": "装卸机头、破碎岩石等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_69534dc4.csv", "_import_date": "2025-06-25T18:13:50.626706", "_row_index": 1}, {"编号": "1-24", "定额项名称": "机械破碎沟槽石方", "工作内容": "装卸机头、破碎岩石等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_69534dc4.csv", "_import_date": "2025-06-25T18:13:50.626787", "_row_index": 2}, {"编号": "1-25", "定额项名称": "机械破碎基坑石方", "工作内容": "装卸机头、破碎岩石等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_69534dc4.csv", "_import_date": "2025-06-25T18:13:50.626865", "_row_index": 3}]}, "parent_quotas_7051ec62": {"metadata": {"source_file": "parent_quotas_7051ec62.csv", "document_count": 3, "fields": ["编号", "定额项名称", "工作内容", "单位", "综合单价（元/单位）"], "collection_name": "parent_quotas_7051ec62"}, "documents": [{"编号": "1-1", "定额项名称": "人工挖一般土方一、二类土", "工作内容": "挖土、余土清理、修整底边等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_7051ec62.csv", "_import_date": "2025-06-25T18:13:50.635791", "_row_index": 0}, {"编号": "1-2", "定额项名称": "人工挖一般土方三类土", "工作内容": "挖土、余土清理、修整底边等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_7051ec62.csv", "_import_date": "2025-06-25T18:13:50.635976", "_row_index": 1}, {"编号": "1-3", "定额项名称": "人工挖一般土方四类土", "工作内容": "挖土、余土清理、修整底边等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_7051ec62.csv", "_import_date": "2025-06-25T18:13:50.636427", "_row_index": 2}]}, "parent_quotas_7ec5dfc3": {"metadata": {"source_file": "parent_quotas_7ec5dfc3.csv", "document_count": 6, "fields": ["编号", "定额项名称", "工作内容", "单位", "综合单价（元/单位）"], "collection_name": "parent_quotas_7ec5dfc3"}, "documents": [{"编号": "1-29", "定额项名称": "人工回填 土", "工作内容": "拌合、回填、平整、夯实等", "单位": "m³", "综合单价（元/单位）": 21.315, "_source_file": "parent_quotas_7ec5dfc3.csv", "_import_date": "2025-06-25T18:13:50.648075", "_row_index": 0}, {"编号": "1-30", "定额项名称": "人工回填 灰土 2:8", "工作内容": "拌合、回填、平整、夯实等", "单位": "m³", "综合单价（元/单位）": 25.578, "_source_file": "parent_quotas_7ec5dfc3.csv", "_import_date": "2025-06-25T18:13:50.648440", "_row_index": 1}, {"编号": "1-31", "定额项名称": "人工回填 灰土 3:7", "工作内容": "拌合、回填、平整、夯实等", "单位": "m³", "综合单价（元/单位）": 25.578, "_source_file": "parent_quotas_7ec5dfc3.csv", "_import_date": "2025-06-25T18:13:50.648766", "_row_index": 2}, {"编号": "1-32", "定额项名称": "人工回填 级配砂石", "工作内容": "拌合、回填、平整、夯实等", "单位": "m³", "综合单价（元/单位）": 15.9558, "_source_file": "parent_quotas_7ec5dfc3.csv", "_import_date": "2025-06-25T18:13:50.648986", "_row_index": 3}, {"编号": "1-33", "定额项名称": "人工回填 砂", "工作内容": "拌合、回填、平整、夯实等", "单位": "m³", "综合单价（元/单位）": 21.5586, "_source_file": "parent_quotas_7ec5dfc3.csv", "_import_date": "2025-06-25T18:13:50.649142", "_row_index": 4}, {"编号": "1-34", "定额项名称": "人工回填 石灰、粉煤灰、碎石", "工作内容": "拌合、回填、平整、夯实等", "单位": "m³", "综合单价（元/单位）": 31.668000000000003, "_source_file": "parent_quotas_7ec5dfc3.csv", "_import_date": "2025-06-25T18:13:50.649281", "_row_index": 5}]}, "parent_quotas_7cb33677": {"metadata": {"source_file": "parent_quotas_7cb33677.csv", "document_count": 6, "fields": ["编号", "定额项名称", "工作内容", "单位", "综合单价（元/单位）"], "collection_name": "parent_quotas_7cb33677"}, "documents": [{"编号": "1-1", "定额项名称": "人工挖一般土方 一、二类土", "工作内容": "人工挖一般土方 一、二类土", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_7cb33677.csv", "_import_date": "2025-06-25T18:13:50.659486", "_row_index": 0}, {"编号": "1-2", "定额项名称": "人工挖一般土方 一、二类土", "工作内容": "人工挖一般土方 一、二类土", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_7cb33677.csv", "_import_date": "2025-06-25T18:13:50.659609", "_row_index": 1}, {"编号": "1-3", "定额项名称": "人工挖一般土方 一、二类土", "工作内容": "人工挖一般土方 一、二类土", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_7cb33677.csv", "_import_date": "2025-06-25T18:13:50.659694", "_row_index": 2}, {"编号": "1-11", "定额项名称": "人工挖一般土方 三类土", "工作内容": "人工挖一般土方 三类土", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_7cb33677.csv", "_import_date": "2025-06-25T18:13:50.659783", "_row_index": 3}, {"编号": "1-12", "定额项名称": "人工挖一般土方 四类土", "工作内容": "人工挖一般土方 四类土", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_7cb33677.csv", "_import_date": "2025-06-25T18:13:50.659858", "_row_index": 4}, {"编号": "1-13", "定额项名称": "人工挖一般土方 四类土", "工作内容": "人工挖一般土方 四类土", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_7cb33677.csv", "_import_date": "2025-06-25T18:13:50.659941", "_row_index": 5}]}, "parent_quotas_72603690": {"metadata": {"source_file": "parent_quotas_72603690.csv", "document_count": 1, "fields": ["编号", "定额项名称", "工作内容", "单位", "综合单价（元/单位）"], "collection_name": "parent_quotas_72603690"}, "documents": [{"编号": "1-1", "定额项名称": "人工挖一般土方 一、二类土", "工作内容": "挖土、余土清理、修整底边、打钎拍底等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_72603690.csv", "_import_date": "2025-06-25T18:13:50.671260", "_row_index": 0}]}, "parent_quotas_7f374c69": {"metadata": {"source_file": "parent_quotas_7f374c69.csv", "document_count": 1, "fields": ["编号", "定额项名称", "工作内容", "单位", "综合单价（元/单位）"], "collection_name": "parent_quotas_7f374c69"}, "documents": [{"编号": "04-01-1-6", "定额项名称": "人工挖沟槽土方 一、二类土", "工作内容": "挖土、余土清理、修整底边、打钉拍底等", "单位": "m³", "综合单价（元/单位）": 22.365525, "_source_file": "parent_quotas_7f374c69.csv", "_import_date": "2025-06-25T18:13:50.687651", "_row_index": 0}]}, "parent_quotas_8bc2175e": {"metadata": {"source_file": "parent_quotas_8bc2175e.csv", "document_count": 3, "fields": ["编号", "定额项名称", "工作内容", "单位", "综合单价（元/单位）"], "collection_name": "parent_quotas_8bc2175e"}, "documents": [{"编号": "1-1", "定额项名称": "人工挖一般土方一、二类土", "工作内容": "挖土、余土清理、修整底边等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_8bc2175e.csv", "_import_date": "2025-06-25T18:13:50.695447", "_row_index": 0}, {"编号": "1-2", "定额项名称": "人工挖一般土方三类土", "工作内容": "挖土、余土清理、修整底边等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_8bc2175e.csv", "_import_date": "2025-06-25T18:13:50.695546", "_row_index": 1}, {"编号": "1-3", "定额项名称": "人工挖一般土方四类土", "工作内容": "挖土、余土清理、修整底边等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_8bc2175e.csv", "_import_date": "2025-06-25T18:13:50.695628", "_row_index": 2}]}, "parent_quotas_938ffd17": {"metadata": {"source_file": "parent_quotas_938ffd17.csv", "document_count": 10, "fields": ["编号", "定额项名称", "工作内容", "单位", "综合单价（元/单位）"], "collection_name": "parent_quotas_938ffd17"}, "documents": [{"编号": "1-4", "定额项名称": "机械挖一般土方 深5m以内", "工作内容": "挖土、甩土或装土、清理机下余土,修理边坡等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_938ffd17.csv", "_import_date": "2025-06-25T18:13:50.708578", "_row_index": 0}, {"编号": "1-5", "定额项名称": "机械挖一般土方 深5m以外", "工作内容": "挖土、甩土或装土、清理机下余土,修理边坡等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_938ffd17.csv", "_import_date": "2025-06-25T18:13:50.709040", "_row_index": 1}, {"编号": "1-6", "定额项名称": "人工挖沟槽土方 一、二类土", "工作内容": "挖土、余土清理、修整底边、打钎拍底等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_938ffd17.csv", "_import_date": "2025-06-25T18:13:50.709163", "_row_index": 2}, {"编号": "1-7", "定额项名称": "人工挖沟槽土方 三类土", "工作内容": "挖土、余土清理、修整底边、打钎拍底等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_938ffd17.csv", "_import_date": "2025-06-25T18:13:50.709278", "_row_index": 3}, {"编号": "1-8", "定额项名称": "人工挖沟槽土方 四类土", "工作内容": "挖土、余土清理、修整底边、打钎拍底等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_938ffd17.csv", "_import_date": "2025-06-25T18:13:50.709519", "_row_index": 4}, {"编号": "1-9", "定额项名称": "机械挖沟槽土方 深5m以内", "工作内容": "挖土、甩土或装土、清理机下余土，修理边坡等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_938ffd17.csv", "_import_date": "2025-06-25T18:13:50.709669", "_row_index": 5}, {"编号": "1-10", "定额项名称": "机械挖沟槽土方 深5m以外", "工作内容": "挖土、甩土或装土、清理机下余土，修理边坡等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_938ffd17.csv", "_import_date": "2025-06-25T18:13:50.709811", "_row_index": 6}, {"编号": "1-11", "定额项名称": "人工挖基坑土方 一、二类土", "工作内容": "挖土、余土清理、修整底边、打钎拍底等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_938ffd17.csv", "_import_date": "2025-06-25T18:13:50.709943", "_row_index": 7}, {"编号": "1-12", "定额项名称": "人工挖基坑土方 三类土", "工作内容": "挖土、余土清理、修整底边、打钎拍底等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_938ffd17.csv", "_import_date": "2025-06-25T18:13:50.710097", "_row_index": 8}, {"编号": "1-13", "定额项名称": "人工挖基坑土方 四类土", "工作内容": "挖土、余土清理、修整底边、打钎拍底等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_938ffd17.csv", "_import_date": "2025-06-25T18:13:50.710218", "_row_index": 9}]}, "parent_quotas_a96d1d5c": {"metadata": {"source_file": "parent_quotas_a96d1d5c.csv", "document_count": 3, "fields": ["编号", "定额项名称", "工作内容", "单位", "综合单价（元/单位）"], "collection_name": "parent_quotas_a96d1d5c"}, "documents": [{"编号": "1-1", "定额项名称": "人工挖一般土方 一、二类土", "工作内容": "挖土、余土清理、修整底边等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_a96d1d5c.csv", "_import_date": "2025-06-25T18:13:50.724406", "_row_index": 0}, {"编号": "1-2", "定额项名称": "人工挖一般土方 二类土", "工作内容": null, "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_a96d1d5c.csv", "_import_date": "2025-06-25T18:13:50.724589", "_row_index": 1}, {"编号": "1-3", "定额项名称": "人工挖一般土方 四类土", "工作内容": null, "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_a96d1d5c.csv", "_import_date": "2025-06-25T18:13:50.724716", "_row_index": 2}]}, "parent_quotas_mongodb_test": {"metadata": {"source_file": "parent_quotas_mongodb_test.csv", "document_count": 5, "fields": ["定额编号", "定额名称", "单位", "人工费", "材料费", "机械费", "合计"], "collection_name": "parent_quotas_mongodb_test"}, "documents": [{"定额编号": "001-001", "定额名称": "混凝土浇筑C30", "单位": "m³", "人工费": 120.5, "材料费": 450.8, "机械费": 80.3, "合计": 651.6, "_source_file": "parent_quotas_mongodb_test.csv", "_import_date": "2025-06-25T18:13:50.740701", "_row_index": 0}, {"定额编号": "001-002", "定额名称": "钢筋绑扎HPB300", "单位": "kg", "人工费": 85.2, "材料费": 320.5, "机械费": 45.8, "合计": 451.5, "_source_file": "parent_quotas_mongodb_test.csv", "_import_date": "2025-06-25T18:13:50.740985", "_row_index": 1}, {"定额编号": "001-003", "定额名称": "模板安装拆除", "单位": "m²", "人工费": 95.8, "材料费": 180.2, "机械费": 25.6, "合计": 301.6, "_source_file": "parent_quotas_mongodb_test.csv", "_import_date": "2025-06-25T18:13:50.741160", "_row_index": 2}, {"定额编号": "002-001", "定额名称": "砌砖工程MU10", "单位": "m³", "人工费": 110.3, "材料费": 280.9, "机械费": 35.2, "合计": 426.4, "_source_file": "parent_quotas_mongodb_test.csv", "_import_date": "2025-06-25T18:13:50.741309", "_row_index": 3}, {"定额编号": "002-002", "定额名称": "抹灰工程", "单位": "m²", "人工费": 75.6, "材料费": 120.4, "机械费": 15.8, "合计": 211.8, "_source_file": "parent_quotas_mongodb_test.csv", "_import_date": "2025-06-25T18:13:50.741468", "_row_index": 4}]}, "parent_quotas_mcp_test": {"metadata": {"source_file": "parent_quotas_mcp_test.csv", "document_count": 5, "fields": ["定额编号", "定额名称", "单位", "人工费", "材料费", "机械费", "合计"], "collection_name": "parent_quotas_mcp_test"}, "documents": [{"定额编号": "001-001", "定额名称": "混凝土浇筑C30", "单位": "m³", "人工费": 120.5, "材料费": 450.8, "机械费": 80.3, "合计": 651.6, "_source_file": "parent_quotas_mcp_test.csv", "_import_date": "2025-06-25T18:13:50.752739", "_row_index": 0}, {"定额编号": "001-002", "定额名称": "钢筋绑扎HPB300", "单位": "kg", "人工费": 85.2, "材料费": 320.5, "机械费": 45.8, "合计": 451.5, "_source_file": "parent_quotas_mcp_test.csv", "_import_date": "2025-06-25T18:13:50.752987", "_row_index": 1}, {"定额编号": "001-003", "定额名称": "模板安装拆除", "单位": "m²", "人工费": 95.8, "材料费": 180.2, "机械费": 25.6, "合计": 301.6, "_source_file": "parent_quotas_mcp_test.csv", "_import_date": "2025-06-25T18:13:50.753176", "_row_index": 2}, {"定额编号": "002-001", "定额名称": "砌砖工程MU10", "单位": "m³", "人工费": 110.3, "材料费": 280.9, "机械费": 35.2, "合计": 426.4, "_source_file": "parent_quotas_mcp_test.csv", "_import_date": "2025-06-25T18:13:50.753318", "_row_index": 3}, {"定额编号": "002-002", "定额名称": "抹灰工程", "单位": "m²", "人工费": 75.6, "材料费": 120.4, "机械费": 15.8, "合计": 211.8, "_source_file": "parent_quotas_mcp_test.csv", "_import_date": "2025-06-25T18:13:50.753445", "_row_index": 4}]}, "parent_quotas_a701e003": {"metadata": {"source_file": "parent_quotas_a701e003.csv", "document_count": 13, "fields": ["编号", "定额项名称", "工作内容", "单位", "综合单价（元/单位）"], "collection_name": "parent_quotas_a701e003"}, "documents": [{"编号": "1-1", "定额项名称": "人工挖一般土方 类型1", "工作内容": "挖土、余土清理、修整底边、打钉拍底等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_a701e003.csv", "_import_date": "2025-06-25T18:13:50.764383", "_row_index": 0}, {"编号": "1-2", "定额项名称": "人工挖一般土方 类型2", "工作内容": "挖土、余土清理、修整底边、打钉拍底等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_a701e003.csv", "_import_date": "2025-06-25T18:13:50.764531", "_row_index": 1}, {"编号": "1-3", "定额项名称": "人工挖一般土方 类型3", "工作内容": "挖土、余土清理、修整底边、打钉拍底等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_a701e003.csv", "_import_date": "2025-06-25T18:13:50.764652", "_row_index": 2}, {"编号": "1-4", "定额项名称": "人工挖一般土方 类型4", "工作内容": "挖土、余土清理、修整底边、打钉拍底等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_a701e003.csv", "_import_date": "2025-06-25T18:13:50.764769", "_row_index": 3}, {"编号": "1-5", "定额项名称": "人工挖一般土方 类型5", "工作内容": "挖土、余土清理、修整底边、打钉拍底等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_a701e003.csv", "_import_date": "2025-06-25T18:13:50.765030", "_row_index": 4}, {"编号": "1-6", "定额项名称": "人工挖一般土方 类型6", "工作内容": "挖土、余土清理、修整底边、打钉拍底等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_a701e003.csv", "_import_date": "2025-06-25T18:13:50.765436", "_row_index": 5}, {"编号": "1-7", "定额项名称": "人工挖一般土方 类型7", "工作内容": "挖土、余土清理、修整底边、打钉拍底等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_a701e003.csv", "_import_date": "2025-06-25T18:13:50.766174", "_row_index": 6}, {"编号": "1-8", "定额项名称": "人工挖一般土方 类型8", "工作内容": "挖土、余土清理、修整底边、打钉拍底等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_a701e003.csv", "_import_date": "2025-06-25T18:13:50.766372", "_row_index": 7}, {"编号": "1-9", "定额项名称": "人工挖一般土方 类型9", "工作内容": "挖土、余土清理、修整底边、打钉拍底等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_a701e003.csv", "_import_date": "2025-06-25T18:13:50.766513", "_row_index": 8}, {"编号": "1-10", "定额项名称": "人工挖一般土方 类型10", "工作内容": "挖土、余土清理、修整底边、打钉拍底等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_a701e003.csv", "_import_date": "2025-06-25T18:13:50.766615", "_row_index": 9}, {"编号": "1-11", "定额项名称": "人工挖一般土方 类型11", "工作内容": "挖土、余土清理、修整底边、打钉拍底等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_a701e003.csv", "_import_date": "2025-06-25T18:13:50.766701", "_row_index": 10}, {"编号": "1-12", "定额项名称": "人工挖一般土方 类型12", "工作内容": "挖土、余土清理、修整底边、打钉拍底等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_a701e003.csv", "_import_date": "2025-06-25T18:13:50.766779", "_row_index": 11}, {"编号": "1-13", "定额项名称": "人工挖一般土方 类型13", "工作内容": "挖土、余土清理、修整底边、打钉拍底等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_a701e003.csv", "_import_date": "2025-06-25T18:13:50.766872", "_row_index": 12}]}, "parent_quotas_9c7d7fd2": {"metadata": {"source_file": "parent_quotas_9c7d7fd2.csv", "document_count": 3, "fields": ["编号", "定额项名称", "工作内容", "单位", "综合单价（元/单位）"], "collection_name": "parent_quotas_9c7d7fd2"}, "documents": [{"编号": "1-1", "定额项名称": "人工挖一般土方 一、二类土", "工作内容": "挖土、余土清理、修整底边、打钎拍底等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_9c7d7fd2.csv", "_import_date": "2025-06-25T18:13:50.776139", "_row_index": 0}, {"编号": "1-2", "定额项名称": "人工挖一般土方 三类土", "工作内容": "挖土、余土清理、修整底边、打钎拍底等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_9c7d7fd2.csv", "_import_date": "2025-06-25T18:13:50.776351", "_row_index": 1}, {"编号": "1-3", "定额项名称": "人工挖一般土方 四类土", "工作内容": "挖土、余土清理、修整底边、打钎拍底等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_9c7d7fd2.csv", "_import_date": "2025-06-25T18:13:50.776506", "_row_index": 2}]}, "parent_quotas_f98f4a2f": {"metadata": {"source_file": "parent_quotas_f98f4a2f.csv", "document_count": 8, "fields": ["编号", "定额项名称", "工作内容", "单位", "综合单价（元/单位）"], "collection_name": "parent_quotas_f98f4a2f"}, "documents": [{"编号": "1-14", "定额项名称": "机械挖基坑土方 深5m以内", "工作内容": "挖土、甩土或装土、清理机下余土,修理边坡等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_f98f4a2f.csv", "_import_date": "2025-06-25T18:13:50.784178", "_row_index": 0}, {"编号": "1-15", "定额项名称": "机械挖基坑土方 深5m以外", "工作内容": "挖土、甩土或装土、清理机下余土,修理边坡等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_f98f4a2f.csv", "_import_date": "2025-06-25T18:13:50.784299", "_row_index": 1}, {"编号": "1-16", "定额项名称": "人工挖淤泥、流砂", "工作内容": "人工挖淤泥、流砂、清理边坡、运输至地面堆放等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_f98f4a2f.csv", "_import_date": "2025-06-25T18:13:50.784409", "_row_index": 2}, {"编号": "1-17", "定额项名称": "机械挖淤泥、流砂", "工作内容": "机械挖淤泥、流砂、清理边坡和机下余土、运输至地面堆放等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_f98f4a2f.csv", "_import_date": "2025-06-25T18:13:50.784688", "_row_index": 3}, {"编号": "1-18", "定额项名称": "箱涵顶进挖土 人工挖土", "工作内容": "安拆挖土支架；铺钢轨、挖土、箱涵内运土、出坑、堆放、清理等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_f98f4a2f.csv", "_import_date": "2025-06-25T18:13:50.784863", "_row_index": 4}, {"编号": "1-19", "定额项名称": "箱涵顶进挖土 人工挖土", "工作内容": "安拆挖土支架；铺钢轨、挖土、箱涵内运土、出坑、堆放、清理等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_f98f4a2f.csv", "_import_date": "2025-06-25T18:13:50.785008", "_row_index": 5}, {"编号": "1-20", "定额项名称": "箱涵顶进挖土 机械挖土", "工作内容": "挖土、箱涵内运土、出坑、堆放、清理等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_f98f4a2f.csv", "_import_date": "2025-06-25T18:13:50.785314", "_row_index": 6}, {"编号": "1-21", "定额项名称": "挖桩间土", "工作内容": "挖土、甩土或装土、清理机下余土等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_f98f4a2f.csv", "_import_date": "2025-06-25T18:13:50.785477", "_row_index": 7}]}, "parent_quotas_test": {"metadata": {"source_file": "parent_quotas_test.csv", "document_count": 5, "fields": ["定额编号", "定额名称", "单位", "人工费", "材料费", "机械费", "合计"], "collection_name": "parent_quotas_test"}, "documents": [{"定额编号": "001-001", "定额名称": "混凝土浇筑C30", "单位": "m³", "人工费": 120.5, "材料费": 450.8, "机械费": 80.3, "合计": 651.6, "_source_file": "parent_quotas_test.csv", "_import_date": "2025-06-25T18:13:50.796535", "_row_index": 0}, {"定额编号": "001-002", "定额名称": "钢筋绑扎HPB300", "单位": "kg", "人工费": 85.2, "材料费": 320.5, "机械费": 45.8, "合计": 451.5, "_source_file": "parent_quotas_test.csv", "_import_date": "2025-06-25T18:13:50.796656", "_row_index": 1}, {"定额编号": "001-003", "定额名称": "模板安装拆除", "单位": "m²", "人工费": 95.8, "材料费": 180.2, "机械费": 25.6, "合计": 301.6, "_source_file": "parent_quotas_test.csv", "_import_date": "2025-06-25T18:13:50.796906", "_row_index": 2}, {"定额编号": "002-001", "定额名称": "砌砖工程MU10", "单位": "m³", "人工费": 110.3, "材料费": 280.9, "机械费": 35.2, "合计": 426.4, "_source_file": "parent_quotas_test.csv", "_import_date": "2025-06-25T18:13:50.797065", "_row_index": 3}, {"定额编号": "002-002", "定额名称": "抹灰工程", "单位": "m²", "人工费": 75.6, "材料费": 120.4, "机械费": 15.8, "合计": 211.8, "_source_file": "parent_quotas_test.csv", "_import_date": "2025-06-25T18:13:50.797178", "_row_index": 4}]}, "parent_quotas_web_test": {"metadata": {"source_file": "parent_quotas_web_test.csv", "document_count": 3, "fields": ["定额编号", "定额名称", "单位", "人工费", "材料费", "机械费", "合计"], "collection_name": "parent_quotas_web_test"}, "documents": [{"定额编号": "001-001", "定额名称": "混凝土浇筑C30", "单位": "m³", "人工费": 120.5, "材料费": 450.8, "机械费": 80.3, "合计": 651.6, "_source_file": "parent_quotas_web_test.csv", "_import_date": "2025-06-25T18:13:50.808336", "_row_index": 0}, {"定额编号": "001-002", "定额名称": "钢筋绑扎HPB300", "单位": "kg", "人工费": 85.2, "材料费": 320.5, "机械费": 45.8, "合计": 451.5, "_source_file": "parent_quotas_web_test.csv", "_import_date": "2025-06-25T18:13:50.808473", "_row_index": 1}, {"定额编号": "001-003", "定额名称": "模板安装拆除", "单位": "m²", "人工费": 95.8, "材料费": 180.2, "机械费": 25.6, "合计": 301.6, "_source_file": "parent_quotas_web_test.csv", "_import_date": "2025-06-25T18:13:50.808579", "_row_index": 2}]}, "child_resources_066ad134": {"metadata": {"source_file": "child_resources_066ad134.csv", "document_count": 12, "fields": ["定额编号", "资源编号", "类别", "子项名称", "单位", "消耗量", "单价", "合价"], "collection_name": "child_resources_066ad134"}, "documents": [{"定额编号": "1-1", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.187, "单价": 0, "合价": 0.0, "_source_file": "child_resources_066ad134.csv", "_import_date": "2025-06-25T18:13:50.821128", "_row_index": 0}, {"定额编号": "1-1", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钉机", "单位": "台班", "消耗量": 0.0039, "单价": 0, "合价": 0.0, "_source_file": "child_resources_066ad134.csv", "_import_date": "2025-06-25T18:13:50.821408", "_row_index": 1}, {"定额编号": "1-2", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.274, "单价": 0, "合价": 0.0, "_source_file": "child_resources_066ad134.csv", "_import_date": "2025-06-25T18:13:50.821610", "_row_index": 2}, {"定额编号": "1-2", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钉机", "单位": "台班", "消耗量": 0.0048, "单价": 0, "合价": 0.0, "_source_file": "child_resources_066ad134.csv", "_import_date": "2025-06-25T18:13:50.821759", "_row_index": 3}, {"定额编号": "1-3", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.361, "单价": 0, "合价": 0.0, "_source_file": "child_resources_066ad134.csv", "_import_date": "2025-06-25T18:13:50.821894", "_row_index": 4}, {"定额编号": "1-3", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钉机", "单位": "台班", "消耗量": 0.0056, "单价": 0, "合价": 0.0, "_source_file": "child_resources_066ad134.csv", "_import_date": "2025-06-25T18:13:50.822160", "_row_index": 5}, {"定额编号": "1-11", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.274, "单价": 0, "合价": 0.0, "_source_file": "child_resources_066ad134.csv", "_import_date": "2025-06-25T18:13:50.822319", "_row_index": 6}, {"定额编号": "1-11", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钉机", "单位": "台班", "消耗量": 0.0048, "单价": 0, "合价": 0.0, "_source_file": "child_resources_066ad134.csv", "_import_date": "2025-06-25T18:13:50.822459", "_row_index": 7}, {"定额编号": "1-12", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.361, "单价": 0, "合价": 0.0, "_source_file": "child_resources_066ad134.csv", "_import_date": "2025-06-25T18:13:50.822600", "_row_index": 8}, {"定额编号": "1-12", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钉机", "单位": "台班", "消耗量": 0.0056, "单价": 0, "合价": 0.0, "_source_file": "child_resources_066ad134.csv", "_import_date": "2025-06-25T18:13:50.822728", "_row_index": 9}, {"定额编号": "1-13", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.361, "单价": 0, "合价": 0.0, "_source_file": "child_resources_066ad134.csv", "_import_date": "2025-06-25T18:13:50.822979", "_row_index": 10}, {"定额编号": "1-13", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钉机", "单位": "台班", "消耗量": 0.0056, "单价": 0, "合价": 0.0, "_source_file": "child_resources_066ad134.csv", "_import_date": "2025-06-25T18:13:50.823120", "_row_index": 11}]}, "child_resources_272cc06e": {"metadata": {"source_file": "child_resources_272cc06e.csv", "document_count": 9, "fields": ["定额编号", "资源编号", "类别", "子项名称", "单位", "消耗量", "单价", "合价"], "collection_name": "child_resources_272cc06e"}, "documents": [{"定额编号": "1-1", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.2, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_272cc06e.csv", "_import_date": "2025-06-25T18:13:50.835021", "_row_index": 0}, {"定额编号": "1-1", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钉机", "单位": "台班", "消耗量": 0.0039, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_272cc06e.csv", "_import_date": "2025-06-25T18:13:50.835297", "_row_index": 1}, {"定额编号": "1-1", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_272cc06e.csv", "_import_date": "2025-06-25T18:13:50.835506", "_row_index": 2}, {"定额编号": "1-2", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.274, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_272cc06e.csv", "_import_date": "2025-06-25T18:13:50.835782", "_row_index": 3}, {"定额编号": "1-2", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钉机", "单位": "台班", "消耗量": 0.0048, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_272cc06e.csv", "_import_date": "2025-06-25T18:13:50.836596", "_row_index": 4}, {"定额编号": "1-2", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_272cc06e.csv", "_import_date": "2025-06-25T18:13:50.836964", "_row_index": 5}, {"定额编号": "1-3", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.364, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_272cc06e.csv", "_import_date": "2025-06-25T18:13:50.837449", "_row_index": 6}, {"定额编号": "1-3", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钉机", "单位": "台班", "消耗量": 0.0056, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_272cc06e.csv", "_import_date": "2025-06-25T18:13:50.837676", "_row_index": 7}, {"定额编号": "1-3", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_272cc06e.csv", "_import_date": "2025-06-25T18:13:50.837848", "_row_index": 8}]}, "child_resources_5b7eca5f": {"metadata": {"source_file": "child_resources_5b7eca5f.csv", "document_count": 3, "fields": ["定额编号", "资源编号", "类别", "子项名称", "单位", "消耗量", "单价", "合价"], "collection_name": "child_resources_5b7eca5f"}, "documents": [{"定额编号": "1-1", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.8, "单价": 120.0, "合价": 96.0, "_source_file": "child_resources_5b7eca5f.csv", "_import_date": "2025-06-25T18:13:50.853227", "_row_index": 0}, {"定额编号": "1-1", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钎机", "单位": "台班", "消耗量": 0.1, "单价": 50.0, "合价": 5.0, "_source_file": "child_resources_5b7eca5f.csv", "_import_date": "2025-06-25T18:13:50.853421", "_row_index": 1}, {"定额编号": "1-1", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 5.0, "单价": 101.0, "合价": 5.05, "_source_file": "child_resources_5b7eca5f.csv", "_import_date": "2025-06-25T18:13:50.853596", "_row_index": 2}]}, "child_resources_753b5cbd": {"metadata": {"source_file": "child_resources_753b5cbd.csv", "document_count": 39, "fields": ["定额编号", "资源编号", "类别", "子项名称", "单位", "消耗量", "单价", "合价"], "collection_name": "child_resources_753b5cbd"}, "documents": [{"定额编号": "1-1", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.11, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.873449", "_row_index": 0}, {"定额编号": "1-1", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打桩机", "单位": "台班", "消耗量": 0.0031, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.873654", "_row_index": 1}, {"定额编号": "1-1", "资源编号": 99460004, "类别": "机械", "子项名称": "其他机具费 占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.873839", "_row_index": 2}, {"定额编号": "1-2", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.12, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.874010", "_row_index": 3}, {"定额编号": "1-2", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打桩机", "单位": "台班", "消耗量": 0.0032, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.874178", "_row_index": 4}, {"定额编号": "1-2", "资源编号": 99460004, "类别": "机械", "子项名称": "其他机具费 占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.874340", "_row_index": 5}, {"定额编号": "1-3", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.13, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.874492", "_row_index": 6}, {"定额编号": "1-3", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打桩机", "单位": "台班", "消耗量": 0.0033, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.874634", "_row_index": 7}, {"定额编号": "1-3", "资源编号": 99460004, "类别": "机械", "子项名称": "其他机具费 占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.874943", "_row_index": 8}, {"定额编号": "1-4", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.14, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.875118", "_row_index": 9}, {"定额编号": "1-4", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打桩机", "单位": "台班", "消耗量": 0.0034, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.875248", "_row_index": 10}, {"定额编号": "1-4", "资源编号": 99460004, "类别": "机械", "子项名称": "其他机具费 占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.875370", "_row_index": 11}, {"定额编号": "1-5", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.15, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.875489", "_row_index": 12}, {"定额编号": "1-5", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打桩机", "单位": "台班", "消耗量": 0.0035, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.875607", "_row_index": 13}, {"定额编号": "1-5", "资源编号": 99460004, "类别": "机械", "子项名称": "其他机具费 占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.875744", "_row_index": 14}, {"定额编号": "1-6", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.16, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.875862", "_row_index": 15}, {"定额编号": "1-6", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打桩机", "单位": "台班", "消耗量": 0.0036, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.875976", "_row_index": 16}, {"定额编号": "1-6", "资源编号": 99460004, "类别": "机械", "子项名称": "其他机具费 占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.876087", "_row_index": 17}, {"定额编号": "1-7", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.17, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.876194", "_row_index": 18}, {"定额编号": "1-7", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打桩机", "单位": "台班", "消耗量": 0.0037, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.876305", "_row_index": 19}, {"定额编号": "1-7", "资源编号": 99460004, "类别": "机械", "子项名称": "其他机具费 占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.876419", "_row_index": 20}, {"定额编号": "1-8", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.18, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.876538", "_row_index": 21}, {"定额编号": "1-8", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打桩机", "单位": "台班", "消耗量": 0.0038, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.876651", "_row_index": 22}, {"定额编号": "1-8", "资源编号": 99460004, "类别": "机械", "子项名称": "其他机具费 占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.876771", "_row_index": 23}, {"定额编号": "1-9", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.19, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.876954", "_row_index": 24}, {"定额编号": "1-9", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打桩机", "单位": "台班", "消耗量": 0.0039, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.877089", "_row_index": 25}, {"定额编号": "1-9", "资源编号": 99460004, "类别": "机械", "子项名称": "其他机具费 占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.877218", "_row_index": 26}, {"定额编号": "1-10", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.2, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.877329", "_row_index": 27}, {"定额编号": "1-10", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打桩机", "单位": "台班", "消耗量": 0.004, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.877438", "_row_index": 28}, {"定额编号": "1-10", "资源编号": 99460004, "类别": "机械", "子项名称": "其他机具费 占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.877550", "_row_index": 29}, {"定额编号": "1-11", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.21, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.877663", "_row_index": 30}, {"定额编号": "1-11", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打桩机", "单位": "台班", "消耗量": 0.0041, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.877779", "_row_index": 31}, {"定额编号": "1-11", "资源编号": 99460004, "类别": "机械", "子项名称": "其他机具费 占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.877893", "_row_index": 32}, {"定额编号": "1-12", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.22, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.878009", "_row_index": 33}, {"定额编号": "1-12", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打桩机", "单位": "台班", "消耗量": 0.0042, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.878144", "_row_index": 34}, {"定额编号": "1-12", "资源编号": 99460004, "类别": "机械", "子项名称": "其他机具费 占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.878259", "_row_index": 35}, {"定额编号": "1-13", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.23, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.878375", "_row_index": 36}, {"定额编号": "1-13", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打桩机", "单位": "台班", "消耗量": 0.0043, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.878844", "_row_index": 37}, {"定额编号": "1-13", "资源编号": 99460004, "类别": "机械", "子项名称": "其他机具费 占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_753b5cbd.csv", "_import_date": "2025-06-25T18:13:50.879279", "_row_index": 38}]}, "child_resources_9b8dce7f": {"metadata": {"source_file": "child_resources_9b8dce7f.csv", "document_count": 22, "fields": ["定额编号", "资源编号", "类别", "子项名称", "单位", "消耗量", "单价", "合价"], "collection_name": "child_resources_9b8dce7f"}, "documents": [{"定额编号": "1-11", "资源编号": "00010701", "类别": "人工", "子项名称": "综合用工三类", "单位": "工日/m³", "消耗量": 0.2, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_9b8dce7f.csv", "_import_date": "2025-06-25T18:13:50.896029", "_row_index": 0}, {"定额编号": "1-11", "资源编号": "99030030", "类别": "机械", "子项名称": "电动打钎机", "单位": "台班/m³", "消耗量": 0.0039, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_9b8dce7f.csv", "_import_date": "2025-06-25T18:13:50.896187", "_row_index": 1}, {"定额编号": "1-11", "资源编号": "99460004", "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_9b8dce7f.csv", "_import_date": "2025-06-25T18:13:50.896334", "_row_index": 2}, {"定额编号": "1-16", "资源编号": "00010701", "类别": "人工", "子项名称": "综合用工三类", "单位": "工日/m³", "消耗量": 0.611, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_9b8dce7f.csv", "_import_date": "2025-06-25T18:13:50.897076", "_row_index": 3}, {"定额编号": "1-16", "资源编号": "99460004", "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_9b8dce7f.csv", "_import_date": "2025-06-25T18:13:50.897488", "_row_index": 4}, {"定额编号": "1-18", "资源编号": "00010701", "类别": "人工", "子项名称": "综合用工三类", "单位": "工日/m³", "消耗量": 0.761, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_9b8dce7f.csv", "_import_date": "2025-06-25T18:13:50.897724", "_row_index": 5}, {"定额编号": "1-18", "资源编号": "05030007", "类别": "材料", "子项名称": "板方材", "单位": "m³/m³", "消耗量": 0.0003, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_9b8dce7f.csv", "_import_date": "2025-06-25T18:13:50.897898", "_row_index": 6}, {"定额编号": "1-18", "资源编号": "35030011", "类别": "材料", "子项名称": "钢支撑", "单位": "kg/m³", "消耗量": 0.155, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_9b8dce7f.csv", "_import_date": "2025-06-25T18:13:50.898067", "_row_index": 7}, {"定额编号": "1-18", "资源编号": "14290005-1", "类别": "材料", "子项名称": "乙炔气", "单位": "m³/m³", "消耗量": 0.001, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_9b8dce7f.csv", "_import_date": "2025-06-25T18:13:50.898369", "_row_index": 8}, {"定额编号": "1-18", "资源编号": "03130101", "类别": "材料", "子项名称": "电焊条(综合)", "单位": "kg/m³", "消耗量": 0.019, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_9b8dce7f.csv", "_import_date": "2025-06-25T18:13:50.898572", "_row_index": 9}, {"定额编号": "1-18", "资源编号": "9907000009", "类别": "机械", "子项名称": "自卸汽车5t", "单位": "台班/m³", "消耗量": 0.0066, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_9b8dce7f.csv", "_import_date": "2025-06-25T18:13:50.898734", "_row_index": 10}, {"定额编号": "1-18", "资源编号": "9925000202", "类别": "机械", "子项名称": "交流电焊机32kV·A", "单位": "台班/m³", "消耗量": 0.0017, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_9b8dce7f.csv", "_import_date": "2025-06-25T18:13:50.898886", "_row_index": 11}, {"定额编号": "1-18", "资源编号": "9909000253", "类别": "机械", "子项名称": "电动卷扬机单筒慢速5t", "单位": "台班/m³", "消耗量": 0.013, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_9b8dce7f.csv", "_import_date": "2025-06-25T18:13:50.899369", "_row_index": 12}, {"定额编号": "1-18", "资源编号": "99460004", "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_9b8dce7f.csv", "_import_date": "2025-06-25T18:13:50.899566", "_row_index": 13}, {"定额编号": "1-21", "资源编号": "00010701", "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.034, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_9b8dce7f.csv", "_import_date": "2025-06-25T18:13:50.899855", "_row_index": 14}, {"定额编号": "1-21", "资源编号": "9901000004", "类别": "机械", "子项名称": "履带式单斗液压挖掘机0.3m³", "单位": "台班", "消耗量": 0.0172, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_9b8dce7f.csv", "_import_date": "2025-06-25T18:13:50.900244", "_row_index": 15}, {"定额编号": "1-21", "资源编号": "9907000102", "类别": "机械", "子项名称": "轮胎式装载机1.5m³", "单位": "台班", "消耗量": 0.0057, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_9b8dce7f.csv", "_import_date": "2025-06-25T18:13:50.900439", "_row_index": 16}, {"定额编号": "1-21", "资源编号": "99460004", "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_9b8dce7f.csv", "_import_date": "2025-06-25T18:13:50.900612", "_row_index": 17}, {"定额编号": "1-22", "资源编号": "00010701", "类别": "人工", "子项名称": "综合用工三类", "单位": "工日/m³", "消耗量": 0.525, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_9b8dce7f.csv", "_import_date": "2025-06-25T18:13:50.900769", "_row_index": 18}, {"定额编号": "1-22", "资源编号": "9943000205", "类别": "机械", "子项名称": "空压机6m³/min", "单位": "台班/m³", "消耗量": 0.25, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_9b8dce7f.csv", "_import_date": "2025-06-25T18:13:50.901170", "_row_index": 19}, {"定额编号": "1-22", "资源编号": "99330001", "类别": "机械", "子项名称": "风镐", "单位": "台班/m³", "消耗量": 0.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_9b8dce7f.csv", "_import_date": "2025-06-25T18:13:50.901565", "_row_index": 20}, {"定额编号": "1-22", "资源编号": "99460004", "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_9b8dce7f.csv", "_import_date": "2025-06-25T18:13:50.902003", "_row_index": 21}]}, "child_resources_d44a152a": {"metadata": {"source_file": "child_resources_d44a152a.csv", "document_count": 9, "fields": ["定额编号", "资源编号", "类别", "子项名称", "单位", "消耗量", "单价", "合价"], "collection_name": "child_resources_d44a152a"}, "documents": [{"定额编号": "1-1", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.187, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_d44a152a.csv", "_import_date": "2025-06-25T18:13:50.914533", "_row_index": 0}, {"定额编号": "1-1", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钎机", "单位": "台班", "消耗量": 0.0039, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_d44a152a.csv", "_import_date": "2025-06-25T18:13:50.914689", "_row_index": 1}, {"定额编号": "1-1", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_d44a152a.csv", "_import_date": "2025-06-25T18:13:50.915434", "_row_index": 2}, {"定额编号": "1-2", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.274, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_d44a152a.csv", "_import_date": "2025-06-25T18:13:50.915785", "_row_index": 3}, {"定额编号": "1-2", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钎机", "单位": "台班", "消耗量": 0.0048, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_d44a152a.csv", "_import_date": "2025-06-25T18:13:50.915985", "_row_index": 4}, {"定额编号": "1-2", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_d44a152a.csv", "_import_date": "2025-06-25T18:13:50.916141", "_row_index": 5}, {"定额编号": "1-3", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.361, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_d44a152a.csv", "_import_date": "2025-06-25T18:13:50.916283", "_row_index": 6}, {"定额编号": "1-3", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钎机", "单位": "台班", "消耗量": 0.0056, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_d44a152a.csv", "_import_date": "2025-06-25T18:13:50.916418", "_row_index": 7}, {"定额编号": "1-3", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_d44a152a.csv", "_import_date": "2025-06-25T18:13:50.916545", "_row_index": 8}]}, "child_resources_f1dcf248": {"metadata": {"source_file": "child_resources_f1dcf248.csv", "document_count": 29, "fields": ["定额编号", "资源编号", "类别", "子项名称", "单位", "消耗量", "单价", "合价"], "collection_name": "child_resources_f1dcf248"}, "documents": [{"定额编号": "1-29", "资源编号": "00010701", "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.175, "单价": 120.0, "合价": 21.0, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.933120", "_row_index": 0}, {"定额编号": "1-29", "资源编号": "9913000202", "类别": "机械", "子项名称": "电动夯实机 20~62kg/m", "单位": "台班", "消耗量": 0.083, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.933498", "_row_index": 1}, {"定额编号": "1-29", "资源编号": "99460004", "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 21.0, "合价": 0.315, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.933703", "_row_index": 2}, {"定额编号": "1-29", "资源编号": "9931000002", "类别": "机械", "子项名称": "洒水车 8t", "单位": "台班", "消耗量": 0.0008, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.934006", "_row_index": 3}, {"定额编号": "1-30", "资源编号": "00010701", "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.21, "单价": 120.0, "合价": 25.2, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.934444", "_row_index": 4}, {"定额编号": "1-30", "资源编号": "9913000202", "类别": "机械", "子项名称": "电动夯实机 20~62kg/m", "单位": "台班", "消耗量": 0.067, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.934926", "_row_index": 5}, {"定额编号": "1-30", "资源编号": "99460004", "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 25.2, "合价": 0.3779999999999999, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.935215", "_row_index": 6}, {"定额编号": "1-30", "资源编号": "9931000002", "类别": "机械", "子项名称": "洒水车 8t", "单位": "台班", "消耗量": 0.002, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.935409", "_row_index": 7}, {"定额编号": "1-30", "资源编号": "04090026", "类别": "材料", "子项名称": "熟石灰", "单位": "kg", "消耗量": 275.0, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.935704", "_row_index": 8}, {"定额编号": "1-31", "资源编号": "00010701", "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.21, "单价": 120.0, "合价": 25.2, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.935877", "_row_index": 9}, {"定额编号": "1-31", "资源编号": "9913000202", "类别": "机械", "子项名称": "电动夯实机 20~62kg/m", "单位": "台班", "消耗量": 0.067, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.936067", "_row_index": 10}, {"定额编号": "1-31", "资源编号": "99460004", "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 25.2, "合价": 0.3779999999999999, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.936494", "_row_index": 11}, {"定额编号": "1-31", "资源编号": "9931000002", "类别": "机械", "子项名称": "洒水车 8t", "单位": "台班", "消耗量": 0.004, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.936677", "_row_index": 12}, {"定额编号": "1-31", "资源编号": "04090026", "类别": "材料", "子项名称": "熟石灰", "单位": "kg", "消耗量": 413.0, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.936813", "_row_index": 13}, {"定额编号": "1-32", "资源编号": "00010701", "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.131, "单价": 120.0, "合价": 15.72, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.937013", "_row_index": 14}, {"定额编号": "1-32", "资源编号": "9913000202", "类别": "机械", "子项名称": "电动夯实机 20~62kg/m", "单位": "台班", "消耗量": 0.025, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.937531", "_row_index": 15}, {"定额编号": "1-32", "资源编号": "99460004", "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 15.72, "合价": 0.2358, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.937689", "_row_index": 16}, {"定额编号": "1-32", "资源编号": "9931000002", "类别": "机械", "子项名称": "洒水车 8t", "单位": "台班", "消耗量": 0.0, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.937982", "_row_index": 17}, {"定额编号": "1-32", "资源编号": "04050011-2", "类别": "材料", "子项名称": "级配砂石", "单位": "kg", "消耗量": 2315.4, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.938185", "_row_index": 18}, {"定额编号": "1-32", "资源编号": "0403000003-2", "类别": "材料", "子项名称": "砂子 中粗砂", "单位": "kg", "消耗量": 1854.2, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.938367", "_row_index": 19}, {"定额编号": "1-33", "资源编号": "00010701", "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.177, "单价": 120.0, "合价": 21.24, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.938504", "_row_index": 20}, {"定额编号": "1-33", "资源编号": "9913000202", "类别": "机械", "子项名称": "电动夯实机 20~62kg/m", "单位": "台班", "消耗量": 0.085, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.938595", "_row_index": 21}, {"定额编号": "1-33", "资源编号": "99460004", "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 21.24, "合价": 0.3186, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.938684", "_row_index": 22}, {"定额编号": "1-33", "资源编号": "9931000002", "类别": "机械", "子项名称": "洒水车 8t", "单位": "台班", "消耗量": 0.004, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.938766", "_row_index": 23}, {"定额编号": "1-34", "资源编号": "00010701", "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.26, "单价": 120.0, "合价": 31.200000000000003, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.938855", "_row_index": 24}, {"定额编号": "1-34", "资源编号": "9913000202", "类别": "机械", "子项名称": "电动夯实机 20~62kg/m", "单位": "台班", "消耗量": 0.085, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.939195", "_row_index": 25}, {"定额编号": "1-34", "资源编号": "99460004", "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 31.200000000000003, "合价": 0.468, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.939568", "_row_index": 26}, {"定额编号": "1-34", "资源编号": "9931000002", "类别": "机械", "子项名称": "洒水车 8t", "单位": "台班", "消耗量": 0.003, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.939708", "_row_index": 27}, {"定额编号": "1-34", "资源编号": "04090031", "类别": "材料", "子项名称": "石灰粉煤灰碎石", "单位": "t", "消耗量": 2.3154, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_f1dcf248.csv", "_import_date": "2025-06-25T18:13:50.939826", "_row_index": 28}]}, "child_resources_test": {"metadata": {"source_file": "child_resources_test.csv", "document_count": 7, "fields": ["定额编号", "资源编号", "资源名称", "资源类型", "数量", "单位", "单价", "合价"], "collection_name": "child_resources_test"}, "documents": [{"定额编号": "001-001", "资源编号": "R001", "资源名称": "C30混凝土", "资源类型": "材料", "数量": 1.05, "单位": "m³", "单价": 420.0, "合价": 441.0, "_source_file": "child_resources_test.csv", "_import_date": "2025-06-25T18:13:50.954652", "_row_index": 0}, {"定额编号": "001-001", "资源编号": "R002", "资源名称": "人工", "资源类型": "人工", "数量": 8.5, "单位": "工日", "单价": 150.0, "合价": 1275.0, "_source_file": "child_resources_test.csv", "_import_date": "2025-06-25T18:13:50.954761", "_row_index": 1}, {"定额编号": "001-001", "资源编号": "R003", "资源名称": "机械台班", "资源类型": "机械", "数量": 2.3, "单位": "台班", "单价": 350.0, "合价": 805.0, "_source_file": "child_resources_test.csv", "_import_date": "2025-06-25T18:13:50.954852", "_row_index": 2}, {"定额编号": "001-002", "资源编号": "R004", "资源名称": "HPB300钢筋", "资源类型": "材料", "数量": 1.02, "单位": "kg", "单价": 4.2, "合价": 4.284, "_source_file": "child_resources_test.csv", "_import_date": "2025-06-25T18:13:50.954949", "_row_index": 3}, {"定额编号": "001-002", "资源编号": "R005", "资源名称": "人工", "资源类型": "人工", "数量": 12.8, "单位": "工日", "单价": 150.0, "合价": 1920.0, "_source_file": "child_resources_test.csv", "_import_date": "2025-06-25T18:13:50.955028", "_row_index": 4}, {"定额编号": "002-001", "资源编号": "R006", "资源名称": "MU10砖", "资源类型": "材料", "数量": 0.98, "单位": "m³", "单价": 280.0, "合价": 274.4, "_source_file": "child_resources_test.csv", "_import_date": "2025-06-25T18:13:50.955116", "_row_index": 5}, {"定额编号": "002-001", "资源编号": "R007", "资源名称": "砂浆", "资源类型": "材料", "数量": 0.25, "单位": "m³", "单价": 180.0, "合价": 45.0, "_source_file": "child_resources_test.csv", "_import_date": "2025-06-25T18:13:50.955191", "_row_index": 6}]}, "child_resources_web_test": {"metadata": {"source_file": "child_resources_web_test.csv", "document_count": 3, "fields": ["定额编号", "资源编号", "资源名称", "资源类型", "数量", "单位", "单价", "合价"], "collection_name": "child_resources_web_test"}, "documents": [{"定额编号": "001-001", "资源编号": "R001", "资源名称": "C30混凝土", "资源类型": "材料", "数量": 1.05, "单位": "m³", "单价": 420.0, "合价": 441.0, "_source_file": "child_resources_web_test.csv", "_import_date": "2025-06-25T18:13:50.970038", "_row_index": 0}, {"定额编号": "001-001", "资源编号": "R002", "资源名称": "人工", "资源类型": "人工", "数量": 8.5, "单位": "工日", "单价": 150.0, "合价": 1275.0, "_source_file": "child_resources_web_test.csv", "_import_date": "2025-06-25T18:13:50.970482", "_row_index": 1}, {"定额编号": "001-002", "资源编号": "R003", "资源名称": "HPB300钢筋", "资源类型": "材料", "数量": 1.02, "单位": "kg", "单价": 4.2, "合价": 4.284, "_source_file": "child_resources_web_test.csv", "_import_date": "2025-06-25T18:13:50.970687", "_row_index": 2}]}, "child_resources_mcp_test": {"metadata": {"source_file": "child_resources_mcp_test.csv", "document_count": 7, "fields": ["定额编号", "资源编号", "资源名称", "资源类型", "数量", "单位", "单价", "合价"], "collection_name": "child_resources_mcp_test"}, "documents": [{"定额编号": "001-001", "资源编号": "R001", "资源名称": "C30混凝土", "资源类型": "材料", "数量": 1.05, "单位": "m³", "单价": 420.0, "合价": 441.0, "_source_file": "child_resources_mcp_test.csv", "_import_date": "2025-06-25T18:13:50.983948", "_row_index": 0}, {"定额编号": "001-001", "资源编号": "R002", "资源名称": "人工", "资源类型": "人工", "数量": 8.5, "单位": "工日", "单价": 150.0, "合价": 1275.0, "_source_file": "child_resources_mcp_test.csv", "_import_date": "2025-06-25T18:13:50.984109", "_row_index": 1}, {"定额编号": "001-001", "资源编号": "R003", "资源名称": "机械台班", "资源类型": "机械", "数量": 2.3, "单位": "台班", "单价": 350.0, "合价": 805.0, "_source_file": "child_resources_mcp_test.csv", "_import_date": "2025-06-25T18:13:50.984271", "_row_index": 2}, {"定额编号": "001-002", "资源编号": "R004", "资源名称": "HPB300钢筋", "资源类型": "材料", "数量": 1.02, "单位": "kg", "单价": 4.2, "合价": 4.284, "_source_file": "child_resources_mcp_test.csv", "_import_date": "2025-06-25T18:13:50.984401", "_row_index": 3}, {"定额编号": "001-002", "资源编号": "R005", "资源名称": "人工", "资源类型": "人工", "数量": 12.8, "单位": "工日", "单价": 150.0, "合价": 1920.0, "_source_file": "child_resources_mcp_test.csv", "_import_date": "2025-06-25T18:13:50.984525", "_row_index": 4}, {"定额编号": "002-001", "资源编号": "R006", "资源名称": "MU10砖", "资源类型": "材料", "数量": 0.98, "单位": "m³", "单价": 280.0, "合价": 274.4, "_source_file": "child_resources_mcp_test.csv", "_import_date": "2025-06-25T18:13:50.984912", "_row_index": 5}, {"定额编号": "002-001", "资源编号": "R007", "资源名称": "砂浆", "资源类型": "材料", "数量": 0.25, "单位": "m³", "单价": 180.0, "合价": 45.0, "_source_file": "child_resources_mcp_test.csv", "_import_date": "2025-06-25T18:13:50.985123", "_row_index": 6}]}, "child_resources_e5a0f3e2": {"metadata": {"source_file": "child_resources_e5a0f3e2.csv", "document_count": 4, "fields": ["定额编号", "资源编号", "类别", "子项名称", "单位", "消耗量", "单价", "合价"], "collection_name": "child_resources_e5a0f3e2"}, "documents": [{"定额编号": "1-1", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.187, "单价": 0, "合价": 0.0, "_source_file": "child_resources_e5a0f3e2.csv", "_import_date": "2025-06-25T18:13:51.000884", "_row_index": 0}, {"定额编号": "1-1", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钉机", "单位": "台班", "消耗量": 0.0039, "单价": 0, "合价": 0.0, "_source_file": "child_resources_e5a0f3e2.csv", "_import_date": "2025-06-25T18:13:51.003056", "_row_index": 1}, {"定额编号": "1-2", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.274, "单价": 0, "合价": 0.0, "_source_file": "child_resources_e5a0f3e2.csv", "_import_date": "2025-06-25T18:13:51.003345", "_row_index": 2}, {"定额编号": "1-3", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.361, "单价": 0, "合价": 0.0, "_source_file": "child_resources_e5a0f3e2.csv", "_import_date": "2025-06-25T18:13:51.003465", "_row_index": 3}]}, "child_resources_bcf7f567": {"metadata": {"source_file": "child_resources_bcf7f567.csv", "document_count": 49, "fields": ["定额编号", "资源编号", "类别", "子项名称", "单位", "消耗量", "单价", "合价"], "collection_name": "child_resources_bcf7f567"}, "documents": [{"定额编号": "1-14", "资源编号": "00010701", "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.013, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.020212", "_row_index": 0}, {"定额编号": "1-14", "资源编号": "9901000016", "类别": "机械", "子项名称": "履带式单斗液压挖掘机1.6m³", "单位": "台班", "消耗量": 0.0025, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.020323", "_row_index": 1}, {"定额编号": "1-14", "资源编号": "9907000703", "类别": "机械", "子项名称": "轮胎式装载机3m³", "单位": "台班", "消耗量": 0.0008, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.020420", "_row_index": 2}, {"定额编号": "1-14", "资源编号": "99460004", "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.020520", "_row_index": 3}, {"定额编号": "1-15", "资源编号": "00010701", "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.015, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.020631", "_row_index": 4}, {"定额编号": "1-15", "资源编号": "9901000016", "类别": "机械", "子项名称": "履带式单斗液压挖掘机1.6m³", "单位": "台班", "消耗量": 0.0028, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.020721", "_row_index": 5}, {"定额编号": "1-15", "资源编号": "9907000703", "类别": "机械", "子项名称": "轮胎式装载机3m³", "单位": "台班", "消耗量": 0.0009, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.020805", "_row_index": 6}, {"定额编号": "1-15", "资源编号": "99460004", "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.020881", "_row_index": 7}, {"定额编号": "1-16", "资源编号": "00010701", "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.611, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.020956", "_row_index": 8}, {"定额编号": "1-16", "资源编号": "9901000016", "类别": "机械", "子项名称": "履带式单斗液压挖掘机1.6m³", "单位": "台班", "消耗量": 0.0, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.021127", "_row_index": 9}, {"定额编号": "1-16", "资源编号": "99460004", "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.021214", "_row_index": 10}, {"定额编号": "1-17", "资源编号": "00010701", "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.013, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.021285", "_row_index": 11}, {"定额编号": "1-17", "资源编号": "9901000016", "类别": "机械", "子项名称": "履带式单斗液压挖掘机1.6m³", "单位": "台班", "消耗量": 0.0042, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.021353", "_row_index": 12}, {"定额编号": "1-17", "资源编号": "99460004", "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.021418", "_row_index": 13}, {"定额编号": "1-18", "资源编号": "00010701", "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.761, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.021495", "_row_index": 14}, {"定额编号": "1-18", "资源编号": "05030007", "类别": "材料", "子项名称": "板方材", "单位": "m³", "消耗量": 0.0003, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.021561", "_row_index": 15}, {"定额编号": "1-18", "资源编号": "35030011", "类别": "材料", "子项名称": "钢支撑", "单位": "kg", "消耗量": 0.155, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.021626", "_row_index": 16}, {"定额编号": "1-18", "资源编号": "14290005-1", "类别": "材料", "子项名称": "乙炔气", "单位": "m³", "消耗量": 0.001, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.021693", "_row_index": 17}, {"定额编号": "1-18", "资源编号": "03130101", "类别": "材料", "子项名称": "电焊条(综合)", "单位": "kg", "消耗量": 0.019, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.021760", "_row_index": 18}, {"定额编号": "1-18", "资源编号": "34000011", "类别": "材料", "子项名称": "其他材料费占材料费", "单位": "%", "消耗量": 1.0, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.021826", "_row_index": 19}, {"定额编号": "1-18", "资源编号": "9907000009", "类别": "机械", "子项名称": "自卸汽车5t", "单位": "台班", "消耗量": 0.0066, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.021893", "_row_index": 20}, {"定额编号": "1-18", "资源编号": "9925000202", "类别": "机械", "子项名称": "交流电焊机32kV·A", "单位": "台班", "消耗量": 0.0017, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.021976", "_row_index": 21}, {"定额编号": "1-18", "资源编号": "9909000253", "类别": "机械", "子项名称": "电动卷扬机单筒慢速5t", "单位": "台班", "消耗量": 0.013, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.022048", "_row_index": 22}, {"定额编号": "1-18", "资源编号": "99460004", "类别": "机械", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.022117", "_row_index": 23}, {"定额编号": "1-19", "资源编号": "00010701", "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.699, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.022191", "_row_index": 24}, {"定额编号": "1-19", "资源编号": "05030007", "类别": "材料", "子项名称": "板方材", "单位": "m³", "消耗量": 0.0003, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.022259", "_row_index": 25}, {"定额编号": "1-19", "资源编号": "35030011", "类别": "材料", "子项名称": "钢支撑", "单位": "kg", "消耗量": 0.155, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.022328", "_row_index": 26}, {"定额编号": "1-19", "资源编号": "14290005-1", "类别": "材料", "子项名称": "乙炔气", "单位": "m³", "消耗量": 0.001, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.022395", "_row_index": 27}, {"定额编号": "1-19", "资源编号": "03130101", "类别": "材料", "子项名称": "电焊条(综合)", "单位": "kg", "消耗量": 0.019, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.022462", "_row_index": 28}, {"定额编号": "1-19", "资源编号": "03150906", "类别": "材料", "子项名称": "铁件", "单位": "kg", "消耗量": 0.05, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.022533", "_row_index": 29}, {"定额编号": "1-19", "资源编号": "3701000201", "类别": "材料", "子项名称": "钢轨38kg/m", "单位": "kg", "消耗量": 0.27, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.022600", "_row_index": 30}, {"定额编号": "1-19", "资源编号": "34000011", "类别": "材料", "子项名称": "其他材料费占材料费", "单位": "%", "消耗量": 1.0, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.022667", "_row_index": 31}, {"定额编号": "1-19", "资源编号": "9907000009", "类别": "机械", "子项名称": "自卸汽车5t", "单位": "台班", "消耗量": 0.0066, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.022735", "_row_index": 32}, {"定额编号": "1-19", "资源编号": "9925000202", "类别": "机械", "子项名称": "交流电焊机32kV·A", "单位": "台班", "消耗量": 0.0017, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.022804", "_row_index": 33}, {"定额编号": "1-19", "资源编号": "9907000701", "类别": "机械", "子项名称": "轮胎式装载机1m³", "单位": "台班", "消耗量": 0.0197, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.022880", "_row_index": 34}, {"定额编号": "1-19", "资源编号": "9909000253", "类别": "机械", "子项名称": "电动卷扬机单筒慢速5t", "单位": "台班", "消耗量": 0.007, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.023114", "_row_index": 35}, {"定额编号": "1-19", "资源编号": "99460004", "类别": "机械", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.023313", "_row_index": 36}, {"定额编号": "1-20", "资源编号": "00010701", "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.078, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.023457", "_row_index": 37}, {"定额编号": "1-20", "资源编号": "03150906", "类别": "材料", "子项名称": "铁件", "单位": "kg", "消耗量": 0.05, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.023601", "_row_index": 38}, {"定额编号": "1-20", "资源编号": "3701000201", "类别": "材料", "子项名称": "钢轨38kg/m", "单位": "kg", "消耗量": 0.27, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.023744", "_row_index": 39}, {"定额编号": "1-20", "资源编号": "34000011", "类别": "材料", "子项名称": "其他材料费占材料费", "单位": "%", "消耗量": 1.0, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.024290", "_row_index": 40}, {"定额编号": "1-20", "资源编号": "9907000009", "类别": "机械", "子项名称": "自卸汽车5t", "单位": "台班", "消耗量": 0.0066, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.024509", "_row_index": 41}, {"定额编号": "1-20", "资源编号": "9901000301", "类别": "机械", "子项名称": "履带式单斗挖土机1.0m³", "单位": "台班", "消耗量": 0.0197, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.024855", "_row_index": 42}, {"定额编号": "1-20", "资源编号": "9909000253", "类别": "机械", "子项名称": "电动卷扬机单筒慢速5t", "单位": "台班", "消耗量": 0.003, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.025037", "_row_index": 43}, {"定额编号": "1-20", "资源编号": "99460004", "类别": "机械", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.025132", "_row_index": 44}, {"定额编号": "1-21", "资源编号": "00010701", "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.034, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.025225", "_row_index": 45}, {"定额编号": "1-21", "资源编号": "9901000004", "类别": "机械", "子项名称": "履带式单斗液压挖掘机0.3m³", "单位": "台班", "消耗量": 0.0172, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.025311", "_row_index": 46}, {"定额编号": "1-21", "资源编号": "9907000102", "类别": "机械", "子项名称": "轮胎式装载机1.5m³", "单位": "台班", "消耗量": 0.0057, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.025387", "_row_index": 47}, {"定额编号": "1-21", "资源编号": "99460004", "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_bcf7f567.csv", "_import_date": "2025-06-25T18:13:51.025463", "_row_index": 48}]}, "child_resources_81699df3": {"metadata": {"source_file": "child_resources_81699df3.csv", "document_count": 49, "fields": ["定额编号", "资源编号", "类别", "子项名称", "单位", "消耗量", "单价", "合价"], "collection_name": "child_resources_81699df3"}, "documents": [{"定额编号": "1-43", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.01, "单价": 120.0, "合价": 1.2, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.038959", "_row_index": 0}, {"定额编号": "1-43", "资源编号": 34000009, "类别": "材料", "子项名称": "弃土或渣土消纳", "单位": "m³", "消耗量": 1.0, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.039055", "_row_index": 1}, {"定额编号": "1-43", "资源编号": 9901000016, "类别": "机械", "子项名称": "履带式单斗液压挖掘机1.6m³", "单位": "台班", "消耗量": 0.0017, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.039135", "_row_index": 2}, {"定额编号": "1-43", "资源编号": 9907000012, "类别": "机械", "子项名称": "自卸汽车12t", "单位": "台班", "消耗量": 0.0041, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.039212", "_row_index": 3}, {"定额编号": "1-43", "资源编号": 9907000703, "类别": "机械", "子项名称": "轮胎式装载机3m³", "单位": "台班", "消耗量": 0.0006, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.039287", "_row_index": 4}, {"定额编号": "1-43", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 1.2, "合价": 0.018, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.039402", "_row_index": 5}, {"定额编号": "1-44", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.01, "单价": 120.0, "合价": 1.2, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.039477", "_row_index": 6}, {"定额编号": "1-44", "资源编号": 34000009, "类别": "材料", "子项名称": "弃土或渣土消纳", "单位": "m³", "消耗量": 1.0, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.039549", "_row_index": 7}, {"定额编号": "1-44", "资源编号": 9901000016, "类别": "机械", "子项名称": "履带式单斗液压挖掘机1.6m³", "单位": "台班", "消耗量": 0.0017, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.039619", "_row_index": 8}, {"定额编号": "1-44", "资源编号": 9907000012, "类别": "机械", "子项名称": "自卸汽车12t", "单位": "台班", "消耗量": 0.0041, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.039689", "_row_index": 9}, {"定额编号": "1-44", "资源编号": 9907000703, "类别": "机械", "子项名称": "轮胎式装载机3m³", "单位": "台班", "消耗量": 0.0006, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.039760", "_row_index": 10}, {"定额编号": "1-44", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 1.2, "合价": 0.018, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.039827", "_row_index": 11}, {"定额编号": "1-45", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.01, "单价": 120.0, "合价": 1.2, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.039895", "_row_index": 12}, {"定额编号": "1-45", "资源编号": 34000009, "类别": "材料", "子项名称": "弃土或渣土消纳", "单位": "m³", "消耗量": 1.0, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.039965", "_row_index": 13}, {"定额编号": "1-45", "资源编号": 9901000016, "类别": "机械", "子项名称": "履带式单斗液压挖掘机1.6m³", "单位": "台班", "消耗量": 0.0017, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.040075", "_row_index": 14}, {"定额编号": "1-45", "资源编号": 9907000012, "类别": "机械", "子项名称": "自卸汽车12t", "单位": "台班", "消耗量": 0.0041, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.040190", "_row_index": 15}, {"定额编号": "1-45", "资源编号": 9907000703, "类别": "机械", "子项名称": "轮胎式装载机3m³", "单位": "台班", "消耗量": 0.0006, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.040299", "_row_index": 16}, {"定额编号": "1-45", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 1.2, "合价": 0.018, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.040408", "_row_index": 17}, {"定额编号": "1-46", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.016, "单价": 120.0, "合价": 1.92, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.040521", "_row_index": 18}, {"定额编号": "1-46", "资源编号": 34000009, "类别": "材料", "子项名称": "弃土或渣土消纳", "单位": "m³", "消耗量": 1.0, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.040632", "_row_index": 19}, {"定额编号": "1-46", "资源编号": 9901000016, "类别": "机械", "子项名称": "履带式单斗液压挖掘机1.6m³", "单位": "台班", "消耗量": 0.0041, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.040739", "_row_index": 20}, {"定额编号": "1-46", "资源编号": 9907000012, "类别": "机械", "子项名称": "自卸汽车12t", "单位": "台班", "消耗量": 0.0057, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.040845", "_row_index": 21}, {"定额编号": "1-46", "资源编号": 9907000703, "类别": "机械", "子项名称": "轮胎式装载机3m³", "单位": "台班", "消耗量": 0.0006, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.040949", "_row_index": 22}, {"定额编号": "1-46", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 1.92, "合价": 0.0288, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.041055", "_row_index": 23}, {"定额编号": "1-47", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.016, "单价": 120.0, "合价": 1.92, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.041165", "_row_index": 24}, {"定额编号": "1-47", "资源编号": 34000009, "类别": "材料", "子项名称": "弃土或渣土消纳", "单位": "m³", "消耗量": 1.0, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.041283", "_row_index": 25}, {"定额编号": "1-47", "资源编号": 9901000016, "类别": "机械", "子项名称": "履带式单斗液压挖掘机1.6m³", "单位": "台班", "消耗量": 0.0041, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.041413", "_row_index": 26}, {"定额编号": "1-47", "资源编号": 9907000012, "类别": "机械", "子项名称": "自卸汽车12t", "单位": "台班", "消耗量": 0.0057, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.041520", "_row_index": 27}, {"定额编号": "1-47", "资源编号": 9907000703, "类别": "机械", "子项名称": "轮胎式装载机3m³", "单位": "台班", "消耗量": 0.0006, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.041626", "_row_index": 28}, {"定额编号": "1-47", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 1.92, "合价": 0.0288, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.041737", "_row_index": 29}, {"定额编号": "1-48", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.016, "单价": 120.0, "合价": 1.92, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.041842", "_row_index": 30}, {"定额编号": "1-48", "资源编号": 34000009, "类别": "材料", "子项名称": "弃土或渣土消纳", "单位": "m³", "消耗量": 1.0, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.041948", "_row_index": 31}, {"定额编号": "1-48", "资源编号": 9901000016, "类别": "机械", "子项名称": "履带式单斗液压挖掘机1.6m³", "单位": "台班", "消耗量": 0.0041, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.042060", "_row_index": 32}, {"定额编号": "1-48", "资源编号": 9907000012, "类别": "机械", "子项名称": "自卸汽车12t", "单位": "台班", "消耗量": 0.0057, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.042564", "_row_index": 33}, {"定额编号": "1-48", "资源编号": 9907000703, "类别": "机械", "子项名称": "轮胎式装载机3m³", "单位": "台班", "消耗量": 0.0006, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.043051", "_row_index": 34}, {"定额编号": "1-48", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 1.92, "合价": 0.0288, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.043243", "_row_index": 35}, {"定额编号": "1-49", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.022, "单价": 120.0, "合价": 2.64, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.043404", "_row_index": 36}, {"定额编号": "1-49", "资源编号": 9944000006, "类别": "机械", "子项名称": "泥浆泵φ100mm", "单位": "台班", "消耗量": 0.0135, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.043543", "_row_index": 37}, {"定额编号": "1-49", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 2.64, "合价": 0.0395999999999999, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.043671", "_row_index": 38}, {"定额编号": "1-50", "资源编号": 34000009, "类别": "材料", "子项名称": "弃土或渣土消纳", "单位": "m³", "消耗量": 1.0, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.043796", "_row_index": 39}, {"定额编号": "1-50", "资源编号": 9907000109, "类别": "机械", "子项名称": "泥浆罐车5000L", "单位": "台班", "消耗量": 0.005, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.043892", "_row_index": 40}, {"定额编号": "1-51", "资源编号": 9907000109, "类别": "机械", "子项名称": "泥浆罐车5000L", "单位": "台班", "消耗量": 0.0016, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.044000", "_row_index": 41}, {"定额编号": "1-52", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.011, "单价": 120.0, "合价": 1.3199999999999998, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.044092", "_row_index": 42}, {"定额编号": "1-52", "资源编号": 9907000012, "类别": "机械", "子项名称": "自卸汽车12t", "单位": "台班", "消耗量": 0.0088, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.044169", "_row_index": 43}, {"定额编号": "1-52", "资源编号": 9907000102, "类别": "机械", "子项名称": "轮胎式装载机1.5m³", "单位": "台班", "消耗量": 0.0037, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.044243", "_row_index": 44}, {"定额编号": "1-52", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 1.3199999999999998, "合价": 0.0197999999999999, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.044474", "_row_index": 45}, {"定额编号": "1-53", "资源编号": 34000009, "类别": "材料", "子项名称": "弃土或渣土消纳", "单位": "m³", "消耗量": 1.0, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.044602", "_row_index": 46}, {"定额编号": "1-53", "资源编号": 9907000012, "类别": "机械", "子项名称": "自卸汽车12t", "单位": "台班", "消耗量": 0.0088, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.044699", "_row_index": 47}, {"定额编号": "1-54", "资源编号": 9907000012, "类别": "机械", "子项名称": "自卸汽车12t", "单位": "台班", "消耗量": 0.0025, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_81699df3.csv", "_import_date": "2025-06-25T18:13:51.044775", "_row_index": 48}]}, "child_resources_62091b57": {"metadata": {"source_file": "child_resources_62091b57.csv", "document_count": 16, "fields": ["定额编号", "资源编号", "类别", "子项名称", "单位", "消耗量", "单价", "合价"], "collection_name": "child_resources_62091b57"}, "documents": [{"定额编号": "1-22", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.525, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_62091b57.csv", "_import_date": "2025-06-25T18:13:51.061192", "_row_index": 0}, {"定额编号": "1-22", "资源编号": 9943000205, "类别": "机械", "子项名称": "空压机6m³/min", "单位": "台班", "消耗量": 0.25, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_62091b57.csv", "_import_date": "2025-06-25T18:13:51.061353", "_row_index": 1}, {"定额编号": "1-22", "资源编号": 99330001, "类别": "机械", "子项名称": "风镐", "单位": "台班", "消耗量": 0.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_62091b57.csv", "_import_date": "2025-06-25T18:13:51.061489", "_row_index": 2}, {"定额编号": "1-22", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_62091b57.csv", "_import_date": "2025-06-25T18:13:51.061617", "_row_index": 3}, {"定额编号": "1-23", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.067, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_62091b57.csv", "_import_date": "2025-06-25T18:13:51.061736", "_row_index": 4}, {"定额编号": "1-23", "资源编号": 99010002, "类别": "机械", "子项名称": "反铲挖掘机(带液压锤)", "单位": "台班", "消耗量": 0.017, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_62091b57.csv", "_import_date": "2025-06-25T18:13:51.061891", "_row_index": 5}, {"定额编号": "1-23", "资源编号": 9907000703, "类别": "机械", "子项名称": "轮胎式装载机3m³", "单位": "台班", "消耗量": 0.0057, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_62091b57.csv", "_import_date": "2025-06-25T18:13:51.062004", "_row_index": 6}, {"定额编号": "1-23", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_62091b57.csv", "_import_date": "2025-06-25T18:13:51.062133", "_row_index": 7}, {"定额编号": "1-24", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.073, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_62091b57.csv", "_import_date": "2025-06-25T18:13:51.062245", "_row_index": 8}, {"定额编号": "1-24", "资源编号": 99010002, "类别": "机械", "子项名称": "反铲挖掘机(带液压锤)", "单位": "台班", "消耗量": 0.0182, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_62091b57.csv", "_import_date": "2025-06-25T18:13:51.062366", "_row_index": 9}, {"定额编号": "1-24", "资源编号": 9907000703, "类别": "机械", "子项名称": "轮胎式装载机3m³", "单位": "台班", "消耗量": 0.0061, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_62091b57.csv", "_import_date": "2025-06-25T18:13:51.062477", "_row_index": 10}, {"定额编号": "1-24", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_62091b57.csv", "_import_date": "2025-06-25T18:13:51.062585", "_row_index": 11}, {"定额编号": "1-25", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.08, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_62091b57.csv", "_import_date": "2025-06-25T18:13:51.062695", "_row_index": 12}, {"定额编号": "1-25", "资源编号": 99010002, "类别": "机械", "子项名称": "反铲挖掘机(带液压锤)", "单位": "台班", "消耗量": 0.02, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_62091b57.csv", "_import_date": "2025-06-25T18:13:51.062800", "_row_index": 13}, {"定额编号": "1-25", "资源编号": 9907000703, "类别": "机械", "子项名称": "轮胎式装载机3m³", "单位": "台班", "消耗量": 0.0067, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_62091b57.csv", "_import_date": "2025-06-25T18:13:51.062905", "_row_index": 14}, {"定额编号": "1-25", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_62091b57.csv", "_import_date": "2025-06-25T18:13:51.063011", "_row_index": 15}]}, "child_resources_397a9ab9": {"metadata": {"source_file": "child_resources_397a9ab9.csv", "document_count": 18, "fields": ["定额编号", "资源编号", "类别", "子项名称", "单位", "消耗量", "单价", "合价"], "collection_name": "child_resources_397a9ab9"}, "documents": [{"定额编号": "1-1", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.2, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_397a9ab9.csv", "_import_date": "2025-06-25T18:13:51.077285", "_row_index": 0}, {"定额编号": "1-1", "资源编号": 99010002, "类别": "机械", "子项名称": "反铲挖掘机（带液压锤）3m³", "单位": "台班", "消耗量": 0.525, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_397a9ab9.csv", "_import_date": "2025-06-25T18:13:51.077445", "_row_index": 1}, {"定额编号": "1-1", "资源编号": 9907000703, "类别": "机械", "子项名称": "轮胎式装载机 6m³/min", "单位": "台班", "消耗量": 0.017, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_397a9ab9.csv", "_import_date": "2025-06-25T18:13:51.077573", "_row_index": 2}, {"定额编号": "1-1", "资源编号": 9943000205, "类别": "机械", "子项名称": "空压机 6m³/min", "单位": "台班", "消耗量": 0.0057, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_397a9ab9.csv", "_import_date": "2025-06-25T18:13:51.077691", "_row_index": 3}, {"定额编号": "1-1", "资源编号": 99430001, "类别": "机械", "子项名称": "风镐", "单位": "台班", "消耗量": 0.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_397a9ab9.csv", "_import_date": "2025-06-25T18:13:51.077802", "_row_index": 4}, {"定额编号": "1-1", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_397a9ab9.csv", "_import_date": "2025-06-25T18:13:51.077913", "_row_index": 5}, {"定额编号": "1-2", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.2, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_397a9ab9.csv", "_import_date": "2025-06-25T18:13:51.078045", "_row_index": 6}, {"定额编号": "1-2", "资源编号": 99010002, "类别": "机械", "子项名称": "反铲挖掘机（带液压锤）3m³", "单位": "台班", "消耗量": 0.525, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_397a9ab9.csv", "_import_date": "2025-06-25T18:13:51.078155", "_row_index": 7}, {"定额编号": "1-2", "资源编号": 9907000703, "类别": "机械", "子项名称": "轮胎式装载机 6m³/min", "单位": "台班", "消耗量": 0.017, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_397a9ab9.csv", "_import_date": "2025-06-25T18:13:51.078263", "_row_index": 8}, {"定额编号": "1-2", "资源编号": 9943000205, "类别": "机械", "子项名称": "空压机 6m³/min", "单位": "台班", "消耗量": 0.0057, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_397a9ab9.csv", "_import_date": "2025-06-25T18:13:51.078911", "_row_index": 9}, {"定额编号": "1-2", "资源编号": 99430001, "类别": "机械", "子项名称": "风镐", "单位": "台班", "消耗量": 0.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_397a9ab9.csv", "_import_date": "2025-06-25T18:13:51.079083", "_row_index": 10}, {"定额编号": "1-2", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_397a9ab9.csv", "_import_date": "2025-06-25T18:13:51.079333", "_row_index": 11}, {"定额编号": "1-3", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.2, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_397a9ab9.csv", "_import_date": "2025-06-25T18:13:51.079517", "_row_index": 12}, {"定额编号": "1-3", "资源编号": 99010002, "类别": "机械", "子项名称": "反铲挖掘机（带液压锤）3m³", "单位": "台班", "消耗量": 0.525, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_397a9ab9.csv", "_import_date": "2025-06-25T18:13:51.079656", "_row_index": 13}, {"定额编号": "1-3", "资源编号": 9907000703, "类别": "机械", "子项名称": "轮胎式装载机 6m³/min", "单位": "台班", "消耗量": 0.017, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_397a9ab9.csv", "_import_date": "2025-06-25T18:13:51.079783", "_row_index": 14}, {"定额编号": "1-3", "资源编号": 9943000205, "类别": "机械", "子项名称": "空压机 6m³/min", "单位": "台班", "消耗量": 0.0057, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_397a9ab9.csv", "_import_date": "2025-06-25T18:13:51.080027", "_row_index": 15}, {"定额编号": "1-3", "资源编号": 99430001, "类别": "机械", "子项名称": "风镐", "单位": "台班", "消耗量": 0.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_397a9ab9.csv", "_import_date": "2025-06-25T18:13:51.080306", "_row_index": 16}, {"定额编号": "1-3", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_397a9ab9.csv", "_import_date": "2025-06-25T18:13:51.081502", "_row_index": 17}]}, "child_resources_0e9d2977": {"metadata": {"source_file": "child_resources_0e9d2977.csv", "document_count": 3, "fields": ["定额编号", "资源编号", "类别", "子项名称", "单位", "消耗量", "单价", "合价"], "collection_name": "child_resources_0e9d2977"}, "documents": [{"定额编号": "1-1", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日/m³", "消耗量": 0.187, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_0e9d2977.csv", "_import_date": "2025-06-25T18:13:51.094596", "_row_index": 0}, {"定额编号": "1-1", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钎机", "单位": "台班/m³", "消耗量": 0.0039, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_0e9d2977.csv", "_import_date": "2025-06-25T18:13:51.094980", "_row_index": 1}, {"定额编号": "1-1", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_0e9d2977.csv", "_import_date": "2025-06-25T18:13:51.095278", "_row_index": 2}]}, "child_resources_0f292a4f": {"metadata": {"source_file": "child_resources_0f292a4f.csv", "document_count": 9, "fields": ["定额编号", "资源编号", "类别", "子项名称", "单位", "消耗量", "单价", "合价"], "collection_name": "child_resources_0f292a4f"}, "documents": [{"定额编号": "1-1", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.187, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_0f292a4f.csv", "_import_date": "2025-06-25T18:13:51.110324", "_row_index": 0}, {"定额编号": "1-1", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钎机", "单位": "台班", "消耗量": 0.0039, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_0f292a4f.csv", "_import_date": "2025-06-25T18:13:51.110527", "_row_index": 1}, {"定额编号": "1-1", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_0f292a4f.csv", "_import_date": "2025-06-25T18:13:51.110674", "_row_index": 2}, {"定额编号": "1-2", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.274, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_0f292a4f.csv", "_import_date": "2025-06-25T18:13:51.110805", "_row_index": 3}, {"定额编号": "1-2", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钎机", "单位": "台班", "消耗量": 0.0048, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_0f292a4f.csv", "_import_date": "2025-06-25T18:13:51.110932", "_row_index": 4}, {"定额编号": "1-2", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_0f292a4f.csv", "_import_date": "2025-06-25T18:13:51.111056", "_row_index": 5}, {"定额编号": "1-3", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.361, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_0f292a4f.csv", "_import_date": "2025-06-25T18:13:51.111180", "_row_index": 6}, {"定额编号": "1-3", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钎机", "单位": "台班", "消耗量": 0.0056, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_0f292a4f.csv", "_import_date": "2025-06-25T18:13:51.111301", "_row_index": 7}, {"定额编号": "1-3", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_0f292a4f.csv", "_import_date": "2025-06-25T18:13:51.111671", "_row_index": 8}]}, "child_resources_43a75417": {"metadata": {"source_file": "child_resources_43a75417.csv", "document_count": 21, "fields": ["定额编号", "资源编号", "类别", "子项名称", "单位", "消耗量", "单价", "合价"], "collection_name": "child_resources_43a75417"}, "documents": [{"定额编号": "1-26", "资源编号": "00010701", "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.01, "单价": 120.0, "合价": 1.2, "_source_file": "child_resources_43a75417.csv", "_import_date": "2025-06-25T18:13:51.128543", "_row_index": 0}, {"定额编号": "1-26", "资源编号": "9907000503", "类别": "机械", "子项名称": "履带式推土机105kW", "单位": "台班", "消耗量": 0.0013, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_43a75417.csv", "_import_date": "2025-06-25T18:13:51.128724", "_row_index": 1}, {"定额编号": "1-26", "资源编号": "99130002", "类别": "机械", "子项名称": "光轮压路机(综合)", "单位": "台班", "消耗量": 0.0057, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_43a75417.csv", "_import_date": "2025-06-25T18:13:51.128891", "_row_index": 2}, {"定额编号": "1-26", "资源编号": "99130001", "类别": "机械", "子项名称": "振动压路机(综合)", "单位": "台班", "消耗量": 0.0013, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_43a75417.csv", "_import_date": "2025-06-25T18:13:51.128980", "_row_index": 3}, {"定额编号": "1-26", "资源编号": "9931000002", "类别": "机械", "子项名称": "洒水车8t", "单位": "台班", "消耗量": 0.0008, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_43a75417.csv", "_import_date": "2025-06-25T18:13:51.129056", "_row_index": 4}, {"定额编号": "1-26", "资源编号": "99460004", "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 1.2, "合价": 0.018, "_source_file": "child_resources_43a75417.csv", "_import_date": "2025-06-25T18:13:51.129245", "_row_index": 5}, {"定额编号": "1-27", "资源编号": "00010701", "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.011, "单价": 120.0, "合价": 1.3199999999999998, "_source_file": "child_resources_43a75417.csv", "_import_date": "2025-06-25T18:13:51.129423", "_row_index": 6}, {"定额编号": "1-27", "资源编号": "04050011-2", "类别": "材料", "子项名称": "级配砂石", "单位": "kg", "消耗量": 2315.4, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_43a75417.csv", "_import_date": "2025-06-25T18:13:51.129533", "_row_index": 7}, {"定额编号": "1-27", "资源编号": "34000011", "类别": "材料", "子项名称": "其他材料费占材料费", "单位": "%", "消耗量": 1.0, "单价": 1.3199999999999998, "合价": 0.0131999999999999, "_source_file": "child_resources_43a75417.csv", "_import_date": "2025-06-25T18:13:51.129623", "_row_index": 8}, {"定额编号": "1-27", "资源编号": "9907000503", "类别": "机械", "子项名称": "履带式推土机105kW", "单位": "台班", "消耗量": 0.004, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_43a75417.csv", "_import_date": "2025-06-25T18:13:51.129703", "_row_index": 9}, {"定额编号": "1-27", "资源编号": "99130004", "类别": "机械", "子项名称": "压路机综合", "单位": "台班", "消耗量": 0.0018, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_43a75417.csv", "_import_date": "2025-06-25T18:13:51.129782", "_row_index": 10}, {"定额编号": "1-27", "资源编号": "9913000301", "类别": "机械", "子项名称": "平地机HP90", "单位": "台班", "消耗量": 0.005, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_43a75417.csv", "_import_date": "2025-06-25T18:13:51.129870", "_row_index": 11}, {"定额编号": "1-27", "资源编号": "99460004", "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 1.3199999999999998, "合价": 0.0197999999999999, "_source_file": "child_resources_43a75417.csv", "_import_date": "2025-06-25T18:13:51.129951", "_row_index": 12}, {"定额编号": "1-28", "资源编号": "00010701", "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.013, "单价": 120.0, "合价": 1.5599999999999998, "_source_file": "child_resources_43a75417.csv", "_import_date": "2025-06-25T18:13:51.130033", "_row_index": 13}, {"定额编号": "1-28", "资源编号": "04090031", "类别": "材料", "子项名称": "石灰粉煤灰碎石", "单位": "t", "消耗量": 2.3154, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_43a75417.csv", "_import_date": "2025-06-25T18:13:51.130114", "_row_index": 14}, {"定额编号": "1-28", "资源编号": "34000011", "类别": "材料", "子项名称": "其他材料费占材料费", "单位": "%", "消耗量": 1.0, "单价": 1.5599999999999998, "合价": 0.0155999999999999, "_source_file": "child_resources_43a75417.csv", "_import_date": "2025-06-25T18:13:51.130197", "_row_index": 15}, {"定额编号": "1-28", "资源编号": "9907000503", "类别": "机械", "子项名称": "履带式推土机105kW", "单位": "台班", "消耗量": 0.004, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_43a75417.csv", "_import_date": "2025-06-25T18:13:51.130283", "_row_index": 16}, {"定额编号": "1-28", "资源编号": "99130004", "类别": "机械", "子项名称": "压路机综合", "单位": "台班", "消耗量": 0.014, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_43a75417.csv", "_import_date": "2025-06-25T18:13:51.130371", "_row_index": 17}, {"定额编号": "1-28", "资源编号": "9913000301", "类别": "机械", "子项名称": "平地机HP90", "单位": "台班", "消耗量": 0.005, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_43a75417.csv", "_import_date": "2025-06-25T18:13:51.130450", "_row_index": 18}, {"定额编号": "1-28", "资源编号": "9931000002", "类别": "机械", "子项名称": "洒水车8t", "单位": "台班", "消耗量": 0.003, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_43a75417.csv", "_import_date": "2025-06-25T18:13:51.130528", "_row_index": 19}, {"定额编号": "1-28", "资源编号": "99460004", "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 1.5599999999999998, "合价": 0.0233999999999999, "_source_file": "child_resources_43a75417.csv", "_import_date": "2025-06-25T18:13:51.130612", "_row_index": 20}]}, "child_resources_6f4a5e48": {"metadata": {"source_file": "child_resources_6f4a5e48.csv", "document_count": 3, "fields": ["定额编号", "资源编号", "类别", "子项名称", "单位", "消耗量", "单价", "合价"], "collection_name": "child_resources_6f4a5e48"}, "documents": [{"定额编号": "04-01-1-6", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日/m³", "消耗量": 0.182, "单价": 120.0, "合价": 21.84, "_source_file": "child_resources_6f4a5e48.csv", "_import_date": "2025-06-25T18:13:51.147414", "_row_index": 0}, {"定额编号": "04-01-1-6", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钉机", "单位": "台班/m³", "消耗量": 0.0039, "单价": 50.0, "合价": 0.1949999999999999, "_source_file": "child_resources_6f4a5e48.csv", "_import_date": "2025-06-25T18:13:51.147530", "_row_index": 1}, {"定额编号": "04-01-1-6", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 22.035, "合价": 0.330525, "_source_file": "child_resources_6f4a5e48.csv", "_import_date": "2025-06-25T18:13:51.147615", "_row_index": 2}]}, "child_resources_97a67801": {"metadata": {"source_file": "child_resources_97a67801.csv", "document_count": 34, "fields": ["定额编号", "资源编号", "类别", "子项名称", "单位", "消耗量", "单价", "合价"], "collection_name": "child_resources_97a67801"}, "documents": [{"定额编号": "1-4", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.009, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.161345", "_row_index": 0}, {"定额编号": "1-4", "资源编号": 9901000016, "类别": "机械", "子项名称": "履带式单斗液压挖掘机1.6m³", "单位": "台班", "消耗量": 0.0018, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.161584", "_row_index": 1}, {"定额编号": "1-4", "资源编号": 9907000703, "类别": "机械", "子项名称": "轮胎式装载机3m³", "单位": "台班", "消耗量": 0.0006, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.162093", "_row_index": 2}, {"定额编号": "1-4", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.162392", "_row_index": 3}, {"定额编号": "1-5", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.01, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.162658", "_row_index": 4}, {"定额编号": "1-5", "资源编号": 9901000016, "类别": "机械", "子项名称": "履带式单斗液压挖掘机1.6m³", "单位": "台班", "消耗量": 0.002, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.162862", "_row_index": 5}, {"定额编号": "1-5", "资源编号": 9907000703, "类别": "机械", "子项名称": "轮胎式装载机3m³", "单位": "台班", "消耗量": 0.0007, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.163009", "_row_index": 6}, {"定额编号": "1-5", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.163172", "_row_index": 7}, {"定额编号": "1-6", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.182, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.163831", "_row_index": 8}, {"定额编号": "1-6", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钎机", "单位": "台班", "消耗量": 0.0039, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.164117", "_row_index": 9}, {"定额编号": "1-6", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.164339", "_row_index": 10}, {"定额编号": "1-7", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.25, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.164515", "_row_index": 11}, {"定额编号": "1-7", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钎机", "单位": "台班", "消耗量": 0.0048, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.164964", "_row_index": 12}, {"定额编号": "1-7", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.165087", "_row_index": 13}, {"定额编号": "1-8", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.4, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.165191", "_row_index": 14}, {"定额编号": "1-8", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钎机", "单位": "台班", "消耗量": 0.0056, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.165274", "_row_index": 15}, {"定额编号": "1-8", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.165348", "_row_index": 16}, {"定额编号": "1-9", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.013, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.165432", "_row_index": 17}, {"定额编号": "1-9", "资源编号": 9901000016, "类别": "机械", "子项名称": "履带式单斗液压挖掘机1.6m³", "单位": "台班", "消耗量": 0.025, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.165502", "_row_index": 18}, {"定额编号": "1-9", "资源编号": 9907000703, "类别": "机械", "子项名称": "轮胎式装载机3m³", "单位": "台班", "消耗量": 0.0008, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.165571", "_row_index": 19}, {"定额编号": "1-9", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.165639", "_row_index": 20}, {"定额编号": "1-10", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.015, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.165709", "_row_index": 21}, {"定额编号": "1-10", "资源编号": 9901000016, "类别": "机械", "子项名称": "履带式单斗液压挖掘机1.6m³", "单位": "台班", "消耗量": 0.028, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.165786", "_row_index": 22}, {"定额编号": "1-10", "资源编号": 9907000703, "类别": "机械", "子项名称": "轮胎式装载机3m³", "单位": "台班", "消耗量": 0.0009, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.165857", "_row_index": 23}, {"定额编号": "1-10", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.165928", "_row_index": 24}, {"定额编号": "1-11", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.2, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.166002", "_row_index": 25}, {"定额编号": "1-11", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钎机", "单位": "台班", "消耗量": 0.0039, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.166075", "_row_index": 26}, {"定额编号": "1-11", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.166195", "_row_index": 27}, {"定额编号": "1-12", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.25, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.166298", "_row_index": 28}, {"定额编号": "1-12", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钎机", "单位": "台班", "消耗量": 0.0048, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.166405", "_row_index": 29}, {"定额编号": "1-12", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.166503", "_row_index": 30}, {"定额编号": "1-13", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.166579", "_row_index": 31}, {"定额编号": "1-13", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钎机", "单位": "台班", "消耗量": 0.0056, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.166650", "_row_index": 32}, {"定额编号": "1-13", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_97a67801.csv", "_import_date": "2025-06-25T18:13:51.166716", "_row_index": 33}]}, "child_resources_c7826334": {"metadata": {"source_file": "child_resources_c7826334.csv", "document_count": 35, "fields": ["定额编号", "资源编号", "类别", "子项名称", "单位", "消耗量", "单价", "合价"], "collection_name": "child_resources_c7826334"}, "documents": [{"定额编号": "1-43", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.01, "单价": 120.0, "合价": 1.2, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.181312", "_row_index": 0}, {"定额编号": "1-43", "资源编号": 34000009, "类别": "材料", "子项名称": "弃土或渣土消纳", "单位": "m³", "消耗量": 1.0, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.181802", "_row_index": 1}, {"定额编号": "1-43", "资源编号": 9901000016, "类别": "机械", "子项名称": "履带式单斗液压挖掘机1.6m³", "单位": "台班", "消耗量": 0.0017, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.181989", "_row_index": 2}, {"定额编号": "1-43", "资源编号": 9907000703, "类别": "机械", "子项名称": "轮胎式装载机3m³", "单位": "台班", "消耗量": 0.0006, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.182262", "_row_index": 3}, {"定额编号": "1-43", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 1.2, "合价": 0.018, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.182420", "_row_index": 4}, {"定额编号": "1-44", "资源编号": 9907000012, "类别": "机械", "子项名称": "自卸汽车12t", "单位": "台班", "消耗量": 0.0041, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.182580", "_row_index": 5}, {"定额编号": "1-44", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.182766", "_row_index": 6}, {"定额编号": "1-45", "资源编号": 9907000012, "类别": "机械", "子项名称": "自卸汽车12t", "单位": "台班", "消耗量": 0.0018, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.182917", "_row_index": 7}, {"定额编号": "1-46", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.016, "单价": 120.0, "合价": 1.92, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.183060", "_row_index": 8}, {"定额编号": "1-46", "资源编号": 34000009, "类别": "材料", "子项名称": "弃土或渣土消纳", "单位": "m³", "消耗量": 1.0, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.183199", "_row_index": 9}, {"定额编号": "1-46", "资源编号": 9901000016, "类别": "机械", "子项名称": "履带式单斗液压挖掘机1.6m³", "单位": "台班", "消耗量": 0.0041, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.183352", "_row_index": 10}, {"定额编号": "1-46", "资源编号": 9907000703, "类别": "机械", "子项名称": "轮胎式装载机3m³", "单位": "台班", "消耗量": 0.0006, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.183514", "_row_index": 11}, {"定额编号": "1-46", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 1.92, "合价": 0.0288, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.183670", "_row_index": 12}, {"定额编号": "1-47", "资源编号": 9907000012, "类别": "机械", "子项名称": "自卸汽车12t", "单位": "台班", "消耗量": 0.0057, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.183815", "_row_index": 13}, {"定额编号": "1-47", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.183927", "_row_index": 14}, {"定额编号": "1-48", "资源编号": 9907000012, "类别": "机械", "子项名称": "自卸汽车12t", "单位": "台班", "消耗量": 0.0025, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.184039", "_row_index": 15}, {"定额编号": "1-49", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.022, "单价": 120.0, "合价": 2.64, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.184153", "_row_index": 16}, {"定额编号": "1-49", "资源编号": 9944000006, "类别": "机械", "子项名称": "泥浆泵φ100mm", "单位": "台班", "消耗量": 0.0135, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.184285", "_row_index": 17}, {"定额编号": "1-49", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 2.64, "合价": 0.0395999999999999, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.184429", "_row_index": 18}, {"定额编号": "1-50", "资源编号": 34000009, "类别": "材料", "子项名称": "弃土或渣土消纳", "单位": "m³", "消耗量": 1.0, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.184548", "_row_index": 19}, {"定额编号": "1-50", "资源编号": 9907000109, "类别": "机械", "子项名称": "泥浆罐车5000L", "单位": "台班", "消耗量": 0.005, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.184688", "_row_index": 20}, {"定额编号": "1-51", "资源编号": 9907000109, "类别": "机械", "子项名称": "泥浆罐车5000L", "单位": "台班", "消耗量": 0.0016, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.184827", "_row_index": 21}, {"定额编号": "1-52", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.011, "单价": 120.0, "合价": 1.3199999999999998, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.184945", "_row_index": 22}, {"定额编号": "1-52", "资源编号": 9907000102, "类别": "机械", "子项名称": "轮胎式装载机1.5m³", "单位": "台班", "消耗量": 0.0037, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.185077", "_row_index": 23}, {"定额编号": "1-52", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 1.3199999999999998, "合价": 0.0197999999999999, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.185190", "_row_index": 24}, {"定额编号": "1-53", "资源编号": 34000009, "类别": "材料", "子项名称": "弃土或渣土消纳", "单位": "m³", "消耗量": 1.0, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.185306", "_row_index": 25}, {"定额编号": "1-53", "资源编号": 9907000012, "类别": "机械", "子项名称": "自卸汽车12t", "单位": "台班", "消耗量": 0.0088, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.185421", "_row_index": 26}, {"定额编号": "1-54", "资源编号": 9907000012, "类别": "机械", "子项名称": "自卸汽车12t", "单位": "台班", "消耗量": 0.0025, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.185540", "_row_index": 27}, {"定额编号": "1-55", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.011, "单价": 120.0, "合价": 1.3199999999999998, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.185664", "_row_index": 28}, {"定额编号": "1-55", "资源编号": 9907000703, "类别": "机械", "子项名称": "轮胎式装载机3m³", "单位": "台班", "消耗量": 0.0008, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.185778", "_row_index": 29}, {"定额编号": "1-55", "资源编号": 9901000016, "类别": "机械", "子项名称": "履带式单斗液压挖掘机1.6m³", "单位": "台班", "消耗量": 0.0024, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.185887", "_row_index": 30}, {"定额编号": "1-55", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 1.3199999999999998, "合价": 0.0197999999999999, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.186002", "_row_index": 31}, {"定额编号": "1-56", "资源编号": 34000009, "类别": "材料", "子项名称": "弃土或渣土消纳", "单位": "m³", "消耗量": 1.0, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.186114", "_row_index": 32}, {"定额编号": "1-56", "资源编号": 9907000012, "类别": "机械", "子项名称": "自卸汽车12t", "单位": "台班", "消耗量": 0.0057, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.186236", "_row_index": 33}, {"定额编号": "1-57", "资源编号": 9907000012, "类别": "机械", "子项名称": "自卸汽车12t", "单位": "台班", "消耗量": 0.0025, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_c7826334.csv", "_import_date": "2025-06-25T18:13:51.186345", "_row_index": 34}]}, "child_resources_ebe3d96a": {"metadata": {"source_file": "child_resources_ebe3d96a.csv", "document_count": 9, "fields": ["定额编号", "资源编号", "类别", "子项名称", "单位", "消耗量", "单价", "合价"], "collection_name": "child_resources_ebe3d96a"}, "documents": [{"定额编号": "1-1", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.187, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_ebe3d96a.csv", "_import_date": "2025-06-25T18:13:51.199413", "_row_index": 0}, {"定额编号": "1-1", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打桩机", "单位": "台班", "消耗量": 0.0039, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_ebe3d96a.csv", "_import_date": "2025-06-25T18:13:51.199614", "_row_index": 1}, {"定额编号": "1-1", "资源编号": 99460004, "类别": "机械", "子项名称": "其他机具费 占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_ebe3d96a.csv", "_import_date": "2025-06-25T18:13:51.200291", "_row_index": 2}, {"定额编号": "1-2", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.274, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_ebe3d96a.csv", "_import_date": "2025-06-25T18:13:51.200509", "_row_index": 3}, {"定额编号": "1-2", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打桩机", "单位": "台班", "消耗量": 0.0048, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_ebe3d96a.csv", "_import_date": "2025-06-25T18:13:51.200830", "_row_index": 4}, {"定额编号": "1-2", "资源编号": 99460004, "类别": "机械", "子项名称": "其他机具费 占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_ebe3d96a.csv", "_import_date": "2025-06-25T18:13:51.201046", "_row_index": 5}, {"定额编号": "1-3", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.361, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_ebe3d96a.csv", "_import_date": "2025-06-25T18:13:51.201221", "_row_index": 6}, {"定额编号": "1-3", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打桩机", "单位": "台班", "消耗量": 0.0056, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_ebe3d96a.csv", "_import_date": "2025-06-25T18:13:51.201393", "_row_index": 7}, {"定额编号": "1-3", "资源编号": 99460004, "类别": "机械", "子项名称": "其他机具费 占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_ebe3d96a.csv", "_import_date": "2025-06-25T18:13:51.201657", "_row_index": 8}]}, "child_resources_mongodb_test": {"metadata": {"source_file": "child_resources_mongodb_test.csv", "document_count": 7, "fields": ["定额编号", "资源编号", "资源名称", "资源类型", "数量", "单位", "单价", "合价"], "collection_name": "child_resources_mongodb_test"}, "documents": [{"定额编号": "001-001", "资源编号": "R001", "资源名称": "C30混凝土", "资源类型": "材料", "数量": 1.05, "单位": "m³", "单价": 420.0, "合价": 441.0, "_source_file": "child_resources_mongodb_test.csv", "_import_date": "2025-06-25T18:13:51.215283", "_row_index": 0}, {"定额编号": "001-001", "资源编号": "R002", "资源名称": "人工", "资源类型": "人工", "数量": 8.5, "单位": "工日", "单价": 150.0, "合价": 1275.0, "_source_file": "child_resources_mongodb_test.csv", "_import_date": "2025-06-25T18:13:51.215391", "_row_index": 1}, {"定额编号": "001-001", "资源编号": "R003", "资源名称": "机械台班", "资源类型": "机械", "数量": 2.3, "单位": "台班", "单价": 350.0, "合价": 805.0, "_source_file": "child_resources_mongodb_test.csv", "_import_date": "2025-06-25T18:13:51.215481", "_row_index": 2}, {"定额编号": "001-002", "资源编号": "R004", "资源名称": "HPB300钢筋", "资源类型": "材料", "数量": 1.02, "单位": "kg", "单价": 4.2, "合价": 4.284, "_source_file": "child_resources_mongodb_test.csv", "_import_date": "2025-06-25T18:13:51.215602", "_row_index": 3}, {"定额编号": "001-002", "资源编号": "R005", "资源名称": "人工", "资源类型": "人工", "数量": 12.8, "单位": "工日", "单价": 150.0, "合价": 1920.0, "_source_file": "child_resources_mongodb_test.csv", "_import_date": "2025-06-25T18:13:51.215751", "_row_index": 4}, {"定额编号": "002-001", "资源编号": "R006", "资源名称": "MU10砖", "资源类型": "材料", "数量": 0.98, "单位": "m³", "单价": 280.0, "合价": 274.4, "_source_file": "child_resources_mongodb_test.csv", "_import_date": "2025-06-25T18:13:51.215856", "_row_index": 5}, {"定额编号": "002-001", "资源编号": "R007", "资源名称": "砂浆", "资源类型": "材料", "数量": 0.25, "单位": "m³", "单价": 180.0, "合价": 45.0, "_source_file": "child_resources_mongodb_test.csv", "_import_date": "2025-06-25T18:13:51.216002", "_row_index": 6}]}}}