-- POSTGRESQL SQL Script
-- Generated on: 2025-06-25 23:14:55
-- Source files: 48 CSV files

-- Table: parent_quotas_01bdaa91
-- Source: parent_quotas_01bdaa91.csv
-- Rows: 3

CREATE TABLE parent_quotas_01bdaa91 (
    col___ VARCHAR(50) NOT NULL,
    col______ VARCHAR(50) NOT NULL,
    col_____ VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col___________ DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col___, col______)
);

INSERT INTO parent_quotas_01bdaa91 (col___, col______, col_____, col____1, col___________)
VALUES
    ('1-1', '人工挖一般土方 一、二类土', '挖土、余土清理、修整底边、打钉拍底等', 'm³', 0.0),
    ('1-2', '人工挖一般土方 三类土', '挖土、余土清理、修整底边、打钉拍底等', 'm³', 0.0),
    ('1-3', '人工挖一般土方 四类土', '挖土、余土清理、修整底边、打钉拍底等', 'm³', 0.0);

-- Table: parent_quotas_2660c31e
-- Source: parent_quotas_2660c31e.csv
-- Rows: 3

CREATE TABLE parent_quotas_2660c31e (
    col___ VARCHAR(50) NOT NULL,
    col______ VARCHAR(50) NOT NULL,
    col_____ VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col___________ DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col___, col______)
);

INSERT INTO parent_quotas_2660c31e (col___, col______, col_____, col____1, col___________)
VALUES
    ('1-26', '路基回填 土', '回填、找平、碾压等。', 'm³', 1.218),
    ('1-27', '路基回填 级配砂石', '回填、找平、碾压等。', 'm³', 1.353),
    ('1-28', '路基回填 石灰、粉煤灰碎石', '回填、找平、碾压等。', 'm³', 1.599);

-- Table: parent_quotas_3a5e7949
-- Source: parent_quotas_3a5e7949.csv
-- Rows: 3

CREATE TABLE parent_quotas_3a5e7949 (
    col___ VARCHAR(50) NOT NULL,
    col______ VARCHAR(50) NOT NULL,
    col_____ VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col___________ DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col___)
);

INSERT INTO parent_quotas_3a5e7949 (col___, col______, col_____, col____1, col___________)
VALUES
    ('1-1', '人工挖一般土方', '挖土、余土清理、修整底边、打钎拍底等。', 'm³', 0.0),
    ('1-2', '人工挖一般土方', '挖土、余土清理、修整底边、打钎拍底等。', 'm³', 0.0),
    ('1-3', '人工挖一般土方', '挖土、余土清理、修整底边、打钎拍底等。', 'm³', 0.0);

-- Table: parent_quotas_72603690
-- Source: parent_quotas_72603690.csv
-- Rows: 1

CREATE TABLE parent_quotas_72603690 (
    col___ VARCHAR(50) NOT NULL,
    col______ VARCHAR(50) NOT NULL,
    col_____ VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col___________ DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col___, col______, col_____, col____1)
);

INSERT INTO parent_quotas_72603690 (col___, col______, col_____, col____1, col___________)
VALUES
    ('1-1', '人工挖一般土方 一、二类土', '挖土、余土清理、修整底边、打钎拍底等。', 'm³', 0.0);

-- Table: parent_quotas_7f374c69
-- Source: parent_quotas_7f374c69.csv
-- Rows: 1

CREATE TABLE parent_quotas_7f374c69 (
    col___ VARCHAR(50) NOT NULL,
    col______ VARCHAR(50) NOT NULL,
    col_____ VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col___________ DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col___, col______, col_____, col____1)
);

INSERT INTO parent_quotas_7f374c69 (col___, col______, col_____, col____1, col___________)
VALUES
    ('04-01-1-6', '人工挖沟槽土方 一、二类土', '挖土、余土清理、修整底边、打钉拍底等', 'm³', 22.365525);

-- Table: parent_quotas_9c7d7fd2
-- Source: parent_quotas_9c7d7fd2.csv
-- Rows: 3

CREATE TABLE parent_quotas_9c7d7fd2 (
    col___ VARCHAR(50) NOT NULL,
    col______ VARCHAR(50) NOT NULL,
    col_____ VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col___________ DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col___, col______)
);

INSERT INTO parent_quotas_9c7d7fd2 (col___, col______, col_____, col____1, col___________)
VALUES
    ('1-1', '人工挖一般土方 一、二类土', '挖土、余土清理、修整底边、打钎拍底等。', 'm³', 0.0),
    ('1-2', '人工挖一般土方 三类土', '挖土、余土清理、修整底边、打钎拍底等。', 'm³', 0.0),
    ('1-3', '人工挖一般土方 四类土', '挖土、余土清理、修整底边、打钎拍底等。', 'm³', 0.0);

-- Table: parent_quotas_f98f4a2f
-- Source: parent_quotas_f98f4a2f.csv
-- Rows: 8

CREATE TABLE parent_quotas_f98f4a2f (
    col___ VARCHAR(50) NOT NULL,
    col______ VARCHAR(50) NOT NULL,
    col_____ VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col___________ DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col___)
);

INSERT INTO parent_quotas_f98f4a2f (col___, col______, col_____, col____1, col___________)
VALUES
    ('1-14', '机械挖基坑土方 深5m以内', '挖土、甩土或装土、清理机下余土,修理边坡等。', 'm³', 0.0),
    ('1-15', '机械挖基坑土方 深5m以外', '挖土、甩土或装土、清理机下余土,修理边坡等。', 'm³', 0.0),
    ('1-16', '人工挖淤泥、流砂', '人工挖淤泥、流砂、清理边坡、运输至地面堆放等。', 'm³', 0.0),
    ('1-17', '机械挖淤泥、流砂', '机械挖淤泥、流砂、清理边坡和机下余土、运输至地面堆放等。', 'm³', 0.0),
    ('1-18', '箱涵顶进挖土 人工挖土', '安拆挖土支架；铺钢轨、挖土、箱涵内运土、出坑、堆放、清理等。', 'm³', 0.0),
    ('1-19', '箱涵顶进挖土 人工挖土', '安拆挖土支架；铺钢轨、挖土、箱涵内运土、出坑、堆放、清理等。', 'm³', 0.0),
    ('1-20', '箱涵顶进挖土 机械挖土', '挖土、箱涵内运土、出坑、堆放、清理等。', 'm³', 0.0),
    ('1-21', '挖桩间土', '挖土、甩土或装土、清理机下余土等。', 'm³', 0.0);

-- Table: parent_quotas_pg_test
-- Source: parent_quotas_pg_test.csv
-- Rows: 3

CREATE TABLE parent_quotas_pg_test (
    col_____ VARCHAR(50) NOT NULL,
    col______1 VARCHAR(50) NOT NULL,
    col___ VARCHAR(50) NOT NULL,
    col____ DECIMAL(10,2) NOT NULL,
    col_____1 DECIMAL(10,2) NOT NULL,
    col_____2 DECIMAL(10,2) NOT NULL,
    col____1 DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col_____, col______1, col___)
);

INSERT INTO parent_quotas_pg_test (col_____, col______1, col___, col____, col_____1, col_____2, col____1)
VALUES
    ('001-001', '混凝土浇筑C30', 'm³', 120.5, 450.8, 80.3, 651.6),
    ('001-002', '钢筋绑扎HPB300', 'kg', 85.2, 320.5, 45.8, 451.5),
    ('001-003', '模板安装拆除', 'm²', 95.8, 180.2, 25.6, 301.6);

-- Table: parent_quotas_2475f0db
-- Source: parent_quotas_2475f0db.csv
-- Rows: 12

CREATE TABLE parent_quotas_2475f0db (
    col___ VARCHAR(50) NOT NULL,
    col______ VARCHAR(50) NOT NULL,
    col_____ VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col___________ DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col___, col______)
);

INSERT INTO parent_quotas_2475f0db (col___, col______, col_____, col____1, col___________)
VALUES
    ('1-43', '土方运输 装车', '装卸土方、淤泥、流砂、清理、运输等。', 'm³', 1.218),
    ('1-44', '土方运输 运距1km以内', '装卸土方、淤泥、流砂、清理、运输等。', 'm³', 1.218),
    ('1-45', '土方运输 每增1km', '装卸土方、淤泥、流砂、清理、运输等。', 'm³', 1.218),
    ('1-46', '淤泥、流砂运输 装车', '装卸土方、淤泥、流砂、清理、运输等。', 'm³', 1.9488),
    ('1-47', '淤泥、流砂运输 运距1km以内', '装卸土方、淤泥、流砂、清理、运输等。', 'm³', 1.9488),
    ('1-48', '淤泥、流砂运输 每增1km', '装卸土方、淤泥、流砂、清理、运输等。', 'm³', 1.9488),
    ('1-49', '泥浆运输 装车', '装卸泥浆、清理、运输等。', 'm³', 2.6796),
    ('1-50', '泥浆运输 运距1km以内', '装卸泥浆、清理、运输等。', 'm³', 0.0),
    ('1-51', '泥浆运输 每增1km', '装卸泥浆、清理、运输等。', 'm³', 0.0),
    ('1-52', '旧路材料等运输 装车', '装卸拆挖后旧路材料、清理、运输等。', 'm³', 1.3398),
    ('1-53', '旧路材料等运输 运距1km以内', '装卸拆挖后旧路材料、清理、运输等。', 'm³', 0.0),
    ('1-54', '旧路材料等运输 每增1km', '装卸拆挖后旧路材料、清理、运输等。', 'm³', 0.0);

-- Table: parent_quotas_2de3313e
-- Source: parent_quotas_2de3313e.csv
-- Rows: 15

CREATE TABLE parent_quotas_2de3313e (
    col___ VARCHAR(50) NOT NULL,
    col______ VARCHAR(50) NOT NULL,
    col_____ VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col___________ DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col___, col______)
);

INSERT INTO parent_quotas_2de3313e (col___, col______, col_____, col____1, col___________)
VALUES
    ('1-43', '土方运输 装车', '装卸土方、淤泥、流砂、清理、运输等。', 'm³', 1.218),
    ('1-44', '土方运输 运距1km以内', '装卸土方、淤泥、流砂、清理、运输等。', 'm³', 0.0),
    ('1-45', '土方运输 每增1km', '装卸土方、淤泥、流砂、清理、运输等。', 'm³', 0.0),
    ('1-46', '淤泥、流砂运输 装车', '装卸土方、淤泥、流砂、清理、运输等。', 'm³', 1.9488),
    ('1-47', '淤泥、流砂运输 运距1km以内', '装卸土方、淤泥、流砂、清理、运输等。', 'm³', 0.0),
    ('1-48', '淤泥、流砂运输 每增1km', '装卸土方、淤泥、流砂、清理、运输等。', 'm³', 0.0),
    ('1-49', '泥浆运输 装车', '装卸泥浆、清理、运输等。', 'm³', 2.6796),
    ('1-50', '泥浆运输 运距1km以内', '装卸泥浆、清理、运输等。', 'm³', 0.0),
    ('1-51', '泥浆运输 每增1km', '装卸泥浆、清理、运输等。', 'm³', 0.0),
    ('1-52', '旧路材料等运输 装车', '装卸拆挖后旧路材料、清理、运输等。', 'm³', 1.3398),
    ('1-53', '旧路材料等运输 运距1km以内', '装卸拆挖后旧路材料、清理、运输等。', 'm³', 0.0),
    ('1-54', '旧路材料等运输 每增1km', '装卸拆挖后旧路材料、清理、运输等。', 'm³', 0.0),
    ('1-55', '石方运输 装车', '装卸石方、清理、运输等。', 'm³', 1.3398),
    ('1-56', '石方运输 运距1km以内', '装卸石方、清理、运输等。', 'm³', 0.0),
    ('1-57', '石方运输 每增1km', '装卸石方、清理、运输等。', 'm³', 0.0);

-- Table: parent_quotas_69534dc4
-- Source: parent_quotas_69534dc4.csv
-- Rows: 4

CREATE TABLE parent_quotas_69534dc4 (
    col___ VARCHAR(50) NOT NULL,
    col______ VARCHAR(50) NOT NULL,
    col_____ VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col___________ DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col___, col______)
);

INSERT INTO parent_quotas_69534dc4 (col___, col______, col_____, col____1, col___________)
VALUES
    ('1-22', '人工凿石方', '凿石、清渣、攒堆、清底修边等。', 'm³', 0.0),
    ('1-23', '机械破碎一般石方', '装卸机头、破碎岩石等。', 'm³', 0.0),
    ('1-24', '机械破碎沟槽石方', '装卸机头、破碎岩石等。', 'm³', 0.0),
    ('1-25', '机械破碎基坑石方', '装卸机头、破碎岩石等。', 'm³', 0.0);

-- Table: parent_quotas_7cb33677
-- Source: parent_quotas_7cb33677.csv
-- Rows: 6

CREATE TABLE parent_quotas_7cb33677 (
    col___ VARCHAR(50) NOT NULL,
    col______ VARCHAR(50) NOT NULL,
    col_____ VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col___________ DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col___)
);

INSERT INTO parent_quotas_7cb33677 (col___, col______, col_____, col____1, col___________)
VALUES
    ('1-1', '人工挖一般土方 一、二类土', '人工挖一般土方 一、二类土', 'm³', 0.0),
    ('1-2', '人工挖一般土方 一、二类土', '人工挖一般土方 一、二类土', 'm³', 0.0),
    ('1-3', '人工挖一般土方 一、二类土', '人工挖一般土方 一、二类土', 'm³', 0.0),
    ('1-11', '人工挖一般土方 三类土', '人工挖一般土方 三类土', 'm³', 0.0),
    ('1-12', '人工挖一般土方 四类土', '人工挖一般土方 四类土', 'm³', 0.0),
    ('1-13', '人工挖一般土方 四类土', '人工挖一般土方 四类土', 'm³', 0.0);

-- Table: parent_quotas_8bc2175e
-- Source: parent_quotas_8bc2175e.csv
-- Rows: 3

CREATE TABLE parent_quotas_8bc2175e (
    col___ VARCHAR(50) NOT NULL,
    col______ VARCHAR(50) NOT NULL,
    col_____ VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col___________ DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col___, col______)
);

INSERT INTO parent_quotas_8bc2175e (col___, col______, col_____, col____1, col___________)
VALUES
    ('1-1', '人工挖一般土方一、二类土', '挖土、余土清理、修整底边等', 'm³', 0.0),
    ('1-2', '人工挖一般土方三类土', '挖土、余土清理、修整底边等', 'm³', 0.0),
    ('1-3', '人工挖一般土方四类土', '挖土、余土清理、修整底边等', 'm³', 0.0);

-- Table: parent_quotas_a701e003
-- Source: parent_quotas_a701e003.csv
-- Rows: 13

CREATE TABLE parent_quotas_a701e003 (
    col___ VARCHAR(50) NOT NULL,
    col______ VARCHAR(50) NOT NULL,
    col_____ VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col___________ DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col___, col______)
);

INSERT INTO parent_quotas_a701e003 (col___, col______, col_____, col____1, col___________)
VALUES
    ('1-1', '人工挖一般土方 类型1', '挖土、余土清理、修整底边、打钉拍底等', 'm³', 0.0),
    ('1-2', '人工挖一般土方 类型2', '挖土、余土清理、修整底边、打钉拍底等', 'm³', 0.0),
    ('1-3', '人工挖一般土方 类型3', '挖土、余土清理、修整底边、打钉拍底等', 'm³', 0.0),
    ('1-4', '人工挖一般土方 类型4', '挖土、余土清理、修整底边、打钉拍底等', 'm³', 0.0),
    ('1-5', '人工挖一般土方 类型5', '挖土、余土清理、修整底边、打钉拍底等', 'm³', 0.0),
    ('1-6', '人工挖一般土方 类型6', '挖土、余土清理、修整底边、打钉拍底等', 'm³', 0.0),
    ('1-7', '人工挖一般土方 类型7', '挖土、余土清理、修整底边、打钉拍底等', 'm³', 0.0),
    ('1-8', '人工挖一般土方 类型8', '挖土、余土清理、修整底边、打钉拍底等', 'm³', 0.0),
    ('1-9', '人工挖一般土方 类型9', '挖土、余土清理、修整底边、打钉拍底等', 'm³', 0.0),
    ('1-10', '人工挖一般土方 类型10', '挖土、余土清理、修整底边、打钉拍底等', 'm³', 0.0),
    ('1-11', '人工挖一般土方 类型11', '挖土、余土清理、修整底边、打钉拍底等', 'm³', 0.0),
    ('1-12', '人工挖一般土方 类型12', '挖土、余土清理、修整底边、打钉拍底等', 'm³', 0.0),
    ('1-13', '人工挖一般土方 类型13', '挖土、余土清理、修整底边、打钉拍底等', 'm³', 0.0);

-- Table: parent_quotas_mcp_test
-- Source: parent_quotas_mcp_test.csv
-- Rows: 5

CREATE TABLE parent_quotas_mcp_test (
    col_____ VARCHAR(50) NOT NULL,
    col______1 VARCHAR(50) NOT NULL,
    col___ VARCHAR(50) NOT NULL,
    col____ DECIMAL(10,2) NOT NULL,
    col_____1 DECIMAL(10,2) NOT NULL,
    col_____2 DECIMAL(10,2) NOT NULL,
    col____1 DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col_____, col______1)
);

INSERT INTO parent_quotas_mcp_test (col_____, col______1, col___, col____, col_____1, col_____2, col____1)
VALUES
    ('001-001', '混凝土浇筑C30', 'm³', 120.5, 450.8, 80.3, 651.6),
    ('001-002', '钢筋绑扎HPB300', 'kg', 85.2, 320.5, 45.8, 451.5),
    ('001-003', '模板安装拆除', 'm²', 95.8, 180.2, 25.6, 301.6),
    ('002-001', '砌砖工程MU10', 'm³', 110.3, 280.9, 35.2, 426.4),
    ('002-002', '抹灰工程', 'm²', 75.6, 120.4, 15.8, 211.8);

-- Table: parent_quotas_test
-- Source: parent_quotas_test.csv
-- Rows: 5

CREATE TABLE parent_quotas_test (
    col_____ VARCHAR(50) NOT NULL,
    col______1 VARCHAR(50) NOT NULL,
    col___ VARCHAR(50) NOT NULL,
    col____ DECIMAL(10,2) NOT NULL,
    col_____1 DECIMAL(10,2) NOT NULL,
    col_____2 DECIMAL(10,2) NOT NULL,
    col____1 DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col_____, col______1)
);

INSERT INTO parent_quotas_test (col_____, col______1, col___, col____, col_____1, col_____2, col____1)
VALUES
    ('001-001', '混凝土浇筑C30', 'm³', 120.5, 450.8, 80.3, 651.6),
    ('001-002', '钢筋绑扎HPB300', 'kg', 85.2, 320.5, 45.8, 451.5),
    ('001-003', '模板安装拆除', 'm²', 95.8, 180.2, 25.6, 301.6),
    ('002-001', '砌砖工程MU10', 'm³', 110.3, 280.9, 35.2, 426.4),
    ('002-002', '抹灰工程', 'm²', 75.6, 120.4, 15.8, 211.8);

-- Table: parent_quotas_web_test
-- Source: parent_quotas_web_test.csv
-- Rows: 3

CREATE TABLE parent_quotas_web_test (
    col_____ VARCHAR(50) NOT NULL,
    col______1 VARCHAR(50) NOT NULL,
    col___ VARCHAR(50) NOT NULL,
    col____ DECIMAL(10,2) NOT NULL,
    col_____1 DECIMAL(10,2) NOT NULL,
    col_____2 DECIMAL(10,2) NOT NULL,
    col____1 DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col_____, col______1, col___)
);

INSERT INTO parent_quotas_web_test (col_____, col______1, col___, col____, col_____1, col_____2, col____1)
VALUES
    ('001-001', '混凝土浇筑C30', 'm³', 120.5, 450.8, 80.3, 651.6),
    ('001-002', '钢筋绑扎HPB300', 'kg', 85.2, 320.5, 45.8, 451.5),
    ('001-003', '模板安装拆除', 'm²', 95.8, 180.2, 25.6, 301.6);

-- Table: parent_quotas_mongodb_test
-- Source: parent_quotas_mongodb_test.csv
-- Rows: 5

CREATE TABLE parent_quotas_mongodb_test (
    col_____ VARCHAR(50) NOT NULL,
    col______1 VARCHAR(50) NOT NULL,
    col___ VARCHAR(50) NOT NULL,
    col____ DECIMAL(10,2) NOT NULL,
    col_____1 DECIMAL(10,2) NOT NULL,
    col_____2 DECIMAL(10,2) NOT NULL,
    col____1 DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col_____, col______1)
);

INSERT INTO parent_quotas_mongodb_test (col_____, col______1, col___, col____, col_____1, col_____2, col____1)
VALUES
    ('001-001', '混凝土浇筑C30', 'm³', 120.5, 450.8, 80.3, 651.6),
    ('001-002', '钢筋绑扎HPB300', 'kg', 85.2, 320.5, 45.8, 451.5),
    ('001-003', '模板安装拆除', 'm²', 95.8, 180.2, 25.6, 301.6),
    ('002-001', '砌砖工程MU10', 'm³', 110.3, 280.9, 35.2, 426.4),
    ('002-002', '抹灰工程', 'm²', 75.6, 120.4, 15.8, 211.8);

-- Table: parent_quotas_a96d1d5c
-- Source: parent_quotas_a96d1d5c.csv
-- Rows: 3

CREATE TABLE parent_quotas_a96d1d5c (
    col___ VARCHAR(50) NOT NULL,
    col______ VARCHAR(50) NOT NULL,
    col_____ VARCHAR(50),
    col____1 VARCHAR(50) NOT NULL,
    col___________ DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col___, col______)
);

INSERT INTO parent_quotas_a96d1d5c (col___, col______, col_____, col____1, col___________)
VALUES
    ('1-1', '人工挖一般土方 一、二类土', '挖土、余土清理、修整底边等。', 'm³', 0.0),
    ('1-2', '人工挖一般土方 二类土', NULL, 'm³', 0.0),
    ('1-3', '人工挖一般土方 四类土', NULL, 'm³', 0.0);

-- Table: parent_quotas_938ffd17
-- Source: parent_quotas_938ffd17.csv
-- Rows: 10

CREATE TABLE parent_quotas_938ffd17 (
    col___ VARCHAR(50) NOT NULL,
    col______ VARCHAR(50) NOT NULL,
    col_____ VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col___________ DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col___, col______)
);

INSERT INTO parent_quotas_938ffd17 (col___, col______, col_____, col____1, col___________)
VALUES
    ('1-4', '机械挖一般土方 深5m以内', '挖土、甩土或装土、清理机下余土,修理边坡等。', 'm³', 0.0),
    ('1-5', '机械挖一般土方 深5m以外', '挖土、甩土或装土、清理机下余土,修理边坡等。', 'm³', 0.0),
    ('1-6', '人工挖沟槽土方 一、二类土', '挖土、余土清理、修整底边、打钎拍底等。', 'm³', 0.0),
    ('1-7', '人工挖沟槽土方 三类土', '挖土、余土清理、修整底边、打钎拍底等。', 'm³', 0.0),
    ('1-8', '人工挖沟槽土方 四类土', '挖土、余土清理、修整底边、打钎拍底等。', 'm³', 0.0),
    ('1-9', '机械挖沟槽土方 深5m以内', '挖土、甩土或装土、清理机下余土，修理边坡等', 'm³', 0.0),
    ('1-10', '机械挖沟槽土方 深5m以外', '挖土、甩土或装土、清理机下余土，修理边坡等', 'm³', 0.0),
    ('1-11', '人工挖基坑土方 一、二类土', '挖土、余土清理、修整底边、打钎拍底等。', 'm³', 0.0),
    ('1-12', '人工挖基坑土方 三类土', '挖土、余土清理、修整底边、打钎拍底等。', 'm³', 0.0),
    ('1-13', '人工挖基坑土方 四类土', '挖土、余土清理、修整底边、打钎拍底等。', 'm³', 0.0);

-- Table: parent_quotas_7ec5dfc3
-- Source: parent_quotas_7ec5dfc3.csv
-- Rows: 6

CREATE TABLE parent_quotas_7ec5dfc3 (
    col___ VARCHAR(50) NOT NULL,
    col______ VARCHAR(50) NOT NULL,
    col_____ VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col___________ DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col___, col______)
);

INSERT INTO parent_quotas_7ec5dfc3 (col___, col______, col_____, col____1, col___________)
VALUES
    ('1-29', '人工回填 土', '拌合、回填、平整、夯实等', 'm³', 21.315),
    ('1-30', '人工回填 灰土 2:8', '拌合、回填、平整、夯实等', 'm³', 25.578),
    ('1-31', '人工回填 灰土 3:7', '拌合、回填、平整、夯实等', 'm³', 25.578),
    ('1-32', '人工回填 级配砂石', '拌合、回填、平整、夯实等', 'm³', 15.9558),
    ('1-33', '人工回填 砂', '拌合、回填、平整、夯实等', 'm³', 21.5586),
    ('1-34', '人工回填 石灰、粉煤灰、碎石', '拌合、回填、平整、夯实等', 'm³', 31.668000000000003);

-- Table: parent_quotas_7051ec62
-- Source: parent_quotas_7051ec62.csv
-- Rows: 3

CREATE TABLE parent_quotas_7051ec62 (
    col___ VARCHAR(50) NOT NULL,
    col______ VARCHAR(50) NOT NULL,
    col_____ VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col___________ DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col___, col______)
);

INSERT INTO parent_quotas_7051ec62 (col___, col______, col_____, col____1, col___________)
VALUES
    ('1-1', '人工挖一般土方一、二类土', '挖土、余土清理、修整底边等', 'm³', 0.0),
    ('1-2', '人工挖一般土方三类土', '挖土、余土清理、修整底边等', 'm³', 0.0),
    ('1-3', '人工挖一般土方四类土', '挖土、余土清理、修整底边等', 'm³', 0.0);

-- Table: parent_quotas_370b78af
-- Source: parent_quotas_370b78af.csv
-- Rows: 1

CREATE TABLE parent_quotas_370b78af (
    col___ VARCHAR(50) NOT NULL,
    col______ VARCHAR(50) NOT NULL,
    col_____ VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col___________ DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col___, col______, col_____, col____1)
);

INSERT INTO parent_quotas_370b78af (col___, col______, col_____, col____1, col___________)
VALUES
    ('1-1', '人工挖一般土方 一、二类土', '挖掘一般土方', '100m³', 106.05);

-- Table: parent_quotas_251c88f2
-- Source: parent_quotas_251c88f2.csv
-- Rows: 5

CREATE TABLE parent_quotas_251c88f2 (
    col___ VARCHAR(50) NOT NULL,
    col______ VARCHAR(50) NOT NULL,
    col_____ VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col___________ DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col___, col______, col_____)
);

INSERT INTO parent_quotas_251c88f2 (col___, col______, col_____, col____1, col___________)
VALUES
    ('1-11', '人工挖基坑土方 一、二类土', '挖土、余土清理、修整底边、打钎拍底等。', 'm³', 0.0),
    ('1-16', '人工挖淤泥、流砂', '人工挖淤泥、流砂、清理边坡、运输至地面堆放等。', 'm³', 0.0),
    ('1-18', '箱涵顶进挖土 人工挖土 人挖人装', '1.人工挖土:安拆挖土支架;铺钢轨、挖土、箱涵内运土、出坑、堆放、清理等。', 'm³', 0.0),
    ('1-21', '挖桩间土', '挖土、甩土或装土、清理机下余土等。', 'm³', 0.0),
    ('1-22', '人工凿石方', '凿石、清渣、装堆、清底修边等。', 'm³', 0.0);

-- Table: child_resources_066ad134
-- Source: child_resources_066ad134.csv
-- Rows: 12

CREATE TABLE child_resources_066ad134 (
    col_____ VARCHAR(50) NOT NULL,
    col______1 INTEGER NOT NULL,
    col___ VARCHAR(50) NOT NULL,
    col______2 VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col____ DECIMAL(10,2) NOT NULL,
    col____2 INTEGER NOT NULL,
    col____3 DECIMAL(10,2) NOT NULL
);

INSERT INTO child_resources_066ad134 (col_____, col______1, col___, col______2, col____1, col____, col____2, col____3)
VALUES
    ('1-1', 10701, '人工', '综合用工三类', '工日', 0.187, 0, 0.0),
    ('1-1', 99030030, '机械', '电动打钉机', '台班', 0.0039, 0, 0.0),
    ('1-2', 10701, '人工', '综合用工三类', '工日', 0.274, 0, 0.0),
    ('1-2', 99030030, '机械', '电动打钉机', '台班', 0.0048, 0, 0.0),
    ('1-3', 10701, '人工', '综合用工三类', '工日', 0.361, 0, 0.0),
    ('1-3', 99030030, '机械', '电动打钉机', '台班', 0.0056, 0, 0.0),
    ('1-11', 10701, '人工', '综合用工三类', '工日', 0.274, 0, 0.0),
    ('1-11', 99030030, '机械', '电动打钉机', '台班', 0.0048, 0, 0.0),
    ('1-12', 10701, '人工', '综合用工三类', '工日', 0.361, 0, 0.0),
    ('1-12', 99030030, '机械', '电动打钉机', '台班', 0.0056, 0, 0.0),
    ('1-13', 10701, '人工', '综合用工三类', '工日', 0.361, 0, 0.0),
    ('1-13', 99030030, '机械', '电动打钉机', '台班', 0.0056, 0, 0.0);

-- Table: child_resources_0e9d2977
-- Source: child_resources_0e9d2977.csv
-- Rows: 3

CREATE TABLE child_resources_0e9d2977 (
    col_____ VARCHAR(50) NOT NULL,
    col______1 INTEGER NOT NULL,
    col___ VARCHAR(50) NOT NULL,
    col______2 VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col____ DECIMAL(10,2) NOT NULL,
    col____2 DECIMAL(10,2) NOT NULL,
    col____3 DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col______1, col___, col______2, col____1)
);

INSERT INTO child_resources_0e9d2977 (col_____, col______1, col___, col______2, col____1, col____, col____2, col____3)
VALUES
    ('1-1', 10701, '人工', '综合用工三类', '工日/m³', 0.187, 0.0, 0.0),
    ('1-1', 99030030, '机械', '电动打钎机', '台班/m³', 0.0039, 0.0, 0.0),
    ('1-1', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0);

-- Table: child_resources_0f292a4f
-- Source: child_resources_0f292a4f.csv
-- Rows: 9

CREATE TABLE child_resources_0f292a4f (
    col_____ VARCHAR(50) NOT NULL,
    col______1 INTEGER NOT NULL,
    col___ VARCHAR(50) NOT NULL,
    col______2 VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col____ DECIMAL(10,2) NOT NULL,
    col____2 DECIMAL(10,2) NOT NULL,
    col____3 DECIMAL(10,2) NOT NULL
);

INSERT INTO child_resources_0f292a4f (col_____, col______1, col___, col______2, col____1, col____, col____2, col____3)
VALUES
    ('1-1', 10701, '人工', '综合用工三类', '工日', 0.187, 0.0, 0.0),
    ('1-1', 99030030, '机械', '电动打钎机', '台班', 0.0039, 0.0, 0.0),
    ('1-1', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-2', 10701, '人工', '综合用工三类', '工日', 0.274, 0.0, 0.0),
    ('1-2', 99030030, '机械', '电动打钎机', '台班', 0.0048, 0.0, 0.0),
    ('1-2', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-3', 10701, '人工', '综合用工三类', '工日', 0.361, 0.0, 0.0),
    ('1-3', 99030030, '机械', '电动打钎机', '台班', 0.0056, 0.0, 0.0),
    ('1-3', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0);

-- Table: child_resources_272cc06e
-- Source: child_resources_272cc06e.csv
-- Rows: 9

CREATE TABLE child_resources_272cc06e (
    col_____ VARCHAR(50) NOT NULL,
    col______1 INTEGER NOT NULL,
    col___ VARCHAR(50) NOT NULL,
    col______2 VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col____ DECIMAL(10,2) NOT NULL,
    col____2 DECIMAL(10,2) NOT NULL,
    col____3 DECIMAL(10,2) NOT NULL
);

INSERT INTO child_resources_272cc06e (col_____, col______1, col___, col______2, col____1, col____, col____2, col____3)
VALUES
    ('1-1', 10701, '人工', '综合用工三类', '工日', 0.2, 0.0, 0.0),
    ('1-1', 99030030, '机械', '电动打钉机', '台班', 0.0039, 0.0, 0.0),
    ('1-1', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-2', 10701, '人工', '综合用工三类', '工日', 0.274, 0.0, 0.0),
    ('1-2', 99030030, '机械', '电动打钉机', '台班', 0.0048, 0.0, 0.0),
    ('1-2', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-3', 10701, '人工', '综合用工三类', '工日', 0.364, 0.0, 0.0),
    ('1-3', 99030030, '机械', '电动打钉机', '台班', 0.0056, 0.0, 0.0),
    ('1-3', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0);

-- Table: child_resources_397a9ab9
-- Source: child_resources_397a9ab9.csv
-- Rows: 18

CREATE TABLE child_resources_397a9ab9 (
    col_____ VARCHAR(50) NOT NULL,
    col______1 INTEGER NOT NULL,
    col___ VARCHAR(50) NOT NULL,
    col______2 VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col____ DECIMAL(10,2) NOT NULL,
    col____2 DECIMAL(10,2) NOT NULL,
    col____3 DECIMAL(10,2) NOT NULL
);

INSERT INTO child_resources_397a9ab9 (col_____, col______1, col___, col______2, col____1, col____, col____2, col____3)
VALUES
    ('1-1', 10701, '人工', '综合用工三类', '工日', 0.2, 0.0, 0.0),
    ('1-1', 99010002, '机械', '反铲挖掘机（带液压锤）3m³', '台班', 0.525, 0.0, 0.0),
    ('1-1', 9907000703, '机械', '轮胎式装载机 6m³/min', '台班', 0.017, 0.0, 0.0),
    ('1-1', 9943000205, '机械', '空压机 6m³/min', '台班', 0.0057, 0.0, 0.0),
    ('1-1', 99430001, '机械', '风镐', '台班', 0.5, 0.0, 0.0),
    ('1-1', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-2', 10701, '人工', '综合用工三类', '工日', 0.2, 0.0, 0.0),
    ('1-2', 99010002, '机械', '反铲挖掘机（带液压锤）3m³', '台班', 0.525, 0.0, 0.0),
    ('1-2', 9907000703, '机械', '轮胎式装载机 6m³/min', '台班', 0.017, 0.0, 0.0),
    ('1-2', 9943000205, '机械', '空压机 6m³/min', '台班', 0.0057, 0.0, 0.0),
    ('1-2', 99430001, '机械', '风镐', '台班', 0.5, 0.0, 0.0),
    ('1-2', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-3', 10701, '人工', '综合用工三类', '工日', 0.2, 0.0, 0.0),
    ('1-3', 99010002, '机械', '反铲挖掘机（带液压锤）3m³', '台班', 0.525, 0.0, 0.0),
    ('1-3', 9907000703, '机械', '轮胎式装载机 6m³/min', '台班', 0.017, 0.0, 0.0),
    ('1-3', 9943000205, '机械', '空压机 6m³/min', '台班', 0.0057, 0.0, 0.0),
    ('1-3', 99430001, '机械', '风镐', '台班', 0.5, 0.0, 0.0),
    ('1-3', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0);

-- Table: child_resources_43a75417
-- Source: child_resources_43a75417.csv
-- Rows: 21

CREATE TABLE child_resources_43a75417 (
    col_____ VARCHAR(50) NOT NULL,
    col______1 VARCHAR(50) NOT NULL,
    col___ VARCHAR(50) NOT NULL,
    col______2 VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col____ DECIMAL(10,2) NOT NULL,
    col____2 DECIMAL(10,2) NOT NULL,
    col____3 DECIMAL(10,2) NOT NULL
);

INSERT INTO child_resources_43a75417 (col_____, col______1, col___, col______2, col____1, col____, col____2, col____3)
VALUES
    ('1-26', '00010701', '人工', '综合用工三类', '工日', 0.01, 120.0, 1.2),
    ('1-26', '9907000503', '机械', '履带式推土机105kW', '台班', 0.0013, 0.0, 0.0),
    ('1-26', '99130002', '机械', '光轮压路机(综合)', '台班', 0.0057, 0.0, 0.0),
    ('1-26', '99130001', '机械', '振动压路机(综合)', '台班', 0.0013, 0.0, 0.0),
    ('1-26', '9931000002', '机械', '洒水车8t', '台班', 0.0008, 0.0, 0.0),
    ('1-26', '99460004', '其他费用', '其他机具费占人工费', '%', 1.5, 1.2, 0.018),
    ('1-27', '00010701', '人工', '综合用工三类', '工日', 0.011, 120.0, 1.3199999999999998),
    ('1-27', '04050011-2', '材料', '级配砂石', 'kg', 2315.4, 0.0, 0.0),
    ('1-27', '34000011', '材料', '其他材料费占材料费', '%', 1.0, 1.3199999999999998, 0.0131999999999999),
    ('1-27', '9907000503', '机械', '履带式推土机105kW', '台班', 0.004, 0.0, 0.0),
    ('1-27', '99130004', '机械', '压路机综合', '台班', 0.0018, 0.0, 0.0),
    ('1-27', '9913000301', '机械', '平地机HP90', '台班', 0.005, 0.0, 0.0),
    ('1-27', '99460004', '其他费用', '其他机具费占人工费', '%', 1.5, 1.3199999999999998, 0.0197999999999999),
    ('1-28', '00010701', '人工', '综合用工三类', '工日', 0.013, 120.0, 1.5599999999999998),
    ('1-28', '04090031', '材料', '石灰粉煤灰碎石', 't', 2.3154, 0.0, 0.0),
    ('1-28', '34000011', '材料', '其他材料费占材料费', '%', 1.0, 1.5599999999999998, 0.0155999999999999),
    ('1-28', '9907000503', '机械', '履带式推土机105kW', '台班', 0.004, 0.0, 0.0),
    ('1-28', '99130004', '机械', '压路机综合', '台班', 0.014, 0.0, 0.0),
    ('1-28', '9913000301', '机械', '平地机HP90', '台班', 0.005, 0.0, 0.0),
    ('1-28', '9931000002', '机械', '洒水车8t', '台班', 0.003, 0.0, 0.0),
    ('1-28', '99460004', '其他费用', '其他机具费占人工费', '%', 1.5, 1.5599999999999998, 0.0233999999999999);

-- Table: child_resources_6f4a5e48
-- Source: child_resources_6f4a5e48.csv
-- Rows: 3

CREATE TABLE child_resources_6f4a5e48 (
    col_____ VARCHAR(50) NOT NULL,
    col______1 INTEGER NOT NULL,
    col___ VARCHAR(50) NOT NULL,
    col______2 VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col____ DECIMAL(10,2) NOT NULL,
    col____2 DECIMAL(10,2) NOT NULL,
    col____3 DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col______1, col___, col______2, col____1)
);

INSERT INTO child_resources_6f4a5e48 (col_____, col______1, col___, col______2, col____1, col____, col____2, col____3)
VALUES
    ('04-01-1-6', 10701, '人工', '综合用工三类', '工日/m³', 0.182, 120.0, 21.84),
    ('04-01-1-6', 99030030, '机械', '电动打钉机', '台班/m³', 0.0039, 50.0, 0.1949999999999999),
    ('04-01-1-6', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 22.035, 0.330525);

-- Table: child_resources_62091b57
-- Source: child_resources_62091b57.csv
-- Rows: 16

CREATE TABLE child_resources_62091b57 (
    col_____ VARCHAR(50) NOT NULL,
    col______1 INTEGER NOT NULL,
    col___ VARCHAR(50) NOT NULL,
    col______2 VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col____ DECIMAL(10,2) NOT NULL,
    col____2 DECIMAL(10,2) NOT NULL,
    col____3 DECIMAL(10,2) NOT NULL
);

INSERT INTO child_resources_62091b57 (col_____, col______1, col___, col______2, col____1, col____, col____2, col____3)
VALUES
    ('1-22', 10701, '人工', '综合用工三类', '工日', 0.525, 0.0, 0.0),
    ('1-22', 9943000205, '机械', '空压机6m³/min', '台班', 0.25, 0.0, 0.0),
    ('1-22', 99330001, '机械', '风镐', '台班', 0.5, 0.0, 0.0),
    ('1-22', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-23', 10701, '人工', '综合用工三类', '工日', 0.067, 0.0, 0.0),
    ('1-23', 99010002, '机械', '反铲挖掘机(带液压锤)', '台班', 0.017, 0.0, 0.0),
    ('1-23', 9907000703, '机械', '轮胎式装载机3m³', '台班', 0.0057, 0.0, 0.0),
    ('1-23', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-24', 10701, '人工', '综合用工三类', '工日', 0.073, 0.0, 0.0),
    ('1-24', 99010002, '机械', '反铲挖掘机(带液压锤)', '台班', 0.0182, 0.0, 0.0),
    ('1-24', 9907000703, '机械', '轮胎式装载机3m³', '台班', 0.0061, 0.0, 0.0),
    ('1-24', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-25', 10701, '人工', '综合用工三类', '工日', 0.08, 0.0, 0.0),
    ('1-25', 99010002, '机械', '反铲挖掘机(带液压锤)', '台班', 0.02, 0.0, 0.0),
    ('1-25', 9907000703, '机械', '轮胎式装载机3m³', '台班', 0.0067, 0.0, 0.0),
    ('1-25', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0);

-- Table: child_resources_5b7eca5f
-- Source: child_resources_5b7eca5f.csv
-- Rows: 3

CREATE TABLE child_resources_5b7eca5f (
    col_____ VARCHAR(50) NOT NULL,
    col______1 INTEGER NOT NULL,
    col___ VARCHAR(50) NOT NULL,
    col______2 VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col____ DECIMAL(10,2) NOT NULL,
    col____2 DECIMAL(10,2) NOT NULL,
    col____3 DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col______1, col___, col______2, col____1)
);

INSERT INTO child_resources_5b7eca5f (col_____, col______1, col___, col______2, col____1, col____, col____2, col____3)
VALUES
    ('1-1', 10701, '人工', '综合用工三类', '工日', 0.8, 120.0, 96.0),
    ('1-1', 99030030, '机械', '电动打钎机', '台班', 0.1, 50.0, 5.0),
    ('1-1', 99460004, '其他费用', '其他机具费占人工费', '%', 5.0, 101.0, 5.05);

-- Table: child_resources_753b5cbd
-- Source: child_resources_753b5cbd.csv
-- Rows: 39

CREATE TABLE child_resources_753b5cbd (
    col_____ VARCHAR(50) NOT NULL,
    col______1 INTEGER NOT NULL,
    col___ VARCHAR(50) NOT NULL,
    col______2 VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col____ DECIMAL(10,2) NOT NULL,
    col____2 DECIMAL(10,2) NOT NULL,
    col____3 DECIMAL(10,2) NOT NULL
);

INSERT INTO child_resources_753b5cbd (col_____, col______1, col___, col______2, col____1, col____, col____2, col____3)
VALUES
    ('1-1', 10701, '人工', '综合用工三类', '工日', 0.11, 0.0, 0.0),
    ('1-1', 99030030, '机械', '电动打桩机', '台班', 0.0031, 0.0, 0.0),
    ('1-1', 99460004, '机械', '其他机具费 占人工费', '%', 1.5, 0.0, 0.0),
    ('1-2', 10701, '人工', '综合用工三类', '工日', 0.12, 0.0, 0.0),
    ('1-2', 99030030, '机械', '电动打桩机', '台班', 0.0032, 0.0, 0.0),
    ('1-2', 99460004, '机械', '其他机具费 占人工费', '%', 1.5, 0.0, 0.0),
    ('1-3', 10701, '人工', '综合用工三类', '工日', 0.13, 0.0, 0.0),
    ('1-3', 99030030, '机械', '电动打桩机', '台班', 0.0033, 0.0, 0.0),
    ('1-3', 99460004, '机械', '其他机具费 占人工费', '%', 1.5, 0.0, 0.0),
    ('1-4', 10701, '人工', '综合用工三类', '工日', 0.14, 0.0, 0.0),
    ('1-4', 99030030, '机械', '电动打桩机', '台班', 0.0034, 0.0, 0.0),
    ('1-4', 99460004, '机械', '其他机具费 占人工费', '%', 1.5, 0.0, 0.0),
    ('1-5', 10701, '人工', '综合用工三类', '工日', 0.15, 0.0, 0.0),
    ('1-5', 99030030, '机械', '电动打桩机', '台班', 0.0035, 0.0, 0.0),
    ('1-5', 99460004, '机械', '其他机具费 占人工费', '%', 1.5, 0.0, 0.0),
    ('1-6', 10701, '人工', '综合用工三类', '工日', 0.16, 0.0, 0.0),
    ('1-6', 99030030, '机械', '电动打桩机', '台班', 0.0036, 0.0, 0.0),
    ('1-6', 99460004, '机械', '其他机具费 占人工费', '%', 1.5, 0.0, 0.0),
    ('1-7', 10701, '人工', '综合用工三类', '工日', 0.17, 0.0, 0.0),
    ('1-7', 99030030, '机械', '电动打桩机', '台班', 0.0037, 0.0, 0.0),
    ('1-7', 99460004, '机械', '其他机具费 占人工费', '%', 1.5, 0.0, 0.0),
    ('1-8', 10701, '人工', '综合用工三类', '工日', 0.18, 0.0, 0.0),
    ('1-8', 99030030, '机械', '电动打桩机', '台班', 0.0038, 0.0, 0.0),
    ('1-8', 99460004, '机械', '其他机具费 占人工费', '%', 1.5, 0.0, 0.0),
    ('1-9', 10701, '人工', '综合用工三类', '工日', 0.19, 0.0, 0.0),
    ('1-9', 99030030, '机械', '电动打桩机', '台班', 0.0039, 0.0, 0.0),
    ('1-9', 99460004, '机械', '其他机具费 占人工费', '%', 1.5, 0.0, 0.0),
    ('1-10', 10701, '人工', '综合用工三类', '工日', 0.2, 0.0, 0.0),
    ('1-10', 99030030, '机械', '电动打桩机', '台班', 0.004, 0.0, 0.0),
    ('1-10', 99460004, '机械', '其他机具费 占人工费', '%', 1.5, 0.0, 0.0),
    ('1-11', 10701, '人工', '综合用工三类', '工日', 0.21, 0.0, 0.0),
    ('1-11', 99030030, '机械', '电动打桩机', '台班', 0.0041, 0.0, 0.0),
    ('1-11', 99460004, '机械', '其他机具费 占人工费', '%', 1.5, 0.0, 0.0),
    ('1-12', 10701, '人工', '综合用工三类', '工日', 0.22, 0.0, 0.0),
    ('1-12', 99030030, '机械', '电动打桩机', '台班', 0.0042, 0.0, 0.0),
    ('1-12', 99460004, '机械', '其他机具费 占人工费', '%', 1.5, 0.0, 0.0),
    ('1-13', 10701, '人工', '综合用工三类', '工日', 0.23, 0.0, 0.0),
    ('1-13', 99030030, '机械', '电动打桩机', '台班', 0.0043, 0.0, 0.0),
    ('1-13', 99460004, '机械', '其他机具费 占人工费', '%', 1.5, 0.0, 0.0);

-- Table: child_resources_81699df3
-- Source: child_resources_81699df3.csv
-- Rows: 49

CREATE TABLE child_resources_81699df3 (
    col_____ VARCHAR(50) NOT NULL,
    col______1 INTEGER NOT NULL,
    col___ VARCHAR(50) NOT NULL,
    col______2 VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col____ DECIMAL(10,2) NOT NULL,
    col____2 DECIMAL(10,2) NOT NULL,
    col____3 DECIMAL(10,2) NOT NULL
);

INSERT INTO child_resources_81699df3 (col_____, col______1, col___, col______2, col____1, col____, col____2, col____3)
VALUES
    ('1-43', 10701, '人工', '综合用工三类', '工日', 0.01, 120.0, 1.2),
    ('1-43', 34000009, '材料', '弃土或渣土消纳', 'm³', 1.0, 0.0, 0.0),
    ('1-43', 9901000016, '机械', '履带式单斗液压挖掘机1.6m³', '台班', 0.0017, 0.0, 0.0),
    ('1-43', 9907000012, '机械', '自卸汽车12t', '台班', 0.0041, 0.0, 0.0),
    ('1-43', 9907000703, '机械', '轮胎式装载机3m³', '台班', 0.0006, 0.0, 0.0),
    ('1-43', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 1.2, 0.018),
    ('1-44', 10701, '人工', '综合用工三类', '工日', 0.01, 120.0, 1.2),
    ('1-44', 34000009, '材料', '弃土或渣土消纳', 'm³', 1.0, 0.0, 0.0),
    ('1-44', 9901000016, '机械', '履带式单斗液压挖掘机1.6m³', '台班', 0.0017, 0.0, 0.0),
    ('1-44', 9907000012, '机械', '自卸汽车12t', '台班', 0.0041, 0.0, 0.0),
    ('1-44', 9907000703, '机械', '轮胎式装载机3m³', '台班', 0.0006, 0.0, 0.0),
    ('1-44', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 1.2, 0.018),
    ('1-45', 10701, '人工', '综合用工三类', '工日', 0.01, 120.0, 1.2),
    ('1-45', 34000009, '材料', '弃土或渣土消纳', 'm³', 1.0, 0.0, 0.0),
    ('1-45', 9901000016, '机械', '履带式单斗液压挖掘机1.6m³', '台班', 0.0017, 0.0, 0.0),
    ('1-45', 9907000012, '机械', '自卸汽车12t', '台班', 0.0041, 0.0, 0.0),
    ('1-45', 9907000703, '机械', '轮胎式装载机3m³', '台班', 0.0006, 0.0, 0.0),
    ('1-45', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 1.2, 0.018),
    ('1-46', 10701, '人工', '综合用工三类', '工日', 0.016, 120.0, 1.92),
    ('1-46', 34000009, '材料', '弃土或渣土消纳', 'm³', 1.0, 0.0, 0.0),
    ('1-46', 9901000016, '机械', '履带式单斗液压挖掘机1.6m³', '台班', 0.0041, 0.0, 0.0),
    ('1-46', 9907000012, '机械', '自卸汽车12t', '台班', 0.0057, 0.0, 0.0),
    ('1-46', 9907000703, '机械', '轮胎式装载机3m³', '台班', 0.0006, 0.0, 0.0),
    ('1-46', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 1.92, 0.0288),
    ('1-47', 10701, '人工', '综合用工三类', '工日', 0.016, 120.0, 1.92),
    ('1-47', 34000009, '材料', '弃土或渣土消纳', 'm³', 1.0, 0.0, 0.0),
    ('1-47', 9901000016, '机械', '履带式单斗液压挖掘机1.6m³', '台班', 0.0041, 0.0, 0.0),
    ('1-47', 9907000012, '机械', '自卸汽车12t', '台班', 0.0057, 0.0, 0.0),
    ('1-47', 9907000703, '机械', '轮胎式装载机3m³', '台班', 0.0006, 0.0, 0.0),
    ('1-47', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 1.92, 0.0288),
    ('1-48', 10701, '人工', '综合用工三类', '工日', 0.016, 120.0, 1.92),
    ('1-48', 34000009, '材料', '弃土或渣土消纳', 'm³', 1.0, 0.0, 0.0),
    ('1-48', 9901000016, '机械', '履带式单斗液压挖掘机1.6m³', '台班', 0.0041, 0.0, 0.0),
    ('1-48', 9907000012, '机械', '自卸汽车12t', '台班', 0.0057, 0.0, 0.0),
    ('1-48', 9907000703, '机械', '轮胎式装载机3m³', '台班', 0.0006, 0.0, 0.0),
    ('1-48', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 1.92, 0.0288),
    ('1-49', 10701, '人工', '综合用工三类', '工日', 0.022, 120.0, 2.64),
    ('1-49', 9944000006, '机械', '泥浆泵φ100mm', '台班', 0.0135, 0.0, 0.0),
    ('1-49', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 2.64, 0.0395999999999999),
    ('1-50', 34000009, '材料', '弃土或渣土消纳', 'm³', 1.0, 0.0, 0.0),
    ('1-50', 9907000109, '机械', '泥浆罐车5000L', '台班', 0.005, 0.0, 0.0),
    ('1-51', 9907000109, '机械', '泥浆罐车5000L', '台班', 0.0016, 0.0, 0.0),
    ('1-52', 10701, '人工', '综合用工三类', '工日', 0.011, 120.0, 1.3199999999999998),
    ('1-52', 9907000012, '机械', '自卸汽车12t', '台班', 0.0088, 0.0, 0.0),
    ('1-52', 9907000102, '机械', '轮胎式装载机1.5m³', '台班', 0.0037, 0.0, 0.0),
    ('1-52', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 1.3199999999999998, 0.0197999999999999),
    ('1-53', 34000009, '材料', '弃土或渣土消纳', 'm³', 1.0, 0.0, 0.0),
    ('1-53', 9907000012, '机械', '自卸汽车12t', '台班', 0.0088, 0.0, 0.0),
    ('1-54', 9907000012, '机械', '自卸汽车12t', '台班', 0.0025, 0.0, 0.0);

-- Table: child_resources_97a67801
-- Source: child_resources_97a67801.csv
-- Rows: 34

CREATE TABLE child_resources_97a67801 (
    col_____ VARCHAR(50) NOT NULL,
    col______1 INTEGER NOT NULL,
    col___ VARCHAR(50) NOT NULL,
    col______2 VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col____ DECIMAL(10,2) NOT NULL,
    col____2 DECIMAL(10,2) NOT NULL,
    col____3 DECIMAL(10,2) NOT NULL
);

INSERT INTO child_resources_97a67801 (col_____, col______1, col___, col______2, col____1, col____, col____2, col____3)
VALUES
    ('1-4', 10701, '人工', '综合用工三类', '工日', 0.009, 0.0, 0.0),
    ('1-4', 9901000016, '机械', '履带式单斗液压挖掘机1.6m³', '台班', 0.0018, 0.0, 0.0),
    ('1-4', 9907000703, '机械', '轮胎式装载机3m³', '台班', 0.0006, 0.0, 0.0),
    ('1-4', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-5', 10701, '人工', '综合用工三类', '工日', 0.01, 0.0, 0.0),
    ('1-5', 9901000016, '机械', '履带式单斗液压挖掘机1.6m³', '台班', 0.002, 0.0, 0.0),
    ('1-5', 9907000703, '机械', '轮胎式装载机3m³', '台班', 0.0007, 0.0, 0.0),
    ('1-5', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-6', 10701, '人工', '综合用工三类', '工日', 0.182, 0.0, 0.0),
    ('1-6', 99030030, '机械', '电动打钎机', '台班', 0.0039, 0.0, 0.0),
    ('1-6', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-7', 10701, '人工', '综合用工三类', '工日', 0.25, 0.0, 0.0),
    ('1-7', 99030030, '机械', '电动打钎机', '台班', 0.0048, 0.0, 0.0),
    ('1-7', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-8', 10701, '人工', '综合用工三类', '工日', 0.4, 0.0, 0.0),
    ('1-8', 99030030, '机械', '电动打钎机', '台班', 0.0056, 0.0, 0.0),
    ('1-8', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-9', 10701, '人工', '综合用工三类', '工日', 0.013, 0.0, 0.0),
    ('1-9', 9901000016, '机械', '履带式单斗液压挖掘机1.6m³', '台班', 0.025, 0.0, 0.0),
    ('1-9', 9907000703, '机械', '轮胎式装载机3m³', '台班', 0.0008, 0.0, 0.0),
    ('1-9', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-10', 10701, '人工', '综合用工三类', '工日', 0.015, 0.0, 0.0),
    ('1-10', 9901000016, '机械', '履带式单斗液压挖掘机1.6m³', '台班', 0.028, 0.0, 0.0),
    ('1-10', 9907000703, '机械', '轮胎式装载机3m³', '台班', 0.0009, 0.0, 0.0),
    ('1-10', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-11', 10701, '人工', '综合用工三类', '工日', 0.2, 0.0, 0.0),
    ('1-11', 99030030, '机械', '电动打钎机', '台班', 0.0039, 0.0, 0.0),
    ('1-11', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-12', 10701, '人工', '综合用工三类', '工日', 0.25, 0.0, 0.0),
    ('1-12', 99030030, '机械', '电动打钎机', '台班', 0.0048, 0.0, 0.0),
    ('1-12', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-13', 10701, '人工', '综合用工三类', '工日', 0.5, 0.0, 0.0),
    ('1-13', 99030030, '机械', '电动打钎机', '台班', 0.0056, 0.0, 0.0),
    ('1-13', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0);

-- Table: child_resources_c7826334
-- Source: child_resources_c7826334.csv
-- Rows: 35

CREATE TABLE child_resources_c7826334 (
    col_____ VARCHAR(50) NOT NULL,
    col______1 INTEGER NOT NULL,
    col___ VARCHAR(50) NOT NULL,
    col______2 VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col____ DECIMAL(10,2) NOT NULL,
    col____2 DECIMAL(10,2) NOT NULL,
    col____3 DECIMAL(10,2) NOT NULL
);

INSERT INTO child_resources_c7826334 (col_____, col______1, col___, col______2, col____1, col____, col____2, col____3)
VALUES
    ('1-43', 10701, '人工', '综合用工三类', '工日', 0.01, 120.0, 1.2),
    ('1-43', 34000009, '材料', '弃土或渣土消纳', 'm³', 1.0, 0.0, 0.0),
    ('1-43', 9901000016, '机械', '履带式单斗液压挖掘机1.6m³', '台班', 0.0017, 0.0, 0.0),
    ('1-43', 9907000703, '机械', '轮胎式装载机3m³', '台班', 0.0006, 0.0, 0.0),
    ('1-43', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 1.2, 0.018),
    ('1-44', 9907000012, '机械', '自卸汽车12t', '台班', 0.0041, 0.0, 0.0),
    ('1-44', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-45', 9907000012, '机械', '自卸汽车12t', '台班', 0.0018, 0.0, 0.0),
    ('1-46', 10701, '人工', '综合用工三类', '工日', 0.016, 120.0, 1.92),
    ('1-46', 34000009, '材料', '弃土或渣土消纳', 'm³', 1.0, 0.0, 0.0),
    ('1-46', 9901000016, '机械', '履带式单斗液压挖掘机1.6m³', '台班', 0.0041, 0.0, 0.0),
    ('1-46', 9907000703, '机械', '轮胎式装载机3m³', '台班', 0.0006, 0.0, 0.0),
    ('1-46', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 1.92, 0.0288),
    ('1-47', 9907000012, '机械', '自卸汽车12t', '台班', 0.0057, 0.0, 0.0),
    ('1-47', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-48', 9907000012, '机械', '自卸汽车12t', '台班', 0.0025, 0.0, 0.0),
    ('1-49', 10701, '人工', '综合用工三类', '工日', 0.022, 120.0, 2.64),
    ('1-49', 9944000006, '机械', '泥浆泵φ100mm', '台班', 0.0135, 0.0, 0.0),
    ('1-49', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 2.64, 0.0395999999999999),
    ('1-50', 34000009, '材料', '弃土或渣土消纳', 'm³', 1.0, 0.0, 0.0),
    ('1-50', 9907000109, '机械', '泥浆罐车5000L', '台班', 0.005, 0.0, 0.0),
    ('1-51', 9907000109, '机械', '泥浆罐车5000L', '台班', 0.0016, 0.0, 0.0),
    ('1-52', 10701, '人工', '综合用工三类', '工日', 0.011, 120.0, 1.3199999999999998),
    ('1-52', 9907000102, '机械', '轮胎式装载机1.5m³', '台班', 0.0037, 0.0, 0.0),
    ('1-52', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 1.3199999999999998, 0.0197999999999999),
    ('1-53', 34000009, '材料', '弃土或渣土消纳', 'm³', 1.0, 0.0, 0.0),
    ('1-53', 9907000012, '机械', '自卸汽车12t', '台班', 0.0088, 0.0, 0.0),
    ('1-54', 9907000012, '机械', '自卸汽车12t', '台班', 0.0025, 0.0, 0.0),
    ('1-55', 10701, '人工', '综合用工三类', '工日', 0.011, 120.0, 1.3199999999999998),
    ('1-55', 9907000703, '机械', '轮胎式装载机3m³', '台班', 0.0008, 0.0, 0.0),
    ('1-55', 9901000016, '机械', '履带式单斗液压挖掘机1.6m³', '台班', 0.0024, 0.0, 0.0),
    ('1-55', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 1.3199999999999998, 0.0197999999999999),
    ('1-56', 34000009, '材料', '弃土或渣土消纳', 'm³', 1.0, 0.0, 0.0),
    ('1-56', 9907000012, '机械', '自卸汽车12t', '台班', 0.0057, 0.0, 0.0),
    ('1-57', 9907000012, '机械', '自卸汽车12t', '台班', 0.0025, 0.0, 0.0);

-- Table: child_resources_bcf7f567
-- Source: child_resources_bcf7f567.csv
-- Rows: 49

CREATE TABLE child_resources_bcf7f567 (
    col_____ VARCHAR(50) NOT NULL,
    col______1 VARCHAR(50) NOT NULL,
    col___ VARCHAR(50) NOT NULL,
    col______2 VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col____ DECIMAL(10,2) NOT NULL,
    col____2 DECIMAL(10,2) NOT NULL,
    col____3 DECIMAL(10,2) NOT NULL
);

INSERT INTO child_resources_bcf7f567 (col_____, col______1, col___, col______2, col____1, col____, col____2, col____3)
VALUES
    ('1-14', '00010701', '人工', '综合用工三类', '工日', 0.013, 0.0, 0.0),
    ('1-14', '9901000016', '机械', '履带式单斗液压挖掘机1.6m³', '台班', 0.0025, 0.0, 0.0),
    ('1-14', '9907000703', '机械', '轮胎式装载机3m³', '台班', 0.0008, 0.0, 0.0),
    ('1-14', '99460004', '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-15', '00010701', '人工', '综合用工三类', '工日', 0.015, 0.0, 0.0),
    ('1-15', '9901000016', '机械', '履带式单斗液压挖掘机1.6m³', '台班', 0.0028, 0.0, 0.0),
    ('1-15', '9907000703', '机械', '轮胎式装载机3m³', '台班', 0.0009, 0.0, 0.0),
    ('1-15', '99460004', '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-16', '00010701', '人工', '综合用工三类', '工日', 0.611, 0.0, 0.0),
    ('1-16', '9901000016', '机械', '履带式单斗液压挖掘机1.6m³', '台班', 0.0, 0.0, 0.0),
    ('1-16', '99460004', '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-17', '00010701', '人工', '综合用工三类', '工日', 0.013, 0.0, 0.0),
    ('1-17', '9901000016', '机械', '履带式单斗液压挖掘机1.6m³', '台班', 0.0042, 0.0, 0.0),
    ('1-17', '99460004', '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-18', '00010701', '人工', '综合用工三类', '工日', 0.761, 0.0, 0.0),
    ('1-18', '05030007', '材料', '板方材', 'm³', 0.0003, 0.0, 0.0),
    ('1-18', '35030011', '材料', '钢支撑', 'kg', 0.155, 0.0, 0.0),
    ('1-18', '14290005-1', '材料', '乙炔气', 'm³', 0.001, 0.0, 0.0),
    ('1-18', '03130101', '材料', '电焊条(综合)', 'kg', 0.019, 0.0, 0.0),
    ('1-18', '34000011', '材料', '其他材料费占材料费', '%', 1.0, 0.0, 0.0),
    ('1-18', '9907000009', '机械', '自卸汽车5t', '台班', 0.0066, 0.0, 0.0),
    ('1-18', '9925000202', '机械', '交流电焊机32kV·A', '台班', 0.0017, 0.0, 0.0),
    ('1-18', '9909000253', '机械', '电动卷扬机单筒慢速5t', '台班', 0.013, 0.0, 0.0),
    ('1-18', '99460004', '机械', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-19', '00010701', '人工', '综合用工三类', '工日', 0.699, 0.0, 0.0),
    ('1-19', '05030007', '材料', '板方材', 'm³', 0.0003, 0.0, 0.0),
    ('1-19', '35030011', '材料', '钢支撑', 'kg', 0.155, 0.0, 0.0),
    ('1-19', '14290005-1', '材料', '乙炔气', 'm³', 0.001, 0.0, 0.0),
    ('1-19', '03130101', '材料', '电焊条(综合)', 'kg', 0.019, 0.0, 0.0),
    ('1-19', '03150906', '材料', '铁件', 'kg', 0.05, 0.0, 0.0),
    ('1-19', '3701000201', '材料', '钢轨38kg/m', 'kg', 0.27, 0.0, 0.0),
    ('1-19', '34000011', '材料', '其他材料费占材料费', '%', 1.0, 0.0, 0.0),
    ('1-19', '9907000009', '机械', '自卸汽车5t', '台班', 0.0066, 0.0, 0.0),
    ('1-19', '9925000202', '机械', '交流电焊机32kV·A', '台班', 0.0017, 0.0, 0.0),
    ('1-19', '9907000701', '机械', '轮胎式装载机1m³', '台班', 0.0197, 0.0, 0.0),
    ('1-19', '9909000253', '机械', '电动卷扬机单筒慢速5t', '台班', 0.007, 0.0, 0.0),
    ('1-19', '99460004', '机械', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-20', '00010701', '人工', '综合用工三类', '工日', 0.078, 0.0, 0.0),
    ('1-20', '03150906', '材料', '铁件', 'kg', 0.05, 0.0, 0.0),
    ('1-20', '3701000201', '材料', '钢轨38kg/m', 'kg', 0.27, 0.0, 0.0),
    ('1-20', '34000011', '材料', '其他材料费占材料费', '%', 1.0, 0.0, 0.0),
    ('1-20', '9907000009', '机械', '自卸汽车5t', '台班', 0.0066, 0.0, 0.0),
    ('1-20', '9901000301', '机械', '履带式单斗挖土机1.0m³', '台班', 0.0197, 0.0, 0.0),
    ('1-20', '9909000253', '机械', '电动卷扬机单筒慢速5t', '台班', 0.003, 0.0, 0.0),
    ('1-20', '99460004', '机械', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-21', '00010701', '人工', '综合用工三类', '工日', 0.034, 0.0, 0.0),
    ('1-21', '9901000004', '机械', '履带式单斗液压挖掘机0.3m³', '台班', 0.0172, 0.0, 0.0),
    ('1-21', '9907000102', '机械', '轮胎式装载机1.5m³', '台班', 0.0057, 0.0, 0.0),
    ('1-21', '99460004', '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0);

-- Table: child_resources_9b8dce7f
-- Source: child_resources_9b8dce7f.csv
-- Rows: 22

CREATE TABLE child_resources_9b8dce7f (
    col_____ VARCHAR(50) NOT NULL,
    col______1 VARCHAR(50) NOT NULL,
    col___ VARCHAR(50) NOT NULL,
    col______2 VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col____ DECIMAL(10,2) NOT NULL,
    col____2 DECIMAL(10,2) NOT NULL,
    col____3 DECIMAL(10,2) NOT NULL
);

INSERT INTO child_resources_9b8dce7f (col_____, col______1, col___, col______2, col____1, col____, col____2, col____3)
VALUES
    ('1-11', '00010701', '人工', '综合用工三类', '工日/m³', 0.2, 0.0, 0.0),
    ('1-11', '99030030', '机械', '电动打钎机', '台班/m³', 0.0039, 0.0, 0.0),
    ('1-11', '99460004', '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-16', '00010701', '人工', '综合用工三类', '工日/m³', 0.611, 0.0, 0.0),
    ('1-16', '99460004', '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-18', '00010701', '人工', '综合用工三类', '工日/m³', 0.761, 0.0, 0.0),
    ('1-18', '05030007', '材料', '板方材', 'm³/m³', 0.0003, 0.0, 0.0),
    ('1-18', '35030011', '材料', '钢支撑', 'kg/m³', 0.155, 0.0, 0.0),
    ('1-18', '14290005-1', '材料', '乙炔气', 'm³/m³', 0.001, 0.0, 0.0),
    ('1-18', '03130101', '材料', '电焊条(综合)', 'kg/m³', 0.019, 0.0, 0.0),
    ('1-18', '9907000009', '机械', '自卸汽车5t', '台班/m³', 0.0066, 0.0, 0.0),
    ('1-18', '9925000202', '机械', '交流电焊机32kV·A', '台班/m³', 0.0017, 0.0, 0.0),
    ('1-18', '9909000253', '机械', '电动卷扬机单筒慢速5t', '台班/m³', 0.013, 0.0, 0.0),
    ('1-18', '99460004', '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-21', '00010701', '人工', '综合用工三类', '工日', 0.034, 0.0, 0.0),
    ('1-21', '9901000004', '机械', '履带式单斗液压挖掘机0.3m³', '台班', 0.0172, 0.0, 0.0),
    ('1-21', '9907000102', '机械', '轮胎式装载机1.5m³', '台班', 0.0057, 0.0, 0.0),
    ('1-21', '99460004', '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-22', '00010701', '人工', '综合用工三类', '工日/m³', 0.525, 0.0, 0.0),
    ('1-22', '9943000205', '机械', '空压机6m³/min', '台班/m³', 0.25, 0.0, 0.0),
    ('1-22', '99330001', '机械', '风镐', '台班/m³', 0.5, 0.0, 0.0),
    ('1-22', '99460004', '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0);

-- Table: child_resources_d44a152a
-- Source: child_resources_d44a152a.csv
-- Rows: 9

CREATE TABLE child_resources_d44a152a (
    col_____ VARCHAR(50) NOT NULL,
    col______1 INTEGER NOT NULL,
    col___ VARCHAR(50) NOT NULL,
    col______2 VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col____ DECIMAL(10,2) NOT NULL,
    col____2 DECIMAL(10,2) NOT NULL,
    col____3 DECIMAL(10,2) NOT NULL
);

INSERT INTO child_resources_d44a152a (col_____, col______1, col___, col______2, col____1, col____, col____2, col____3)
VALUES
    ('1-1', 10701, '人工', '综合用工三类', '工日', 0.187, 0.0, 0.0),
    ('1-1', 99030030, '机械', '电动打钎机', '台班', 0.0039, 0.0, 0.0),
    ('1-1', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-2', 10701, '人工', '综合用工三类', '工日', 0.274, 0.0, 0.0),
    ('1-2', 99030030, '机械', '电动打钎机', '台班', 0.0048, 0.0, 0.0),
    ('1-2', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0),
    ('1-3', 10701, '人工', '综合用工三类', '工日', 0.361, 0.0, 0.0),
    ('1-3', 99030030, '机械', '电动打钎机', '台班', 0.0056, 0.0, 0.0),
    ('1-3', 99460004, '其他费用', '其他机具费占人工费', '%', 1.5, 0.0, 0.0);

-- Table: child_resources_f1dcf248
-- Source: child_resources_f1dcf248.csv
-- Rows: 29

CREATE TABLE child_resources_f1dcf248 (
    col_____ VARCHAR(50) NOT NULL,
    col______1 VARCHAR(50) NOT NULL,
    col___ VARCHAR(50) NOT NULL,
    col______2 VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col____ DECIMAL(10,2) NOT NULL,
    col____2 DECIMAL(10,2) NOT NULL,
    col____3 DECIMAL(10,2) NOT NULL
);

INSERT INTO child_resources_f1dcf248 (col_____, col______1, col___, col______2, col____1, col____, col____2, col____3)
VALUES
    ('1-29', '00010701', '人工', '综合用工三类', '工日', 0.175, 120.0, 21.0),
    ('1-29', '9913000202', '机械', '电动夯实机 20~62kg/m', '台班', 0.083, 0.0, 0.0),
    ('1-29', '99460004', '其他费用', '其他机具费占人工费', '%', 1.5, 21.0, 0.315),
    ('1-29', '9931000002', '机械', '洒水车 8t', '台班', 0.0008, 0.0, 0.0),
    ('1-30', '00010701', '人工', '综合用工三类', '工日', 0.21, 120.0, 25.2),
    ('1-30', '9913000202', '机械', '电动夯实机 20~62kg/m', '台班', 0.067, 0.0, 0.0),
    ('1-30', '99460004', '其他费用', '其他机具费占人工费', '%', 1.5, 25.2, 0.3779999999999999),
    ('1-30', '9931000002', '机械', '洒水车 8t', '台班', 0.002, 0.0, 0.0),
    ('1-30', '04090026', '材料', '熟石灰', 'kg', 275.0, 0.0, 0.0),
    ('1-31', '00010701', '人工', '综合用工三类', '工日', 0.21, 120.0, 25.2),
    ('1-31', '9913000202', '机械', '电动夯实机 20~62kg/m', '台班', 0.067, 0.0, 0.0),
    ('1-31', '99460004', '其他费用', '其他机具费占人工费', '%', 1.5, 25.2, 0.3779999999999999),
    ('1-31', '9931000002', '机械', '洒水车 8t', '台班', 0.004, 0.0, 0.0),
    ('1-31', '04090026', '材料', '熟石灰', 'kg', 413.0, 0.0, 0.0),
    ('1-32', '00010701', '人工', '综合用工三类', '工日', 0.131, 120.0, 15.72),
    ('1-32', '9913000202', '机械', '电动夯实机 20~62kg/m', '台班', 0.025, 0.0, 0.0),
    ('1-32', '99460004', '其他费用', '其他机具费占人工费', '%', 1.5, 15.72, 0.2358),
    ('1-32', '9931000002', '机械', '洒水车 8t', '台班', 0.0, 0.0, 0.0),
    ('1-32', '04050011-2', '材料', '级配砂石', 'kg', 2315.4, 0.0, 0.0),
    ('1-32', '0403000003-2', '材料', '砂子 中粗砂', 'kg', 1854.2, 0.0, 0.0),
    ('1-33', '00010701', '人工', '综合用工三类', '工日', 0.177, 120.0, 21.24),
    ('1-33', '9913000202', '机械', '电动夯实机 20~62kg/m', '台班', 0.085, 0.0, 0.0),
    ('1-33', '99460004', '其他费用', '其他机具费占人工费', '%', 1.5, 21.24, 0.3186),
    ('1-33', '9931000002', '机械', '洒水车 8t', '台班', 0.004, 0.0, 0.0),
    ('1-34', '00010701', '人工', '综合用工三类', '工日', 0.26, 120.0, 31.200000000000003),
    ('1-34', '9913000202', '机械', '电动夯实机 20~62kg/m', '台班', 0.085, 0.0, 0.0),
    ('1-34', '99460004', '其他费用', '其他机具费占人工费', '%', 1.5, 31.200000000000003, 0.468),
    ('1-34', '9931000002', '机械', '洒水车 8t', '台班', 0.003, 0.0, 0.0),
    ('1-34', '04090031', '材料', '石灰粉煤灰碎石', 't', 2.3154, 0.0, 0.0);

-- Table: child_resources_pg_test
-- Source: child_resources_pg_test.csv
-- Rows: 3

CREATE TABLE child_resources_pg_test (
    col_____ VARCHAR(50) NOT NULL,
    col______1 VARCHAR(50) NOT NULL,
    col______2 VARCHAR(50) NOT NULL,
    col______3 VARCHAR(50) NOT NULL,
    col___ DECIMAL(10,2) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col____2 DECIMAL(10,2) NOT NULL,
    col____3 DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col______1, col______2, col____1)
);

INSERT INTO child_resources_pg_test (col_____, col______1, col______2, col______3, col___, col____1, col____2, col____3)
VALUES
    ('001-001', 'R001', 'C30混凝土', '材料', 1.05, 'm³', 420.0, 441.0),
    ('001-001', 'R002', '人工', '人工', 8.5, '工日', 150.0, 1275.0),
    ('001-002', 'R003', 'HPB300钢筋', '材料', 1.02, 'kg', 4.2, 4.284);

-- Table: child_resources_test
-- Source: child_resources_test.csv
-- Rows: 7

CREATE TABLE child_resources_test (
    col_____ VARCHAR(50) NOT NULL,
    col______1 VARCHAR(50) NOT NULL,
    col______2 VARCHAR(50) NOT NULL,
    col______3 VARCHAR(50) NOT NULL,
    col___ DECIMAL(10,2) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col____2 DECIMAL(10,2) NOT NULL,
    col____3 DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col______1)
);

INSERT INTO child_resources_test (col_____, col______1, col______2, col______3, col___, col____1, col____2, col____3)
VALUES
    ('001-001', 'R001', 'C30混凝土', '材料', 1.05, 'm³', 420.0, 441.0),
    ('001-001', 'R002', '人工', '人工', 8.5, '工日', 150.0, 1275.0),
    ('001-001', 'R003', '机械台班', '机械', 2.3, '台班', 350.0, 805.0),
    ('001-002', 'R004', 'HPB300钢筋', '材料', 1.02, 'kg', 4.2, 4.284),
    ('001-002', 'R005', '人工', '人工', 12.8, '工日', 150.0, 1920.0),
    ('002-001', 'R006', 'MU10砖', '材料', 0.98, 'm³', 280.0, 274.4),
    ('002-001', 'R007', '砂浆', '材料', 0.25, 'm³', 180.0, 45.0);

-- Table: child_resources_mcp_test
-- Source: child_resources_mcp_test.csv
-- Rows: 7

CREATE TABLE child_resources_mcp_test (
    col_____ VARCHAR(50) NOT NULL,
    col______1 VARCHAR(50) NOT NULL,
    col______2 VARCHAR(50) NOT NULL,
    col______3 VARCHAR(50) NOT NULL,
    col___ DECIMAL(10,2) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col____2 DECIMAL(10,2) NOT NULL,
    col____3 DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col______1)
);

INSERT INTO child_resources_mcp_test (col_____, col______1, col______2, col______3, col___, col____1, col____2, col____3)
VALUES
    ('001-001', 'R001', 'C30混凝土', '材料', 1.05, 'm³', 420.0, 441.0),
    ('001-001', 'R002', '人工', '人工', 8.5, '工日', 150.0, 1275.0),
    ('001-001', 'R003', '机械台班', '机械', 2.3, '台班', 350.0, 805.0),
    ('001-002', 'R004', 'HPB300钢筋', '材料', 1.02, 'kg', 4.2, 4.284),
    ('001-002', 'R005', '人工', '人工', 12.8, '工日', 150.0, 1920.0),
    ('002-001', 'R006', 'MU10砖', '材料', 0.98, 'm³', 280.0, 274.4),
    ('002-001', 'R007', '砂浆', '材料', 0.25, 'm³', 180.0, 45.0);

-- Table: child_resources_e5a0f3e2
-- Source: child_resources_e5a0f3e2.csv
-- Rows: 4

CREATE TABLE child_resources_e5a0f3e2 (
    col_____ VARCHAR(50) NOT NULL,
    col______1 INTEGER NOT NULL,
    col___ VARCHAR(50) NOT NULL,
    col______2 VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col____ DECIMAL(10,2) NOT NULL,
    col____2 INTEGER NOT NULL,
    col____3 DECIMAL(10,2) NOT NULL
);

INSERT INTO child_resources_e5a0f3e2 (col_____, col______1, col___, col______2, col____1, col____, col____2, col____3)
VALUES
    ('1-1', 10701, '人工', '综合用工三类', '工日', 0.187, 0, 0.0),
    ('1-1', 99030030, '机械', '电动打钉机', '台班', 0.0039, 0, 0.0),
    ('1-2', 10701, '人工', '综合用工三类', '工日', 0.274, 0, 0.0),
    ('1-3', 10701, '人工', '综合用工三类', '工日', 0.361, 0, 0.0);

-- Table: child_resources_ebe3d96a
-- Source: child_resources_ebe3d96a.csv
-- Rows: 9

CREATE TABLE child_resources_ebe3d96a (
    col_____ VARCHAR(50) NOT NULL,
    col______1 INTEGER NOT NULL,
    col___ VARCHAR(50) NOT NULL,
    col______2 VARCHAR(50) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col____ DECIMAL(10,2) NOT NULL,
    col____2 DECIMAL(10,2) NOT NULL,
    col____3 DECIMAL(10,2) NOT NULL
);

INSERT INTO child_resources_ebe3d96a (col_____, col______1, col___, col______2, col____1, col____, col____2, col____3)
VALUES
    ('1-1', 10701, '人工', '综合用工三类', '工日', 0.187, 0.0, 0.0),
    ('1-1', 99030030, '机械', '电动打桩机', '台班', 0.0039, 0.0, 0.0),
    ('1-1', 99460004, '机械', '其他机具费 占人工费', '%', 1.5, 0.0, 0.0),
    ('1-2', 10701, '人工', '综合用工三类', '工日', 0.274, 0.0, 0.0),
    ('1-2', 99030030, '机械', '电动打桩机', '台班', 0.0048, 0.0, 0.0),
    ('1-2', 99460004, '机械', '其他机具费 占人工费', '%', 1.5, 0.0, 0.0),
    ('1-3', 10701, '人工', '综合用工三类', '工日', 0.361, 0.0, 0.0),
    ('1-3', 99030030, '机械', '电动打桩机', '台班', 0.0056, 0.0, 0.0),
    ('1-3', 99460004, '机械', '其他机具费 占人工费', '%', 1.5, 0.0, 0.0);

-- Table: child_resources_mongodb_test
-- Source: child_resources_mongodb_test.csv
-- Rows: 7

CREATE TABLE child_resources_mongodb_test (
    col_____ VARCHAR(50) NOT NULL,
    col______1 VARCHAR(50) NOT NULL,
    col______2 VARCHAR(50) NOT NULL,
    col______3 VARCHAR(50) NOT NULL,
    col___ DECIMAL(10,2) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col____2 DECIMAL(10,2) NOT NULL,
    col____3 DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col______1)
);

INSERT INTO child_resources_mongodb_test (col_____, col______1, col______2, col______3, col___, col____1, col____2, col____3)
VALUES
    ('001-001', 'R001', 'C30混凝土', '材料', 1.05, 'm³', 420.0, 441.0),
    ('001-001', 'R002', '人工', '人工', 8.5, '工日', 150.0, 1275.0),
    ('001-001', 'R003', '机械台班', '机械', 2.3, '台班', 350.0, 805.0),
    ('001-002', 'R004', 'HPB300钢筋', '材料', 1.02, 'kg', 4.2, 4.284),
    ('001-002', 'R005', '人工', '人工', 12.8, '工日', 150.0, 1920.0),
    ('002-001', 'R006', 'MU10砖', '材料', 0.98, 'm³', 280.0, 274.4),
    ('002-001', 'R007', '砂浆', '材料', 0.25, 'm³', 180.0, 45.0);

-- Table: child_resources_web_test
-- Source: child_resources_web_test.csv
-- Rows: 3

CREATE TABLE child_resources_web_test (
    col_____ VARCHAR(50) NOT NULL,
    col______1 VARCHAR(50) NOT NULL,
    col______2 VARCHAR(50) NOT NULL,
    col______3 VARCHAR(50) NOT NULL,
    col___ DECIMAL(10,2) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col____2 DECIMAL(10,2) NOT NULL,
    col____3 DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col______1, col______2, col____1)
);

INSERT INTO child_resources_web_test (col_____, col______1, col______2, col______3, col___, col____1, col____2, col____3)
VALUES
    ('001-001', 'R001', 'C30混凝土', '材料', 1.05, 'm³', 420.0, 441.0),
    ('001-001', 'R002', '人工', '人工', 8.5, '工日', 150.0, 1275.0),
    ('001-002', 'R003', 'HPB300钢筋', '材料', 1.02, 'kg', 4.2, 4.284);
