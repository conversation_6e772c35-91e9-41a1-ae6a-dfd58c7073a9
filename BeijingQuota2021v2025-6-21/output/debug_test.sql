-- POSTGRESQL SQL Script
-- Generated on: 2025-06-25 23:31:48
-- Source files: 2 CSV files

-- Table: debug_parent_quotas
-- Source: debug_parent_quotas.csv
-- Rows: 3

CREATE TABLE debug_parent_quotas (
    col_____ VARCHAR(50) NOT NULL,
    col______1 VARCHAR(50) NOT NULL,
    col___ VARCHAR(50) NOT NULL,
    col____ DECIMAL(10,2) NOT NULL,
    col_____1 DECIMAL(10,2) NOT NULL,
    col_____2 DECIMAL(10,2) NOT NULL,
    col____1 DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col_____, col______1, col___)
);

INSERT INTO debug_parent_quotas (col_____, col______1, col___, col____, col_____1, col_____2, col____1)
VALUES
    ('001-001', '混凝土浇筑C30', 'm³', 120.5, 450.8, 80.3, 651.6),
    ('001-002', '钢筋绑扎HPB300', 'kg', 85.2, 320.5, 45.8, 451.5),
    ('001-003', '模板安装拆除', 'm²', 95.8, 180.2, 25.6, 301.6);

-- Table: debug_child_resources
-- Source: debug_child_resources.csv
-- Rows: 3

CREATE TABLE debug_child_resources (
    col_____ VARCHAR(50) NOT NULL,
    col______1 VARCHAR(50) NOT NULL,
    col______2 VARCHAR(50) NOT NULL,
    col______3 VARCHAR(50) NOT NULL,
    col___ DECIMAL(10,2) NOT NULL,
    col____1 VARCHAR(50) NOT NULL,
    col____2 DECIMAL(10,2) NOT NULL,
    col____3 DECIMAL(10,2) NOT NULL,
    PRIMARY KEY (col______1, col______2, col____1)
);

INSERT INTO debug_child_resources (col_____, col______1, col______2, col______3, col___, col____1, col____2, col____3)
VALUES
    ('001-001', 'R001', 'C30混凝土', '材料', 1.05, 'm³', 420.0, 441.0),
    ('001-001', 'R002', '人工', '人工', 8.5, '工日', 150.0, 1275.0),
    ('001-002', 'R003', 'HPB300钢筋', '材料', 1.02, 'kg', 4.2, 4.284);
