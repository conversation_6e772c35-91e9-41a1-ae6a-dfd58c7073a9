{"metadata": {"export_date": "2025-06-25T23:55:20.707409", "source_files": 4, "format": "MongoDB JSON Export", "description": "CSV to MongoDB collection export"}, "collections": {"parent_quotas_01bdaa91": {"metadata": {"source_file": "parent_quotas_01bdaa91.csv", "document_count": 3, "fields": ["编号", "定额项名称", "工作内容", "单位", "综合单价（元/单位）"], "collection_name": "parent_quotas_01bdaa91"}, "documents": [{"编号": "1-1", "定额项名称": "人工挖一般土方 一、二类土", "工作内容": "挖土、余土清理、修整底边、打钉拍底等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_01bdaa91.csv", "_import_date": "2025-06-25T23:55:20.714605", "_row_index": 0}, {"编号": "1-2", "定额项名称": "人工挖一般土方 三类土", "工作内容": "挖土、余土清理、修整底边、打钉拍底等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_01bdaa91.csv", "_import_date": "2025-06-25T23:55:20.714647", "_row_index": 1}, {"编号": "1-3", "定额项名称": "人工挖一般土方 四类土", "工作内容": "挖土、余土清理、修整底边、打钉拍底等", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_01bdaa91.csv", "_import_date": "2025-06-25T23:55:20.714677", "_row_index": 2}]}, "parent_quotas_2475f0db": {"metadata": {"source_file": "parent_quotas_2475f0db.csv", "document_count": 12, "fields": ["编号", "定额项名称", "工作内容", "单位", "综合单价（元/单位）"], "collection_name": "parent_quotas_2475f0db"}, "documents": [{"编号": "1-43", "定额项名称": "土方运输 装车", "工作内容": "装卸土方、淤泥、流砂、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 1.218, "_source_file": "parent_quotas_2475f0db.csv", "_import_date": "2025-06-25T23:55:20.717268", "_row_index": 0}, {"编号": "1-44", "定额项名称": "土方运输 运距1km以内", "工作内容": "装卸土方、淤泥、流砂、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 1.218, "_source_file": "parent_quotas_2475f0db.csv", "_import_date": "2025-06-25T23:55:20.717306", "_row_index": 1}, {"编号": "1-45", "定额项名称": "土方运输 每增1km", "工作内容": "装卸土方、淤泥、流砂、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 1.218, "_source_file": "parent_quotas_2475f0db.csv", "_import_date": "2025-06-25T23:55:20.717333", "_row_index": 2}, {"编号": "1-46", "定额项名称": "淤泥、流砂运输 装车", "工作内容": "装卸土方、淤泥、流砂、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 1.9488, "_source_file": "parent_quotas_2475f0db.csv", "_import_date": "2025-06-25T23:55:20.717358", "_row_index": 3}, {"编号": "1-47", "定额项名称": "淤泥、流砂运输 运距1km以内", "工作内容": "装卸土方、淤泥、流砂、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 1.9488, "_source_file": "parent_quotas_2475f0db.csv", "_import_date": "2025-06-25T23:55:20.717382", "_row_index": 4}, {"编号": "1-48", "定额项名称": "淤泥、流砂运输 每增1km", "工作内容": "装卸土方、淤泥、流砂、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 1.9488, "_source_file": "parent_quotas_2475f0db.csv", "_import_date": "2025-06-25T23:55:20.717406", "_row_index": 5}, {"编号": "1-49", "定额项名称": "泥浆运输 装车", "工作内容": "装卸泥浆、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 2.6796, "_source_file": "parent_quotas_2475f0db.csv", "_import_date": "2025-06-25T23:55:20.717429", "_row_index": 6}, {"编号": "1-50", "定额项名称": "泥浆运输 运距1km以内", "工作内容": "装卸泥浆、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_2475f0db.csv", "_import_date": "2025-06-25T23:55:20.717453", "_row_index": 7}, {"编号": "1-51", "定额项名称": "泥浆运输 每增1km", "工作内容": "装卸泥浆、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_2475f0db.csv", "_import_date": "2025-06-25T23:55:20.717476", "_row_index": 8}, {"编号": "1-52", "定额项名称": "旧路材料等运输 装车", "工作内容": "装卸拆挖后旧路材料、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 1.3398, "_source_file": "parent_quotas_2475f0db.csv", "_import_date": "2025-06-25T23:55:20.717500", "_row_index": 9}, {"编号": "1-53", "定额项名称": "旧路材料等运输 运距1km以内", "工作内容": "装卸拆挖后旧路材料、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_2475f0db.csv", "_import_date": "2025-06-25T23:55:20.717522", "_row_index": 10}, {"编号": "1-54", "定额项名称": "旧路材料等运输 每增1km", "工作内容": "装卸拆挖后旧路材料、清理、运输等。", "单位": "m³", "综合单价（元/单位）": 0.0, "_source_file": "parent_quotas_2475f0db.csv", "_import_date": "2025-06-25T23:55:20.717545", "_row_index": 11}]}, "child_resources_066ad134": {"metadata": {"source_file": "child_resources_066ad134.csv", "document_count": 12, "fields": ["定额编号", "资源编号", "类别", "子项名称", "单位", "消耗量", "单价", "合价"], "collection_name": "child_resources_066ad134"}, "documents": [{"定额编号": "1-1", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.187, "单价": 0, "合价": 0.0, "_source_file": "child_resources_066ad134.csv", "_import_date": "2025-06-25T23:55:20.721172", "_row_index": 0}, {"定额编号": "1-1", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钉机", "单位": "台班", "消耗量": 0.0039, "单价": 0, "合价": 0.0, "_source_file": "child_resources_066ad134.csv", "_import_date": "2025-06-25T23:55:20.721213", "_row_index": 1}, {"定额编号": "1-2", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.274, "单价": 0, "合价": 0.0, "_source_file": "child_resources_066ad134.csv", "_import_date": "2025-06-25T23:55:20.721244", "_row_index": 2}, {"定额编号": "1-2", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钉机", "单位": "台班", "消耗量": 0.0048, "单价": 0, "合价": 0.0, "_source_file": "child_resources_066ad134.csv", "_import_date": "2025-06-25T23:55:20.721273", "_row_index": 3}, {"定额编号": "1-3", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.361, "单价": 0, "合价": 0.0, "_source_file": "child_resources_066ad134.csv", "_import_date": "2025-06-25T23:55:20.721299", "_row_index": 4}, {"定额编号": "1-3", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钉机", "单位": "台班", "消耗量": 0.0056, "单价": 0, "合价": 0.0, "_source_file": "child_resources_066ad134.csv", "_import_date": "2025-06-25T23:55:20.721324", "_row_index": 5}, {"定额编号": "1-11", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.274, "单价": 0, "合价": 0.0, "_source_file": "child_resources_066ad134.csv", "_import_date": "2025-06-25T23:55:20.721349", "_row_index": 6}, {"定额编号": "1-11", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钉机", "单位": "台班", "消耗量": 0.0048, "单价": 0, "合价": 0.0, "_source_file": "child_resources_066ad134.csv", "_import_date": "2025-06-25T23:55:20.721375", "_row_index": 7}, {"定额编号": "1-12", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.361, "单价": 0, "合价": 0.0, "_source_file": "child_resources_066ad134.csv", "_import_date": "2025-06-25T23:55:20.721400", "_row_index": 8}, {"定额编号": "1-12", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钉机", "单位": "台班", "消耗量": 0.0056, "单价": 0, "合价": 0.0, "_source_file": "child_resources_066ad134.csv", "_import_date": "2025-06-25T23:55:20.721426", "_row_index": 9}, {"定额编号": "1-13", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日", "消耗量": 0.361, "单价": 0, "合价": 0.0, "_source_file": "child_resources_066ad134.csv", "_import_date": "2025-06-25T23:55:20.721451", "_row_index": 10}, {"定额编号": "1-13", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钉机", "单位": "台班", "消耗量": 0.0056, "单价": 0, "合价": 0.0, "_source_file": "child_resources_066ad134.csv", "_import_date": "2025-06-25T23:55:20.721476", "_row_index": 11}]}, "child_resources_0e9d2977": {"metadata": {"source_file": "child_resources_0e9d2977.csv", "document_count": 3, "fields": ["定额编号", "资源编号", "类别", "子项名称", "单位", "消耗量", "单价", "合价"], "collection_name": "child_resources_0e9d2977"}, "documents": [{"定额编号": "1-1", "资源编号": 10701, "类别": "人工", "子项名称": "综合用工三类", "单位": "工日/m³", "消耗量": 0.187, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_0e9d2977.csv", "_import_date": "2025-06-25T23:55:20.724516", "_row_index": 0}, {"定额编号": "1-1", "资源编号": 99030030, "类别": "机械", "子项名称": "电动打钎机", "单位": "台班/m³", "消耗量": 0.0039, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_0e9d2977.csv", "_import_date": "2025-06-25T23:55:20.724554", "_row_index": 1}, {"定额编号": "1-1", "资源编号": 99460004, "类别": "其他费用", "子项名称": "其他机具费占人工费", "单位": "%", "消耗量": 1.5, "单价": 0.0, "合价": 0.0, "_source_file": "child_resources_0e9d2977.csv", "_import_date": "2025-06-25T23:55:20.724583", "_row_index": 2}]}}}