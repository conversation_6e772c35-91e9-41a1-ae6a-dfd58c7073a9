#!/usr/bin/env python3
"""
系统测试脚本
用于验证各个模块是否正常工作
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        from src.config import Config
        print("✅ Config模块导入成功")
        
        from src.pdf_processor import PDFProcessor
        print("✅ PDFProcessor模块导入成功")
        
        from src.browser_automation import BrowserAutomation
        print("✅ BrowserAutomation模块导入成功")
        
        from src.data_processor import DataProcessor
        print("✅ DataProcessor模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_config():
    """测试配置"""
    print("\n🔍 测试配置...")
    
    try:
        from src.config import Config
        config = Config()
        
        print(f"✅ 基础目录: {config.BASE_DIR}")
        print(f"✅ 临时目录: {config.TEMP_DIR}")
        print(f"✅ 输出目录: {config.OUTPUT_DIR}")
        print(f"✅ DeepSeek URL: {config.DEEPSEEK_URL}")
        
        # 检查目录是否存在
        if config.TEMP_DIR.exists():
            print("✅ 临时目录已创建")
        else:
            print("❌ 临时目录不存在")
            
        if config.OUTPUT_DIR.exists():
            print("✅ 输出目录已创建")
        else:
            print("❌ 输出目录不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_pdf_processor():
    """测试PDF处理器"""
    print("\n🔍 测试PDF处理器...")
    
    try:
        from src.pdf_processor import PDFProcessor
        processor = PDFProcessor()
        
        print("✅ PDF处理器初始化成功")
        
        # 检查是否有测试PDF文件
        test_pdf = None
        for file in Path(".").glob("*.pdf"):
            test_pdf = str(file)
            break
        
        if test_pdf:
            print(f"✅ 找到测试PDF文件: {test_pdf}")
            
            # 测试PDF信息获取（不实际转换）
            try:
                from pdf2image import pdfinfo_from_path
                info = pdfinfo_from_path(test_pdf)
                print(f"✅ PDF页数: {info['Pages']}")
            except Exception as e:
                print(f"⚠️ PDF信息获取失败: {e}")
        else:
            print("⚠️ 未找到测试PDF文件")
        
        return True
        
    except Exception as e:
        print(f"❌ PDF处理器测试失败: {e}")
        return False

def test_browser_automation():
    """测试浏览器自动化"""
    print("\n🔍 测试浏览器自动化...")
    
    try:
        from src.browser_automation import BrowserAutomation
        automation = BrowserAutomation()
        
        print("✅ 浏览器自动化初始化成功")
        
        # 测试ChromeDriver
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.service import Service
            from selenium.webdriver.chrome.options import Options
            from webdriver_manager.chrome import ChromeDriverManager
            
            options = Options()
            options.add_argument('--headless')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=options)
            driver.get("https://www.google.com")
            driver.quit()
            
            print("✅ ChromeDriver测试成功")
            
        except Exception as e:
            print(f"❌ ChromeDriver测试失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 浏览器自动化测试失败: {e}")
        return False

def test_data_processor():
    """测试数据处理器"""
    print("\n🔍 测试数据处理器...")
    
    try:
        from src.data_processor import DataProcessor
        processor = DataProcessor()
        
        print("✅ 数据处理器初始化成功")
        
        # 测试JSON解析
        test_json = '''
        {
            "parent_quota": {
                "code": "01-001",
                "name": "挖土方",
                "work_content": "人工挖土方",
                "unit": "m³"
            },
            "resource_consumption": [
                {
                    "resource_code": "A001",
                    "category": "人工",
                    "name": "普通工",
                    "unit": "工日",
                    "consumption": "0.5"
                }
            ]
        }
        '''
        
        result = processor.parse_recognition_result(test_json, 1)
        if result:
            print(f"✅ JSON解析测试成功，解析出 {len(result)} 条数据")
        else:
            print("⚠️ JSON解析测试返回空结果")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据处理器测试失败: {e}")
        return False

def test_dependencies():
    """测试依赖包"""
    print("\n🔍 测试依赖包...")
    
    dependencies = [
        ("gradio", "Gradio Web框架"),
        ("pdf2image", "PDF转图片"),
        ("PIL", "图片处理"),
        ("pandas", "数据处理"),
        ("selenium", "浏览器自动化"),
        ("cv2", "OpenCV图像处理"),
        ("numpy", "数值计算"),
        ("requests", "HTTP请求"),
        ("webdriver_manager", "WebDriver管理")
    ]
    
    success_count = 0
    
    for package, description in dependencies:
        try:
            __import__(package)
            print(f"✅ {description} ({package})")
            success_count += 1
        except ImportError:
            print(f"❌ {description} ({package}) - 未安装")
    
    print(f"\n依赖包测试结果: {success_count}/{len(dependencies)} 成功")
    
    return success_count == len(dependencies)

async def test_async_functions():
    """测试异步函数"""
    print("\n🔍 测试异步函数...")
    
    try:
        from src.pdf_processor import PDFProcessor
        from src.browser_automation import BrowserAutomation
        
        pdf_processor = PDFProcessor()
        browser_automation = BrowserAutomation()
        
        print("✅ 异步模块初始化成功")
        
        # 测试异步函数调用（不执行实际操作）
        print("✅ 异步函数测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 异步函数测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 北京市消耗定额智能提取系统 - 系统测试")
    print("=" * 60)
    
    tests = [
        ("依赖包", test_dependencies),
        ("模块导入", test_imports),
        ("配置", test_config),
        ("PDF处理器", test_pdf_processor),
        ("浏览器自动化", test_browser_automation),
        ("数据处理器", test_data_processor),
        ("异步函数", lambda: asyncio.run(test_async_functions()))
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常运行。")
        print("\n启动系统:")
        print("python main.py")
    else:
        print("⚠️ 部分测试失败，请检查安装和配置。")
        print("\n建议:")
        print("1. 运行安装脚本: python install.py")
        print("2. 检查依赖包安装")
        print("3. 确认Chrome浏览器已安装")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
