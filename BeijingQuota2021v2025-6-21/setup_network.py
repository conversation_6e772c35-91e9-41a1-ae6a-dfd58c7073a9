#!/usr/bin/env python3
"""
网络配置脚本 - 帮助解决AI模型API调用的网络问题
"""

import os
import sys
import requests
import time
from pathlib import Path

def clear_proxy_settings():
    """清除可能导致问题的代理设置"""
    proxy_vars = ['http_proxy', 'https_proxy', 'HTTP_PROXY', 'HTTPS_PROXY', 'ftp_proxy', 'FTP_PROXY']
    
    print("🔧 清除代理设置...")
    for var in proxy_vars:
        if var in os.environ:
            print(f"   移除环境变量: {var}")
            del os.environ[var]
    
    print("✅ 代理设置已清除")

def test_network_connectivity():
    """测试网络连接性"""
    print("\n🌐 测试网络连接...")
    
    test_urls = [
        ("百度", "https://www.baidu.com"),
        ("阿里云", "https://dashscope.aliyuncs.com"),
        ("OpenAI", "https://api.openai.com"),
        ("DeepSeek", "https://api.deepseek.com")
    ]
    
    results = {}
    
    for name, url in test_urls:
        try:
            print(f"   测试 {name} ({url})...")
            response = requests.get(url, timeout=10, verify=False)
            if response.status_code in [200, 302, 404]:  # 404也表示能连通
                results[name] = "✅ 连接正常"
            else:
                results[name] = f"⚠️ 状态码: {response.status_code}"
        except requests.exceptions.SSLError as e:
            results[name] = f"🔒 SSL错误: {str(e)[:50]}..."
        except requests.exceptions.ProxyError as e:
            results[name] = f"🚫 代理错误: {str(e)[:50]}..."
        except requests.exceptions.ConnectionError as e:
            results[name] = f"❌ 连接错误: {str(e)[:50]}..."
        except requests.exceptions.Timeout:
            results[name] = "⏱️ 连接超时"
        except Exception as e:
            results[name] = f"❓ 未知错误: {str(e)[:50]}..."
    
    print("\n📊 网络连接测试结果:")
    for name, result in results.items():
        print(f"   {name}: {result}")
    
    return results

def setup_ssl_config():
    """配置SSL设置"""
    print("\n🔐 配置SSL设置...")
    
    # 创建SSL配置文件
    ssl_config = """
# SSL配置建议
# 如果遇到SSL错误，可以尝试以下解决方案：

1. 更新CA证书：
   pip install --upgrade certifi

2. 设置环境变量（临时解决方案）：
   export PYTHONHTTPSVERIFY=0
   或在Python代码中：
   import ssl
   ssl._create_default_https_context = ssl._create_unverified_context

3. 使用系统证书库：
   export SSL_CERT_FILE=$(python -m certifi)
   export REQUESTS_CA_BUNDLE=$(python -m certifi)
"""
    
    ssl_config_path = Path("ssl_config.txt")
    ssl_config_path.write_text(ssl_config, encoding='utf-8')
    print(f"   SSL配置已保存到: {ssl_config_path}")

def setup_environment_variables():
    """设置环境变量"""
    print("\n⚙️ 配置环境变量...")
    
    env_file = Path(".env")
    
    # 读取现有环境变量
    env_content = ""
    if env_file.exists():
        env_content = env_file.read_text(encoding='utf-8')
    
    # 添加网络相关配置
    network_config = """
# 网络配置
PYTHONHTTPSVERIFY=1
REQUESTS_TIMEOUT=120
REQUESTS_RETRIES=3

# SSL配置（如果遇到SSL问题，可以临时设置为0）
PYTHONHTTPSVERIFY=1
"""
    
    if "# 网络配置" not in env_content:
        env_content += network_config
        env_file.write_text(env_content, encoding='utf-8')
        print("   环境变量配置已添加到 .env 文件")
    else:
        print("   环境变量配置已存在")

def fix_common_issues():
    """修复常见网络问题"""
    print("\n🔧 修复常见网络问题...")
    
    # 更新requests和urllib3
    print("   更新网络相关库...")
    try:
        import subprocess
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "requests", "urllib3", "certifi"], 
                      capture_output=True, text=True)
        print("   ✅ 网络库已更新")
    except Exception as e:
        print(f"   ❌ 更新失败: {e}")
    
    # 创建网络修复脚本
    fix_script = """#!/usr/bin/env python3
import os
import ssl
import requests
from urllib3.util import ssl_

# 修复SSL证书问题
try:
    import certifi
    os.environ['SSL_CERT_FILE'] = certifi.where()
    os.environ['REQUESTS_CA_BUNDLE'] = certifi.where()
    print("SSL证书路径已设置")
except ImportError:
    print("请安装certifi: pip install certifi")

# 禁用SSL警告
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

print("网络修复脚本执行完成")
"""
    
    fix_script_path = Path("fix_network.py")
    fix_script_path.write_text(fix_script, encoding='utf-8')
    print(f"   网络修复脚本已创建: {fix_script_path}")

def test_dashscope_api():
    """测试阿里云百炼API连接"""
    print("\n🧪 测试阿里云百炼API连接...")
    
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if not api_key:
        print("   ⚠️ 未找到DASHSCOPE_API_KEY环境变量")
        print("   请设置API密钥: export DASHSCOPE_API_KEY=your_api_key")
        return False
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 简单的测试请求
    test_data = {
        "model": "qwen-max",
        "messages": [{"role": "user", "content": "Hello"}],
        "max_tokens": 10
    }
    
    try:
        response = requests.post(
            "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions",
            headers=headers,
            json=test_data,
            timeout=30,
            verify=False  # 临时禁用SSL验证进行测试
        )
        
        if response.status_code == 200:
            print("   ✅ 阿里云百炼API连接正常")
            return True
        else:
            print(f"   ❌ API返回错误: {response.status_code} - {response.text[:100]}")
            return False
            
    except Exception as e:
        print(f"   ❌ 连接失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 北京定额提取系统 - 网络配置工具")
    print("=" * 50)
    
    # 1. 清除代理设置
    clear_proxy_settings()
    
    # 2. 测试网络连接
    network_results = test_network_connectivity()
    
    # 3. 配置SSL
    setup_ssl_config()
    
    # 4. 设置环境变量
    setup_environment_variables()
    
    # 5. 修复常见问题
    fix_common_issues()
    
    # 6. 测试阿里云API
    dashscope_ok = test_dashscope_api()
    
    print("\n" + "=" * 50)
    print("📋 网络配置完成")
    print()
    
    # 给出建议
    ssl_issues = any("SSL错误" in result for result in network_results.values())
    proxy_issues = any("代理错误" in result for result in network_results.values())
    
    if ssl_issues:
        print("🔒 检测到SSL问题，建议:")
        print("   1. 运行: pip install --upgrade certifi")
        print("   2. 如果问题持续，可以临时禁用SSL验证")
        print("   3. 检查系统时间是否正确")
    
    if proxy_issues:
        print("🚫 检测到代理问题，建议:")
        print("   1. 检查网络代理设置")
        print("   2. 尝试直连或更换网络")
        print("   3. 联系网络管理员")
    
    if not dashscope_ok:
        print("⚠️ 阿里云API连接异常，建议:")
        print("   1. 检查API密钥是否正确")
        print("   2. 确认账户余额充足")
        print("   3. 检查API服务状态")
    
    print("\n💡 如果问题仍然存在，请:")
    print("   1. 运行: python fix_network.py")
    print("   2. 重启终端/命令行")
    print("   3. 重新启动系统")

if __name__ == "__main__":
    main() 