# 信息价识别功能使用指南

## 🎯 功能位置

信息价识别功能已成功集成到系统中，您可以按以下步骤找到并使用：

### 1. 启动系统
```bash
python main.py
```

### 2. 访问Web界面
- 打开浏览器访问：http://localhost:7864
- 系统已在运行，可直接访问

### 3. 导航到信息价识别功能
1. 在主界面中，向下滚动找到 **"⚡高级定额管理系统"** 部分
2. 在高级定额管理系统中，您会看到多个标签页：
   - 🔗 数据库连接
   - 🗂️ 数据库浏览  
   - 🔍 智能搜索
   - 💰 资源价格
   - **📊 信息价识别** ← 这就是新增的功能！

## 🚀 使用步骤

### 第一步：信息价识别
1. 点击 **"📊 信息价识别"** 标签页
2. 在 **"💰信息价识别处理"** 区域：
   - 上传北京市造价信息PDF文件
   - 设置开始页码和结束页码
   - 选择AI模型（推荐：阿里通义千问-QVQ-Max）
   - 点击 **"🚀 开始识别信息价"**

### 第二步：查看识别结果
- 系统会显示处理状态和统计信息
- 识别完成后会显示数据预览
- 可以下载生成的CSV和JSON文件

### 第三步：数据合并（可选）
1. 在同一页面下方的 **"🔗信息价与定额数据合并"** 区域
2. 点击 **"🔄 刷新文件列表"** 获取最新文件
3. 选择信息价文件和定额资源文件
4. 点击 **"🔗 开始合并"**
5. 下载合并结果文件

## 📊 功能特点

### 智能识别能力
- **页眉标识识别**：自动识别工程造价信息、市场参考价、厂家参考价等
- **章节分类识别**：如"1．黑色及有色金属（编码：01）"
- **表格数据提取**：资源编号、产品名称、规格型号、价格等

### 数据处理能力
- **多格式输出**：CSV、JSON格式
- **数据验证**：自动验证数据完整性
- **智能合并**：基于资源编号匹配定额数据

### AI模型支持
- **阿里通义千问-QVQ-Max**：云端高精度模型
- **LM Studio本地模型**：本地部署的qwen2.5-vl-7b

## 🔧 配置要求

### API密钥（使用QVQ模型时）
确保已设置环境变量：
```bash
DASHSCOPE_API_KEY=your_api_key_here
```

### 本地模型（使用LM Studio时）
确保LM Studio在1234端口运行

## 📁 输出文件

### 信息价识别结果
- `price_info_result_YYYYMMDD_HHMMSS.csv`：CSV格式的识别结果
- `price_info_result_YYYYMMDD_HHMMSS.json`：JSON格式的识别结果

### 合并结果
- `merged_quota_price_info_YYYYMMDD_HHMMSS.csv`：合并后的完整数据

## 🎯 使用场景

### 1. 价格信息更新
- 定期处理北京市最新造价信息
- 自动提取价格数据到数据库
- 更新定额资源价格

### 2. 成本分析
- 对比不同时期的价格变化
- 分析材料价格趋势
- 支持项目成本预算

### 3. 数据管理
- 建立完整的价格信息数据库
- 实现价格信息的标准化管理
- 支持多维度查询分析

## 🔍 故障排除

### 常见问题

#### 1. 找不到"📊 信息价识别"标签页
- **解决方案**：确保向下滚动到"⚡高级定额管理系统"部分
- **位置**：在主界面的下半部分，不在顶部的主要标签页中

#### 2. API请求失败（400错误）
- **原因**：API密钥未设置或无效
- **解决方案**：检查DASHSCOPE_API_KEY环境变量

#### 3. 识别结果为空
- **原因**：PDF图片质量差或格式不支持
- **解决方案**：尝试不同的页码范围或使用更清晰的PDF

#### 4. 合并匹配率低
- **原因**：资源编号格式不一致
- **解决方案**：检查资源编号的格式和完整性

## 📈 最佳实践

### 1. PDF处理建议
- 使用高质量的PDF文件
- 先测试1-2页验证效果
- 避免处理过多页面（建议每次不超过10页）

### 2. 数据质量保证
- 定期验证识别结果的准确性
- 建立数据质量检查流程
- 保留原始PDF文件作为备份

### 3. 系统性能优化
- 合理设置页码范围
- 避免同时运行多个识别任务
- 定期清理临时文件

## 🎉 功能验证

系统已通过以下测试：
- ✅ 模拟数据处理测试
- ✅ 数据库存储功能测试  
- ✅ 数据合并功能测试
- ✅ 界面集成测试
- ✅ 服务器运行状态测试

匹配率：83.3%（模拟数据测试结果）

## 📞 技术支持

如果您在使用过程中遇到问题：

1. **检查系统状态**：运行 `python verify_price_info_interface.py`
2. **查看日志**：检查控制台输出的错误信息
3. **测试功能**：运行 `python test_price_info_simple.py` 进行功能测试

---

**🌟 现在就开始使用信息价识别功能吧！**

访问 http://localhost:7864，找到"⚡高级定额管理系统"中的"📊 信息价识别"标签页开始体验。
