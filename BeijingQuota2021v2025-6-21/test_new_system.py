#!/usr/bin/env python3
"""
测试新的AI模型系统
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """测试导入"""
    print("🔍 测试模块导入...")
    
    try:
        from src.config import Config
        print("✅ Config导入成功")
        
        from src.pdf_processor import PDFProcessor
        print("✅ PDFProcessor导入成功")
        
        from src.ai_model_processor import AIModelProcessor
        print("✅ AIModelProcessor导入成功")
        
        from src.data_processor import DataProcessor
        print("✅ DataProcessor导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_ai_processor():
    """测试AI处理器"""
    print("\n🔍 测试AI处理器...")
    
    try:
        from src.ai_model_processor import AIModelProcessor
        
        processor = AIModelProcessor()
        print("✅ AI处理器初始化成功")
        
        # 获取可用模型
        available_models = processor.get_available_models()
        print(f"✅ 可用模型数量: {len(available_models)}")
        
        for model_id, model_name in available_models.items():
            print(f"  • {model_name} ({model_id})")
        
        if not available_models:
            print("⚠️ 没有可用的AI模型")
            print("请配置API密钥或启动Ollama服务")
        
        return True
    except Exception as e:
        print(f"❌ AI处理器测试失败: {e}")
        return False

def test_gradio_simple():
    """测试简单的Gradio界面"""
    print("\n🔍 测试Gradio界面...")
    
    try:
        import gradio as gr
        
        def simple_function(text):
            return f"处理结果: {text}"
        
        # 创建简单界面
        interface = gr.Interface(
            fn=simple_function,
            inputs=gr.Textbox(label="输入"),
            outputs=gr.Textbox(label="输出"),
            title="测试界面"
        )
        
        print("✅ Gradio界面创建成功")
        
        # 不启动，只测试创建
        return True
    except Exception as e:
        print(f"❌ Gradio测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 新AI模型系统测试")
    print("=" * 40)
    
    tests = [
        ("模块导入", test_imports),
        ("AI处理器", test_ai_processor),
        ("Gradio界面", test_gradio_simple)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 40)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！新系统可以正常运行。")
    else:
        print("⚠️ 部分测试失败，请检查配置。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
