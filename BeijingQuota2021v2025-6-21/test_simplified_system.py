#!/usr/bin/env python3
"""
测试简化后的AI模型系统
"""

import sys
import os
from pathlib import Path

# 添加src目录到路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_simplified_ai_processor():
    """测试简化的AI处理器"""
    print("🧪 测试简化的AI处理器")
    print("=" * 50)
    
    try:
        from src.ai_model_processor import AIModelProcessor
        
        # 创建AI处理器
        processor = AIModelProcessor()
        
        print("✅ AI处理器创建成功")
        
        # 检查支持的模型
        print(f"\n📋 支持的模型:")
        for model_id, model_name in processor.supported_models.items():
            print(f"  • {model_id}: {model_name}")
        
        # 检查可用模型
        print(f"\n📋 可用模型:")
        available_models = processor.get_available_models()
        if available_models:
            for model_id, model_name in available_models.items():
                print(f"  • {model_id}: {model_name}")
        else:
            print("  ⚠️ 无可用模型 (需要配置API密钥或启动本地服务)")
        
        # 测试LM Studio检查
        print(f"\n🔍 LM Studio qwen2.5-vl-7b检查:")
        if processor._check_lm_studio_qwen_available():
            print("  ✅ LM Studio qwen2.5-vl-7b可用")
        else:
            print("  ⚠️ LM Studio qwen2.5-vl-7b不可用")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simplified_interface():
    """测试简化的界面"""
    print(f"\n🎨 测试简化的界面")
    print("=" * 40)
    
    try:
        # 检查主文件中的简化结构
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查简化的组件
        simplified_components = [
            ("阿里通义千问-QVQ-Max.*在线", "千问QVQ-Max选项"),
            ("LM Studio.*qwen2.5-vl-7b.*本地", "LM Studio qwen2.5-vl-7b选项"),
            ("API密钥.*仅千问QVQ-Max需要", "API密钥输入框"),
            ("本地模型无需API密钥", "本地模型提示"),
            ("推荐使用千问QVQ-Max", "推荐提示"),
            ("save_api_key", "API密钥保存功能")
        ]
        
        import re
        
        print("📋 检查简化组件:")
        for pattern, description in simplified_components:
            if re.search(pattern, content):
                print(f"✅ {description}: 已实现")
            else:
                print(f"❌ {description}: 未实现")
        
        # 检查是否移除了复杂配置
        removed_components = [
            ("provider_select", "Provider选择器"),
            ("config_modal", "配置模态框"),
            ("test_connection_btn", "测试连接按钮"),
            ("dashscope_config", "阿里云配置区域"),
            ("deepseek_config", "DeepSeek配置区域"),
            ("openai_config", "OpenAI配置区域")
        ]
        
        print(f"\n📋 检查移除的复杂组件:")
        for pattern, description in removed_components:
            if pattern not in content:
                print(f"✅ {description}: 已移除")
            else:
                print(f"⚠️ {description}: 仍存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_model_choices():
    """测试模型选择"""
    print(f"\n🎯 测试模型选择")
    print("=" * 30)
    
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查模型选择的choices
        expected_choices = [
            ("阿里通义千问-QVQ-Max.*在线.*qwen_qvq_max", "千问QVQ-Max选项"),
            ("LM Studio.*qwen2.5-vl-7b.*本地.*lm_studio_qwen2_5_vl_7b", "LM Studio选项"),
            ("value.*qwen_qvq_max", "默认选择千问QVQ-Max")
        ]
        
        import re
        
        print("📋 检查模型选择:")
        for pattern, description in expected_choices:
            if re.search(pattern, content):
                print(f"✅ {description}: 已配置")
            else:
                print(f"❌ {description}: 未配置")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_api_key_handling():
    """测试API密钥处理"""
    print(f"\n🔑 测试API密钥处理")
    print("=" * 35)
    
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查API密钥相关功能
        api_key_features = [
            ("def save_api_key", "API密钥保存函数"),
            ("DASHSCOPE_API_KEY", "阿里云API密钥环境变量"),
            ("reload_api_keys", "重新加载API密钥"),
            ("type.*password", "密码类型输入框"),
            ("save_api_key_btn", "保存按钮")
        ]
        
        import re
        
        print("📋 检查API密钥功能:")
        for pattern, description in api_key_features:
            if re.search(pattern, content):
                print(f"✅ {description}: 已实现")
            else:
                print(f"❌ {description}: 未实现")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_system_startup():
    """测试系统启动"""
    print(f"\n🚀 测试系统启动")
    print("=" * 30)
    
    try:
        # 检查系统是否正常启动
        import requests
        
        try:
            response = requests.get("http://0.0.0.0:7863", timeout=5)
            if response.status_code == 200:
                print("✅ 系统启动成功，界面可访问")
                return True
            else:
                print(f"⚠️ 系统响应异常: {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            print("⚠️ 无法连接到系统 (可能正在启动)")
            return False
        except Exception as e:
            print(f"⚠️ 连接测试失败: {e}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 简化AI模型系统测试")
    print("=" * 60)
    
    # 运行各项测试
    tests = [
        ("简化AI处理器", test_simplified_ai_processor),
        ("简化界面", test_simplified_interface),
        ("模型选择", test_model_choices),
        ("API密钥处理", test_api_key_handling),
        ("系统启动", test_system_startup)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        results[test_name] = test_func()
    
    # 汇总结果
    print(f"\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print(f"\n🎉 简化系统完全成功！")
        print(f"💡 主要简化:")
        print(f"   • 只保留千问QVQ-Max和LM Studio qwen2.5-vl-7b")
        print(f"   • 移除复杂的配置弹出窗口")
        print(f"   • 直接在主界面配置API密钥")
        print(f"   • 简化的模型选择和状态显示")
        print(f"   • 更清晰的用户体验")
        print(f"\n🚀 系统运行地址: http://0.0.0.0:7863")
    else:
        print(f"\n⚠️ 部分功能需要进一步检查")

if __name__ == "__main__":
    main()
