#!/usr/bin/env python3
"""
启动完整的北京市消耗定额智能提取系统
包含主功能界面和模型配置界面
"""

import subprocess
import sys
import time
import webbrowser
from pathlib import Path

def check_dependencies():
    """检查依赖"""
    try:
        import gradio
        import requests
        import pandas
        import PIL
        print("✅ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def start_main_interface():
    """启动主功能界面"""
    print("🚀 启动主功能界面...")
    try:
        process = subprocess.Popen([
            sys.executable, "main.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        return process
    except Exception as e:
        print(f"❌ 启动主界面失败: {e}")
        return None

def start_config_interface():
    """启动模型配置界面"""
    print("⚙️ 启动模型配置界面...")
    try:
        process = subprocess.Popen([
            sys.executable, "model_config_standalone.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        return process
    except Exception as e:
        print(f"❌ 启动配置界面失败: {e}")
        return None

def wait_for_service(url, timeout=30):
    """等待服务启动"""
    import requests
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            response = requests.get(url, timeout=1)
            if response.status_code == 200:
                return True
        except:
            pass
        time.sleep(1)
    return False

def main():
    """主函数"""
    print("🎉 北京市消耗定额智能提取系统")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 启动服务
    main_process = start_main_interface()
    config_process = start_config_interface()
    
    if not main_process or not config_process:
        print("❌ 启动失败")
        return
    
    print("\n⏳ 等待服务启动...")
    
    # 等待主界面启动
    if wait_for_service("http://localhost:7862"):
        print("✅ 主功能界面已启动: http://localhost:7862")
    else:
        print("⚠️ 主功能界面启动超时")
    
    # 等待配置界面启动
    if wait_for_service("http://localhost:7863"):
        print("✅ 模型配置界面已启动: http://localhost:7863")
    else:
        print("⚠️ 模型配置界面启动超时")
    
    print("\n" + "=" * 50)
    print("🌐 系统已启动，请在浏览器中访问:")
    print()
    print("📊 主功能界面 (定额提取): http://localhost:7862")
    print("⚙️ 模型配置界面 (API配置): http://localhost:7863")
    print()
    print("💡 使用流程:")
    print("1. 先访问 http://localhost:7863 配置AI模型")
    print("2. 配置完成后访问 http://localhost:7862 使用系统")
    print("3. 在主界面点击'刷新模型列表'按钮更新可用模型")
    print()
    print("⚠️ 注意: 关闭此窗口将停止所有服务")
    print("按 Ctrl+C 停止系统")
    
    # 自动打开浏览器
    try:
        time.sleep(2)
        webbrowser.open("http://localhost:7863")  # 先打开配置界面
        time.sleep(1)
        webbrowser.open("http://localhost:7862")  # 再打开主界面
    except:
        pass
    
    try:
        # 等待用户中断
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n\n🛑 正在停止系统...")
        
        # 停止进程
        if main_process:
            main_process.terminate()
            print("✅ 主功能界面已停止")
        
        if config_process:
            config_process.terminate()
            print("✅ 模型配置界面已停止")
        
        print("👋 系统已完全停止")

if __name__ == "__main__":
    main()
