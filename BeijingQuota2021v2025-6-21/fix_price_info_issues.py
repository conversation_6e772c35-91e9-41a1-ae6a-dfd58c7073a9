#!/usr/bin/env python3
"""
信息价识别问题排查和修复脚本
Troubleshoot and fix price information recognition issues
"""

import os
import sys
import requests
import json
import asyncio

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def check_api_key():
    """检查API密钥设置"""
    print("🔑 检查API密钥设置")
    print("=" * 40)
    
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if api_key:
        print(f"✅ 找到API密钥: {api_key[:10]}...{api_key[-4:]}")
        return api_key
    else:
        print("❌ 未设置DASHSCOPE_API_KEY环境变量")
        print("💡 请设置环境变量：")
        print("   Windows: set DASHSCOPE_API_KEY=your_api_key")
        print("   Linux/Mac: export DASHSCOPE_API_KEY=your_api_key")
        return None

def test_dashscope_sdk():
    """测试DashScope SDK"""
    print("\n🧪 测试DashScope SDK")
    print("=" * 40)
    
    try:
        import dashscope
        from dashscope import Generation
        print("✅ DashScope SDK 导入成功")
        
        api_key = os.getenv("DASHSCOPE_API_KEY")
        if not api_key:
            print("❌ 未设置API密钥，跳过SDK测试")
            return False
        
        dashscope.api_key = api_key
        
        # 测试简单的文本生成
        try:
            response = Generation.call(
                model='qwen-max',
                prompt='你好',
                max_tokens=10
            )
            
            if response.status_code == 200:
                print("✅ DashScope SDK 调用成功")
                return True
            else:
                print(f"❌ DashScope SDK 调用失败: {response.message}")
                return False
                
        except Exception as e:
            print(f"❌ DashScope SDK 调用异常: {str(e)}")
            return False
            
    except ImportError:
        print("❌ DashScope SDK 未安装")
        print("💡 请安装: pip install dashscope")
        return False

def test_network_connectivity():
    """测试网络连接"""
    print("\n🌐 测试网络连接")
    print("=" * 40)
    
    test_urls = [
        ("阿里云DashScope", "https://dashscope.aliyuncs.com"),
        ("LM Studio本地", "http://127.0.0.1:1234"),
        ("百度", "https://www.baidu.com"),
    ]
    
    results = []
    for name, url in test_urls:
        try:
            response = requests.get(url, timeout=5, proxies={})
            if response.status_code in [200, 404]:  # 404也表示能连接
                print(f"✅ {name}: 连接正常")
                results.append(True)
            else:
                print(f"⚠️ {name}: 状态码 {response.status_code}")
                results.append(False)
        except requests.exceptions.ProxyError:
            print(f"❌ {name}: 代理错误")
            results.append(False)
        except requests.exceptions.ConnectionError:
            print(f"❌ {name}: 连接失败")
            results.append(False)
        except Exception as e:
            print(f"❌ {name}: {str(e)}")
            results.append(False)
    
    return results

def test_lm_studio():
    """测试LM Studio连接"""
    print("\n🤖 测试LM Studio")
    print("=" * 40)
    
    try:
        # 检查模型列表
        response = requests.get("http://127.0.0.1:1234/v1/models", timeout=5)
        if response.status_code == 200:
            models = response.json()
            print("✅ LM Studio 连接成功")
            print(f"📋 可用模型: {len(models.get('data', []))}")
            for model in models.get('data', []):
                print(f"   - {model.get('id', 'Unknown')}")
            return True
        else:
            print(f"❌ LM Studio 响应异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ LM Studio 连接失败: {str(e)}")
        print("💡 请确保:")
        print("   1. LM Studio 已启动")
        print("   2. 服务运行在 1234 端口")
        print("   3. 已加载视觉语言模型 (如 qwen2.5-vl-7b)")
        return False

def test_price_info_processor():
    """测试信息价处理器"""
    print("\n🔧 测试信息价处理器")
    print("=" * 40)
    
    try:
        from src.intelligent_price_info_processor import IntelligentPriceInfoProcessor
        processor = IntelligentPriceInfoProcessor()
        print("✅ 信息价处理器导入成功")
        
        # 检查API密钥状态
        api_status = processor.get_api_key_status()
        print("🔑 API密钥状态:")
        for provider, is_set in api_status.items():
            status = "✅ 已设置" if is_set else "❌ 未设置"
            print(f"   {provider}: {status}")
        
        # 测试解析功能
        test_json = '{"page_header": "测试", "chapters": []}'
        result = processor._parse_price_info_result(test_json, 1)
        if result is not None:
            print("✅ 解析功能正常")
        else:
            print("❌ 解析功能异常")
        
        return True
        
    except Exception as e:
        print(f"❌ 信息价处理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def create_test_image():
    """创建测试图片"""
    print("\n🖼️ 创建测试图片")
    print("=" * 40)
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # 创建一个简单的测试图片
        width, height = 800, 600
        image = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(image)
        
        # 绘制表格框架
        draw.rectangle([50, 50, width-50, height-50], outline='black', width=2)
        
        # 添加文字
        try:
            font = ImageFont.truetype("arial.ttf", 20)
        except:
            font = ImageFont.load_default()
        
        draw.text((100, 80), "北京市造价信息测试", fill='black', font=font)
        draw.text((100, 120), "1. 黑色及有色金属（编码：01）", fill='black', font=font)
        draw.text((100, 160), "3001010401 网络摄像机 台 1200.00", fill='black', font=font)
        
        # 保存图片
        test_image_path = "test_price_info_image.png"
        image.save(test_image_path)
        print(f"✅ 测试图片已创建: {test_image_path}")
        
        return test_image_path
        
    except Exception as e:
        print(f"❌ 创建测试图片失败: {str(e)}")
        return None

async def test_ai_processing():
    """测试AI处理功能"""
    print("\n🤖 测试AI处理功能")
    print("=" * 40)
    
    # 创建测试图片
    test_image = create_test_image()
    if not test_image:
        print("❌ 无法创建测试图片，跳过AI测试")
        return False
    
    try:
        from src.intelligent_price_info_processor import IntelligentPriceInfoProcessor
        processor = IntelligentPriceInfoProcessor()
        
        # 测试LM Studio（如果可用）
        if test_lm_studio():
            print("\n🔄 测试LM Studio AI处理...")
            try:
                result = await processor._process_with_lm_studio(test_image)
                if result:
                    print("✅ LM Studio AI处理成功")
                    print(f"📄 结果长度: {len(result)} 字符")
                else:
                    print("❌ LM Studio AI处理失败")
            except Exception as e:
                print(f"❌ LM Studio AI处理异常: {str(e)}")
        
        # 测试DashScope（如果可用）
        if os.getenv("DASHSCOPE_API_KEY"):
            print("\n🔄 测试DashScope AI处理...")
            try:
                result = await processor._process_with_qwen_qvq(test_image)
                if result:
                    print("✅ DashScope AI处理成功")
                    print(f"📄 结果长度: {len(result)} 字符")
                else:
                    print("❌ DashScope AI处理失败")
            except Exception as e:
                print(f"❌ DashScope AI处理异常: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI处理测试失败: {str(e)}")
        return False
    finally:
        # 清理测试图片
        if test_image and os.path.exists(test_image):
            try:
                os.remove(test_image)
                print(f"🧹 已清理测试图片")
            except:
                pass

def provide_solutions():
    """提供解决方案"""
    print("\n💡 问题解决方案")
    print("=" * 40)
    
    print("🔧 针对 'current user api does not support http call' 错误:")
    print("   1. 安装DashScope SDK: pip install dashscope")
    print("   2. 使用SDK调用而不是HTTP调用")
    print("   3. 检查API密钥权限，联系阿里云客服")
    print("   4. 尝试使用LM Studio本地模型")
    
    print("\n🌐 针对网络连接问题:")
    print("   1. 检查网络连接")
    print("   2. 禁用代理设置")
    print("   3. 尝试使用VPN")
    print("   4. 使用本地模型避免网络问题")
    
    print("\n🤖 针对LM Studio问题:")
    print("   1. 确保LM Studio已启动")
    print("   2. 检查端口1234是否被占用")
    print("   3. 加载支持视觉的模型")
    print("   4. 检查模型是否正确加载")
    
    print("\n📦 针对依赖问题:")
    print("   1. 运行: python install_price_info_dependencies.py")
    print("   2. 手动安装: pip install dashscope PyMuPDF Pillow")
    print("   3. 检查Python版本兼容性")

def main():
    """主函数"""
    print("🔍 信息价识别问题排查和修复")
    print("=" * 60)
    
    # 运行检查
    checks = [
        ("API密钥", check_api_key),
        ("DashScope SDK", test_dashscope_sdk),
        ("网络连接", test_network_connectivity),
        ("信息价处理器", test_price_info_processor),
    ]
    
    results = []
    for check_name, check_func in checks:
        print(f"\n{'='*20} {check_name} {'='*20}")
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ {check_name} 检查异常: {str(e)}")
            results.append((check_name, False))
    
    # AI处理测试
    print(f"\n{'='*20} AI处理测试 {'='*20}")
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        ai_result = loop.run_until_complete(test_ai_processing())
        results.append(("AI处理", ai_result))
    except Exception as e:
        print(f"❌ AI处理测试异常: {str(e)}")
        results.append(("AI处理", False))
    finally:
        loop.close()
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 检查总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for check_name, result in results:
        if isinstance(result, list):  # 网络连接返回列表
            passed_count = sum(result)
            total_count = len(result)
            status = f"✅ {passed_count}/{total_count}" if passed_count > 0 else "❌ 全部失败"
        else:
            status = "✅ 通过" if result else "❌ 失败"
        print(f"   {check_name}: {status}")
    
    print(f"\n🎯 总体状态: {passed}/{total} 检查通过")
    
    # 提供解决方案
    provide_solutions()

if __name__ == "__main__":
    main()
