#!/usr/bin/env python3
"""
测试多定额项识别修复效果
"""

import json
from src.data_processor import DataProcessor

def test_multi_quota_parsing():
    """测试多定额项解析"""
    
    # 模拟AI返回的多定额项JSON数据
    mock_response = """
    {
        "quotas": [
            {
                "parent_quota": {
                    "code": "1-11",
                    "name": "人工挖基坑土方 一、二类土",
                    "work_content": "挖土、余土清理、修整底边、打钉拍底等",
                    "unit": "m³"
                },
                "resource_consumption": [
                    {
                        "resource_code": "00010701",
                        "category": "人工",
                        "name": "综合用工三类",
                        "unit": "工日",
                        "consumption": "0.200"
                    },
                    {
                        "resource_code": "99030030",
                        "category": "机械",
                        "name": "电动打桩机",
                        "unit": "台班",
                        "consumption": "0.0039"
                    },
                    {
                        "resource_code": "99460004",
                        "category": "机械",
                        "name": "其他机具费 占人工费",
                        "unit": "%",
                        "consumption": "1.50"
                    }
                ]
            },
            {
                "parent_quota": {
                    "code": "1-12",
                    "name": "人工挖基坑土方 三类土",
                    "work_content": "挖土、余土清理、修整底边、打钉拍底等",
                    "unit": "m³"
                },
                "resource_consumption": [
                    {
                        "resource_code": "00010701",
                        "category": "人工",
                        "name": "综合用工三类",
                        "unit": "工日",
                        "consumption": "0.250"
                    },
                    {
                        "resource_code": "99030030",
                        "category": "机械",
                        "name": "电动打桩机",
                        "unit": "台班",
                        "consumption": "0.0048"
                    },
                    {
                        "resource_code": "99460004",
                        "category": "机械",
                        "name": "其他机具费 占人工费",
                        "unit": "%",
                        "consumption": "1.50"
                    }
                ]
            },
            {
                "parent_quota": {
                    "code": "1-13",
                    "name": "人工挖基坑土方 四类土",
                    "work_content": "挖土、余土清理、修整底边、打钉拍底等",
                    "unit": "m³"
                },
                "resource_consumption": [
                    {
                        "resource_code": "00010701",
                        "category": "人工",
                        "name": "综合用工三类",
                        "unit": "工日",
                        "consumption": "0.500"
                    },
                    {
                        "resource_code": "99030030",
                        "category": "机械",
                        "name": "电动打桩机",
                        "unit": "台班",
                        "consumption": "0.0056"
                    },
                    {
                        "resource_code": "99460004",
                        "category": "机械",
                        "name": "其他机具费 占人工费",
                        "unit": "%",
                        "consumption": "1.50"
                    }
                ]
            }
        ]
    }
    """
    
    print("🧪 测试多定额项识别修复效果")
    print("=" * 50)
    
    # 创建数据处理器
    processor = DataProcessor()
    
    # 解析模拟响应
    result = processor.parse_recognition_result(mock_response, page_number=1)
    
    print(f"📊 解析结果统计:")
    print(f"- 总记录数: {len(result)}")
    
    # 统计父级和子级记录
    parent_records = [r for r in result if r["type"] == "parent"]
    child_records = [r for r in result if r["type"] == "child"]
    
    print(f"- 父级定额项: {len(parent_records)}")
    print(f"- 子级资源项: {len(child_records)}")
    
    print("\n📋 详细结果:")
    print("-" * 30)
    
    # 按定额编号分组显示
    quota_groups = {}
    for record in result:
        quota_code = record["quota_code"]
        if quota_code not in quota_groups:
            quota_groups[quota_code] = {"parent": None, "children": []}
        
        if record["type"] == "parent":
            quota_groups[quota_code]["parent"] = record
        else:
            quota_groups[quota_code]["children"].append(record)
    
    for quota_code, group in quota_groups.items():
        parent = group["parent"]
        children = group["children"]
        
        print(f"\n🏷️  定额项: {quota_code}")
        if parent:
            print(f"   名称: {parent['quota_name']}")
            print(f"   单位: {parent['unit']}")
        
        print(f"   资源消耗 ({len(children)} 项):")
        for child in children:
            print(f"     - {child['resource_code']} {child['resource_name']} "
                  f"{child['consumption']} {child['resource_unit']}")
    
    # 验证期望结果
    expected_quotas = 3  # 应该有3个定额项
    expected_resources_per_quota = 3  # 每个定额项应该有3个资源消耗
    
    print(f"\n✅ 验证结果:")
    print(f"- 期望定额项数: {expected_quotas}, 实际: {len(parent_records)}")
    print(f"- 期望总资源项数: {expected_quotas * expected_resources_per_quota}, 实际: {len(child_records)}")
    
    success = (len(parent_records) == expected_quotas and 
               len(child_records) == expected_quotas * expected_resources_per_quota)
    
    if success:
        print("🎉 测试通过！多定额项识别修复成功！")
    else:
        print("❌ 测试失败！需要进一步检查。")
    
    return success

if __name__ == "__main__":
    test_multi_quota_parsing()
