#!/usr/bin/env python3
"""
测试智能信息价识别模块
验证独立的信息价识别处理器是否正常工作
"""

import os
import sys
import asyncio

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_processor_import():
    """测试处理器导入"""
    print("🧪 测试智能信息价识别处理器导入")
    print("=" * 50)
    
    try:
        from src.intelligent_price_info_processor import IntelligentPriceInfoProcessor
        processor = IntelligentPriceInfoProcessor()
        print("✅ IntelligentPriceInfoProcessor 导入成功")
        
        # 检查API密钥状态
        api_status = processor.get_api_key_status()
        print("🔑 API密钥状态:")
        for provider, is_set in api_status.items():
            status = "✅ 已设置" if is_set else "❌ 未设置"
            print(f"   {provider}: {status}")
        
        return True, processor
        
    except Exception as e:
        print(f"❌ 导入失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, None

def test_interface_import():
    """测试界面导入"""
    print("\n🧪 测试智能信息价识别界面导入")
    print("=" * 50)
    
    try:
        from src.intelligent_price_info_interface import IntelligentPriceInfoInterface
        interface = IntelligentPriceInfoInterface()
        print("✅ IntelligentPriceInfoInterface 导入成功")
        
        # 测试界面创建
        components = interface.create_price_info_recognition_interface()
        print(f"✅ 界面组件创建成功，组件数: {len(components)}")
        
        # 检查关键组件
        key_components = ['pdf_input', 'process_btn', 'status_output', 'preview_output']
        for comp in key_components:
            if comp in components:
                print(f"   ✅ {comp}: 存在")
            else:
                print(f"   ❌ {comp}: 缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_api_key_management(processor):
    """测试API密钥管理"""
    print("\n🧪 测试API密钥管理")
    print("=" * 50)
    
    try:
        # 测试设置API密钥
        test_key = "sk-test123456"
        result = processor.set_api_key("dashscope", test_key)
        
        if result:
            print("✅ API密钥设置成功")
            
            # 验证密钥是否设置
            status = processor.get_api_key_status()
            if status.get("dashscope"):
                print("✅ API密钥状态验证成功")
            else:
                print("❌ API密钥状态验证失败")
                return False
        else:
            print("❌ API密钥设置失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_parsing_methods(processor):
    """测试解析方法"""
    print("\n🧪 测试解析方法")
    print("=" * 50)
    
    try:
        # 测试JSON解析
        test_json = '''
        {
            "page_header": "工程造价信息",
            "chapters": [
                {
                    "chapter_code": "01",
                    "chapter_name": "黑色及有色金属",
                    "remarks": "",
                    "price_items": [
                        {
                            "resource_code": "3001010401",
                            "product_name": "网络球型摄像机",
                            "specifications": "1/3英寸CMOS，200万像素",
                            "unit": "台",
                            "price_with_tax": "1200.00",
                            "price_without_tax": "1061.95"
                        }
                    ]
                }
            ]
        }
        '''
        
        result = processor._parse_price_info_result(test_json, 1)
        if result:
            print(f"✅ JSON解析成功，章节数: {len(result)}")
            print(f"   价格条目数: {len(result[0]['price_items'])}")
        else:
            print("❌ JSON解析失败")
            return False
        
        # 测试文本解析
        test_text = """
        工程造价信息
        1. 黑色及有色金属（编码：01）
        3001010401 网络球型摄像机 1/3英寸CMOS，200万像素 台 1200.00 1061.95
        """
        
        result = processor._parse_price_info_result(test_text, 2)
        if result:
            print(f"✅ 文本解析成功，章节数: {len(result)}")
            if result[0]['price_items']:
                print(f"   价格条目数: {len(result[0]['price_items'])}")
            else:
                print("   ⚠️ 未提取到价格条目")
        else:
            print("❌ 文本解析失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_output_generation(processor):
    """测试输出文件生成"""
    print("\n🧪 测试输出文件生成")
    print("=" * 50)
    
    try:
        # 创建测试数据
        test_data = [
            {
                'page_number': 1,
                'page_header': '工程造价信息',
                'chapter_code': '01',
                'chapter_name': '黑色及有色金属',
                'remarks': '测试备注',
                'price_items': [
                    {
                        'resource_code': '3001010401',
                        'product_name': '网络球型摄像机',
                        'specifications': '1/3英寸CMOS，200万像素',
                        'unit': '台',
                        'price_with_tax': '1200.00',
                        'price_without_tax': '1061.95'
                    }
                ]
            }
        ]
        
        # 创建临时目录
        import tempfile
        with tempfile.TemporaryDirectory() as temp_dir:
            # 异步生成文件
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                output_files = loop.run_until_complete(
                    processor._generate_output_files(test_data, temp_dir)
                )
                
                if output_files:
                    print(f"✅ 文件生成成功，文件数: {len(output_files)}")
                    
                    for file_path in output_files:
                        if os.path.exists(file_path):
                            size_kb = os.path.getsize(file_path) / 1024
                            print(f"   📄 {os.path.basename(file_path)}: {size_kb:.1f} KB")
                        else:
                            print(f"   ❌ 文件不存在: {file_path}")
                            return False
                    
                    return True
                else:
                    print("❌ 未生成任何文件")
                    return False
                    
            finally:
                loop.close()
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_merge_functionality(processor):
    """测试合并功能"""
    print("\n🧪 测试合并功能")
    print("=" * 50)
    
    try:
        import tempfile
        import pandas as pd
        
        # 创建测试的信息价文件
        price_data = [
            {
                '页眉标识': '工程造价信息',
                '章节编号': '01',
                '章节名称': '黑色及有色金属',
                '资源编号': '3001010401',
                '产品名称': '网络球型摄像机',
                '规格型号及特征': '1/3英寸CMOS，200万像素',
                '计量单位': '台',
                '市场参考价（含税）': '1200.00',
                '市场参考价（不含税）': '1061.95',
                '备注': ''
            }
        ]
        
        # 创建测试的定额资源文件
        quota_data = [
            {
                '定额编号': '1-1',
                '资源编号': '3001010401',
                '资源类别': '材料',
                '子项名称': '网络球型摄像机',
                '单位': '台',
                '消耗量': '2.0',
                '单价': '0.0',
                '合价': '0.0'
            },
            {
                '定额编号': '1-2',
                '资源编号': '9999999999',
                '资源类别': '人工',
                '子项名称': '综合用工',
                '单位': '工日',
                '消耗量': '1.0',
                '单价': '120.0',
                '合价': '120.0'
            }
        ]
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 保存测试文件
            price_file = os.path.join(temp_dir, "test_price_info.csv")
            quota_file = os.path.join(temp_dir, "test_quota_resources.csv")
            
            pd.DataFrame(price_data).to_csv(price_file, index=False, encoding='utf-8-sig')
            pd.DataFrame(quota_data).to_csv(quota_file, index=False, encoding='utf-8-sig')
            
            # 执行合并
            success, message, output_file = processor.merge_price_info_with_quotas(
                price_file, quota_file, temp_dir
            )
            
            if success:
                print("✅ 合并功能测试成功")
                print(f"📋 合并结果: {message}")
                
                # 验证输出文件
                if os.path.exists(output_file):
                    df = pd.read_csv(output_file, encoding='utf-8-sig')
                    print(f"   📊 合并文件记录数: {len(df)}")
                    
                    # 检查匹配状态
                    if '匹配状态' in df.columns:
                        matched = len(df[df['匹配状态'] == '已匹配'])
                        print(f"   🎯 匹配成功: {matched} 条")
                    
                    return True
                else:
                    print(f"❌ 输出文件不存在: {output_file}")
                    return False
            else:
                print(f"❌ 合并失败: {message}")
                return False
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 智能信息价识别模块测试")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("处理器导入", test_processor_import),
        ("界面导入", test_interface_import),
    ]
    
    results = []
    processor = None
    
    for test_name, test_func in tests:
        try:
            if test_name == "处理器导入":
                result, processor = test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 如果处理器导入成功，继续其他测试
    if processor:
        additional_tests = [
            ("API密钥管理", lambda: test_api_key_management(processor)),
            ("解析方法", lambda: test_parsing_methods(processor)),
            ("输出文件生成", lambda: test_output_generation(processor)),
            ("合并功能", lambda: test_merge_functionality(processor))
        ]
        
        for test_name, test_func in additional_tests:
            try:
                result = test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"❌ {test_name} 测试异常: {str(e)}")
                results.append((test_name, False))
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 测试总结:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！智能信息价识别模块已准备就绪。")
        print("💡 现在可以在Web界面中使用独立的信息价识别功能。")
        print("🌐 访问 http://localhost:7864 查看新的界面")
    elif passed >= total - 1:
        print("✅ 基本功能正常！可能有个别小问题。")
        print("💡 建议在Web界面中测试实际功能。")
    else:
        print("⚠️ 存在多个问题，需要进一步检查。")

if __name__ == "__main__":
    main()
