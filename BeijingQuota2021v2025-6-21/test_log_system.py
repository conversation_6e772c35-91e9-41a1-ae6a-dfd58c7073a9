#!/usr/bin/env python3
"""
测试日志系统
Test log system
"""

import sys
import os

# 添加src目录到路径
sys.path.insert(0, 'src')

def test_log_manager():
    """测试日志管理器"""
    print("🧪 测试日志管理器...")
    
    try:
        from log_manager import get_log_manager, get_logger
        
        # 获取日志管理器
        log_manager = get_log_manager()
        print("✅ 日志管理器创建成功")
        
        # 获取日志记录器
        logger = get_logger('TestModule')
        print("✅ 日志记录器获取成功")
        
        # 测试不同级别的日志
        logger.debug("这是一条调试信息")
        logger.info("这是一条信息日志")
        logger.warning("这是一条警告信息")
        logger.error("这是一条错误信息")
        
        print("✅ 日志记录测试完成")
        
        # 等待日志处理
        import time
        time.sleep(1)
        
        # 获取日志历史
        recent_logs = log_manager.get_recent_logs(10)
        print(f"✅ 获取到 {len(recent_logs)} 条日志记录")
        
        # 显示日志
        for log in recent_logs:
            print(f"   {log['timestamp']} | {log['level']} | {log['module']} | {log['message']}")
        
        # 测试格式化显示
        formatted = log_manager.format_logs_for_display(recent_logs)
        print("✅ 日志格式化测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 日志管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gradio_integration():
    """测试Gradio集成"""
    print("\n🧪 测试Gradio集成...")
    
    try:
        import gradio as gr
        from log_manager import get_log_manager, get_logger
        
        log_manager = get_log_manager()
        logger = get_logger('GradioTest')
        
        # 记录一些测试日志
        logger.info("开始Gradio集成测试")
        logger.warning("这是一个测试警告")
        logger.error("这是一个测试错误")
        
        def refresh_logs_test(level_filter, module_filter):
            """测试日志刷新功能"""
            try:
                if level_filter == "ALL":
                    logs = log_manager.get_recent_logs(50)
                else:
                    logs = log_manager.get_logs_by_level(level_filter, 50)
                
                if module_filter != "ALL":
                    logs = [log for log in logs if module_filter.lower() in log['module'].lower()]
                
                formatted = log_manager.format_logs_for_display(logs)
                stats = f"显示 {len(logs)} 条日志"
                
                return formatted, stats
                
            except Exception as e:
                return f"错误: {str(e)}", ""
        
        # 创建简单的测试界面
        with gr.Blocks(title="日志系统测试") as demo:
            gr.HTML("<h1>日志系统测试</h1>")
            
            with gr.Row():
                level_filter = gr.Dropdown(
                    label="日志级别",
                    choices=[("全部", "ALL"), ("信息", "INFO"), ("警告", "WARNING"), ("错误", "ERROR")],
                    value="ALL"
                )
                module_filter = gr.Dropdown(
                    label="模块过滤",
                    choices=[("全部", "ALL"), ("测试模块", "Test")],
                    value="ALL"
                )
                refresh_btn = gr.Button("刷新日志")
            
            log_display = gr.HTML(value="日志加载中...")
            log_stats = gr.HTML(value="")
            
            refresh_btn.click(
                fn=refresh_logs_test,
                inputs=[level_filter, module_filter],
                outputs=[log_display, log_stats]
            )
        
        print("✅ Gradio界面创建成功")
        
        # 启动测试界面
        print("🚀 启动测试界面...")
        demo.launch(
            server_name="0.0.0.0",
            server_port=7866,
            share=False,
            debug=False,
            quiet=True
        )
        
    except Exception as e:
        print(f"❌ Gradio集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 日志系统测试")
    print("=" * 50)
    
    # 测试日志管理器
    if test_log_manager():
        print("✅ 日志管理器测试通过")
    else:
        print("❌ 日志管理器测试失败")
        return
    
    # 测试Gradio集成
    print("\n" + "=" * 50)
    test_gradio_integration()

if __name__ == "__main__":
    main()
