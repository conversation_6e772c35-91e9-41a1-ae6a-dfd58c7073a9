#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PostgreSQL数据库修复脚本
清理错误命名的数据库，重新创建正确的数据库
"""

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

def fix_postgresql_database():
    """修复PostgreSQL数据库"""
    try:
        print("🔧 PostgreSQL数据库修复工具")
        print("=" * 50)
        
        # 连接配置
        config = {
            'host': 'localhost',
            'port': 5432,
            'user': 'postgres',
            'password': 'postgres123',  # 请根据实际情况修改
            'database': 'postgres'
        }
        
        print(f"🔍 连接到PostgreSQL服务器...")
        conn = psycopg2.connect(**config)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # 获取现有数据库列表
        cursor.execute("SELECT datname FROM pg_database WHERE datistemplate = false")
        databases = [row[0] for row in cursor.fetchall()]
        print(f"📋 现有数据库: {databases}")
        
        # 检查错误命名的数据库
        wrong_db_names = [
            'beijing2021_quota_database.db',
            'quota_database.db',
            'bejing2021_quota_dadabase.db'  # 可能的拼写错误
        ]
        
        correct_db_name = 'beijing2021_quota_database'
        
        # 删除错误命名的数据库
        for wrong_name in wrong_db_names:
            if wrong_name in databases:
                print(f"\n🗑️ 发现错误命名的数据库: {wrong_name}")
                
                # 断开所有连接
                cursor.execute(f"""
                    SELECT pg_terminate_backend(pid)
                    FROM pg_stat_activity
                    WHERE datname = '{wrong_name}' AND pid <> pg_backend_pid()
                """)
                
                # 删除数据库
                cursor.execute(f'DROP DATABASE "{wrong_name}"')
                print(f"✅ 已删除错误数据库: {wrong_name}")
        
        # 检查正确的数据库是否存在
        cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (correct_db_name,))
        correct_db_exists = cursor.fetchone() is not None
        
        if not correct_db_exists:
            # 创建正确的数据库
            cursor.execute(f'CREATE DATABASE "{correct_db_name}"')
            print(f"✅ 创建正确的数据库: {correct_db_name}")
        else:
            print(f"✅ 正确的数据库已存在: {correct_db_name}")
        
        # 验证结果
        cursor.execute("SELECT datname FROM pg_database WHERE datistemplate = false")
        final_databases = [row[0] for row in cursor.fetchall()]
        print(f"\n📋 修复后的数据库列表: {final_databases}")
        
        cursor.close()
        conn.close()
        
        print(f"\n✅ 数据库修复完成！")
        print(f"🎯 正确的数据库名称: {correct_db_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def test_connection_after_fix():
    """修复后测试连接"""
    try:
        print("\n🔍 测试修复后的连接...")
        
        from src.config_persistence_manager import ConfigPersistenceManager
        import json
        
        # 加载用户配置
        config_manager = ConfigPersistenceManager()
        config = config_manager.load_config()
        
        quota_db_config = config.get('database_configs', {}).get('quota_db', {})
        
        # 连接到正确的数据库
        conn = psycopg2.connect(
            host=quota_db_config.get('host', 'localhost'),
            port=int(quota_db_config.get('port', 5432)),
            user=quota_db_config.get('username', 'postgres'),
            password=quota_db_config.get('password', ''),
            database=quota_db_config.get('db_name', 'beijing2021_quota_database'),
            client_encoding='utf8'
        )
        
        cursor = conn.cursor()
        cursor.execute("SELECT current_database()")
        current_db = cursor.fetchone()[0]
        
        print(f"✅ 成功连接到数据库: {current_db}")
        
        # 检查表结构
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
        """)
        tables = [row[0] for row in cursor.fetchall()]
        
        if tables:
            print(f"📊 现有表: {tables}")
        else:
            print("📊 数据库为空，可以导入数据")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def main():
    """主函数"""
    # 1. 修复数据库
    if fix_postgresql_database():
        # 2. 测试连接
        test_connection_after_fix()
        
        print("\n🎉 PostgreSQL数据库修复完成！")
        print("💡 现在可以在程序中正常使用PostgreSQL数据库了")
    else:
        print("\n❌ 修复失败，请检查PostgreSQL服务和权限")

if __name__ == "__main__":
    main()
