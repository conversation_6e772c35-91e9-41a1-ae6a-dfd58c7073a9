#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PostgreSQL修复验证脚本
验证所有PostgreSQL相关问题是否已解决
"""

import psycopg2
import json
from pathlib import Path

def verify_postgresql_connection():
    """验证PostgreSQL连接"""
    try:
        print("🔍 验证PostgreSQL基本连接...")
        
        conn = psycopg2.connect(
            host='localhost',
            port=5432,
            user='postgres',
            password='postgres123',
            database='postgres',
            client_encoding='utf8'
        )
        
        cursor = conn.cursor()
        cursor.execute("SELECT version()")
        version = cursor.fetchone()[0]
        
        print(f"✅ PostgreSQL连接正常")
        print(f"   版本: {version.split(',')[0]}")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ PostgreSQL连接失败: {e}")
        return False

def verify_database_encoding():
    """验证数据库编码"""
    try:
        print("\n🔍 验证数据库编码...")
        
        conn = psycopg2.connect(
            host='localhost',
            port=5432,
            user='postgres',
            password='postgres123',
            database='beijing2021_quota_database',
            client_encoding='utf8'
        )
        
        cursor = conn.cursor()
        
        # 检查数据库编码
        cursor.execute("SELECT pg_encoding_to_char(encoding) FROM pg_database WHERE datname = current_database()")
        db_encoding = cursor.fetchone()[0]
        
        cursor.execute("SHOW server_encoding")
        server_encoding = cursor.fetchone()[0]
        
        cursor.execute("SHOW client_encoding")
        client_encoding = cursor.fetchone()[0]
        
        print(f"✅ 数据库编码验证成功:")
        print(f"   数据库编码: {db_encoding}")
        print(f"   服务器编码: {server_encoding}")
        print(f"   客户端编码: {client_encoding}")
        
        # 测试中文字符
        cursor.execute("SELECT '测试中文字符' as test_chinese")
        chinese_test = cursor.fetchone()[0]
        print(f"   中文测试: {chinese_test}")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库编码验证失败: {e}")
        return False

def verify_user_config():
    """验证用户配置"""
    try:
        print("\n🔍 验证用户配置...")
        
        from src.config_persistence_manager import ConfigPersistenceManager
        
        config_manager = ConfigPersistenceManager()
        config = config_manager.load_config()
        
        quota_db_config = config.get('database_configs', {}).get('quota_db', {})
        
        print(f"✅ 用户配置验证:")
        print(f"   数据库类型: {quota_db_config.get('db_type')}")
        print(f"   数据库名称: {quota_db_config.get('db_name')}")
        print(f"   主机: {quota_db_config.get('host')}")
        print(f"   端口: {quota_db_config.get('port')}")
        print(f"   用户名: {quota_db_config.get('username')}")
        
        # 验证数据库名称格式
        db_name = quota_db_config.get('db_name', '')
        if db_name.endswith('.db'):
            print(f"❌ 数据库名称格式错误: {db_name}")
            return False
        
        print(f"✅ 数据库名称格式正确: {db_name}")
        return True
        
    except Exception as e:
        print(f"❌ 用户配置验证失败: {e}")
        return False

def verify_program_integration():
    """验证程序集成"""
    try:
        print("\n🔍 验证程序集成...")
        
        from src.config_persistence_manager import ConfigPersistenceManager
        
        config_manager = ConfigPersistenceManager()
        config = config_manager.load_config()
        
        quota_db_config = config.get('database_configs', {}).get('quota_db', {})
        
        # 使用程序配置连接数据库
        conn = psycopg2.connect(
            host=quota_db_config.get('host', 'localhost'),
            port=int(quota_db_config.get('port', 5432)),
            user=quota_db_config.get('username', 'postgres'),
            password=quota_db_config.get('password', ''),
            database=quota_db_config.get('db_name', 'beijing2021_quota_database'),
            client_encoding='utf8'
        )
        
        cursor = conn.cursor()
        cursor.execute("SELECT current_database(), current_user")
        db_name, user_name = cursor.fetchone()
        
        print(f"✅ 程序集成验证成功:")
        print(f"   连接数据库: {db_name}")
        print(f"   连接用户: {user_name}")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 程序集成验证失败: {e}")
        return False

def verify_table_creation():
    """验证表创建功能"""
    try:
        print("\n🔍 验证表创建功能...")
        
        from src.config_persistence_manager import ConfigPersistenceManager
        
        config_manager = ConfigPersistenceManager()
        config = config_manager.load_config()
        
        quota_db_config = config.get('database_configs', {}).get('quota_db', {})
        
        conn = psycopg2.connect(
            host=quota_db_config.get('host', 'localhost'),
            port=int(quota_db_config.get('port', 5432)),
            user=quota_db_config.get('username', 'postgres'),
            password=quota_db_config.get('password', ''),
            database=quota_db_config.get('db_name', 'beijing2021_quota_database'),
            client_encoding='utf8'
        )
        
        cursor = conn.cursor()
        
        # 创建测试表
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS test_encoding (
                id SERIAL PRIMARY KEY,
                chinese_text TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 插入中文测试数据
        cursor.execute("""
            INSERT INTO test_encoding (chinese_text) 
            VALUES ('测试中文编码：定额管理系统')
        """)
        
        # 读取测试数据
        cursor.execute("SELECT chinese_text FROM test_encoding ORDER BY id DESC LIMIT 1")
        result = cursor.fetchone()[0]
        
        print(f"✅ 表创建和中文数据测试成功:")
        print(f"   插入的中文: 测试中文编码：定额管理系统")
        print(f"   读取的中文: {result}")
        
        # 清理测试数据
        cursor.execute("DROP TABLE test_encoding")
        
        conn.commit()
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 表创建验证失败: {e}")
        return False

def main():
    """主验证流程"""
    print("🔍 PostgreSQL修复验证工具")
    print("=" * 50)
    
    all_tests_passed = True
    
    # 1. 验证基本连接
    if not verify_postgresql_connection():
        all_tests_passed = False
    
    # 2. 验证数据库编码
    if not verify_database_encoding():
        all_tests_passed = False
    
    # 3. 验证用户配置
    if not verify_user_config():
        all_tests_passed = False
    
    # 4. 验证程序集成
    if not verify_program_integration():
        all_tests_passed = False
    
    # 5. 验证表创建功能
    if not verify_table_creation():
        all_tests_passed = False
    
    print("\n" + "=" * 50)
    
    if all_tests_passed:
        print("🎉 所有验证测试通过！")
        print("✅ PostgreSQL编码问题已完全解决")
        print("✅ 数据库连接正常")
        print("✅ 中文字符支持正常")
        print("✅ 程序集成正常")
        print("✅ 表创建功能正常")
        
        print("\n💡 现在可以:")
        print("1. 在pgAdmin4中正常连接和管理数据库")
        print("2. 在程序中使用PostgreSQL数据库功能")
        print("3. 正常创建和导入定额数据")
        print("4. 使用定额修订功能")
    else:
        print("❌ 部分验证测试失败")
        print("💡 请检查失败的项目并重新修复")

if __name__ == "__main__":
    main()
