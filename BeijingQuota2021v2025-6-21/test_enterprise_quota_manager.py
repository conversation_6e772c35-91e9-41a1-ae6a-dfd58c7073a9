#!/usr/bin/env python3
"""
测试企业定额管理系统
"""

import os
import sys
import pandas as pd
from pathlib import Path

# 添加src目录到路径
sys.path.append('src')

def create_test_data():
    """创建测试数据文件"""
    # 确保output目录存在
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    # 创建parent_quotas测试数据
    parent_data = {
        '定额编号': ['001-001', '001-002', '001-003', '002-001', '002-002'],
        '定额名称': ['混凝土浇筑C30', '钢筋绑扎HPB300', '模板安装拆除', '砌砖工程MU10', '抹灰工程'],
        '单位': ['m³', 'kg', 'm²', 'm³', 'm²'],
        '人工费': [120.5, 85.2, 95.8, 110.3, 75.6],
        '材料费': [450.8, 320.5, 180.2, 280.9, 120.4],
        '机械费': [80.3, 45.8, 25.6, 35.2, 15.8],
        '合计': [651.6, 451.5, 301.6, 426.4, 211.8]
    }
    
    parent_df = pd.DataFrame(parent_data)
    parent_csv_path = output_dir / "parent_quotas_test.csv"
    parent_df.to_csv(parent_csv_path, index=False, encoding='utf-8-sig')
    
    # 创建child_resources测试数据
    child_data = {
        '定额编号': ['001-001', '001-001', '001-001', '001-002', '001-002', '002-001', '002-001'],
        '资源编号': ['R001', 'R002', 'R003', 'R004', 'R005', 'R006', 'R007'],
        '资源名称': ['C30混凝土', '人工', '机械台班', 'HPB300钢筋', '人工', 'MU10砖', '砂浆'],
        '资源类型': ['材料', '人工', '机械', '材料', '人工', '材料', '材料'],
        '数量': [1.05, 8.5, 2.3, 1.02, 12.8, 0.98, 0.25],
        '单位': ['m³', '工日', '台班', 'kg', '工日', 'm³', 'm³'],
        '单价': [420.0, 150.0, 350.0, 4.2, 150.0, 280.0, 180.0],
        '合价': [441.0, 1275.0, 805.0, 4.284, 1920.0, 274.4, 45.0]
    }
    
    child_df = pd.DataFrame(child_data)
    child_csv_path = output_dir / "child_resources_test.csv"
    child_df.to_csv(child_csv_path, index=False, encoding='utf-8-sig')
    
    print(f"✅ 创建测试数据文件:")
    print(f"   - {parent_csv_path}")
    print(f"   - {child_csv_path}")
    
    return str(parent_csv_path), str(child_csv_path)

def test_enterprise_quota_manager():
    """测试企业定额管理系统"""
    try:
        from enterprise_quota_manager import EnterpriseQuotaManager
        
        # 创建测试数据
        parent_file, child_file = create_test_data()
        
        # 创建管理器
        manager = EnterpriseQuotaManager()
        
        # 测试数据库创建
        print("\n🔨 测试数据库创建...")
        db_config = {
            'database_path': 'output/test_enterprise_quota.db'
        }
        
        success, message = manager.create_quota_database(
            'sqlite', db_config, [parent_file], [child_file]
        )
        
        if success:
            print("✅ 数据库创建成功!")
            print(f"📋 {message}")
        else:
            print(f"❌ 数据库创建失败: {message}")
            return False
        
        # 测试数据库连接
        print("\n🔗 测试数据库连接...")
        success, message = manager.connect_to_database('output/test_enterprise_quota.db')
        
        if success:
            print("✅ 数据库连接成功!")
        else:
            print(f"❌ 数据库连接失败: {message}")
            return False
        
        # 测试统计信息
        print("\n📊 测试统计信息...")
        success, message, stats = manager.get_database_statistics()
        
        if success:
            print("✅ 统计信息获取成功!")
            print(f"   - 定额项数量: {stats['quota_count']}")
            print(f"   - 资源记录数: {stats['resource_count']}")
            print(f"   - 关联定额项: {stats['linked_quota_count']}")
            print(f"   - 数据库大小: {stats['db_size_kb']:.2f} KB")
        else:
            print(f"❌ 统计信息获取失败: {message}")
        
        # 测试定额搜索
        print("\n🔍 测试定额搜索...")
        success, message, quotas = manager.search_quotas("混凝土")
        
        if success:
            print(f"✅ 搜索成功: {message}")
            for quota in quotas:
                print(f"   - {quota['quota_code']}: {quota['quota_name']} ({quota['total_cost']}元)")
        else:
            print(f"❌ 搜索失败: {message}")
        
        # 测试资源查询
        print("\n🔧 测试资源查询...")
        if quotas:
            quota_code = quotas[0]['quota_code']
            success, message, resources = manager.get_quota_resources(quota_code)
            
            if success:
                print(f"✅ 资源查询成功: {message}")
                for resource in resources:
                    print(f"   - {resource['resource_code']}: {resource['resource_name']} "
                          f"({resource['quantity']}{resource['unit']}, {resource['total_price']}元)")
            else:
                print(f"❌ 资源查询失败: {message}")
        
        # 测试数据导出
        print("\n📤 测试数据导出...")
        if quotas:
            quota_codes = [quota['quota_code'] for quota in quotas[:2]]
            success, message = manager.export_quota_data(quota_codes, 'output/test_export.xlsx')
            
            if success:
                print(f"✅ 数据导出成功: {message}")
            else:
                print(f"❌ 数据导出失败: {message}")
        
        # 关闭连接
        manager.close_connection()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 开始测试企业定额管理系统")
    print("=" * 50)
    
    # 检查依赖
    try:
        import pandas as pd
        print("✅ pandas 已安装")
    except ImportError:
        print("❌ pandas 未安装")
        return False
    
    try:
        import sqlite3
        print("✅ sqlite3 已安装")
    except ImportError:
        print("❌ sqlite3 未安装")
        return False
    
    # 运行测试
    success = test_enterprise_quota_manager()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 企业定额管理系统测试通过!")
        print("\n💡 使用说明:")
        print("1. 在Web界面中选择定额项和资源CSV文件")
        print("2. 配置数据库连接信息")
        print("3. 点击'创建定额数据库'按钮")
        print("4. 连接数据库并进行定额查询管理")
        print("5. 支持定额项搜索和关联资源查看")
        print("6. 可导出选中的定额数据")
    else:
        print("❌ 企业定额管理系统测试失败!")
    
    return success

if __name__ == "__main__":
    main()
