# 企业定额管理系统使用说明

## 系统概述

企业定额管理系统是北京市定额提取工具的核心功能模块，专门为企业定额数据的创建、管理和查询而设计。系统实现了定额项与资源的智能关联，支持多种数据库格式，提供完整的定额数据管理解决方案。

## 核心功能

### 🔨 1. 定额数据库创建

#### 支持的数据库类型
- **📱 SQLite本地数据库**: 适合本地测试和小型项目
- **🐬 MySQL数据库**: 适合中大型企业应用
- **🐘 PostgreSQL数据库**: 适合高性能企业级应用

#### 数据文件要求
- **定额项文件**: `parent_quotas*.csv` - 包含定额编号、名称、单位、价格等信息
- **资源文件**: `child_resources*.csv` - 包含资源编号、名称、类型、数量、单价等信息

#### 数据库结构
```sql
-- 定额项表
CREATE TABLE parent_quotas (
    id INTEGER PRIMARY KEY,
    quota_code TEXT UNIQUE NOT NULL,    -- 定额编号
    quota_name TEXT NOT NULL,           -- 定额名称
    unit TEXT,                          -- 单位
    labor_cost REAL DEFAULT 0,          -- 人工费
    material_cost REAL DEFAULT 0,       -- 材料费
    machinery_cost REAL DEFAULT 0,      -- 机械费
    total_cost REAL DEFAULT 0,          -- 合计
    source_file TEXT,                   -- 来源文件
    created_at TIMESTAMP,               -- 创建时间
    updated_at TIMESTAMP                -- 更新时间
);

-- 资源表
CREATE TABLE child_resources (
    id INTEGER PRIMARY KEY,
    quota_code TEXT NOT NULL,           -- 定额编号（外键）
    resource_code TEXT NOT NULL,        -- 资源编号
    resource_name TEXT NOT NULL,        -- 资源名称
    resource_type TEXT,                 -- 资源类型
    quantity REAL DEFAULT 0,            -- 数量
    unit TEXT,                          -- 单位
    unit_price REAL DEFAULT 0,          -- 单价
    total_price REAL DEFAULT 0,         -- 合价
    source_file TEXT,                   -- 来源文件
    created_at TIMESTAMP,               -- 创建时间
    FOREIGN KEY (quota_code) REFERENCES parent_quotas (quota_code)
);
```

### 🔍 2. 定额查询管理

#### 智能搜索功能
- **模糊搜索**: 支持定额编号和名称的模糊匹配
- **分页显示**: 大数据量时自动分页，提高查询效率
- **实时过滤**: 输入关键词即时显示匹配结果

#### 关联资源查看
- **一键查看**: 点击定额项即可查看所有关联资源
- **详细信息**: 显示资源编号、名称、类型、数量、单价、合价
- **来源追踪**: 显示数据来源文件，便于数据溯源

#### 价格计算功能
- **自动计算**: 根据关联资源自动计算定额项总价
- **分类统计**: 按人工费、材料费、机械费分类统计
- **实时更新**: 资源价格变动时自动更新定额项价格

### 📊 3. 数据统计分析

#### 数据库统计
- **定额项数量**: 统计数据库中的定额项总数
- **资源记录数**: 统计所有资源记录数量
- **关联定额项**: 统计有关联资源的定额项数量
- **数据库大小**: 显示数据库文件大小
- **最后更新**: 显示数据最后更新时间

#### 数据质量分析
- **关联完整性**: 检查定额项与资源的关联关系
- **数据一致性**: 验证价格计算的准确性
- **来源文件**: 追踪数据来源，确保数据可靠性

### 📤 4. 数据导出功能

#### 支持的导出格式
- **Excel格式**: 包含定额项和关联资源两个工作表
- **分表导出**: 定额项和资源数据分别导出
- **完整信息**: 包含所有字段和元数据信息

#### 导出内容
- **定额项工作表**: 定额编号、名称、单位、各项费用、来源文件
- **关联资源工作表**: 资源编号、名称、类型、数量、单价、合价、来源文件

## 使用流程

### 第一步：准备数据文件

1. **整理定额项数据**
   - 确保CSV文件包含定额编号、定额名称、单位等基本信息
   - 文件命名建议：`parent_quotas_项目名称.csv`

2. **整理资源数据**
   - 确保CSV文件包含定额编号、资源编号、资源名称等信息
   - 文件命名建议：`child_resources_项目名称.csv`

3. **上传文件**
   - 将CSV文件放置在`output`目录下
   - 系统会自动识别相关文件

### 第二步：创建定额数据库

1. **选择数据库类型**
   - 本地测试推荐选择SQLite
   - 企业应用推荐选择MySQL或PostgreSQL

2. **配置数据库连接**
   - SQLite：只需设置数据库文件路径
   - MySQL/PostgreSQL：需要设置服务器地址、端口、用户名、密码

3. **选择数据文件**
   - 勾选要导入的定额项文件
   - 勾选要导入的资源文件

4. **创建数据库**
   - 点击"创建定额数据库"按钮
   - 等待系统完成数据导入和关联

### 第三步：查询管理定额

1. **连接数据库**
   - 输入数据库文件路径
   - 点击"连接数据库"按钮

2. **搜索定额项**
   - 输入搜索关键词（定额编号或名称）
   - 点击"搜索"按钮查看结果

3. **查看关联资源**
   - 在定额项列表中点击任意一行
   - 系统自动显示该定额项的所有关联资源

4. **导出数据**
   - 选择需要导出的定额项
   - 点击"导出选中定额"或"导出全部定额"

## 技术特性

### 🚀 性能优化
- **索引优化**: 为关键字段创建索引，提高查询速度
- **分页查询**: 大数据量时采用分页机制，避免内存溢出
- **连接池**: 数据库连接复用，提高并发性能

### 🔒 数据安全
- **事务处理**: 确保数据导入的原子性和一致性
- **错误处理**: 完善的异常处理机制，保证系统稳定性
- **数据验证**: 导入前验证数据格式和完整性

### 🎯 智能识别
- **字段映射**: 自动识别CSV文件中的字段名称
- **数据类型**: 智能判断数据类型，保持数值精度
- **编码处理**: 自动处理中文编码问题

### 🔧 扩展性
- **模块化设计**: 核心功能模块化，便于扩展和维护
- **多数据库支持**: 支持多种主流数据库系统
- **API接口**: 预留API接口，支持第三方系统集成

## 常见问题

### Q1: 如何确保定额项与资源正确关联？
**A**: 系统通过定额编号字段自动建立关联关系。请确保：
- 定额项文件中的定额编号字段完整且唯一
- 资源文件中的定额编号与定额项文件中的编号一致
- 编号格式统一（如：001-001、001-002等）

### Q2: 支持哪些CSV文件格式？
**A**: 系统支持：
- UTF-8编码的CSV文件（推荐）
- UTF-8-BOM编码的CSV文件
- GBK编码的CSV文件
- 逗号分隔或分号分隔的CSV文件

### Q3: 如何处理价格计算错误？
**A**: 系统提供自动价格计算功能：
- 根据关联资源的合价自动计算定额项总价
- 支持手动更新价格计算
- 提供价格验证和错误提示

### Q4: 数据库文件过大怎么办？
**A**: 建议采用以下优化策略：
- 使用MySQL或PostgreSQL替代SQLite
- 定期清理历史数据
- 使用数据分区技术
- 优化查询条件，减少全表扫描

## 技术支持

- **项目地址**: 北京市2021消耗定额智能提取工具
- **技术支持**: <EMAIL>
- **更新日志**: 查看项目根目录的CHANGELOG.md文件
- **问题反馈**: 通过GitHub Issues或邮件联系

## 版本信息

- **当前版本**: v2.0.0
- **发布日期**: 2025-06-25
- **主要更新**: 新增企业定额管理系统，支持定额数据库创建和智能查询管理
