#!/usr/bin/env python3
"""
启动脚本 - 一键启动北京市消耗定额智能提取系统
"""

import sys
import os
import subprocess
import time
from pathlib import Path

def check_installation():
    """检查系统是否已正确安装"""
    print("🔍 检查系统安装状态...")
    
    # 检查必要文件
    required_files = [
        "main.py",
        "requirements.txt",
        "src/config.py",
        "src/pdf_processor.py",
        "src/browser_automation.py",
        "src/data_processor.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    # 检查目录
    required_dirs = ["src", "temp", "output"]
    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        if not dir_path.exists():
            print(f"📁 创建目录: {dir_name}")
            dir_path.mkdir(exist_ok=True)
    
    print("✅ 系统文件检查完成")
    return True

def check_dependencies():
    """检查Python依赖"""
    print("\n🔍 检查Python依赖...")
    
    try:
        # 运行测试脚本检查依赖
        # 在Windows环境中使用py命令
        python_cmd = "py" if os.name == 'nt' else sys.executable
        result = subprocess.run([python_cmd, "test_system.py"],
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ 依赖检查通过")
            return True
        else:
            print("❌ 依赖检查失败")
            print("错误信息:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ 依赖检查超时")
        return False
    except FileNotFoundError:
        print("❌ 找不到测试脚本")
        return False
    except Exception as e:
        print(f"❌ 依赖检查异常: {e}")
        return False

def install_dependencies():
    """安装依赖"""
    print("\n📦 安装Python依赖...")
    
    try:
        python_cmd = "py" if os.name == 'nt' else sys.executable
        result = subprocess.run([
            python_cmd, "-m", "pip", "install", "-r", "requirements.txt"
        ], check=True, capture_output=True, text=True)
        
        print("✅ 依赖安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print("❌ 依赖安装失败")
        print(f"错误信息: {e.stderr}")
        return False

def run_installation():
    """运行完整安装"""
    print("\n🚀 运行完整安装程序...")
    
    try:
        python_cmd = "py" if os.name == 'nt' else sys.executable
        result = subprocess.run([python_cmd, "install.py"],
                              check=True, capture_output=True, text=True)
        
        print("✅ 安装程序运行成功")
        print(result.stdout)
        return True
        
    except subprocess.CalledProcessError as e:
        print("❌ 安装程序运行失败")
        print(f"错误信息: {e.stderr}")
        return False
    except FileNotFoundError:
        print("❌ 找不到安装脚本")
        return False

def start_system():
    """启动系统"""
    print("\n🚀 启动北京市消耗定额智能提取系统...")
    print("=" * 60)
    
    try:
        # 启动主程序
        python_cmd = "py" if os.name == 'nt' else sys.executable
        subprocess.run([python_cmd, "main.py"], check=True)
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 系统启动失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n👋 用户中断，系统已停止")
        return True
    except Exception as e:
        print(f"❌ 系统运行异常: {e}")
        return False
    
    return True

def show_menu():
    """显示菜单"""
    print("\n" + "=" * 60)
    print("🏠 北京市消耗定额智能提取系统 - 启动菜单")
    print("=" * 60)
    print("1. 直接启动系统")
    print("2. 检查系统状态")
    print("3. 安装/更新依赖")
    print("4. 运行完整安装")
    print("5. 运行系统测试")
    print("6. 查看帮助")
    print("0. 退出")
    print("=" * 60)

def show_help():
    """显示帮助信息"""
    print("\n📖 帮助信息")
    print("=" * 60)
    print("系统功能:")
    print("- 自动从PDF定额文件中提取表格数据")
    print("- 通过DeepSeek AI进行图像识别")
    print("- 生成包含价格计算公式的CSV文件")
    print()
    print("使用步骤:")
    print("1. 启动系统后在浏览器中打开 http://localhost:7860")
    print("2. 上传PDF定额文件")
    print("3. 设置要提取的页码范围")
    print("4. 点击'开始提取'按钮")
    print("5. 等待处理完成后下载CSV文件")
    print()
    print("注意事项:")
    print("- 确保Chrome浏览器已安装")
    print("- 首次使用可能需要手动登录DeepSeek网站")
    print("- 处理大文件时请耐心等待")
    print()
    print("故障排除:")
    print("- 如果启动失败，请先运行'4. 运行完整安装'")
    print("- 如果识别不准确，可以尝试提高PDF质量")
    print("- 查看logs目录下的日志文件获取详细错误信息")

def main():
    """主函数"""
    while True:
        show_menu()
        
        try:
            choice = input("\n请选择操作 (0-6): ").strip()
            
            if choice == "0":
                print("👋 再见！")
                break
                
            elif choice == "1":
                # 直接启动系统
                if check_installation():
                    start_system()
                else:
                    print("❌ 系统检查失败，请先运行安装程序")
                    
            elif choice == "2":
                # 检查系统状态
                check_installation()
                check_dependencies()
                
            elif choice == "3":
                # 安装/更新依赖
                install_dependencies()
                
            elif choice == "4":
                # 运行完整安装
                run_installation()
                
            elif choice == "5":
                # 运行系统测试
                print("\n🧪 运行系统测试...")
                try:
                    python_cmd = "py" if os.name == 'nt' else sys.executable
                    subprocess.run([python_cmd, "test_system.py"], check=True)
                except subprocess.CalledProcessError:
                    print("❌ 测试失败")
                except FileNotFoundError:
                    print("❌ 找不到测试脚本")
                    
            elif choice == "6":
                # 查看帮助
                show_help()
                
            else:
                print("❌ 无效选择，请输入 0-6 之间的数字")
                
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"❌ 操作异常: {e}")
        
        # 等待用户按键继续
        if choice != "0":
            input("\n按回车键继续...")

if __name__ == "__main__":
    main()
