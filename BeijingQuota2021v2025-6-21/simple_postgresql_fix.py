#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PostgreSQL编码问题简单修复脚本
尝试修复现有数据库的编码问题
"""

import os
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

def fix_postgresql_encoding():
    """修复PostgreSQL编码问题"""
    try:
        print("🔧 PostgreSQL编码问题修复工具")
        print("=" * 50)
        
        # 连接配置
        config = {
            'host': 'localhost',
            'port': 5432,
            'user': 'postgres',
            'password': 'postgres123',  # 请根据实际情况修改
            'database': 'postgres'
        }
        
        print("🔍 尝试连接PostgreSQL...")
        
        # 尝试不同的编码设置
        encodings = ['utf8', 'latin1', 'cp1252', 'gbk']
        
        for encoding in encodings:
            try:
                print(f"   尝试编码: {encoding}")
                conn = psycopg2.connect(
                    **config,
                    client_encoding=encoding
                )
                
                cursor = conn.cursor()
                cursor.execute("SELECT version()")
                version = cursor.fetchone()[0]
                
                print(f"✅ 连接成功! 编码: {encoding}")
                print(f"   版本: {version.split(',')[0]}")
                
                # 检查现有数据库
                cursor.execute("SELECT datname FROM pg_database WHERE datistemplate = false")
                databases = [row[0] for row in cursor.fetchall()]
                print(f"   现有数据库: {databases}")
                
                # 删除损坏的数据库
                damaged_dbs = [
                    'beijing2021_quota_database',
                    'beijing2021_quota_database.db'
                ]
                
                conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
                
                for db_name in damaged_dbs:
                    if db_name in databases:
                        print(f"🗑️ 删除可能损坏的数据库: {db_name}")
                        
                        # 断开所有连接
                        cursor.execute(f"""
                            SELECT pg_terminate_backend(pid)
                            FROM pg_stat_activity
                            WHERE datname = '{db_name}' AND pid <> pg_backend_pid()
                        """)
                        
                        # 删除数据库
                        cursor.execute(f'DROP DATABASE "{db_name}"')
                        print(f"✅ 已删除: {db_name}")
                
                # 创建新的干净数据库
                new_db_name = 'beijing2021_quota_database'
                print(f"🗄️ 创建新的数据库: {new_db_name}")
                
                cursor.execute(f"""
                    CREATE DATABASE "{new_db_name}"
                    WITH ENCODING 'UTF8'
                    LC_COLLATE = 'C'
                    LC_CTYPE = 'C'
                    TEMPLATE = template0
                """)
                
                print(f"✅ 创建成功: {new_db_name}")
                
                # 测试新数据库连接
                cursor.close()
                conn.close()
                
                # 连接到新数据库测试
                test_conn = psycopg2.connect(
                    host=config['host'],
                    port=config['port'],
                    user=config['user'],
                    password=config['password'],
                    database=new_db_name,
                    client_encoding='utf8'
                )
                
                test_cursor = test_conn.cursor()
                test_cursor.execute("SELECT current_database()")
                current_db = test_cursor.fetchone()[0]
                
                test_cursor.execute("SHOW server_encoding")
                server_encoding = test_cursor.fetchone()[0]
                
                test_cursor.execute("SHOW client_encoding")
                client_encoding = test_cursor.fetchone()[0]
                
                print(f"✅ 新数据库测试成功:")
                print(f"   当前数据库: {current_db}")
                print(f"   服务器编码: {server_encoding}")
                print(f"   客户端编码: {client_encoding}")
                
                test_cursor.close()
                test_conn.close()
                
                return True
                
            except Exception as e:
                print(f"   编码 {encoding} 失败: {e}")
                continue
        
        print("❌ 所有编码尝试都失败了")
        return False
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def update_user_config():
    """更新用户配置"""
    try:
        print("\n🔧 更新用户配置...")
        
        from src.config_persistence_manager import ConfigPersistenceManager
        
        config_manager = ConfigPersistenceManager()
        config = config_manager.load_config()
        
        # 确保数据库配置正确
        quota_db_config = config.get('database_configs', {}).get('quota_db', {})
        quota_db_config.update({
            'db_type': 'postgresql',
            'db_name': 'beijing2021_quota_database',  # 确保没有.db扩展名
            'host': 'localhost',
            'port': '5432',
            'username': 'postgres',
            'default_db': 'postgres'
        })
        
        # 保存配置
        success = config_manager.save_config(config)
        
        if success:
            print("✅ 用户配置已更新")
            return True
        else:
            print("❌ 配置更新失败")
            return False
            
    except Exception as e:
        print(f"❌ 更新配置失败: {e}")
        return False

def test_program_connection():
    """测试程序连接"""
    try:
        print("\n🔍 测试程序连接...")
        
        from src.config_persistence_manager import ConfigPersistenceManager
        
        config_manager = ConfigPersistenceManager()
        config = config_manager.load_config()
        
        quota_db_config = config.get('database_configs', {}).get('quota_db', {})
        
        conn = psycopg2.connect(
            host=quota_db_config.get('host', 'localhost'),
            port=int(quota_db_config.get('port', 5432)),
            user=quota_db_config.get('username', 'postgres'),
            password=quota_db_config.get('password', ''),
            database=quota_db_config.get('db_name', 'beijing2021_quota_database'),
            client_encoding='utf8'
        )
        
        cursor = conn.cursor()
        cursor.execute("SELECT current_database(), version()")
        db_name, version = cursor.fetchone()
        
        print(f"✅ 程序连接测试成功:")
        print(f"   数据库: {db_name}")
        print(f"   版本: {version.split(',')[0]}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 程序连接测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 PostgreSQL编码问题简单修复")
    print("=" * 50)
    
    # 1. 修复编码问题
    if not fix_postgresql_encoding():
        print("\n❌ 编码修复失败")
        return
    
    # 2. 更新用户配置
    if not update_user_config():
        print("\n❌ 配置更新失败")
        return
    
    # 3. 测试程序连接
    if not test_program_connection():
        print("\n❌ 程序连接测试失败")
        return
    
    print("\n🎉 PostgreSQL编码问题修复完成！")
    print("✅ 数据库已重新创建")
    print("✅ 编码设置正确")
    print("✅ 用户配置已更新")
    print("✅ 程序连接正常")
    
    print("\n💡 现在可以:")
    print("1. 在pgAdmin4中正常连接 beijing2021_quota_database")
    print("2. 在程序中使用PostgreSQL数据库功能")
    print("3. 重新导入定额数据")

if __name__ == "__main__":
    main()
