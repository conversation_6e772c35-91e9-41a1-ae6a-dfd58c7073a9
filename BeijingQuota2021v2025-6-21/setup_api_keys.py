#!/usr/bin/env python3
"""
API密钥配置助手
帮助用户配置各种AI模型的API密钥
"""

import os
from pathlib import Path

def setup_api_keys():
    """交互式配置API密钥"""
    print("🔑 AI模型API密钥配置助手")
    print("=" * 50)
    
    # 检查.env文件
    env_file = Path(".env")
    if env_file.exists():
        print("✅ 找到现有的.env配置文件")
        choice = input("是否要更新现有配置？(y/n): ").lower().strip()
        if choice != 'y':
            return
    else:
        print("📝 创建新的.env配置文件")
    
    # API密钥配置
    api_configs = {
        "OPENAI_API_KEY": {
            "name": "OpenAI GPT-4 Vision",
            "description": "支持图像理解的GPT-4模型",
            "url": "https://platform.openai.com/api-keys",
            "example": "sk-..."
        },
        "ANTHROPIC_API_KEY": {
            "name": "Anthropic Claude Vision", 
            "description": "Claude-3支持图像理解",
            "url": "https://console.anthropic.com/",
            "example": "sk-ant-..."
        },
        "GOOGLE_API_KEY": {
            "name": "Google Gemini Vision",
            "description": "Google的多模态AI模型",
            "url": "https://makersuite.google.com/app/apikey",
            "example": "AIza..."
        },
        "DEEPSEEK_API_KEY": {
            "name": "DeepSeek API",
            "description": "DeepSeek的视觉理解模型",
            "url": "https://platform.deepseek.com/api_keys",
            "example": "sk-..."
        },
        "DASHSCOPE_API_KEY": {
            "name": "阿里云百炼/通义千问-QVQ",
            "description": "阿里云百炼平台的QVQ视觉推理模型，具有强大的思维链推理能力",
            "url": "https://bailian.console.aliyun.com/",
            "example": "sk-..."
        }
    }
    
    # 读取现有配置
    existing_config = {}
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                if '=' in line and not line.strip().startswith('#'):
                    key, value = line.strip().split('=', 1)
                    existing_config[key] = value
    
    # 配置每个API密钥
    new_config = {}
    
    print("\n📋 可用的AI模型:")
    for i, (key, info) in enumerate(api_configs.items(), 1):
        print(f"{i}. {info['name']} - {info['description']}")
    
    print("\n🔧 开始配置API密钥:")
    print("提示: 直接按回车跳过某个API密钥的配置")
    
    for key, info in api_configs.items():
        print(f"\n--- {info['name']} ---")
        print(f"描述: {info['description']}")
        print(f"获取地址: {info['url']}")
        print(f"格式示例: {info['example']}")
        
        current_value = existing_config.get(key, "")
        if current_value:
            print(f"当前值: {current_value[:10]}...")
        
        new_value = input(f"请输入{info['name']}的API密钥 (回车跳过): ").strip()
        
        if new_value:
            new_config[key] = new_value
            print("✅ 已设置")
        elif current_value:
            new_config[key] = current_value
            print("✅ 保持现有配置")
        else:
            print("⏭️ 跳过")
    
    # 添加其他配置
    other_configs = {
        "PDF_DPI": "200",
        "IMAGE_FORMAT": "PNG",
        "BROWSER_HEADLESS": "false",
        "BROWSER_TIMEOUT": "30"
    }
    
    for key, default_value in other_configs.items():
        current_value = existing_config.get(key, default_value)
        new_config[key] = current_value
    
    # 写入配置文件
    print(f"\n💾 保存配置到 {env_file}")
    with open(env_file, 'w', encoding='utf-8') as f:
        f.write("# AI模型API密钥配置\n")
        f.write("# 北京市消耗定额智能提取系统\n\n")
        
        # API密钥部分
        f.write("# AI模型API密钥\n")
        for key in api_configs.keys():
            if key in new_config:
                f.write(f"{key}={new_config[key]}\n")
            else:
                f.write(f"# {key}=your_api_key_here\n")
        
        f.write("\n# 其他配置\n")
        for key, value in other_configs.items():
            f.write(f"{key}={new_config.get(key, value)}\n")
    
    print("✅ 配置保存完成!")
    
    # 显示配置摘要
    configured_models = [
        api_configs[key]["name"] 
        for key in api_configs.keys() 
        if key in new_config and new_config[key]
    ]
    
    print(f"\n📊 配置摘要:")
    print(f"已配置的模型: {len(configured_models)}")
    for model in configured_models:
        print(f"  ✅ {model}")
    
    if not configured_models:
        print("⚠️ 未配置任何API密钥")
        print("系统将只能使用本地Ollama模型（如果可用）")
    
    print(f"\n🚀 现在可以运行系统:")
    print("py main.py")

def check_api_keys():
    """检查API密钥配置状态"""
    print("🔍 检查API密钥配置状态")
    print("=" * 30)
    
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ 未找到.env配置文件")
        print("请运行: py setup_api_keys.py")
        return False
    
    # 检查各个API密钥
    api_keys = {
        "OPENAI_API_KEY": "OpenAI GPT-4 Vision",
        "ANTHROPIC_API_KEY": "Anthropic Claude Vision",
        "GOOGLE_API_KEY": "Google Gemini Vision",
        "DEEPSEEK_API_KEY": "DeepSeek API",
        "DASHSCOPE_API_KEY": "阿里云百炼/通义千问-QVQ"
    }
    
    configured_count = 0
    
    for key, name in api_keys.items():
        value = os.getenv(key)
        if value and value != "your_api_key_here":
            print(f"✅ {name}")
            configured_count += 1
        else:
            print(f"❌ {name}")
    
    # 检查本地Ollama
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ 本地Ollama服务")
            configured_count += 1
        else:
            print("❌ 本地Ollama服务")
    except:
        print("❌ 本地Ollama服务")
    
    print(f"\n📊 总计: {configured_count} 个可用模型")
    
    if configured_count == 0:
        print("\n⚠️ 没有可用的AI模型!")
        print("请配置至少一个API密钥或启动Ollama服务")
        return False
    
    return True

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "check":
        check_api_keys()
    else:
        setup_api_keys()

if __name__ == "__main__":
    main()
