#!/usr/bin/env python3
"""
安装信息价识别模块依赖
Install dependencies for price information recognition module
"""

import subprocess
import sys
import os

def install_package(package_name, description=""):
    """安装Python包"""
    try:
        print(f"📦 安装 {package_name}...")
        if description:
            print(f"   {description}")
        
        result = subprocess.run(
            [sys.executable, "-m", "pip", "install", package_name],
            capture_output=True,
            text=True,
            check=True
        )
        
        print(f"✅ {package_name} 安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败:")
        print(f"   错误: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ {package_name} 安装异常: {str(e)}")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def main():
    """主安装函数"""
    print("🚀 信息价识别模块依赖安装")
    print("=" * 50)
    
    # 需要安装的包列表
    packages = [
        ("dashscope", "阿里云百炼SDK - 支持QVQ等模型"),
        ("requests", "HTTP请求库"),
        ("pandas", "数据处理库"),
        ("PyMuPDF", "PDF处理库 (fitz)"),
        ("Pillow", "图像处理库"),
        ("gradio", "Web界面库"),
        ("asyncio", "异步处理库"),
    ]
    
    # 检查当前安装状态
    print("🔍 检查当前依赖状态:")
    installed_packages = []
    missing_packages = []
    
    for package, description in packages:
        # 特殊处理一些包名
        check_name = package
        if package == "PyMuPDF":
            check_name = "fitz"
        elif package == "Pillow":
            check_name = "PIL"
        
        if check_package(check_name):
            print(f"   ✅ {package}: 已安装")
            installed_packages.append(package)
        else:
            print(f"   ❌ {package}: 未安装")
            missing_packages.append((package, description))
    
    print(f"\n📊 统计: {len(installed_packages)} 已安装, {len(missing_packages)} 需要安装")
    
    # 安装缺失的包
    if missing_packages:
        print(f"\n📦 开始安装缺失的依赖...")
        
        success_count = 0
        for package, description in missing_packages:
            if install_package(package, description):
                success_count += 1
            print()  # 空行分隔
        
        print(f"📊 安装结果: {success_count}/{len(missing_packages)} 成功")
        
        if success_count == len(missing_packages):
            print("🎉 所有依赖安装成功!")
        else:
            print("⚠️ 部分依赖安装失败，请手动安装")
    else:
        print("✅ 所有依赖都已安装!")
    
    # 验证关键功能
    print(f"\n🧪 验证关键功能...")
    
    # 验证DashScope SDK
    try:
        import dashscope
        print("✅ DashScope SDK: 可用")
    except ImportError:
        print("❌ DashScope SDK: 不可用")
        print("   请手动安装: pip install dashscope")
    
    # 验证PDF处理
    try:
        import fitz
        print("✅ PDF处理 (PyMuPDF): 可用")
    except ImportError:
        print("❌ PDF处理: 不可用")
        print("   请手动安装: pip install PyMuPDF")
    
    # 验证图像处理
    try:
        from PIL import Image
        print("✅ 图像处理 (Pillow): 可用")
    except ImportError:
        print("❌ 图像处理: 不可用")
        print("   请手动安装: pip install Pillow")
    
    # 验证数据处理
    try:
        import pandas
        print("✅ 数据处理 (pandas): 可用")
    except ImportError:
        print("❌ 数据处理: 不可用")
        print("   请手动安装: pip install pandas")
    
    print(f"\n🎯 安装完成!")
    print("💡 提示:")
    print("   1. 确保设置了正确的API密钥")
    print("   2. 如果使用LM Studio，确保服务在1234端口运行")
    print("   3. 如果网络有问题，可以尝试使用代理或VPN")
    
    # 提供故障排除建议
    print(f"\n🔧 故障排除:")
    print("   如果遇到'current user api does not support http call'错误:")
    print("   - 检查API密钥是否正确")
    print("   - 尝试使用DashScope SDK而不是HTTP调用")
    print("   - 联系阿里云客服确认API权限")
    print()
    print("   如果遇到网络连接问题:")
    print("   - 检查网络连接")
    print("   - 尝试禁用代理")
    print("   - 使用LM Studio本地模型作为替代")

if __name__ == "__main__":
    main()
