# MongoDB数据库导出功能说明

## 功能概述

MCP数据库转换工具现已支持MongoDB JSON导出功能，可以将CSV文件转换为MongoDB兼容的JSON格式，便于直接导入到MongoDB数据库中。

## 新增功能特性

### 1. MongoDB JSON导出格式
- **格式类型**: 🍃 MongoDB JSON导出
- **输出文件**: `.json` 格式
- **数据结构**: 完整的MongoDB集合导出格式

### 2. 智能字段名处理
- **中文字段名**: 完全保留中文字段名
- **特殊字符**: 自动处理MongoDB不兼容的字符（如 `$` 和 `.`）
- **数字开头**: 自动为数字开头的字段名添加前缀
- **空字段名**: 自动生成默认字段名

### 3. 数据类型保持
- **数值类型**: 自动保持整数和浮点数类型
- **字符串类型**: 文本数据保持字符串格式
- **空值处理**: NULL值转换为MongoDB的null

### 4. 元数据支持
- **导出信息**: 包含导出时间、源文件数量等元数据
- **集合信息**: 每个集合包含源文件、文档数量、字段列表等信息
- **文档追踪**: 每个文档包含源文件、导入时间、行索引等追踪信息

## 使用方法

### 在Web界面中使用

1. **选择数据库格式**
   - 在"MCP数据库转换工具"区域
   - 选择"🍃 MongoDB JSON导出"格式

2. **设置输出文件名**
   - 输入文件名（如：`quota_database`）
   - 系统自动添加`.json`扩展名

3. **选择CSV文件**
   - 在文件列表中选择要转换的CSV文件
   - 支持多文件批量转换

4. **执行转换**
   - 点击"🔄 转换选中文件"按钮
   - 等待转换完成

5. **预览和下载**
   - 使用"👀 预览数据库"查看转换结果
   - 下载生成的JSON文件

### 命令行测试

```bash
# 运行测试脚本
py test_mongodb_converter.py
```

## 输出格式说明

### JSON文件结构

```json
{
  "metadata": {
    "export_date": "2025-06-25T11:02:57.660364",
    "source_files": 1,
    "format": "MongoDB JSON Export",
    "description": "CSV to MongoDB collection export"
  },
  "collections": {
    "collection_name": {
      "metadata": {
        "source_file": "source.csv",
        "document_count": 5,
        "fields": ["字段1", "字段2", "字段3"],
        "collection_name": "collection_name"
      },
      "documents": [
        {
          "字段1": "值1",
          "字段2": 123.45,
          "字段3": "值3",
          "_source_file": "source.csv",
          "_import_date": "2025-06-25T11:02:57.665183",
          "_row_index": 0
        }
      ]
    }
  }
}
```

### 字段说明

#### 元数据字段
- `export_date`: 导出时间
- `source_files`: 源文件数量
- `format`: 导出格式标识
- `description`: 格式描述

#### 集合元数据
- `source_file`: 源CSV文件名
- `document_count`: 文档数量
- `fields`: 原始字段列表
- `collection_name`: 集合名称

#### 文档追踪字段
- `_source_file`: 源文件名
- `_import_date`: 导入时间
- `_row_index`: 原始行索引

## MongoDB导入方法

### 使用mongoimport命令

```bash
# 导入单个集合（需要先提取集合数据）
mongoimport --db your_database --collection collection_name --file collection_data.json --jsonArray

# 使用MongoDB Compass图形界面导入
# 1. 打开MongoDB Compass
# 2. 连接到数据库
# 3. 选择数据库和集合
# 4. 点击"Import Data"
# 5. 选择JSON文件导入
```

### 使用Python脚本导入

```python
import json
from pymongo import MongoClient

# 连接MongoDB
client = MongoClient('mongodb://localhost:27017/')
db = client['your_database']

# 读取导出文件
with open('quota_database.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# 导入每个集合
for collection_name, collection_data in data['collections'].items():
    collection = db[collection_name]
    documents = collection_data['documents']
    
    # 批量插入文档
    if documents:
        collection.insert_many(documents)
        print(f"导入集合 {collection_name}: {len(documents)} 个文档")
```

## 技术特性

### 1. 字段名兼容性
- 保留中文字段名
- 处理MongoDB保留字符
- 自动生成安全的字段名

### 2. 数据类型映射
- CSV字符串 → MongoDB字符串
- CSV数值 → MongoDB数值（保持类型）
- CSV空值 → MongoDB null

### 3. 集合命名规则
- 基于CSV文件名生成
- 移除特殊字符
- 确保以字母开头
- 转换为小写

### 4. 预览功能
- 完整的HTML预览界面
- 显示集合统计信息
- 展示示例文档
- 支持字段列表查看

## 注意事项

1. **文件大小**: 大型CSV文件可能生成较大的JSON文件
2. **内存使用**: 转换过程中需要足够内存加载CSV数据
3. **字符编码**: 确保CSV文件使用UTF-8编码
4. **字段名**: 避免使用MongoDB保留字符（`$` 和 `.`）

## 更新日志

### v1.0.0 (2025-06-25)
- ✅ 新增MongoDB JSON导出功能
- ✅ 支持中文字段名
- ✅ 智能数据类型处理
- ✅ 完整的预览功能
- ✅ 元数据和追踪信息
- ✅ 批量文件转换支持

## 技术支持

如有问题或建议，请联系：
- 邮箱: <EMAIL>
- 项目: 北京市2021消耗定额智能提取工具
